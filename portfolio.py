import datetime
import os
import joblib
from lightning import LightningModule
from pytorch_lightning.trainer import Trainer
from sklearn.preprocessing import MinMaxScaler
from core.data_module import KlineDataModule
from core.predictor_config import PredictorConfig
import torch
from core.cst import AllocationType, PositionType, TaskType, Optimizers, TopkType
from core.dot_dict import DotDict as dd
from core.fit_val_test import fit_val_test
torch.set_float32_matmul_precision('medium') 


pred_cfg = PredictorConfig()
pred_cfg.script_name = __file__
# pred_cfg.concat_prediction = True
# pred_cfg.concat_hidden = True
# pred_cfg.proba_as_prediction = False
# pred_cfg.feature_category.factor = True
# pred_cfg.feature_category.extra = False
# pred_cfg.backbone_with_fc = False # very different results

# pred_cfg.selected_indices = [0, 1, 8, 13, 16, 17, 18]
pred_cfg.set_seed()
pred_cfg.n_codes = 20
pred_cfg.earliest_date_before_train_start = True
# pred_cfg.code_sort_by_quote = True
pred_cfg.monitor = 'mean_pnl_before_fee'
# pred_cfg.monitor = 'mean_pnl_after_fee'
# pred_cfg.position_enum == PositionType.Long = True
# pred_cfg.use_softmax = False
# pred_cfg.clip_return.train = True
pred_cfg.limit_margin_per_code = True
# pred_cfg.adapt_distr_shift.val = True
# pred_cfg.adapt_distr_shift.test = True
# pred_cfg.directional_balance = True
# pred_cfg.use_scaler = False
# pred_cfg.scale_per_code = True

# pred_cfg.task_enum = TaskType.Portfolio

# pred_cfg.position_enum = PositionType.Short

# pred_cfg.shuffling.train = False
# pred_cfg.shuffling.codewise = True
# pred_cfg.limit_shift_scale = 0.001


# pred_cfg.position_punishment.in_use = True
pred_cfg.dropout_rate = 0.
# pred_cfg.weight_decay = 0.0001 # 0.0001 bad for tsmx
# pred_cfg.noise_std = 0.01 # 0.05 bad
# pred_cfg.use_batch_norm = True
# pred_cfg.use_projection = True
# pred_cfg.embedding_dim = 16

pred_cfg.pred_len = 1
outer_step = pred_cfg.outer_step = pred_cfg.pred_len
if not pred_cfg.shuffling.train:
    pred_cfg.outer_step = outer_step
pred_cfg.patience = 30
pred_cfg.num_epochs = 30
pred_cfg.batch_size = 64
pred_cfg.learning_rate = 20e-5
pred_cfg.hidden_size = 8 # 1 for episodic_backward, 2 for not
# pred_cfg.num_epochs = 100
# pred_cfg.hidden_size = 8
# pred_cfg.episodic_backward = True
# pred_cfg.batch_size = 64
# pred_cfg.learning_rate = 1000e-5
# pred_cfg.optimize_sharpe = True
# pred_cfg.fee_ratio = 0
# pred_cfg.tcn_with_rnn = True
pred_cfg.num_rnn_layers = 2
# pred_cfg.num_heads = 1
# pred_cfg.mhsa_before_rnn = True
# pred_cfg.mhsa_after_rnn = True
# pred_cfg.bidirectional = True
# pred_cfg.rnn_output_last = False
# pred_cfg.kan_as_fc = True

# pred_cfg.num_block_convs = 3
# pred_cfg.num_tcn_blocks = 2
# pred_cfg.num_tcn_stride = 1
# pred_cfg.zigzag_labelling.in_use = True
# pred_cfg.num_classes = 2
# pred_cfg.sign_as_position = True
# pred_cfg.num_epochs = 1
# pred_cfg.position_enum == PositionType.Long = True
# pred_cfg.optimizer_enum = cst.Optimizers.SGD
# pred_cfg.learning_rate *= .1#1e-4
# pred_cfg.use_mse_loss = True
# pred_cfg.use_cum_mse_loss = True
# pred_cfg.use_bce_loss = True
# pred_cfg.use_cum_bce_loss = True
# pred_cfg.use_mad_loss = True
# pred_cfg.pred_len_as_batch_size.train = True
# pred_cfg.model_name = 'tsmx'
# pred_cfg.model_name = 'mlp'
pred_cfg.model_name = 'tcn'
# pred_cfg.model_name = 'rnn'
# pred_cfg.rnn_name = 'slstm'
# pred_cfg.rnn_name = 'lstm'
pred_cfg.rnn_name = 'gru'
# pred_cfg.rnn_name = 'rnn'
# pred_cfg.model_name = 'itrm'
# pred_cfg.model_name = 'tcn'
# pred_cfg.fee_ratio = 0.001
# pred_cfg.interval_cfg.base = 240
pred_cfg.symbol = 'BTCUSDT'
# pred_cfg.learning_rate *= .100
# pred_cfg.pred_multi_step = False
# pred_cfg.use_ta = True
# pred_cfg.use_original = True
# pred_cfg.decompsition.in_use = True
# pred_cfg.decompsition.concat = True
# pred_cfg.decompsition.with_original = True
# pred_cfg.decompsition.trend_only = True
# pred_cfg.is_channel_independent = True

# pred_cfg.augment_data.train = True
# pred_cfg.augment_data.val = True
pred_cfg.augment_data.rev = True
# pred_cfg.train_start_date = '2021.01.01'
# pred_cfg.train_end_date = '2022.01.01'    
# pred_cfg.val_end_date = '2022.03.01'
# pred_cfg.test_end_date = '2022.05.01'
# pred_cfg.train_end_date = '2022.03.01'    
# pred_cfg.val_end_date = '2022.05.01'
# pred_cfg.test_end_date = '2022.07.01'
# pred_cfg.train_end_date = '2022.05.01'    
# pred_cfg.val_end_date = '2022.07.01'
# pred_cfg.test_end_date = '2022.09.01'    
# pred_cfg.train_end_date = '2022.07.01'    
# pred_cfg.val_end_date = '2022.09.01'
# pred_cfg.test_end_date = '2022.11.01'
# pred_cfg.train_end_date = '2022.09.01'    
# pred_cfg.val_end_date = '2022.11.01'
# pred_cfg.test_end_date = '2023.01.01'      

# pred_cfg.train_end_date = '2022.11.01'    
# pred_cfg.val_end_date = '2023.02.01'
# pred_cfg.test_end_date = '2023.05.01'   

# pred_cfg.train_end_date = '2023.01.01'    
# pred_cfg.val_end_date = '2023.02.01'
# pred_cfg.test_end_date = '2023.03.01'   
# pred_cfg.train_end_date = '2023.02.01'    
# pred_cfg.val_end_date = '2023.03.01'
# pred_cfg.test_end_date = '2023.04.01'       
# pred_cfg.train_end_date = '2023.03.01'    
# pred_cfg.val_end_date = '2023.04.01'
# pred_cfg.test_end_date = '2023.05.01'           
# pred_cfg.train_end_date = '2023.04.01'    
# pred_cfg.val_end_date = '2023.05.01'
# pred_cfg.test_end_date = '2023.06.01'           
# pred_cfg.train_end_date = '2023.01.01'    
# pred_cfg.val_end_date = '2023.03.01'
# pred_cfg.test_end_date = '2023.05.01'      
# pred_cfg.train_start_date = '2022.11.01'  
# pred_cfg.train_end_date = '2023.03.01'    
# pred_cfg.val_end_date = '2023.05.01'
# pred_cfg.test_end_date = '2023.07.01'  
# pred_cfg.train_start_date = '2023.01.01'  
# pred_cfg.train_end_date = '2023.05.01'    
# pred_cfg.val_end_date = '2023.07.01'
# pred_cfg.test_end_date = '2023.09.01'      
# pred_cfg.train_start_date = '2023.03.01'  
# pred_cfg.train_end_date = '2023.07.01'    
# pred_cfg.val_end_date = '2023.09.01'
# pred_cfg.test_end_date = '2023.11.01'         
# pred_cfg.train_end_date = '2023.09.01'    
# pred_cfg.val_end_date = '2023.11.01'
# pred_cfg.test_end_date = '2024.01.01'  

# pred_cfg.train_end_date = '2023.11.01'
# pred_cfg.val_end_date = '2023.12.01'
# pred_cfg.test_end_date = '2024.01.01'

# pred_cfg.train_end_date = '2023.12.01'
# pred_cfg.val_end_date = '2024.01.01'
# pred_cfg.test_end_date = '2024.02.01'

pred_cfg.start_date.multi = '2021.01.01'
# pred_cfg.train_end_date = '2024.01.01'    # hard
# pred_cfg.val_end_date = '2024.02.01'
# pred_cfg.test_end_date = '2024.03.01'
# pred_cfg.train_end_date = '2022.03.01'
# pred_cfg.val_end_date = '2022.07.01'    
# pred_cfg.test_end_date = '2022.11.01'

# pred_cfg.train_end_date = '2022.07.01'
# pred_cfg.val_end_date = '2022.11.01'    
# pred_cfg.test_end_date = '2023.03.01'

# pred_cfg.train_end_date = '2022.11.01'
# pred_cfg.val_end_date = '2023.03.01'    
# pred_cfg.test_end_date = '2023.07.01'
# pred_cfg.train_end_date = '2023.03.01'
# pred_cfg.val_end_date = '2023.07.01'    
# pred_cfg.test_end_date = '2023.11.01'
# pred_cfg.train_end_date = '2023.07.01'
# pred_cfg.val_end_date = '2023.11.01'    
# pred_cfg.test_end_date = '2024.03.01'
# pred_cfg.train_end_date = '2022.07.01'
# pred_cfg.val_end_date = '2023.01.01'    
# pred_cfg.test_end_date = '2023.07.01'
# pred_cfg.train_end_date = '2023.11.01'
# pred_cfg.val_end_date = '2024.03.01'
# pred_cfg.test_end_date = '2024.07.01'
# pred_cfg.train_end_date = '2024.01.01'
# pred_cfg.val_end_date = '2024.03.01'
# pred_cfg.test_end_date = '2024.07.01'

# pred_cfg.train_end_date = '2024.05.01'
# pred_cfg.val_start_date = '2024.03.01'
# pred_cfg.val_end_date = '2024.05.01'
# pred_cfg.test_start_date = '2024.05.01'
# pred_cfg.test_end_date = '2024.07.01'

# pred_cfg.train_end_date = '2024.03.01'
# pred_cfg.val_start_date = '2024.03.01'
# pred_cfg.val_end_date = '2024.05.01'
# pred_cfg.test_start_date = '2024.05.01'
# pred_cfg.test_end_date = '2024.07.01'
# pred_cfg.train_end_date = '2024.05.01'
# pred_cfg.val_end_date = '2024.07.01'
# pred_cfg.test_end_date = '2024.07.01'
pred_cfg.train_end_date = '2024.01.01'
pred_cfg.val_end_date = '2024.09.01'
# pred_cfg.fracdiff = .2
# pred_cfg.cum_feature_num = 7
# pred_cfg.execute_phase.val = False
pred_cfg.execute_phase.test = False

# pred_cfg.adapt.in_use = True
if pred_cfg.meta_adapt.in_use:
    pred_cfg.task_enum = TaskType.PortfolioDoubleAdapt
    # pred_cfg.optimizer_enum = Optimizers.RMSPROP
    pred_cfg.optimizer_enum = Optimizers.ADAM
    pred_cfg.sample_pair = True
    # pred_cfg.adapt.transform_x = False
    pred_cfg.meta_adapt.transform_y = False
    pred_cfg.meta_adapt.offline_lr_dict.outer = pred_cfg.meta_adapt.offline_lr_dict.inner = pred_cfg.learning_rate = 1e-4
    pred_cfg.batch_size = 42




if __name__ == '__main__':
    # pred_cfg.force_train = False
    # pred_cfg.shuffling.codewise = True
    # pred_cfg.rnn_name = 'lstm'
    # pred_cfg.resume_from_ckpt = True
    pred_cfg.num_epochs = 50
    # pred_cfg.step_size.val = 2
    # pred_cfg.interval_cfg.base = 120
    # pred_cfg.scaler = MinMaxScaler()
    # pred_cfg.margin_scale = 1
    # pred_cfg.fee_ratio.stop = 0.0002
    # pred_cfg.skip_step = True
    # pred_cfg.optimize_sharpe = True
    # pred_cfg.optimize_profit_ratio = True
    # pred_cfg.optimize_to_oracle = True
    pred_cfg.limit_shift_scale = 0.003

    pred_cfg.stop_loss.learnable = True
    # pred_cfg.stop_loss.in_use = True
    # pred_cfg.stop_loss.with_position = True
    # pred_cfg.stop_loss.train = True
    # pred_cfg.stop_loss.val = True
    
    pred_cfg.stop_loss.scale = 0.01
    pred_cfg.stop_loss.stop_per_code = True
    pred_cfg.stop_loss.min_ratio = 0.015
    # pred_cfg.stop_loss.min_ratio = 0.0002    
    # pred_cfg.feature_category.temporal = True
    # pred_cfg.augment_data.train = False
    # pred_cfg.augment_data.rev = False
    # pred_cfg.episodic_backward = True
    # pred_cfg.learning_rate = 30e-3
    # pred_cfg.loss_with_oracle = True
    # pred_cfg.use_scaler = False
    # pred_cfg.feature_category.rolling = False
    # pred_cfg.feature_category.original = True
    # pred_cfg.use_projection = True
    # pred_cfg.bidirectional = True
    # pred_cfg.channel_att_after_rnn = True
    # pred_cfg.channel_att_after_tcn = True
    # pred_cfg.tcn_layer_with_temporal_att = True
    # pred_cfg.hidden_size = 8
    # pred_cfg.seq_len = 42
    # pred_cfg.step_size.train = pred_cfg.step_size.val = pred_cfg.pred_len = 48
    # pred_cfg.label_enum == LabelType.LogReturn = True
    # pred_cfg.batch_size = 512
    # pred_cfg.feature_category.rolling = False
    # pred_cfg.feature_category.factor = True
    # pred_cfg.interval_cfg.base = 120
    # pred_cfg.n_codes = 2
    # pred_cfg.train_end_date = '2023.08.01'
    pred_cfg.start_date.multi = '2021.01.01'
    # pred_cfg.limit_margin_per_code = False
    # pred_cfg.interval_cfg.base = 120    
    # pred_cfg.adapt_distr_shift.val = True
    # pred_cfg.pnl_decay.in_use = True
    # pred_cfg.pnl_decay.threshold = 0.00002
    # pred_cfg.feature_category.factor = False
    # pred_cfg.ckpt_file_name = ''
    # pred_cfg.merge_history_data = True
    # pred_cfg.history_file_name = '2024_0828_005253_s20210101'
    # pred_cfg.history_prefix = '2024_0828_004611_s20220101'
    fit_val_test(pred_cfg)