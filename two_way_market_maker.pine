//@version=5
strategy("Two-Way Market Maker", shorttitle="TWMM", overlay=true, pyramiding=100, default_qty_type=strategy.percent_of_equity, default_qty_value=1, commission_type=strategy.commission.percent, commission_value=0.05, process_orders_on_close=true)

// Input parameters
trade_direction = input.string("Both", title="Trading Direction", options=["Long Only", "Short Only", "Both"], group="Strategy Settings")

use_atr = input.bool(false, title="Use ATR for Entry/Scale-in (otherwise fixed %)", group="Entry Parameters")
atr_period = input.int(1, title="ATR Period", minval=1, group="Entry Parameters")
atr_multiplier = input.float(1.0, title="ATR Multiplier", minval=0.1, step=0.1, group="Entry Parameters")
fixed_percent = input.float(1.0, title="Fixed Percentage (%)", minval=0.1, step=0.1, group="Entry Parameters") / 100

cooldown_period = input.int(1, title="Cooldown Period (bars)", minval=1, group="Position Sizing")
initial_position_pct = input.float(1.0, title="Initial Position Size (%)", minval=0.1, step=0.1, group="Position Sizing") / 100
scale_in_coef = input.float(1.0, title="Scale-in Order Coefficient", minval=1.0, step=0.01, group="Position Sizing")
max_scale_in = input.int(100, title="Maximum Scale-in Orders", minval=1, maxval=100, group="Position Sizing")

take_profit_pct = input.float(1.0, title="Take Profit (%)", minval=0.1, step=0.1, group="Exit Parameters") / 100
inventory_decay = input.float(0.99, title="Inventory Decay", minval=0.1, step=0.01, group="Exit Parameters")

// Variables
var float base_price = na
var float long_base_price = na
var float short_base_price = na
var int long_scale_in_count = 0
var int short_scale_in_count = 0
var float long_avg_price = na
var float short_avg_price = na
var float long_position_size = 0.0
var float short_position_size = 0.0
var float long_position_ratio = 0.0
var float short_position_ratio = 0.0
var float long_tp_level = na
var float short_tp_level = na
var float long_entry_level = na
var float short_entry_level = na
var string long_tp_id = na
var string short_tp_id = na
var float max_long_ratio = 0
var float max_short_ratio = 0
// 冷却期变量
var int last_long_entry_bar = -9999  // 最后一次多头开仓的bar索引
var int last_short_entry_bar = -9999 // 最后一次空头开仓的bar索引
long_entry_str = "L_"
short_entry_str = "S_"
atr = ta.atr(atr_period)
atr_distance = (na(atr) ? ta.tr(true): atr) * atr_multiplier
// atr_distance = math.abs(close - open)
// atr_distance = ta.tr(true) * atr_multiplier
// Calculate entry levels - recalculated on each bar
get_entry_level(price, side) =>
    distance = use_atr ? atr_distance: price * fixed_percent
    price - side * distance

// Initialize base price on first bar
if bar_index == 0 or strategy.position_size == 0 and strategy.position_size[1] != 0
    // Update base price to current close price
    base_price := close
    long_base_price := close
    short_base_price := close
    long_tp_level := close * (1 + take_profit_pct)
    short_tp_level := close * (1 - take_profit_pct)
    long_entry_level := get_entry_level(close, 1)
    short_entry_level := get_entry_level(close, -1)
    // Cancel any existing take profit orders
    if strategy.position_size[1] > 0
        // Long position was closed
        long_scale_in_count := 0
        long_position_size := 0.0
    else
        // Short position was closed
        short_scale_in_count := 0
        short_position_size := 0.0


// Calculate position sizes for scale-in orders
get_scale_in_ratio(count) =>
    math.pow(scale_in_coef, count)

get_tp_level(avg_price, count, side) =>
    avg_price * (1 + side * math.pow(inventory_decay, count) * take_profit_pct)

// Function to calculate average position price
calculate_avg_price(current_avg, current_size, new_price, new_size) =>
    total_size = current_size + new_size
    (current_avg * current_size + new_price * new_size) / total_size

// 检查是否在冷却期内
long_in_cooldown = (bar_index - last_long_entry_bar) < cooldown_period
short_in_cooldown = (bar_index - last_short_entry_bar) < cooldown_period

// 检查交易方向
allow_long = trade_direction == "Long Only" or trade_direction == "Both"
allow_short = trade_direction == "Short Only" or trade_direction == "Both"

// Long entry logic
if close <= long_entry_level and long_scale_in_count < max_scale_in and not long_in_cooldown and allow_long
    order_ratio = get_scale_in_ratio(long_scale_in_count)
    order_size = order_ratio * strategy.default_entry_qty(close)
    long_scale_in_count += 1
    
    long_entry_comment = long_entry_str + str.tostring(long_scale_in_count)
    strategy.entry(long_entry_str, strategy.long, qty=order_size, comment=long_entry_comment)

    // 立即更新平均价格和仓位大小
    if na(long_avg_price) or long_position_size == 0
        long_avg_price := long_entry_level  // Use limit price instead of close
        long_position_size := order_size
        long_position_ratio := order_ratio
    else
        long_avg_price := calculate_avg_price(long_avg_price, long_position_size, long_entry_level, order_size)
        long_position_size += order_size
        long_position_ratio += order_ratio
    
    max_long_ratio := math.max(max_long_ratio, long_position_ratio)
    // 更新平仓价格并立即更新平仓限价单
    long_tp_level := get_tp_level(long_avg_price, long_scale_in_count, 1)
    strategy.exit(long_entry_str, limit=long_tp_level, comment=long_entry_comment)
    short_base_price := math.max(long_tp_level, base_price)
    short_entry_level := get_entry_level(short_base_price, -1)
    long_entry_level := get_entry_level(close, 1)

    // 更新最后一次多头开仓时间
    last_long_entry_bar := bar_index


// Short entry logic
else if close >= short_entry_level and short_scale_in_count < max_scale_in and not short_in_cooldown and allow_short
    order_ratio = get_scale_in_ratio(short_scale_in_count)
    order_size = order_ratio * strategy.default_entry_qty(close)
    short_scale_in_count += 1
    
    short_entry_comment = short_entry_str + str.tostring(short_scale_in_count)
    strategy.entry(short_entry_str, strategy.short, qty=order_size, comment=short_entry_comment)

    // 立即更新平均价格和仓位大小
    if na(short_avg_price) or short_position_size == 0
        short_avg_price := short_entry_level  // Use limit price instead of close
        short_position_size := order_size
        short_position_ratio := order_ratio
    else
        short_avg_price := calculate_avg_price(short_avg_price, short_position_size, short_entry_level, order_size)
        short_position_size += order_size
        short_position_ratio += order_ratio
    
    max_short_ratio := math.max(max_short_ratio, short_position_ratio)
    // 更新平仓价格并立即更新平仓限价单
    short_tp_level := get_tp_level(short_avg_price, short_scale_in_count, -1)
    strategy.exit(short_entry_str, limit=short_tp_level, comment=short_entry_comment)
    long_base_price := math.min(short_tp_level, base_price)
    long_entry_level := get_entry_level(long_base_price, 1)
    short_entry_level := get_entry_level(close, -1)

    // 更新最后一次空头开仓时间
    last_short_entry_bar := bar_index


// Visualization using plotchar with line-like characters
plot(base_price, title="Base Price", color=color.orange, style = plot.style_stepline) // Solid line for base price

// Long entry and take profit levels with different characters
plot(allow_long ? long_entry_level : na, title="Long Entry Level", color=color.lime, style = plot.style_stepline) // Dashed line for entry
plot(allow_long ? long_tp_level : na, title="Long Take Profit", color=color.aqua, style = plot.style_stepline) // Dotted line for take profit

// Short entry and take profit levels with different characters
plot(allow_short ? short_entry_level : na, title="Short Entry Level", color=color.red, style = plot.style_stepline) // Dashed line for entry
plot(allow_short ? short_tp_level : na, title="Short Take Profit", color=color.fuchsia, style = plot.style_stepline) // Dotted line for take profit

// Display information
var table info_table = table.new(position.top_right, 8, 8, color.black, color.white, 1, color.gray, 1)
table.cell(info_table, 0, 0, "Trading Direction", text_color=color.white)
table.cell(info_table, 1, 0, trade_direction, text_color=color.white)
table.cell(info_table, 0, 1, "Base Price", text_color=color.orange)
table.cell(info_table, 1, 1, str.tostring(base_price, "#.#####"), text_color=color.orange)

// 根据交易方向显示相关信息
if allow_long
    table.cell(info_table, 0, 2, "Long Avg Price", text_color=color.green)
    table.cell(info_table, 1, 2, str.tostring(long_avg_price, "#.####"), text_color=color.green)
    table.cell(info_table, 0, 4, "Long Scale-in Count", text_color=color.green)
    table.cell(info_table, 1, 4, str.tostring(long_scale_in_count), text_color=color.green)
    table.cell(info_table, 0, 6, "Max Long Ratio", text_color=color.green)
    table.cell(info_table, 1, 6, str.tostring(max_long_ratio, "#.###"), text_color=color.green)

if allow_short
    table.cell(info_table, 0, 3, "Short Avg Price", text_color=color.red)
    table.cell(info_table, 1, 3, str.tostring(short_avg_price, "#.####"), text_color=color.red)
    table.cell(info_table, 0, 5, "Short Scale-in Count", text_color=color.red)
    table.cell(info_table, 1, 5, str.tostring(short_scale_in_count), text_color=color.red)
    table.cell(info_table, 0, 7, "Max Short Ratio", text_color=color.red)
    table.cell(info_table, 1, 7, str.tostring(max_short_ratio, "#.###"), text_color=color.red)

// 显示冷却状态
var label long_cooldown_label = na
var label short_cooldown_label = na

if long_in_cooldown and allow_long
    long_cooldown_label := label.new(bar_index, high, "L冷却中", color=color.new(color.green, 70), style=label.style_label_down, textcolor=color.white, size=size.small)
    label.delete(long_cooldown_label[1])

if short_in_cooldown and allow_short
    short_cooldown_label := label.new(bar_index, low, "S冷却中", color=color.new(color.red, 70), style=label.style_label_up, textcolor=color.white, size=size.small)
    label.delete(short_cooldown_label[1])