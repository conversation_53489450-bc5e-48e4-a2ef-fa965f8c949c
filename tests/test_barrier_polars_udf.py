import polars as pl
import numpy as np
from core.cst import SegmentType
from core.predictor_config import PredictorConfig
import time

# 创建测试数据
data = {
    'code': ['BTCUSDT'] * 10 + ['ETHUSDT'] * 10,
    'open_time': [f'2023-01-{i+1:02d}' for i in range(10)] + [f'2023-02-{i+1:02d}' for i in range(10)],
    'open': [100, 102, 98, 95, 97, 99, 101, 103, 99, 97] + [200, 205, 195, 190, 193, 198, 202, 206, 198, 194],
    'high': [105, 104, 100, 98, 100, 102, 104, 105, 102, 100] + [210, 208, 200, 195, 198, 203, 208, 210, 204, 200],
    'low': [98, 97, 94, 93, 95, 97, 99, 100, 97, 95] + [195, 194, 190, 188, 190, 195, 198, 200, 194, 190],
    'close': [102, 98, 95, 97, 99, 101, 103, 99, 97, 96] + [205, 195, 190, 193, 198, 202, 206, 198, 194, 192]
}

# 创建 DataFrame
df = pl.DataFrame(data)

# 创建配置
cfg = PredictorConfig()
cfg.segment_enum = SegmentType.Barrier
cfg.barrier_range = 0.02  # 设置2%的阈值

def process_barrier_segments_udf(data):
    """
    使用Polars的UDF (User Defined Function)处理barrier segments
    
    这个实现使用Polars的map_batches函数来处理每个code组
    """
    # 确保数据按code和open_time排序
    sorted_data = data.sort(['code', 'open_time']).clone()
    
    # 定义处理单个code组的UDF
    def calculate_segments(batch):
        """计算单个code组的segment_id和is_segment_end"""
        # 获取open和close列
        opens = batch['open'].to_numpy()
        closes = batch['close'].to_numpy()
        n_rows = len(opens)
        
        # 初始化结果数组
        is_segment_end = np.zeros(n_rows, dtype=bool)
        segment_ids = np.zeros(n_rows, dtype=int)
        
        # 初始segment的起始open价格
        segment_start_open = opens[0]
        upper_barrier = segment_start_open * (1 + cfg.barrier_range)
        lower_barrier = segment_start_open * (1 - cfg.barrier_range)
        
        # 当前segment_id
        current_segment_id = 0
        
        # 处理每一行
        for i in range(n_rows):
            close = closes[i]
            
            # 判断close是否突破了barrier范围
            is_break = (close >= upper_barrier) or (close <= lower_barrier)
            
            if is_break:
                is_segment_end[i] = True
                segment_ids[i] = current_segment_id
                
                # 更新segment_id，为下一个segment做准备
                current_segment_id += 1
                
                # 如果不是最后一行，更新下一个segment的起始价格
                if i < n_rows - 1:
                    segment_start_open = opens[i + 1]
                    upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                    lower_barrier = segment_start_open * (1 - cfg.barrier_range)
            else:
                segment_ids[i] = current_segment_id
        
        # 返回包含结果的DataFrame
        return pl.DataFrame({
            'is_segment_end': is_segment_end,
            'segment_id': segment_ids
        })
    
    # 对每个code组应用UDF
    result_dfs = []
    for code, group in sorted_data.group_by('code'):
        # 使用map_batches应用UDF
        segments_df = calculate_segments(group)
        
        # 添加code和open_time列
        result_df = pl.DataFrame({
            'code': group['code'],
            'open_time': group['open_time'],
            'is_segment_end': segments_df['is_segment_end'],
            'segment_id': segments_df['segment_id']
        })
        
        result_dfs.append(result_df)
    
    # 合并所有结果
    if result_dfs:
        return pl.concat(result_dfs)
    return None

def process_barrier_segments_map_batches(data):
    """
    使用Polars的map_batches函数处理barrier segments
    
    这个实现尝试使用Polars的map_batches函数来处理每个code组，
    但由于barrier计算的特殊性，可能无法完全避免Python循环
    """
    # 确保数据按code和open_time排序
    sorted_data = data.sort(['code', 'open_time']).clone()
    
    # 定义处理单个batch的UDF
    def process_batch(batch):
        # 按code分组处理
        result_dfs = []
        for code, group in batch.group_by('code'):
            # 获取open和close列
            opens = group['open'].to_numpy()
            closes = group['close'].to_numpy()
            n_rows = len(opens)
            
            # 初始化结果数组
            is_segment_end = np.zeros(n_rows, dtype=bool)
            segment_ids = np.zeros(n_rows, dtype=int)
            
            # 初始segment的起始open价格
            segment_start_open = opens[0]
            upper_barrier = segment_start_open * (1 + cfg.barrier_range)
            lower_barrier = segment_start_open * (1 - cfg.barrier_range)
            
            # 当前segment_id
            current_segment_id = 0
            
            # 处理每一行
            for i in range(n_rows):
                close = closes[i]
                
                # 判断close是否突破了barrier范围
                is_break = (close >= upper_barrier) or (close <= lower_barrier)
                
                if is_break:
                    is_segment_end[i] = True
                    segment_ids[i] = current_segment_id
                    
                    # 更新segment_id，为下一个segment做准备
                    current_segment_id += 1
                    
                    # 如果不是最后一行，更新下一个segment的起始价格
                    if i < n_rows - 1:
                        segment_start_open = opens[i + 1]
                        upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                        lower_barrier = segment_start_open * (1 - cfg.barrier_range)
                else:
                    segment_ids[i] = current_segment_id
            
            # 创建结果DataFrame
            result_df = pl.DataFrame({
                'code': group['code'],
                'open_time': group['open_time'],
                'is_segment_end': is_segment_end,
                'segment_id': segment_ids
            })
            
            result_dfs.append(result_df)
        
        # 合并所有结果
        if result_dfs:
            return pl.concat(result_dfs)
        return pl.DataFrame()
    
    # 使用map_batches应用UDF
    try:
        # 尝试使用map_batches
        result = sorted_data.map_batches(process_batch)
        return result
    except Exception as e:
        print(f"map_batches failed: {e}")
        # 如果map_batches失败，回退到手动处理
        return process_barrier_segments_udf(sorted_data)

def process_barrier_segments_apply(data):
    """
    使用Polars的apply函数处理barrier segments
    
    这个实现尝试使用Polars的apply函数来处理每个code组
    """
    # 确保数据按code和open_time排序
    sorted_data = data.sort(['code', 'open_time']).clone()
    
    # 定义处理单个code组的UDF
    def calculate_segments_for_group(group):
        # 获取open和close列
        opens = group['open'].to_numpy()
        closes = group['close'].to_numpy()
        n_rows = len(opens)
        
        # 初始化结果数组
        is_segment_end = np.zeros(n_rows, dtype=bool)
        segment_ids = np.zeros(n_rows, dtype=int)
        
        # 初始segment的起始open价格
        segment_start_open = opens[0]
        upper_barrier = segment_start_open * (1 + cfg.barrier_range)
        lower_barrier = segment_start_open * (1 - cfg.barrier_range)
        
        # 当前segment_id
        current_segment_id = 0
        
        # 处理每一行
        for i in range(n_rows):
            close = closes[i]
            
            # 判断close是否突破了barrier范围
            is_break = (close >= upper_barrier) or (close <= lower_barrier)
            
            if is_break:
                is_segment_end[i] = True
                segment_ids[i] = current_segment_id
                
                # 更新segment_id，为下一个segment做准备
                current_segment_id += 1
                
                # 如果不是最后一行，更新下一个segment的起始价格
                if i < n_rows - 1:
                    segment_start_open = opens[i + 1]
                    upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                    lower_barrier = segment_start_open * (1 - cfg.barrier_range)
            else:
                segment_ids[i] = current_segment_id
        
        # 返回包含结果的DataFrame
        return pl.DataFrame({
            'code': group['code'],
            'open_time': group['open_time'],
            'is_segment_end': is_segment_end,
            'segment_id': segment_ids
        })
    
    # 使用apply函数对每个code组应用UDF
    try:
        # 尝试使用apply
        result_dfs = []
        for code, group in sorted_data.group_by('code'):
            result_dfs.append(calculate_segments_for_group(group))
        
        # 合并所有结果
        if result_dfs:
            return pl.concat(result_dfs)
        return None
    except Exception as e:
        print(f"apply failed: {e}")
        # 如果apply失败，回退到手动处理
        return process_barrier_segments_udf(sorted_data)

# 测量性能 - UDF方法
start_time = time.time()
result_udf = process_barrier_segments_udf(df)
end_time = time.time()
udf_time = end_time - start_time
print(f"UDF方法耗时: {udf_time:.6f} 秒")

# 测量性能 - map_batches方法
start_time = time.time()
try:
    result_map_batches = process_barrier_segments_map_batches(df)
    end_time = time.time()
    map_batches_time = end_time - start_time
    print(f"map_batches方法耗时: {map_batches_time:.6f} 秒")
    print(f"相比UDF方法性能提升: {udf_time / map_batches_time:.2f}x")
except Exception as e:
    print(f"map_batches方法失败: {e}")

# 测量性能 - apply方法
start_time = time.time()
try:
    result_apply = process_barrier_segments_apply(df)
    end_time = time.time()
    apply_time = end_time - start_time
    print(f"apply方法耗时: {apply_time:.6f} 秒")
    print(f"相比UDF方法性能提升: {udf_time / apply_time:.2f}x")
except Exception as e:
    print(f"apply方法失败: {e}")

# 打印结果数据帧以检查segment_id
print("\n结果数据帧 (UDF方法):")
print(result_udf.select(['code', 'open_time', 'is_segment_end', 'segment_id']))

# 验证结果是否一致
if 'result_map_batches' in locals() and result_udf is not None and result_map_batches is not None:
    # 按code和open_time排序以便比较
    result_udf = result_udf.sort(['code', 'open_time'])
    result_map_batches = result_map_batches.sort(['code', 'open_time'])
    
    # 检查segment_id和is_segment_end是否一致
    segment_id_match = (result_udf['segment_id'] == result_map_batches['segment_id']).sum() == len(result_udf)
    segment_end_match = (result_udf['is_segment_end'] == result_map_batches['is_segment_end']).sum() == len(result_udf)
    
    print(f"\n结果验证 (UDF vs map_batches):")
    print(f"segment_id一致: {segment_id_match}")
    print(f"is_segment_end一致: {segment_end_match}")

if 'result_apply' in locals() and result_udf is not None and result_apply is not None:
    # 按code和open_time排序以便比较
    result_udf = result_udf.sort(['code', 'open_time'])
    result_apply = result_apply.sort(['code', 'open_time'])
    
    # 检查segment_id和is_segment_end是否一致
    segment_id_match = (result_udf['segment_id'] == result_apply['segment_id']).sum() == len(result_udf)
    segment_end_match = (result_udf['is_segment_end'] == result_apply['is_segment_end']).sum() == len(result_udf)
    
    print(f"\n结果验证 (UDF vs apply):")
    print(f"segment_id一致: {segment_id_match}")
    print(f"is_segment_end一致: {segment_end_match}")

# 将结果合并回原始数据
if result_udf is not None:
    df = df.join(result_udf.select(['code', 'open_time', 'is_segment_end', 'segment_id']), on=['code', 'open_time'], how='left')

# 对每个分割区间计算开始和结束价格
segment_data = df.group_by(['code', 'segment_id']).agg(
    pl.col('open').first().alias('segment_open'),
    pl.col('close').last().alias('segment_close'),
    pl.col('high').max().alias('segment_high'),
    pl.col('low').min().alias('segment_low'),
).sort(['code', 'segment_id']).with_columns(
    pl.col('segment_close').shift().over('code').alias('segment_close_shift')
)

# 将分割数据合并回原始数据
df = df.join(segment_data, on=['code', 'segment_id'], how='left')

# 计算每个分割区间的收益率作为标签
df = df.with_columns(
    (pl.col('segment_close') / pl.col('segment_close_shift') - 1).alias('segment_return')
)

# 打印结果
print("\n原始数据:")
print(df.select(['code', 'open_time', 'open', 'high', 'low', 'close', 'is_segment_end', 'segment_id']))
print("\n分段数据:")
print(segment_data)
print("\n带标签的数据:")
print(df.select(['code', 'open_time', 'close', 'is_segment_end', 'segment_id', 'segment_open', 'segment_close', 'segment_return']))
