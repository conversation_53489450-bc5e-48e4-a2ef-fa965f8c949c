import numpy as np
import pandas as pd

# 生成示例数据（T=100天，Features=[Open, High, Low, Close, Volume]）
periods=100
np.random.seed(42)
dates = pd.date_range("20230101", periods=periods)
arr = np.cumprod(1 + np.random.normal(0, 0.02, 4 * periods)).reshape(periods, 4)
data = pd.DataFrame({
    "Open": arr[:, 0],
    "High": arr.max(axis=1),
    "Low": arr.min(axis=1),
    "Close": arr[:, -1],
    "Volume": np.random.randint(1e6, 2e6, periods)
}, index=dates)

import talib
from sklearn.preprocessing import MinMaxScaler

# 计算MACD (动量指标)
data['MACD'] = talib.MACD(data['Close'].values)[0]  # 取MACD线
data['MACD'] = MinMaxScaler().fit_transform(data[['MACD']].values).flatten()

# 计算RSI (超买超卖指标)
data['RSI'] = talib.RSI(data['Close'].values, timeperiod=14)
data['RSI'] = MinMaxScaler().fit_transform(data[['RSI']].values).flatten()

# 填充NaN（技术指标前几日可能无法计算）
data.fillna(method='bfill', inplace=True)


def preprocess_data(df, feature_ranges):
    """归一化处理，保留价格相对关系"""
    scalers = {}
    processed = pd.DataFrame()
    for col in df.columns:
        if feature_ranges[col] == 'percentage':
            # 价格类：保留相对变化，归一化到[0,1]
            scaler = MinMaxScaler(feature_range=(0, 1))
            col_arr = df[[col]].values
            processed[col] = scaler.fit_transform(col_arr).flatten()
        elif feature_ranges[col] == 'log':
            # 成交量：对数变换后归一化
            col_arr = np.log1p(df[[col]])
            scaler = MinMaxScaler(feature_range=(0, 1))
            processed[col] = scaler.fit_transform(col_arr).flatten()
        scalers[col] = scaler
    return processed, scalers

feature_ranges = {
    'Open': 'percentage',
    'High': 'percentage', 
    'Low': 'percentage',
    'Close': 'percentage',
    'Volume': 'log',
    'MACD': 'percentage',
    'RSI': 'percentage'
}

data_norm, scalers = preprocess_data(data, feature_ranges)


def build_image_channels(data_norm, time_steps=periods):
    """
    将时间序列转换为图像结构
    :param img_size: (H, W) 目标图像尺寸，需满足 H*W == time_steps
    :return: (C, H, W) 的图像数据
    """
    # assert img_size[0] * img_size[1] == time_steps, "尺寸需满足H*W=T"
    img_size=(periods, periods)
    # 定义通道组成
    channels = {
        'Channel1': ['Open', 'High', 'Low', 'Close'],  # OHLC作为多通道
        'Channel2': ['Volume'],
        'Channel3': ['MACD'],
        'Channel4': ['RSI']
    }
    
    images = []
    for ch_name, features in channels.items():
        # 提取特征数据 (T, F)
        ch_data = data_norm[features].values
        
        # 重塑为3D张量 (H, W, F)
        reshaped = ch_data.reshape(img_size[0], img_size[1], -1)
        
        # 调整维度顺序为 (F, H, W)
        if reshaped.shape[-1] > 1:  # 多特征通道
            img = np.moveaxis(reshaped, -1, 0)  # (F, H, W)
        else:
            img = reshaped.transpose(2,0,1)     # (1, H, W)
            
        images.append(img)
    
    # 合并所有通道 (C, H, W)
    return np.concatenate(images, axis=0)

# 示例转换 (H=6, W=5)
image_data = build_image_channels(data_norm, img_size=(6,5))
print(f"图像Shape: {image_data.shape}")  # 输出: (1+1+1+4=7, 6, 5)


import torch
from torch.utils.data import Dataset

class FinancialImageDataset(Dataset):
    def __init__(self, raw_data, window_size=30, img_size=(6,5)):
        self.windows = []
        
        # 滑动窗口生成样本
        for i in range(len(raw_data) - window_size + 1):
            window_data = raw_data.iloc[i:i+window_size]
            processed, _ = preprocess_data(window_data, feature_ranges)
            # processed = add_technical_indicators(processed)  # 添加技术指标
            img = build_image_channels(processed, img_size=img_size)
            self.windows.append(img)
            
    def __len__(self):
        return len(self.windows)
    
    def __getitem__(self, idx):
        img = torch.FloatTensor(self.windows[idx])  # (C, H, W)
        return img

# 使用示例
dataset = FinancialImageDataset(data, window_size=30)
dataloader = torch.utils.data.DataLoader(dataset, batch_size=32, shuffle=True)

import matplotlib.pyplot as plt

def plot_channels(image, channel_names):
    plt.figure(figsize=(15,4))
    for i in range(image.shape[0]):
        plt.subplot(1, image.shape[0], i+1)
        plt.imshow(image[i], cmap='viridis' if i==4 else 'RdYlGn', 
                  vmin=0, vmax=1)
        plt.title(channel_names[i])
        plt.axis('off')
    plt.show()

# 示例可视化
channel_names = ['Open', 'High', 'Low', 'Close', 'Volume', 'MACD', 'RSI']
plot_channels(image_data, channel_names)