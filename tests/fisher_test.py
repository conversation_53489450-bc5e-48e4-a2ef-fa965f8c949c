import math
import polars as pl
import numpy as np
import matplotlib.pyplot as plt
data_len = 200
p = 5 + 0.5 * (np.random.rand(data_len) - 0.5).cumsum()

# Sample data: assuming H and L are in a DataFrame
data = pl.DataFrame({
    'Price': p,
})
# Set Len to 10
rolling_len = 5

# Calculate MaxH and MinL
data = data.with_columns(
    pl.col('Price').rolling_min(window_size=rolling_len).alias('MinL'),
    pl.col('Price').rolling_max(window_size=rolling_len).alias('MaxH')
).with_columns(
    ((pl.col('Price') - pl.col('MinL')) / (pl.col('MaxH') - pl.col('MinL')) - .5).alias('Value1'),
).with_columns(
    pl.col('Value1').ewm_mean(alpha=0.1).alias('Value1EMA')
).with_columns(
    (1 + pl.col('Value1EMA') / (1 - pl.col('Value1EMA'))).log().alias('Fish')
).with_columns(
    pl.col('Fish').ewm_mean(alpha=0.5).alias('FishEMA')
)


# Plotting (assuming you have a plotting library like matplotlib)

plt.plot(data['Price'], label='Price')
# plt.plot(data['Fish'], label='Fish')
# plt.plot(data['FishEMA'], label='FishEMA')
plt.plot(data['Value1'], label='Value1')
plt.plot(data['Value1EMA'], label='Value1EMA')
plt.grid()
plt.legend()
plt.show()