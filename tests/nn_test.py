import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd import Variable

# 假设我们已经准备好了数据
# week 数据集
# 把过去 150 个交易日按照每五个交易日高开低收、vwap、turnover 字段作为一张数据图片
# 最终得到时序长度为 30 的“图片”时序数据
# 此时数据图片大小为 6 × 5
# msnew 数据集
# 把日内八个“半小时 K 线”对应的高开低收、amt 字段作为一张数据图片
# 字段预处理方法采用高开低收价格分别除以前一天收盘价，成交额除以流通市值
# 过去三十个交易日每天的数据图片拼成一个长度为 30 的“图片”时序数据
# 此时数据图片大小为 5 × 8

# 假设我们已经有了数据，并且数据已经被适当地预处理过了
# 下面构造数据图片
data_picture_week = torch.randn(30, 6, 5)  # 30 张数据图片，每张图片大小为 6 × 5
data_picture_msnew = torch.randn(30, 5, 8)  # 30 张数据图片，每张图片大小为 5 × 8

class BasicBlock(nn.Module):
    expansion = 1

    def __init__(self, in_channels, out_channels, stride=1, downsample=None):
        super(BasicBlock, self).__init__()
        self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm1d(out_channels)
        self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size=3, stride=1, padding=1, bias=False)
        self.bn2 = nn.BatchNorm1d(out_channels)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        residual = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = F.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)

        if self.downsample is not None:
            residual = self.downsample(x)

        out += residual
        out = F.relu(out)

        return out


class ResNet(nn.Module):
    def __init__(self, block, layers, num_classes=1000):
        self.inplanes = 64
        super(ResNet, self).__init__()
        self.conv1 = nn.Conv1d(1, 64, kernel_size=7, stride=2, padding=3, bias=False)
        self.bn1 = nn.BatchNorm1d(64)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool1d(kernel_size=3, stride=2, padding=1)
        self.layer1 = self._make_layer(block, 64, layers[0])
        self.layer2 = self._make_layer(block, 128, layers[1], stride=2)
        self.layer3 = self._make_layer(block, 256, layers[2], stride=2)
        self.layer4 = self._make_layer(block, 512, layers[3], stride=2)
        self.avgpool = nn.AvgPool1d(7, stride=1)
        self.fc = nn.Linear(512 * block.expansion, num_classes)

        for m in self.modules():
            if isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def _make_layer(self, block, planes, blocks, stride=1):
        downsample = None
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                nn.Conv1d(self.inplanes, planes * block.expansion, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm1d(planes * block.expansion),
            )

        layers = []
        layers.append(block(self.inplanes, planes, stride, downsample))
        self.inplanes = planes * block.expansion
        for i in range(1, blocks):
            layers.append(block(self.inplanes, planes))

        return nn.Sequential(*layers)

    def forward(self, x):
        x = x.unsqueeze(1)  # 增加通道维度
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)

        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)

        x = self.avgpool(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)

        return x
    
    # 数据预处理，例如标准化
mean = torch.mean(data_picture_week)
std = torch.std(data_picture_week)
data_picture_week = (data_picture_week - mean) / std

mean = torch.mean(data_picture_msnew)
std = torch.std(data_picture_msnew)
data_picture_msnew = (data_picture_msnew - mean) / std

# 初始化ResNet模型
resnet = ResNet(BasicBlock, [2, 2, 2, 2])

# 对week数据集进行特征提取
features_week = resnet(data_picture_week)

# 对msnew数据集进行特征提取
features_msnew = resnet(data_picture_msnew)