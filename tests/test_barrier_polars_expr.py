import polars as pl
import numpy as np
from core.cst import SegmentType
from core.predictor_config import PredictorConfig
import time

# 创建测试数据
data = {
    'code': ['BTCUSDT'] * 10 + ['ETHUSDT'] * 10,
    'open_time': [f'2023-01-{i+1:02d}' for i in range(10)] + [f'2023-02-{i+1:02d}' for i in range(10)],
    'open': [100, 102, 98, 95, 97, 99, 101, 103, 99, 97] + [200, 205, 195, 190, 193, 198, 202, 206, 198, 194],
    'high': [105, 104, 100, 98, 100, 102, 104, 105, 102, 100] + [210, 208, 200, 195, 198, 203, 208, 210, 204, 200],
    'low': [98, 97, 94, 93, 95, 97, 99, 100, 97, 95] + [195, 194, 190, 188, 190, 195, 198, 200, 194, 190],
    'close': [102, 98, 95, 97, 99, 101, 103, 99, 97, 96] + [205, 195, 190, 193, 198, 202, 206, 198, 194, 192]
}

# 创建 DataFrame
df = pl.DataFrame(data)

# 创建配置
cfg = PredictorConfig()
cfg.segment_enum = SegmentType.Barrier
cfg.barrier_range = 0.02  # 设置2%的阈值

def process_barrier_segments_expr(data):
    """
    尝试使用Polars表达式API实现barrier segments处理
    
    这个实现使用自定义的Polars表达式来处理barrier segments，
    尽可能减少Python循环和条件判断
    """
    # 确保数据按code和open_time排序
    sorted_data = data.sort(['code', 'open_time']).clone()
    
    # 使用自定义的Python函数作为UDF (User Defined Function)
    def calculate_segments(group_df):
        """计算单个code组的segment_id和is_segment_end"""
        # 获取open和close列
        opens = group_df['open'].to_numpy()
        closes = group_df['close'].to_numpy()
        n_rows = len(opens)
        
        # 初始化结果数组
        is_segment_end = np.zeros(n_rows, dtype=bool)
        segment_ids = np.zeros(n_rows, dtype=int)
        
        # 初始segment的起始open价格
        segment_start_open = opens[0]
        upper_barrier = segment_start_open * (1 + cfg.barrier_range)
        lower_barrier = segment_start_open * (1 - cfg.barrier_range)
        
        # 当前segment_id
        current_segment_id = 0
        
        # 处理每一行
        for i in range(n_rows):
            close = closes[i]
            
            # 判断close是否突破了barrier范围
            is_break = (close >= upper_barrier) or (close <= lower_barrier)
            
            if is_break:
                is_segment_end[i] = True
                segment_ids[i] = current_segment_id
                
                # 更新segment_id，为下一个segment做准备
                current_segment_id += 1
                
                # 如果不是最后一行，更新下一个segment的起始价格
                if i < n_rows - 1:
                    segment_start_open = opens[i + 1]
                    upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                    lower_barrier = segment_start_open * (1 - cfg.barrier_range)
            else:
                segment_ids[i] = current_segment_id
        
        return pl.DataFrame({
            'is_segment_end': is_segment_end,
            'segment_id': segment_ids
        })
    
    # 使用apply函数对每个code组应用UDF
    result_dfs = []
    for code, group in sorted_data.group_by('code'):
        # 计算segments
        segments_df = calculate_segments(group)
        
        # 添加code和open_time列
        result_df = pl.DataFrame({
            'code': group['code'],
            'open_time': group['open_time'],
            'is_segment_end': segments_df['is_segment_end'],
            'segment_id': segments_df['segment_id']
        })
        
        result_dfs.append(result_df)
    
    # 合并所有结果
    if result_dfs:
        return pl.concat(result_dfs)
    return None

def process_barrier_segments_expr_optimized(data):
    """
    使用Polars表达式API和窗口函数优化barrier segments处理
    
    这个实现尝试使用Polars的表达式API和窗口函数来处理barrier segments，
    但由于barrier计算的特殊性，可能无法完全避免Python循环
    """
    # 确保数据按code和open_time排序
    sorted_data = data.sort(['code', 'open_time']).clone()
    
    # 为每个code组创建一个first_open列，表示该组的第一个open价格
    sorted_data = sorted_data.with_columns([
        pl.col('open').first().over('code').alias('first_open')
    ])
    
    # 计算每个code组的first_open的upper_barrier和lower_barrier
    sorted_data = sorted_data.with_columns([
        (pl.col('first_open') * (1 + cfg.barrier_range)).alias('initial_upper_barrier'),
        (pl.col('first_open') * (1 - cfg.barrier_range)).alias('initial_lower_barrier')
    ])
    
    # 检查第一行是否突破了初始barrier范围
    sorted_data = sorted_data.with_columns([
        ((pl.col('close') >= pl.col('initial_upper_barrier')) | 
         (pl.col('close') <= pl.col('initial_lower_barrier'))).alias('is_initial_break')
    ])
    
    # 使用自定义函数处理每个code组
    def process_with_initial_break(group):
        # 转换为Pandas DataFrame以便更灵活地处理
        pdf = group.to_pandas()
        
        # 初始化结果列
        pdf['is_segment_end'] = False
        pdf['segment_id'] = 0
        
        # 检查第一行是否突破了初始barrier范围
        if pdf['is_initial_break'].iloc[0]:
            pdf.loc[0, 'is_segment_end'] = True
            
            # 如果有多于一行，处理剩余的行
            if len(pdf) > 1:
                # 更新segment_start_open为第二行的open价格
                segment_start_open = pdf['open'].iloc[1]
                upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                lower_barrier = segment_start_open * (1 - cfg.barrier_range)
                
                # 当前segment_id
                current_segment_id = 1
                
                # 处理从第二行开始的每一行
                for i in range(1, len(pdf)):
                    pdf.loc[i, 'segment_id'] = current_segment_id
                    close = pdf['close'].iloc[i]
                    
                    # 判断close是否突破了barrier范围
                    is_break = (close >= upper_barrier) or (close <= lower_barrier)
                    
                    if is_break:
                        pdf.loc[i, 'is_segment_end'] = True
                        
                        # 更新segment_id，为下一个segment做准备
                        current_segment_id += 1
                        
                        # 如果不是最后一行，更新下一个segment的起始价格
                        if i < len(pdf) - 1:
                            segment_start_open = pdf['open'].iloc[i + 1]
                            upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                            lower_barrier = segment_start_open * (1 - cfg.barrier_range)
        else:
            # 初始segment的起始open价格
            segment_start_open = pdf['open'].iloc[0]
            upper_barrier = segment_start_open * (1 + cfg.barrier_range)
            lower_barrier = segment_start_open * (1 - cfg.barrier_range)
            
            # 当前segment_id
            current_segment_id = 0
            
            # 处理每一行
            for i in range(len(pdf)):
                pdf.loc[i, 'segment_id'] = current_segment_id
                close = pdf['close'].iloc[i]
                
                # 判断close是否突破了barrier范围
                is_break = (close >= upper_barrier) or (close <= lower_barrier)
                
                if is_break:
                    pdf.loc[i, 'is_segment_end'] = True
                    
                    # 更新segment_id，为下一个segment做准备
                    current_segment_id += 1
                    
                    # 如果不是最后一行，更新下一个segment的起始价格
                    if i < len(pdf) - 1:
                        segment_start_open = pdf['open'].iloc[i + 1]
                        upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                        lower_barrier = segment_start_open * (1 - cfg.barrier_range)
        
        # 返回包含结果的Polars DataFrame
        return pl.from_pandas(pdf[['code', 'open_time', 'is_segment_end', 'segment_id']])
    
    # 对每个code组应用处理函数
    result_dfs = []
    for code, group in sorted_data.group_by('code'):
        result_dfs.append(process_with_initial_break(group))
    
    # 合并所有结果
    if result_dfs:
        return pl.concat(result_dfs)
    return None

def process_barrier_segments_expr_experimental(data):
    """
    实验性的Polars表达式API实现
    
    这个实现尝试使用Polars的表达式API来处理barrier segments，
    但由于barrier计算的特殊性，可能无法完全避免Python循环
    """
    # 确保数据按code和open_time排序
    sorted_data = data.sort(['code', 'open_time']).clone()
    
    # 使用自定义的Python函数作为UDF (User Defined Function)
    def calculate_segments_with_polars(group_df):
        """使用Polars表达式计算单个code组的segment_id和is_segment_end"""
        # 创建一个新的DataFrame用于存储结果
        result = group_df.select(['code', 'open_time', 'open', 'close'])
        
        # 初始化segment_id和is_segment_end列
        result = result.with_columns([
            pl.lit(0).alias('segment_id'),
            pl.lit(False).alias('is_segment_end')
        ])
        
        # 获取第一行的open价格
        first_open = result['open'][0]
        
        # 计算初始的upper_barrier和lower_barrier
        upper_barrier = first_open * (1 + cfg.barrier_range)
        lower_barrier = first_open * (1 - cfg.barrier_range)
        
        # 创建一个可变的DataFrame来存储中间结果
        temp_result = result.to_pandas()
        
        # 当前segment_id
        current_segment_id = 0
        
        # 处理每一行
        for i in range(len(temp_result)):
            close = temp_result['close'].iloc[i]
            
            # 判断close是否突破了barrier范围
            is_break = (close >= upper_barrier) or (close <= lower_barrier)
            
            if is_break:
                temp_result.loc[i, 'is_segment_end'] = True
                temp_result.loc[i, 'segment_id'] = current_segment_id
                
                # 更新segment_id，为下一个segment做准备
                current_segment_id += 1
                
                # 如果不是最后一行，更新下一个segment的起始价格
                if i < len(temp_result) - 1:
                    segment_start_open = temp_result['open'].iloc[i + 1]
                    upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                    lower_barrier = segment_start_open * (1 - cfg.barrier_range)
            else:
                temp_result.loc[i, 'segment_id'] = current_segment_id
        
        # 将结果转换回Polars DataFrame
        return pl.from_pandas(temp_result[['code', 'open_time', 'is_segment_end', 'segment_id']])
    
    # 对每个code组应用UDF
    result_dfs = []
    for code, group in sorted_data.group_by('code'):
        result_dfs.append(calculate_segments_with_polars(group))
    
    # 合并所有结果
    if result_dfs:
        return pl.concat(result_dfs)
    return None

# 测量性能 - 表达式方法
start_time = time.time()
result_expr = process_barrier_segments_expr(df)
end_time = time.time()
expr_time = end_time - start_time
print(f"表达式方法耗时: {expr_time:.6f} 秒")

# 测量性能 - 优化表达式方法
start_time = time.time()
result_expr_optimized = process_barrier_segments_expr_optimized(df)
end_time = time.time()
expr_optimized_time = end_time - start_time
print(f"优化表达式方法耗时: {expr_optimized_time:.6f} 秒")
print(f"性能提升: {expr_time / expr_optimized_time:.2f}x")

# 测量性能 - 实验性表达式方法
start_time = time.time()
result_expr_experimental = process_barrier_segments_expr_experimental(df)
end_time = time.time()
expr_experimental_time = end_time - start_time
print(f"实验性表达式方法耗时: {expr_experimental_time:.6f} 秒")
print(f"相比表达式方法性能提升: {expr_time / expr_experimental_time:.2f}x")

# 打印结果数据帧以检查segment_id
print("\n结果数据帧 (表达式方法):")
print(result_expr.select(['code', 'open_time', 'is_segment_end', 'segment_id']))

print("\n结果数据帧 (优化表达式方法):")
print(result_expr_optimized.select(['code', 'open_time', 'is_segment_end', 'segment_id']))

print("\n结果数据帧 (实验性表达式方法):")
print(result_expr_experimental.select(['code', 'open_time', 'is_segment_end', 'segment_id']))

# 验证结果是否一致
if result_expr is not None and result_expr_optimized is not None:
    # 按code和open_time排序以便比较
    result_expr = result_expr.sort(['code', 'open_time'])
    result_expr_optimized = result_expr_optimized.sort(['code', 'open_time'])
    
    # 检查segment_id和is_segment_end是否一致
    segment_id_match = (result_expr['segment_id'] == result_expr_optimized['segment_id']).sum() == len(result_expr)
    segment_end_match = (result_expr['is_segment_end'] == result_expr_optimized['is_segment_end']).sum() == len(result_expr)
    
    print(f"\n结果验证 (表达式方法 vs 优化表达式方法):")
    print(f"segment_id一致: {segment_id_match}")
    print(f"is_segment_end一致: {segment_end_match}")

# 验证实验性方法的结果是否一致
if result_expr is not None and result_expr_experimental is not None:
    # 按code和open_time排序以便比较
    result_expr = result_expr.sort(['code', 'open_time'])
    result_expr_experimental = result_expr_experimental.sort(['code', 'open_time'])
    
    # 检查segment_id和is_segment_end是否一致
    segment_id_match = (result_expr['segment_id'] == result_expr_experimental['segment_id']).sum() == len(result_expr)
    segment_end_match = (result_expr['is_segment_end'] == result_expr_experimental['is_segment_end']).sum() == len(result_expr)
    
    print(f"\n结果验证 (表达式方法 vs 实验性表达式方法):")
    print(f"segment_id一致: {segment_id_match}")
    print(f"is_segment_end一致: {segment_end_match}")

# 将结果合并回原始数据
if result_expr_experimental is not None:
    df = df.join(result_expr_experimental.select(['code', 'open_time', 'is_segment_end', 'segment_id']), on=['code', 'open_time'], how='left')

# 对每个分割区间计算开始和结束价格
segment_data = df.group_by(['code', 'segment_id']).agg(
    pl.col('open').first().alias('segment_open'),
    pl.col('close').last().alias('segment_close'),
    pl.col('high').max().alias('segment_high'),
    pl.col('low').min().alias('segment_low'),
).sort(['code', 'segment_id']).with_columns(
    pl.col('segment_close').shift().over('code').alias('segment_close_shift')
)

# 将分割数据合并回原始数据
df = df.join(segment_data, on=['code', 'segment_id'], how='left')

# 计算每个分割区间的收益率作为标签
df = df.with_columns(
    (pl.col('segment_close') / pl.col('segment_close_shift') - 1).alias('segment_return')
)

# 打印结果
print("\n原始数据:")
print(df.select(['code', 'open_time', 'open', 'high', 'low', 'close', 'is_segment_end', 'segment_id']))
print("\n分段数据:")
print(segment_data)
print("\n带标签的数据:")
print(df.select(['code', 'open_time', 'close', 'is_segment_end', 'segment_id', 'segment_open', 'segment_close', 'segment_return']))
