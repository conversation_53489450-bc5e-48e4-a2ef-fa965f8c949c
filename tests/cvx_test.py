import torch
import torch.nn as nn
import cvxpy as cp
from cvxpylayers.torch import CvxpyLayer

# 定义一个简单的凸优化问题
n = 2
m = 3
A = cp.Parameter((m, n))
b = cp.Parameter(m)
x = cp.Variable(n)
objective = cp.Minimize(0.5 * cp.pnorm(A @ x - b, p=2)**2)
constraints = [x >= 0]
problem = cp.Problem(objective, constraints)

# 创建 CvxpyLayer 层
cvxpylayer = CvxpyLayer(problem, parameters=[A, b], variables=[x])

class ConvexOptimizationModel(nn.Module):
    def __init__(self):
        super(ConvexOptimizationModel, self).__init__()
        # 初始化可学习参数
        self.A = nn.Parameter(torch.randn(m, n))
        self.b = nn.Parameter(torch.randn(m))

    def forward(self, A_input=None, b_input=None):
        if A_input is not None:
            A_value = A_input
        else:
            A_value = self.A
        
        if b_input is not None:
            b_value = b_input
        else:
            b_value = self.b
        
        # 解决凸优化问题
        solution, = cvxpylayer(A_value, b_value)
        return solution

# 初始化模型
model = ConvexOptimizationModel()

# 示例输入
A_input = torch.randn(m, n)
b_input = torch.randn(m)

# 前向传播
solution = model(A_input, b_input)
print("Solution:", solution)

# 或者直接使用模型中的参数
solution_with_params = model()
print("Solution with params:", solution_with_params)