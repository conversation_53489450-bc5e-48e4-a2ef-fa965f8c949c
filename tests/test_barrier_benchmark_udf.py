import polars as pl
import numpy as np
from core.cst import SegmentType
from core.predictor_config import PredictorConfig
import concurrent.futures
import time
import random

# 设置随机种子以确保结果可重现
random.seed(42)
np.random.seed(42)

# 创建更大的测试数据集
def generate_test_data(num_codes=10, rows_per_code=1000):
    codes = [f"CODE{i}" for i in range(num_codes)]
    all_data = []
    
    for code in codes:
        # 生成初始价格
        initial_price = random.uniform(100, 1000)
        
        # 生成价格序列
        prices = [initial_price]
        for _ in range(rows_per_code - 1):
            # 添加一些随机波动
            change = random.uniform(-0.02, 0.02)
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        # 生成open, high, low, close价格
        opens = prices.copy()
        closes = [p * (1 + random.uniform(-0.01, 0.01)) for p in prices]
        highs = [max(o, c) * (1 + random.uniform(0, 0.01)) for o, c in zip(opens, closes)]
        lows = [min(o, c) * (1 - random.uniform(0, 0.01)) for o, c in zip(opens, closes)]
        
        # 生成时间戳
        timestamps = [f"2023-01-{i+1:02d}" if i < 31 else f"2023-02-{i-30:02d}" for i in range(rows_per_code)]
        
        # 为每个code添加数据
        for i in range(rows_per_code):
            all_data.append({
                'code': code,
                'open_time': timestamps[i],
                'open': opens[i],
                'high': highs[i],
                'low': lows[i],
                'close': closes[i]
            })
    
    return pl.DataFrame(all_data)

# 原始的处理函数（循环方式）
def process_barrier_segments_original(data):
    # 创建一个存储结果的列表
    result_dfs = []
    
    # 对每个code分组处理
    for code, group in data.group_by('code'):
        df = group.sort('open_time')  # 确保按时间排序
        
        # 初始化变量
        segment_id = 0
        segment_start_open = df['open'][0]  # 初始segment的起始open价格
        upper_barrier = segment_start_open * (1 + cfg.barrier_range)
        lower_barrier = segment_start_open * (1 - cfg.barrier_range)
        is_segment_end = []
        segment_ids = []
        
        # 遍历每一行数据
        for i, row in enumerate(df.iter_rows(named=True)):
            close = row['close']
            # 判断close是否突破了barrier范围
            if close >= upper_barrier or close <= lower_barrier:
                is_segment_end.append(True)
                segment_ids.append(segment_id)
                # 更新segment_id和起始价格，为下一个segment做准备
                segment_id += 1
                if i < len(df) - 1:  # 确保不会越界
                    segment_start_open = df['open'][i + 1]
                    upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                    lower_barrier = segment_start_open * (1 - cfg.barrier_range)
            else:
                is_segment_end.append(False)
                segment_ids.append(segment_id)
        
        # 创建结果数据帧
        result = pl.DataFrame({
            'code': df['code'],
            'open_time': df['open_time'],
            'is_segment_end': is_segment_end,
            'segment_id': segment_ids
        })
        
        result_dfs.append(result)
    
    # 合并所有结果
    if result_dfs:
        return pl.concat(result_dfs)
    return None

# 向量化+并行处理方法
def process_single_code_vectorized(group_df):
    """使用向量化的方式处理单个code的数据"""
    df = group_df.sort('open_time').clone()  # 确保按时间排序并创建副本
    
    # 获取所有的open和close值
    opens = df['open'].to_numpy()
    closes = df['close'].to_numpy()
    n_rows = len(df)
    
    # 初始化结果数组
    is_segment_end = np.zeros(n_rows, dtype=bool)
    segment_ids = np.zeros(n_rows, dtype=int)
    
    # 初始segment的起始open价格
    segment_start_open = opens[0]
    upper_barrier = segment_start_open * (1 + cfg.barrier_range)
    lower_barrier = segment_start_open * (1 - cfg.barrier_range)
    
    # 当前segment_id
    current_segment_id = 0
    
    # 使用NumPy的向量化操作来计算barrier突破
    for i in range(n_rows):
        # 判断close是否突破了barrier范围
        is_break = (closes[i] >= upper_barrier) or (closes[i] <= lower_barrier)
        
        if is_break:
            is_segment_end[i] = True
            segment_ids[i] = current_segment_id
            
            # 更新segment_id，为下一个segment做准备
            current_segment_id += 1
            
            # 如果不是最后一行，更新下一个segment的起始价格
            if i < n_rows - 1:
                segment_start_open = opens[i + 1]
                upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                lower_barrier = segment_start_open * (1 - cfg.barrier_range)
        else:
            segment_ids[i] = current_segment_id
    
    # 创建结果数据帧
    result = pl.DataFrame({
        'code': df['code'],
        'open_time': df['open_time'],
        'is_segment_end': is_segment_end,
        'segment_id': segment_ids
    })
    
    return result

def process_barrier_segments_parallel(data):
    """并行处理多个code的数据"""
    # 按code分组
    unique_codes = data['code'].unique().to_list()
    
    # 为每个code创建一个数据子集
    code_dfs = [data.filter(pl.col('code') == code) for code in unique_codes]
    
    # 使用线程池并行处理每个code
    with concurrent.futures.ThreadPoolExecutor() as executor:
        results = list(executor.map(process_single_code_vectorized, code_dfs))
    
    # 合并所有结果
    if results:
        return pl.concat(results)
    return None

# Polars UDF方法
def process_barrier_segments_udf(data: pl.DataFrame):
    """使用Polars的UDF处理barrier segments"""
    # 确保数据按code和open_time排序
    sorted_data = data.sort(['code', 'open_time']).clone()
    
    # 定义处理单个code组的UDF
    def calculate_segments(group):
        """计算单个code组的segment_id和is_segment_end"""
        # 获取open和close列
        opens = group['open'].to_numpy()
        closes = group['close'].to_numpy()
        n_rows = len(opens)
        
        # 初始化结果数组
        is_segment_end = np.zeros(n_rows, dtype=bool)
        segment_ids = np.zeros(n_rows, dtype=int)
        
        # 初始segment的起始open价格
        segment_start_open = opens[0]
        upper_barrier = segment_start_open * (1 + cfg.barrier_range)
        lower_barrier = segment_start_open * (1 - cfg.barrier_range)
        
        # 当前segment_id
        current_segment_id = 0
        
        # 处理每一行
        for i in range(n_rows):
            close = closes[i]
            
            # 判断close是否突破了barrier范围
            is_break = (close >= upper_barrier) or (close <= lower_barrier)
            
            if is_break:
                is_segment_end[i] = True
                segment_ids[i] = current_segment_id
                
                # 更新segment_id，为下一个segment做准备
                current_segment_id += 1
                
                # 如果不是最后一行，更新下一个segment的起始价格
                if i < n_rows - 1:
                    segment_start_open = opens[i + 1]
                    upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                    lower_barrier = segment_start_open * (1 - cfg.barrier_range)
            else:
                segment_ids[i] = current_segment_id
        
        # 返回包含结果的DataFrame
        return pl.DataFrame({
            'code': group['code'],
            'open_time': group['open_time'],
            'is_segment_end': is_segment_end,
            'segment_id': segment_ids
        })
    return sorted_data.group_by('code').map_groups(calculate_segments)
    # 对每个code组应用UDF
    result_dfs = []
    for code, group in sorted_data.group_by('code'):
        result_dfs.append(calculate_segments(group))
    
    # 合并所有结果
    if result_dfs:
        return pl.concat(result_dfs)
    return None

# 创建配置
cfg = PredictorConfig()
cfg.segment_enum = SegmentType.Barrier
cfg.barrier_range = 0.02  # 设置2%的阈值

# 生成测试数据
print("生成测试数据...")
df = generate_test_data(num_codes=30, rows_per_code=100000)
print(f"测试数据大小: {len(df)} 行")

# 测试原始方法
print("\n测试原始方法...")
start_time = time.time()
result_original = process_barrier_segments_original(df)
end_time = time.time()
original_time = end_time - start_time
print(f"原始方法耗时: {original_time:.6f} 秒")

# 测试向量化+并行方法
print("\n测试向量化+并行方法...")
start_time = time.time()
result_parallel = process_barrier_segments_parallel(df)
end_time = time.time()
parallel_time = end_time - start_time
print(f"向量化+并行方法耗时: {parallel_time:.6f} 秒")
print(f"性能提升: {original_time / parallel_time:.2f}x")

# 测试Polars UDF方法
print("\n测试Polars UDF方法...")
start_time = time.time()
result_udf = process_barrier_segments_udf(df)
end_time = time.time()
udf_time = end_time - start_time
print(f"Polars UDF方法耗时: {udf_time:.6f} 秒")
print(f"相比原始方法性能提升: {original_time / udf_time:.2f}x")
print(f"相比向量化+并行方法性能提升: {parallel_time / udf_time:.2f}x")

# 验证结果是否一致
if result_original is not None and result_parallel is not None and result_udf is not None:
    # 按code和open_time排序以便比较
    result_original = result_original.sort(['code', 'open_time'])
    result_parallel = result_parallel.sort(['code', 'open_time'])
    result_udf = result_udf.sort(['code', 'open_time'])
    
    # 检查segment_id和is_segment_end是否一致
    segment_id_match_parallel = (result_original['segment_id'] == result_parallel['segment_id']).sum() == len(result_original)
    segment_end_match_parallel = (result_original['is_segment_end'] == result_parallel['is_segment_end']).sum() == len(result_original)
    
    segment_id_match_udf = (result_original['segment_id'] == result_udf['segment_id']).sum() == len(result_original)
    segment_end_match_udf = (result_original['is_segment_end'] == result_udf['is_segment_end']).sum() == len(result_original)
    
    print(f"\n结果验证:")
    print(f"向量化+并行方法 segment_id一致: {segment_id_match_parallel}")
    print(f"向量化+并行方法 is_segment_end一致: {segment_end_match_parallel}")
    print(f"Polars UDF方法 segment_id一致: {segment_id_match_udf}")
    print(f"Polars UDF方法 is_segment_end一致: {segment_end_match_udf}")
else:
    print("无法比较结果，至少有一个方法返回了None")
