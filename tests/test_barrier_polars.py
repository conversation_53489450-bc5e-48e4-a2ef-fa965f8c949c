import polars as pl
import numpy as np
from core.cst import SegmentType
from core.predictor_config import PredictorConfig
import time

# 创建测试数据
data = {
    'code': ['BTCUSDT'] * 10 + ['ETHUSDT'] * 10,
    'open_time': [f'2023-01-{i+1:02d}' for i in range(10)] + [f'2023-02-{i+1:02d}' for i in range(10)],
    'open': [100, 102, 98, 95, 97, 99, 101, 103, 99, 97] + [200, 205, 195, 190, 193, 198, 202, 206, 198, 194],
    'high': [105, 104, 100, 98, 100, 102, 104, 105, 102, 100] + [210, 208, 200, 195, 198, 203, 208, 210, 204, 200],
    'low': [98, 97, 94, 93, 95, 97, 99, 100, 97, 95] + [195, 194, 190, 188, 190, 195, 198, 200, 194, 190],
    'close': [102, 98, 95, 97, 99, 101, 103, 99, 97, 96] + [205, 195, 190, 193, 198, 202, 206, 198, 194, 192]
}

# 创建 DataFrame
df = pl.DataFrame(data)

# 创建配置
cfg = PredictorConfig()
cfg.segment_enum = SegmentType.Barrier
cfg.barrier_range = 0.02  # 设置2%的阈值

def process_barrier_segments_polars(data):
    """
    使用Polars原生函数处理barrier segments

    这个实现使用Polars的窗口函数和表达式来计算segment_id和is_segment_end，
    避免使用Python循环和NumPy数组
    """
    # 确保数据按code和open_time排序
    sorted_data = data.sort(['code', 'open_time']).clone()

    # 为每个code创建一个唯一的组
    result_dfs = []

    # 对每个code分别处理
    for code, group in sorted_data.group_by('code'):
        # 初始化segment_id和is_segment_end列
        df_with_segments = group.with_columns([
            pl.lit(0).alias('segment_id'),
            pl.lit(False).alias('is_segment_end')
        ])

        # 获取第一行的open价格作为初始segment的起始价格
        first_open = df_with_segments['open'][0]

        # 计算初始的upper_barrier和lower_barrier
        upper_barrier = first_open * (1 + cfg.barrier_range)
        lower_barrier = first_open * (1 - cfg.barrier_range)

        # 创建一个空的结果列表
        segments = []
        current_segment_id = 0
        segment_start_open = first_open

        # 遍历每一行
        for i, row in enumerate(df_with_segments.iter_rows(named=True)):
            close = row['close']

            # 判断close是否突破了barrier范围
            is_break = (close >= upper_barrier) or (close <= lower_barrier)

            # 创建当前行的结果
            current_row = {
                'code': row['code'],
                'open_time': row['open_time'],
                'is_segment_end': is_break,
                'segment_id': current_segment_id
            }
            segments.append(current_row)

            # 如果突破了barrier范围，更新segment_id和barrier范围
            if is_break:
                current_segment_id += 1
                if i < len(df_with_segments) - 1:
                    segment_start_open = df_with_segments['open'][i + 1]
                    upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                    lower_barrier = segment_start_open * (1 - cfg.barrier_range)

        # 将结果转换为DataFrame并添加到结果列表
        result_dfs.append(pl.DataFrame(segments))

    # 合并所有结果
    if result_dfs:
        return pl.concat(result_dfs)
    return None

def process_barrier_segments_polars_optimized(data):
    """
    使用Polars的表达式API优化barrier segments处理

    这个实现使用Polars的表达式API来处理每个code组
    """
    # 确保数据按code和open_time排序
    sorted_data = data.sort(['code', 'open_time']).clone()

    # 定义处理单个code组的函数
    def process_group(group):
        n_rows = len(group)

        # 初始化结果数组
        is_segment_end = [False] * n_rows
        segment_ids = [0] * n_rows

        # 初始segment的起始open价格
        segment_start_open = group['open'][0]
        upper_barrier = segment_start_open * (1 + cfg.barrier_range)
        lower_barrier = segment_start_open * (1 - cfg.barrier_range)

        # 当前segment_id
        current_segment_id = 0

        # 处理每一行
        for i in range(n_rows):
            close = group['close'][i]

            # 判断close是否突破了barrier范围
            is_break = (close >= upper_barrier) or (close <= lower_barrier)

            if is_break:
                is_segment_end[i] = True
                segment_ids[i] = current_segment_id

                # 更新segment_id，为下一个segment做准备
                current_segment_id += 1

                # 如果不是最后一行，更新下一个segment的起始价格
                if i < n_rows - 1:
                    segment_start_open = group['open'][i + 1]
                    upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                    lower_barrier = segment_start_open * (1 - cfg.barrier_range)
            else:
                segment_ids[i] = current_segment_id

        # 返回包含结果的DataFrame
        return pl.DataFrame({
            'code': group['code'],
            'open_time': group['open_time'],
            'is_segment_end': is_segment_end,
            'segment_id': segment_ids
        })

    # 对每个code组应用处理函数
    result_dfs = []
    for code, group in sorted_data.group_by('code'):
        result_dfs.append(process_group(group))

    # 合并所有结果
    if result_dfs:
        return pl.concat(result_dfs)
    return None

def process_barrier_segments_polars_fully_optimized(data):
    """
    尝试使用Polars的表达式和窗口函数完全优化barrier segments处理

    注意：由于barrier计算的特殊性（每次计算依赖于前面的结果），
    完全使用Polars表达式可能很难实现，这里提供一个部分优化的版本
    """
    # 确保数据按code和open_time排序
    sorted_data = data.sort(['code', 'open_time']).clone()

    # 使用自定义的Python函数来处理每个code组
    def process_code_group(group):
        # 转换为Pandas DataFrame以便更灵活地处理
        pdf = group.to_pandas()

        # 初始化结果列
        pdf['is_segment_end'] = False
        pdf['segment_id'] = 0

        # 初始segment的起始open价格
        segment_start_open = pdf['open'].iloc[0]
        upper_barrier = segment_start_open * (1 + cfg.barrier_range)
        lower_barrier = segment_start_open * (1 - cfg.barrier_range)

        # 当前segment_id
        current_segment_id = 0

        # 处理每一行
        for i in range(len(pdf)):
            close = pdf['close'].iloc[i]

            # 判断close是否突破了barrier范围
            is_break = (close >= upper_barrier) or (close <= lower_barrier)

            if is_break:
                pdf.loc[i, 'is_segment_end'] = True
                pdf.loc[i, 'segment_id'] = current_segment_id

                # 更新segment_id，为下一个segment做准备
                current_segment_id += 1

                # 如果不是最后一行，更新下一个segment的起始价格
                if i < len(pdf) - 1:
                    segment_start_open = pdf['open'].iloc[i + 1]
                    upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                    lower_barrier = segment_start_open * (1 - cfg.barrier_range)
            else:
                pdf.loc[i, 'segment_id'] = current_segment_id

        # 返回包含结果的Polars DataFrame
        return pl.from_pandas(pdf[['code', 'open_time', 'is_segment_end', 'segment_id']])

    # 对每个code组应用处理函数
    result_dfs = []
    for code, group in sorted_data.group_by('code'):
        result_dfs.append(process_code_group(group))

    # 合并所有结果
    if result_dfs:
        return pl.concat(result_dfs)
    return None

# 测量性能 - 原始方法
start_time = time.time()
result_original = process_barrier_segments_polars(df)
end_time = time.time()
original_time = end_time - start_time
print(f"原始Polars方法耗时: {original_time:.6f} 秒")

# 测量性能 - 优化方法
start_time = time.time()
result_optimized = process_barrier_segments_polars_optimized(df)
end_time = time.time()
optimized_time = end_time - start_time
print(f"优化Polars方法耗时: {optimized_time:.6f} 秒")
print(f"性能提升: {original_time / optimized_time:.2f}x")

# 测量性能 - 完全优化方法
start_time = time.time()
result_fully_optimized = process_barrier_segments_polars_fully_optimized(df)
end_time = time.time()
fully_optimized_time = end_time - start_time
print(f"完全优化Polars方法耗时: {fully_optimized_time:.6f} 秒")
print(f"相比原始方法性能提升: {original_time / fully_optimized_time:.2f}x")

# 打印结果数据帧以检查segment_id
print("\n结果数据帧 (原始方法):")
print(result_original.select(['code', 'open_time', 'is_segment_end', 'segment_id']))

print("\n结果数据帧 (优化方法):")
print(result_optimized.select(['code', 'open_time', 'is_segment_end', 'segment_id']))

print("\n结果数据帧 (完全优化方法):")
print(result_fully_optimized.select(['code', 'open_time', 'is_segment_end', 'segment_id']))

# 验证结果是否一致
if result_original is not None and result_fully_optimized is not None:
    # 按code和open_time排序以便比较
    result_original = result_original.sort(['code', 'open_time'])
    result_fully_optimized = result_fully_optimized.sort(['code', 'open_time'])

    # 检查segment_id和is_segment_end是否一致
    segment_id_match = (result_original['segment_id'] == result_fully_optimized['segment_id']).sum() == len(result_original)
    segment_end_match = (result_original['is_segment_end'] == result_fully_optimized['is_segment_end']).sum() == len(result_original)

    print(f"\n结果验证:")
    print(f"segment_id一致: {segment_id_match}")
    print(f"is_segment_end一致: {segment_end_match}")

# 将结果合并回原始数据
if result_fully_optimized is not None:
    df = df.join(result_fully_optimized.select(['code', 'open_time', 'is_segment_end', 'segment_id']), on=['code', 'open_time'], how='left')

# 对每个分割区间计算开始和结束价格
segment_data = df.group_by(['code', 'segment_id']).agg(
    pl.col('open').first().alias('segment_open'),
    pl.col('close').last().alias('segment_close'),
    pl.col('high').max().alias('segment_high'),
    pl.col('low').min().alias('segment_low'),
).sort(['code', 'segment_id']).with_columns(
    pl.col('segment_close').shift().over('code').alias('segment_close_shift')
)

# 将分割数据合并回原始数据
df = df.join(segment_data, on=['code', 'segment_id'], how='left')

# 计算每个分割区间的收益率作为标签
df = df.with_columns(
    (pl.col('segment_close') / pl.col('segment_close_shift') - 1).alias('segment_return')
)

# 打印结果
print("\n原始数据:")
print(df.select(['code', 'open_time', 'open', 'high', 'low', 'close', 'is_segment_end', 'segment_id']))
print("\n分段数据:")
print(segment_data)
print("\n带标签的数据:")
print(df.select(['code', 'open_time', 'close', 'is_segment_end', 'segment_id', 'segment_open', 'segment_close', 'segment_return']))
