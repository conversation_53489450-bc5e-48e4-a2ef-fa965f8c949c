# 定义品种特征的 Embedding 层
import torch
import torch.nn as nn

category_categories = 50  # 品种类别数
category_embedding_dim = 8  # 嵌入维度

category_embedding = nn.Embedding(category_categories, category_embedding_dim)

# 输入品种特征（假设有 3 个样本，分别是品种 0、25、49）
category_inputs = torch.tensor([0, 25, 49])

# 嵌入向量
category_vectors = category_embedding(category_inputs)
print(f'{category_vectors.shape = }')
print(category_vectors)
