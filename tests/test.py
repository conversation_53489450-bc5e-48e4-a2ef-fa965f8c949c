import numpy as np
import pandas as pd

def grid_trading_profit(ohlc, grid_spacing, trade_size, initial_cash, price_range):
    # 初始化
    cash = initial_cash
    position = 0  # 正值表示多头，负值表示空头
    profit = 0
    open_price = None

    # 定义买入（开多）和卖出（开空）价格网格
    grid_levels = [price for price in range(price_range[0], price_range[1] + grid_spacing, grid_spacing)]
    
    # 遍历价格序列
    for i, row in ohlc.iterrows():
        print(f"Processing {i}th row: {row}")
        # 获取当前的收盘价格（可以使用High/Low进行更细粒度的模拟）
        
        open_price = row['Open']
        current_price = row['Close']
        
        # 检查开多仓（买入开仓）条件
        for buy_price in grid_levels:
            if current_price <= buy_price and cash >= buy_price * trade_size:
                # 执行买入操作，增加多头仓位
                cash -= buy_price * trade_size
                position += trade_size  # 多仓增加
                print(f"Open Long at {buy_price}, Cash: {cash}, Position: {position}")

        # 检查平多仓条件
        for sell_price in grid_levels:
            if current_price >= sell_price and position > 0:
                # 执行卖出操作，减少多头仓位
                cash += sell_price * trade_size
                position -= trade_size  # 多仓减少
                profit += (sell_price - buy_price) * trade_size
                print(f"Close Long at {sell_price}, Cash: {cash}, Position: {position}, Profit: {profit}")
        
        # 检查开空仓（卖出开仓）条件
        for sell_price in grid_levels:
            if current_price >= sell_price and cash >= sell_price * trade_size:
                # 执行卖出操作，增加空头仓位
                cash -= sell_price * trade_size
                position -= trade_size  # 空仓增加（负值）
                print(f"Open Short at {sell_price}, Cash: {cash}, Position: {position}")
        
        # 检查平空仓条件
        for buy_price in grid_levels:
            if current_price <= buy_price and position < 0:
                # 执行买入操作，减少空头仓位
                cash += buy_price * trade_size
                position += trade_size  # 空仓减少（负值回升）
                profit += (sell_price - buy_price) * trade_size
                print(f"Close Short at {buy_price}, Cash: {cash}, Position: {position}, Profit: {profit}")
    
    # 返回最终结果
    return profit, position

# 示例数据
data = {'Open': [100, 102, 101, 99, 98],
        'High': [103, 104, 105, 100, 101],
        'Low': [98, 99, 97, 95, 96],
        'Close': [101, 100, 99, 98, 97]}
ohlc = pd.DataFrame(data)

# 调用函数
profit, final_position = grid_trading_profit(
    ohlc, grid_spacing=5, trade_size=1, initial_cash=1000, price_range=(95, 105)
)

print(f"捕捉到的总利润: {profit}, 结束时的仓位: {final_position}")


import polars as pl

# 创建一个示例 DataFrame
df = pl.DataFrame({
    "code": ["A", "A", "B", "B", "C"],
    "value": [101, 102, 103, 104, 105]
})

# 将 'code' 列转换为 Polars List 列表并封装回 DataFrame
df_with_list = df.select([
    pl.col("code").implode().alias("code_list"),
    pl.col("value").implode().list.sum().alias("value_sum")
    ])

print(df_with_list)


import polars as pl

def get_onehot(struct, l):
    arr = np.zeros(l)
    arr[struct['code_index']] = struct['value']
    return arr.tolist()


# 假设的 code_arr
code_arr = ["A", "B", "C", "D"]

# 创建一个示例 DataFrame
df = pl.DataFrame({
    "code": ["A", "B"],
    "value": [101, 102]
})

# 创建一个映射字典，快速查找 code 对应的 index
code_to_index = {v: i for i, v in enumerate(code_arr)}
l = len(code_arr)
eye = np.eye(l)

print(code_to_index)
# 使用 pl.map_dict 来快速找到对应的索引，并生成 one-hot 编码
df_onehot = df.with_columns([
    pl.col("code").replace(code_to_index, default=None).alias("code_index"),
]).with_columns([
    pl.col("code_index").map_elements(lambda x: eye[x].tolist()).alias("code_onehot"),
    pl.struct("code_index", "value").map_elements(lambda x: get_onehot(x, l)).alias("value_onehot"),
])


# 查看结果
print(df_onehot)



import numpy as np

# Given parameters
m = 10  # Number of arrays to be extracted
n = 100  # Size of the identity matrix

# Create an identity matrix of size n x n
identity_matrix = np.eye(n)

# Generate random indices within the range [0, n) for m elements
random_index_arr = np.random.choice(n, m, replace=False)

# Extract m arrays from the identity matrix using the random index array
extracted_arrays = identity_matrix[random_index_arr]

# Sum the extracted arrays along axis 1
sum_of_arrays_axis0 = np.sum(extracted_arrays, axis=0)
sum_of_arrays_axis0

# print(f'{random_index_arr = }\n{sum_of_arrays_axis0 = }')

import pandas as pd

# 示例数据
data = {
    'code': ['A', 'A', 'B', 'B', 'C'],
    'value': [1, 2, 3, 4, 5]
}
df = pd.DataFrame(data)

# 按照 'code' 列进行 groupby，并将索引放入字典
grouped_indices = {code: group.index.tolist() for code, group in df.groupby('code')}

print(grouped_indices)


import numpy as np

class RollingRsquare:
    def __init__(self, window):
        self.window = window
        self.buffer = np.full(window, np.nan)
        self.x = np.arange(window)  # x 表示窗口内数据点的位置
        self.na_count = 0  # 缺失值计数

    def update(self, val):
        if not np.isnan(val):
            self.na_count -= (np.isnan(self.buffer[0]) and self.buffer[0] == self.buffer[0])
        
        self.buffer = np.roll(self.buffer, -1)
        self.buffer[-1] = val
        
        if not np.isnan(val):
            self.na_count += (np.isnan(self.buffer[-1]) and self.buffer[-1] == self.buffer[-1])
        
        valid_indices = ~np.isnan(self.buffer)
        N = np.sum(valid_indices)  # 有效数据点的数量
        if N < 2:
            return np.nan  # 不足以计算 R^2
        
        y = self.buffer[valid_indices]
        x = self.x[valid_indices]
        
        xy_sum = np.sum(x * y)
        x_sum = np.sum(x)
        y_sum = np.sum(y)
        x2_sum = np.sum(x ** 2)
        y2_sum = np.sum(y ** 2)
        
        numerator = N * xy_sum - x_sum * y_sum
        denominator = np.sqrt((N * x2_sum - x_sum ** 2) * (N * y2_sum - y_sum ** 2))
        
        if denominator == 0:
            return 0.0  # 避免除以零的情况
        
        rvalue = numerator / denominator
        return rvalue ** 2

# 使用示例
rsq = RollingRsquare(5)

# 假设我们有一些数据
data = [1.0, 3.0, 2.0, 4.0, 6.0, 5.0, 7.0, 9.0, 8.0, 10.0]

for val in data:
    print(f"R^2: {rsq.update(val)}")