import cvxpy as cp
from cvxpylayers.torch import Cvxpy<PERSON>ayer
import numpy as np
import torch
import torch.nn as nn

Batch = 100
n = 5
c = 1/2

#风险预算模型依赖的参数：资产协方差矩阵的cholesky分解L和风险预算向量b(n*n)
#首先定义该凸优化问题
b = cp.Parameter(n,nonneg = True)
L = cp.Parameter((n,n)) #这里的L是cholesky分解后的下三角矩阵
y = cp.Variable(n)
cons = [y>=0,b.T @ cp.log(y) >= c]
obj = cp.Minimize(cp.sum_squares(L.T @ y))
prob = cp.Problem(obj,cons)

#risk budget net
class RiskNet(nn.Module):
    
    def __init__(self,num_assets,num_factors):
        super(RiskNet,self).__init__()
        self.num_features = num_assets * num_factors
        self.fc1_num= 10
        self.fc2_num = 5
        self.fc1 = nn.Linear(self.num_features,self.fc1_num)
        self.fc2 = nn.Linear(self.fc1_num,self.fc2_num)
        self.Leakyrelu = nn.PReLU()
        self.HardTanh = nn.Hardtanh(0.05,0.3) #控制风险预算的范围,由于先经过softmax层因此都是正实数
        self.softmax = nn.Softmax(dim = 1)
    
    def forward(self,x):
        #forward过程写成了分步形式方便理解
        x = x.view(-1,self.num_features) #Batch*55
        x = self.fc1(x)
        x = self.Leakyrelu(x)
        x = self.fc2(x)
        x = self.softmax(x)
        x = self.HardTanh(x)
        return x    

#实例化网络层
risknet = RiskNet(5,11)
layer = CvxpyLayer(prob,[b,L],[y])
for param in layer.parameters():
    print(f'{param = }')
batch_factor = torch.randn((Batch,11,n),requires_grad=True) #5个资产各11个特征，55个因子
def get_cov_matrix(num):    
    cov_matrix = np.random.rand(num, num)
    cov_matrix = (cov_matrix + cov_matrix.T) / 2
    # 修正协方差矩阵为正定
    eigvals, eigvecs = np.linalg.eigh(cov_matrix)
    eigvals[eigvals < 1e-6] = 1e-6  # 修正负特征值
    cov_matrix = eigvecs @ np.diag(eigvals) @ eigvecs.T
    return cov_matrix.astype(np.float32)

Sigma = [get_cov_matrix(n) for _ in range(Batch)] #5个资产的协方差矩阵(这里实际上不能用随机数，要确保是满足协方差矩阵的对称矩阵
Sigma = np.array(Sigma)
print(f'{Sigma.max() = }')
L = [torch.tensor(np.linalg.cholesky(Q)) for Q in Sigma]
r = torch.randn((Batch,n), requires_grad = True) #label，未来10日的收益率向量
b = risknet(batch_factor)
loss_sum = 0
for i in range(Batch): #cvxpylayer貌似不能按照batch进行计算
    risk_budget = b[i]
    l = L[i]
    return_vector = r[i]
    y_star, = layer(risk_budget,l)
    weight = y_star/torch.sum(y_star)
    loss_sum += -1 * 1/Batch * torch.matmul(weight,return_vector)#组合收益率的负数
loss_sum.backward()


import cvxpy as cp
import numpy as np
from cvxpylayers.torch import CvxpyLayer
import torch

# 配置
n_codes = 5

# 定义动态参数
predicted_return = cp.Parameter(n_codes, name="predicted_return")  # 动态预测收益
cov_matrix = cp.Parameter((n_codes, n_codes), PSD=True, name="cov_matrix")  # 动态协方差矩阵

# 定义变量
weights = cp.Variable(n_codes, name="weights")

# 定义目标函数
pnl = predicted_return @ weights
total_var = cp.quad_form(weights, cov_matrix, assume_PSD=True)

allocation_type = "Sharpe"  # 示例：选择配置类型
if allocation_type == "Sharpe":
    objective = cp.Maximize(pnl - total_var * 0.1)
elif allocation_type == "MinVar":
    objective = cp.Minimize(total_var)
else:
    objective = cp.Maximize(pnl)

# 定义约束
constraints = [
    cp.norm1(weights) == 1,  # 权重总和（绝对值）为 1
    cp.abs(weights) <= 0.5   # 单资产权重限制在 [-0.5, 0.5]
]

# 构建优化问题
problem = cp.Problem(objective, constraints)

# 检查是否符合 DPP 规则
assert problem.is_dpp(), "Problem does not satisfy DPP rules!"

# 定义 CvxpyLayer
allocator = CvxpyLayer(
    problem,
    parameters=[predicted_return, cov_matrix],
    variables=[weights]
)

# 示例动态参数值
np.random.seed(42)
predicted_return_value = np.random.randn(n_codes)
raw_cov_matrix = np.random.rand(n_codes, n_codes)
raw_cov_matrix = (raw_cov_matrix + raw_cov_matrix.T) / 2  # 对称化

# 修正协方差矩阵为正定
eigvals, eigvecs = np.linalg.eigh(raw_cov_matrix)
eigvals[eigvals < 1e-6] = 1e-6
cov_matrix_value = eigvecs @ np.diag(eigvals) @ eigvecs.T

# 设置动态参数的值
predicted_return.value = predicted_return_value
cov_matrix.value = cov_matrix_value

# 测试分配器
predicted_return_torch = torch.tensor(predicted_return_value, dtype=torch.float32)
cov_matrix_torch = torch.tensor(cov_matrix_value, dtype=torch.float32)

# 使用 CvxpyLayer
optimal_weights, = allocator(predicted_return_torch, cov_matrix_torch)
print("Optimal weights:", optimal_weights.detach().numpy())

