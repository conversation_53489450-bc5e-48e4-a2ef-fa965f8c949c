import time
import numpy as np

def advanced_grid_trading_loss(Open, High, Low, Close, d, n, p, s=1, fee_rate=0.0002):
    """
    高级网格交易损失函数
    
    参数:
    Open, High, Low, Close -- 价格序列向量 (numpy数组)
    d -- 网格间距 (如0.01表示1%)
    n -- 网格数量 (总网格数2n+1)
    p -- 每格基础仓位百分比 (如0.01表示1%)
    s -- 仓位递增乘数 (默认为1，表示等额仓位)
    fee_rate -- 交易手续费率 (默认为0.02%)
    
    返回:
    dict: {
        'total_loss': 累计损失,
        'long_loss': 多头损失,
        'short_loss': 空头损失,
        'fee_cost': 手续费,
        'transactions': 交易记录,
        'grid_levels': 网格价格,
        'final_position': 最终仓位
    }
    """
    # 初始化网格
    m = Open[0]  # 中间价为开盘价
    grid_levels = [m * (1 + i * d) for i in range(-n, n+1)]
    grid_levels = sorted(grid_levels)
    
    # 计算每个网格的仓位权重 (考虑递增乘数)
    grid_weights = [p * (s ** abs(i-n-1)) for i in range(2*n+1)]
    total_weight = sum(grid_weights)
    grid_weights = [w/total_weight for w in grid_weights]  # 归一化
    
    # 初始化变量
    current_price = Open[0]
    current_long = 0
    current_short = 0
    cash = 1  # 初始资金为1
    transactions = []
    
    # 损失记录
    long_loss = 0
    short_loss = 0
    total_fee = 0
    
    # 确定价格在K线期间触发的网格
    def get_triggered_grids(open_p, high_p, low_p, close_p):
        triggered = set()
        for i, level in enumerate(grid_levels):
            if low_p <= level <= high_p:
                triggered.add(i)
        return sorted(triggered)
    
    # 主循环
    for i in range(len(Open)):
        open_p, high_p, low_p, close_p = Open[i], High[i], Low[i], Close[i]
        triggered_grids = get_triggered_grids(open_p, high_p, low_p, close_p)
        
        for grid_idx in triggered_grids:
            grid_price = grid_levels[grid_idx]
            position_change = grid_weights[grid_idx]
            
            # 多头网格 (当前价格高于网格价)
            if close_p > grid_price:
                # 平空头仓位
                if current_short > 0:
                    close_amount = min(current_short, position_change)
                    close_value = close_amount * grid_price
                    fee = close_value * fee_rate
                    
                    # 记录空头平仓
                    profit = (grid_price - close_p) * close_amount  # 空头利润 = (开仓价-平仓价)*数量
                    short_loss += profit
                    transactions.append((
                        f"Bar {i}", "Close Short", grid_price, close_p, 
                        -close_amount, profit, fee
                    ))
                    
                    current_short -= close_amount
                    cash += close_value - fee
                    total_fee += fee
                
                # 开多头仓位
                if cash > 0:
                    open_amount = min(position_change, cash / close_p)
                    open_value = open_amount * close_p
                    fee = open_value * fee_rate
                    
                    transactions.append((
                        f"Bar {i}", "Open Long", close_p, None,
                        open_amount, 0, fee
                    ))
                    
                    current_long += open_amount
                    cash -= open_value + fee
                    total_fee += fee
            
            # 空头网格 (当前价格低于网格价)
            elif close_p < grid_price:
                # 平多头仓位
                if current_long > 0:
                    close_amount = min(current_long, position_change)
                    close_value = close_amount * grid_price
                    fee = close_value * fee_rate
                    
                    # 记录多头平仓
                    profit = (close_p - grid_price) * close_amount  # 多头利润 = (平仓价-开仓价)*数量
                    long_loss += profit
                    transactions.append((
                        f"Bar {i}", "Close Long", grid_price, close_p, 
                        -close_amount, profit, fee
                    ))
                    
                    current_long -= close_amount
                    cash += close_value - fee
                    total_fee += fee
                
                # 开空头仓位
                if cash > 0:
                    open_amount = min(position_change, cash / close_p)
                    open_value = open_amount * close_p
                    fee = open_value * fee_rate
                    
                    transactions.append((
                        f"Bar {i}", "Open Short", close_p, None,
                        -open_amount, 0, fee
                    ))
                    
                    current_short += open_amount
                    cash -= open_value + fee  # 开空需要冻结保证金
                    total_fee += fee
    
    # 最终平仓
    final_price = Close[-1]
    if current_long > 0:
        loss = (final_price - grid_levels[-1]) * current_long
        long_loss += loss
        transactions.append((
            "Final", "Close Long", grid_levels[-1], final_price,
            -current_long, loss, 0
        ))
    if current_short > 0:
        loss = (grid_levels[0] - final_price) * current_short
        short_loss += loss
        transactions.append((
            "Final", "Close Short", grid_levels[0], final_price,
            current_short, loss, 0
        ))
    
    total_loss = long_loss + short_loss
    
    return {
        'total_loss': total_loss,
        'long_loss': long_loss,
        'short_loss': short_loss,
        'fee_cost': total_fee,
        'transactions': transactions,
        'grid_levels': grid_levels,
        'final_position': current_long - current_short
    }
import numpy as np

def vectorized_grid_trading(Open, High, Low, Close, d, n, p, s=1, fee_rate=0.0002):
    """
    向量化实现的网格交易损失函数
    
    参数:
    Open, High, Low, Close -- 价格序列 (numpy数组)
    d -- 网格间距 (如0.01表示1%)
    n -- 网格数量 (总网格数2n+1)
    p -- 每格基础仓位百分比
    s -- 仓位递增乘数
    fee_rate -- 手续费率
    
    返回:
    与之前相同的字典结构
    """
    # 生成网格水平 (向量化)
    m = Open[0]
    grid_levels = m * (1 + np.arange(-n, n+1) * d)
    grid_levels.sort()
    
    # 计算网格权重 (向量化)
    dist_from_center = np.abs(np.arange(2*n+1) - n)
    grid_weights = p * (s ** dist_from_center)
    grid_weights /= grid_weights.sum()  # 归一化
    
    # 初始化仓位记录 (向量化)
    n_bars = len(Open)
    position_changes = np.zeros(2*n+1)  # 每个网格的累计仓位变化
    
    # 检测每个K线触发的网格 (向量化)
    triggered = (Low[:, None] <= grid_levels) & (High[:, None] >= grid_levels)
    
    # 计算交易信号 (向量化)
    close_above = Close[:, None] > grid_levels
    close_below = Close[:, None] < grid_levels
    
    # 多头信号 (价格高于网格且被触发)
    long_signals = triggered & close_above
    # 空头信号 (价格低于网格且被触发)
    short_signals = triggered & close_below
    
    # 计算仓位变化 (向量化)
    long_changes = np.where(long_signals, grid_weights, 0)
    short_changes = np.where(short_signals, -grid_weights, 0)
    total_changes = long_changes + short_changes
    
    # 计算累计仓位 (向量化累加)
    cumulative_position = np.cumsum(total_changes, axis=0)
    
    # 计算交易价格 (向量化)
    trade_prices = np.where(triggered, 
                          np.where(close_above, Close[:, None], 
                                 np.where(close_below, Close[:, None], np.nan)),
                          np.nan)
    
    # 计算交易价值 (向量化)
    trade_values = total_changes * trade_prices
    trade_values = np.nan_to_num(trade_values)
    
    # 计算手续费 (向量化)
    fees = np.abs(trade_values) * fee_rate
    
    # 计算利润 (向量化)
    entry_prices = np.full_like(trade_prices, np.nan)
    entry_prices[0] = np.where(triggered[0], trade_prices[0], np.nan)
    
    for i in range(1, n_bars):
        entry_prices[i] = np.where(~triggered[i], entry_prices[i-1], 
                                 np.where(triggered[i], trade_prices[i], np.nan))
    
    profits = total_changes * (trade_prices - entry_prices)
    
    # 汇总结果 (向量化)
    long_profits = np.where(profits > 0, profits, 0).sum()
    short_profits = np.where(profits < 0, profits, 0).sum()
    total_fee = fees.sum()
    
    # 最终平仓 (向量化)
    final_position = cumulative_position[-1]
    if final_position > 0:  # 平多
        long_profits += final_position * (Close[-1] - entry_prices[-1, np.argmax(cumulative_position[-1] > 0)])
    elif final_position < 0:  # 平空
        short_profits += final_position * (Close[-1] - entry_prices[-1, np.argmax(cumulative_position[-1] < 0)])
    
    total_loss = long_profits + short_profits
    
    # 生成交易记录 (部分向量化)
    transactions = []
    for i in range(n_bars):
        for j in range(2*n+1):
            if triggered[i, j]:
                action = "Open Long" if total_changes[i, j] > 0 else "Open Short" if total_changes[i, j] < 0 else None
                if action:
                    transactions.append((
                        f"Bar {i}", action, grid_levels[j], trade_prices[i, j],
                        total_changes[i, j], profits[i, j], fees[i, j]
                    ))
    
    return {
        'total_loss': total_loss,
        'long_loss': long_profits,
        'short_loss': short_profits,
        'fee_cost': total_fee,
        'transactions': transactions,
        'grid_levels': grid_levels,
        'final_position': final_position
    }

# 性能对比测试
if __name__ == "__main__":
    np.random.seed(42)
    n_bars = 10000  # 使用更大的数据量测试性能
    base = np.cumprod(1 + np.random.normal(0, 0.01, n_bars)) * 100
    Open = base
    High = base * (1 + np.abs(np.random.normal(0, 0.005, n_bars)))
    Low = base * (1 - np.abs(np.random.normal(0, 0.005, n_bars)))
    Close = (High + Low) / 2
    
    # 设置参数
    params = {
        'd': 0.01,    # 1%网格间距
        'n': 10,       # 每边5个网格
        'p': 0.02,    # 每格基础仓位2%
        's': 1.01,     # 仓位递增乘数
        'fee_rate': 0.0002
    }


    for fn in [advanced_grid_trading_loss, vectorized_grid_trading]:
        start = time.time()
        result = fn(Open, High, Low, Close, **params)
        end = time.time()
        print(f"{fn.__name__} 耗时: {end - start:.4f} 秒")
        # print(f"网格价格水平: {result['grid_levels']}")
        # print(f"总损失: {result['total_loss']:.4f}")
        # print(f"多头损失: {result['long_loss']:.4f}")
        # print(f"空头损失: {result['short_loss']:.4f}")
        # print(f"手续费: {result['fee_cost']:.4f}")
        # print(f"最终仓位: {result['final_position']:.4f}")
        
        # # 打印交易
        # print("\n交易记录:")
        # for t in result['transactions']:
        #     print(f"{t[0]:<6} {t[1]:<12} @ {t[2]:.2f} 数量: {t[4]:.4f} 利润: {t[5]:.4f} 手续费: {t[6]:.4f}")