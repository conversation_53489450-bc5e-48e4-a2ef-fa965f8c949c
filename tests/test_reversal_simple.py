import polars as pl
import numpy as np

# 创建测试数据
data = {
    'code': ['BTCUSDT'] * 10,
    'open': [100, 102, 98, 95, 97, 99, 101, 103, 99, 97],
    'high': [105, 104, 100, 98, 100, 102, 104, 105, 102, 100],
    'low': [98, 97, 94, 93, 95, 97, 99, 100, 97, 95],
    'close': [102, 98, 95, 97, 99, 101, 103, 99, 97, 96]
}

# 创建 DataFrame
df = pl.DataFrame(data)

# 计算收益率
df = df.with_columns(
    pl.col('close').pct_change().over('code').fill_null(0.).alias('close_roc')
)

# 检测反转点
df = df.with_columns(
    (pl.col('close_roc') * pl.col('close_roc').shift().over('code') <= 0).alias('is_reversal_point').
    fill_null(False).cast(pl.Int32)
)

# 创建分组标识符
df = df.with_row_index('_idx')  # 如果没有索引，创建一个

# 反向排序，应用累积求和，然后恢复原始顺序
df_reversed = df.sort(pl.col('_idx').reverse())
df_reversed = df_reversed.with_columns(
    pl.col('is_reversal_point').cum_sum().over('code').alias('reversal_segment_id')
)
df = df_reversed.sort('_idx')
df = df.drop('_idx').with_columns(
    reversal_segment_id = pl.col('reversal_segment_id').max() - pl.col('reversal_segment_id')
)

# 对每个分割区间计算开始和结束价格
segment_data = df.group_by(['code', 'reversal_segment_id']).agg(
    pl.col('open').first().alias('segment_start_open'),
    pl.col('close').last().alias('segment_end_close')
).sort(['code', 'reversal_segment_id']).with_columns(
    pl.col('segment_end_close').shift().over('code').alias('segment_close_shift')
)

# 将分割数据合并回原始数据
df = df.join(segment_data, on=['code', 'reversal_segment_id'], how='left')

# 计算每个分割区间的收益率
df = df.with_columns(
    (pl.col('segment_end_close') / pl.col('segment_start_open') - 1).alias('reversal_return')
)

# 打印结果
print("原始数据和反转分割结果:")
print(df)

# 打印每个分割区间的信息
print("\n每个反转区间的信息:")
print(segment_data.with_columns(
    (pl.col('segment_end_close') / pl.col('segment_start_open') - 1).alias('reversal_return')
))
