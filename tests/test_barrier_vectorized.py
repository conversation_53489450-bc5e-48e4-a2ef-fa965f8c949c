import polars as pl
import numpy as np
from core.cst import SegmentType
from core.predictor_config import PredictorConfig
import concurrent.futures
import time

# 创建测试数据
data = {
    'code': ['BTCUSDT'] * 10 + ['ETHUSDT'] * 10,
    'open_time': [f'2023-01-{i+1:02d}' for i in range(10)] + [f'2023-02-{i+1:02d}' for i in range(10)],
    'open': [100, 102, 98, 95, 97, 99, 101, 103, 99, 97] + [200, 205, 195, 190, 193, 198, 202, 206, 198, 194],
    'high': [105, 104, 100, 98, 100, 102, 104, 105, 102, 100] + [210, 208, 200, 195, 198, 203, 208, 210, 204, 200],
    'low': [98, 97, 94, 93, 95, 97, 99, 100, 97, 95] + [195, 194, 190, 188, 190, 195, 198, 200, 194, 190],
    'close': [102, 98, 95, 97, 99, 101, 103, 99, 97, 96] + [205, 195, 190, 193, 198, 202, 206, 198, 194, 192]
}

# 创建 DataFrame
df = pl.DataFrame(data)

# 创建配置
cfg = PredictorConfig()
cfg.segment_enum = SegmentType.Barrier
cfg.barrier_range = 0.02  # 设置2%的阈值

def process_single_code_vectorized(group_df):
    """使用完全向量化的方式处理单个code的数据"""
    code = group_df['code'][0]
    df = group_df.sort('open_time').clone()  # 确保按时间排序并创建副本
    
    # 获取所有的open和close值
    opens = df['open'].to_numpy()
    closes = df['close'].to_numpy()
    n_rows = len(df)
    
    # 初始化结果数组
    is_segment_end = np.zeros(n_rows, dtype=bool)
    segment_ids = np.zeros(n_rows, dtype=int)
    
    # 初始segment的起始open价格
    segment_start_open = opens[0]
    upper_barrier = segment_start_open * (1 + cfg.barrier_range)
    lower_barrier = segment_start_open * (1 - cfg.barrier_range)
    
    print(f"Code: {code}, 初始segment_start_open: {segment_start_open}, upper_barrier: {upper_barrier}, lower_barrier: {lower_barrier}")
    
    # 当前segment_id
    current_segment_id = 0
    
    # 使用NumPy的向量化操作来计算barrier突破
    for i in range(n_rows):
        # 判断close是否突破了barrier范围
        is_break = (closes[i] >= upper_barrier) or (closes[i] <= lower_barrier)
        
        if is_break:
            is_segment_end[i] = True
            segment_ids[i] = current_segment_id
            print(f"Code: {code}, 行 {i}: close={closes[i]} 突破了barrier范围 [{lower_barrier}, {upper_barrier}]")
            
            # 更新segment_id，为下一个segment做准备
            current_segment_id += 1
            
            # 如果不是最后一行，更新下一个segment的起始价格
            if i < n_rows - 1:
                segment_start_open = opens[i + 1]
                upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                lower_barrier = segment_start_open * (1 - cfg.barrier_range)
                print(f"  Code: {code}, 新的segment_start_open: {segment_start_open}, upper_barrier: {upper_barrier}, lower_barrier: {lower_barrier}")
        else:
            segment_ids[i] = current_segment_id
            print(f"Code: {code}, 行 {i}: close={closes[i]} 在barrier范围 [{lower_barrier}, {upper_barrier}] 内")
    
    # 创建结果数据帧
    result = pl.DataFrame({
        'code': df['code'],
        'open_time': df['open_time'],
        'is_segment_end': is_segment_end,
        'segment_id': segment_ids
    })
    
    return result

def process_barrier_segments_fully_vectorized(data):
    """尝试完全向量化的方法处理barrier segments"""
    # 这个函数是一个概念验证，实际上由于barrier的计算依赖于前面的结果，
    # 完全向量化可能很难实现，但我们可以尝试减少循环次数
    
    # 按code分组
    unique_codes = data['code'].unique().to_list()
    
    # 为每个code创建一个数据子集
    code_dfs = [data.filter(pl.col('code') == code) for code in unique_codes]
    
    # 使用线程池并行处理每个code
    with concurrent.futures.ThreadPoolExecutor() as executor:
        results = list(executor.map(process_single_code_vectorized, code_dfs))
    
    # 合并所有结果
    if results:
        return pl.concat(results)
    return None

# 测量性能
start_time = time.time()
result_df = process_barrier_segments_fully_vectorized(df)
end_time = time.time()
print(f"\n向量化处理耗时: {end_time - start_time:.6f} 秒")

# 打印结果数据帧以检查segment_id
print("\n结果数据帧:")
print(result_df.select(['code', 'open_time', 'is_segment_end', 'segment_id']))

# 将结果合并回原始数据
if result_df is not None:
    df = df.join(result_df.select(['code', 'open_time', 'is_segment_end', 'segment_id']), on=['code', 'open_time'], how='left')

# 对每个分割区间计算开始和结束价格
segment_data = df.group_by(['code', 'segment_id']).agg(
    pl.col('open').first().alias('segment_open'),
    pl.col('close').last().alias('segment_close'),
    pl.col('high').max().alias('segment_high'),
    pl.col('low').min().alias('segment_low'),
).sort(['code', 'segment_id']).with_columns(
    pl.col('segment_close').shift().over('code').alias('segment_close_shift')
)

# 将分割数据合并回原始数据
df = df.join(segment_data, on=['code', 'segment_id'], how='left')

# 计算每个分割区间的收益率作为标签
df = df.with_columns(
    (pl.col('segment_close') / pl.col('segment_close_shift') - 1).alias('segment_return')
)

# 打印结果
print("\n原始数据:")
print(df.select(['code', 'open_time', 'open', 'high', 'low', 'close', 'is_segment_end', 'segment_id']))
print("\n分段数据:")
print(segment_data)
print("\n带标签的数据:")
print(df.select(['code', 'open_time', 'close', 'is_segment_end', 'segment_id', 'segment_open', 'segment_close', 'segment_return']))
