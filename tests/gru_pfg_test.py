import torch
import torch.nn as nn
import torch.nn.functional as F

# 假设你已经有了形状为 [m, 360] 的 Alpha360 因子数据
m = 100
alpha360_data = torch.randn(m, 360)

class GRUNet(nn.Module):
    def __init__(self, input_size=6, hidden_size=64, num_layers=1):
        super(GRUNet, self).__init__()
        self.gru = nn.GRU(input_size=input_size, hidden_size=hidden_size, num_layers=num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size, hidden_size)  # 可选：用于进一步处理 GRU 输出
    
    def forward(self, x):
        # 将输入重塑为 [m, 60, 6]，即 [batch_size, seq_length, input_size]
        x = x.view(x.size(0), 60, 6)
        
        # GRU 处理
        out, _ = self.gru(x)
        
        # 获取最后一个时间步的输出
        out = out[:, -1, :]
        
        # 可选：通过全连接层进一步处理
        out = self.fc(out)
        
        return out

# 初始化 GRU 网络
gru_net = GRUNet()

# 使用 GRU 网络处理 Alpha360 因子数据
X = gru_net(alpha360_data)

print("Shape of X:", X.shape)  # 应该是 [m, 64]

# X 已经是所需的矩阵，可以直接使用
# X[m, n] 表示第 m 只股票在第 n 维特征上的值

# 打印前几行和前列以验证
print("部分 X 矩阵：")
print(X[:5, :5])  # 打印前 5 只股票的前 5 个特征值作为示例

def compute_X_minus_and_X_pipe(X):
    # 沿着每一行应用 softmax，得到 X− (每个股票内部不同特征的重要性)
    X_minus = F.softmax(X, dim=1)  # shape: [m, n]
    
    # 沿着每一列应用 softmax，得到 X| (同一特征在不同股票间的相对重要性)
    X_pipe = F.softmax(X, dim=0)  # shape: [m, n]

    return X_minus, X_pipe

# 调用函数并获取结果
X_minus, X_pipe = compute_X_minus_and_X_pipe(X)

print("Shape of X_minus:", X_minus.shape)  # 应该是 [m, n]
print("Shape of X_pipe:", X_pipe.shape)  # 应该是 [m, n]


def compute_pearson_corr_coefficient(x, y):
    """计算两个向量 x 和 y 之间的 Pearson 相关系数"""
    # 中心化
    x_centered = x - x.mean()
    y_centered = y - y.mean()
    
    # 计算协方差
    cov = torch.dot(x_centered, y_centered) / (len(x) - 1)
    
    # 计算标准差
    std_x = torch.std(x, unbiased=True)
    std_y = torch.std(y, unbiased=True)
    
    # 避免除零错误
    if std_x == 0 or std_y == 0:
        return 0
    else:
        return cov / (std_x * std_y)


def compute_R_xy(X_minus, X_pipe):
    """计算 R_xy 矩阵"""
    num_stocks = X_minus.shape[0]
    R_xy = torch.zeros((num_stocks, num_stocks))
    
    for i in range(num_stocks):
        for j in range(num_stocks):
            # 获取第 i 只股票在 X− 中的特征向量
            F_x = X_minus[i, :]
            # 获取第 j 只股票在 X| 中的特征向量
            F_y = X_pipe[j, :]

            R_xy[i, j] = compute_pearson_corr_coefficient(F_x, F_y)

            # # 计算均值
            # mean_F_x = F_x.mean()
            # mean_F_y = F_y.mean()
            
            # # 计算协方差
            # cov = torch.dot(F_x - mean_F_x, F_y - mean_F_y) / (len(F_x) - 1)
            
            # # 计算标准差
            # std_F_x = torch.std(F_x, unbiased=True)
            # std_F_y = torch.std(F_y, unbiased=True)
            
            # # 避免除零错误
            # if std_F_x == 0 or std_F_y == 0:
            #     R_xy[i, j] = 0
            # else:
            #     R_xy[i, j] = cov / (std_F_x * std_F_y)
    
    return R_xy


def compute_R_xy_optimized(X_minus, X_pipe):
    """使用矩阵运算优化计算 R_xy 矩阵"""
    # 中心化 X_minus 和 X_pipe
    X_minus_centered = X_minus - X_minus.mean(dim=1, keepdim=True)
    X_pipe_centered = X_pipe - X_pipe.mean(dim=0, keepdim=True)
    
    # 计算协方差矩阵
    cov_matrix = torch.mm(X_minus_centered, X_pipe_centered.T) / (X_minus.shape[1] - 1)
    
    # 计算标准差
    std_X_minus = torch.std(X_minus, dim=1, unbiased=True)
    std_X_pipe = torch.std(X_pipe, dim=1, unbiased=True)
    
    # 构建对角矩阵 D，其对角线元素为标准差的倒数
    D_minus = torch.diag(1 / std_X_minus)
    D_pipe = torch.diag(1 / std_X_pipe)
    
    # 计算相关系数矩阵
    corr_matrix = torch.mm(D_minus, torch.mm(cov_matrix, D_pipe))
    
    return corr_matrix

# 计算 R_xy 矩阵（优化版本）
R_xy = compute_R_xy(X_minus, X_pipe)
R1 = compute_R_xy_optimized(X_minus, X_pipe)

print("Shape of R_xy:", R_xy.shape)  # 应该是 [m, m]
print("Shape of R1:", R1.shape)  # 应该是 [m, m]

# 加权 X 得到初步关系提取特征
weighted_X = torch.mm(R1, X)


def compute_X_hid(X, X_minus, X_pipe, weighted_X):
    """计算 X_hid"""
    # 定义可学习参数 Wa 和 Wb
    Wa = nn.Parameter(torch.randn(1))
    Wb = nn.Parameter(torch.randn(1))
    
    # 计算 X_hid
    X_hid = X - Wa * X_minus - Wb * X_pipe
    
    # 再次计算 R2
    R2 = compute_R_xy_optimized(X_minus, X_pipe)
    
    # 加权 X_hid
    weighted_X_hid = torch.mm(R2, X_hid)
    
    return weighted_X_hid, R2

# 计算 X_hid
X_hid, R2 = compute_X_hid(X, X_minus, X_pipe, weighted_X)

print("Shape of X_hid:", X_hid.shape)  # 应该是 [m, 64]

class SecondaryRelationExtractor(nn.Module):
    def __init__(self, hidden_size=64):
        super(SecondaryRelationExtractor, self).__init__()
        self.Wa = nn.Parameter(torch.randn(1))
        self.Wb = nn.Parameter(torch.randn(1))
        self.fc = nn.Linear(hidden_size, hidden_size)
    
    def forward(self, X):
        # 计算 X− 和 X|
        X_minus = torch.softmax(X, dim=1)
        X_pipe = torch.softmax(X, dim=0)
        
        # 计算 R1
        def pearson_corr_coefficient(x, y):
            x_centered = x - x.mean()
            y_centered = y - y.mean()
            cov = torch.dot(x_centered, y_centered) / (len(x) - 1)
            std_x = torch.std(x, unbiased=True)
            std_y = torch.std(y, unbiased=True)
            if std_x == 0 or std_y == 0:
                return 0
            else:
                return cov / (std_x * std_y)
        
        num_stocks = X.shape[0]
        R1 = torch.zeros((num_stocks, num_stocks))
        for i in range(num_stocks):
            for j in range(num_stocks):
                F_x = X_minus[i, :]
                F_y = X_pipe[j, :]
                R1[i, j] = pearson_corr_coefficient(F_x, F_y)
        
        # 加权 X 得到初步关系提取特征
        weighted_X = torch.mm(R1, X)
        
        # 计算 X_hid
        X_hid = X - self.Wa * X_minus - self.Wb * X_pipe
        
        # 再次计算 R2
        R2 = torch.zeros((num_stocks, num_stocks))
        for i in range(num_stocks):
            for j in range(num_stocks):
                F_x = X_minus[i, :]
                F_y = X_pipe[j, :]
                R2[i, j] = pearson_corr_coefficient(F_x, F_y)
        
        # 加权 X_hid
        weighted_X_hid = torch.mm(R2, X_hid)
        
        return weighted_X_hid

# 初始化次级关系提取器并获取 X_hid
secondary_extractor = SecondaryRelationExtractor()
X_hid = secondary_extractor(X)

print("Shape of X_hid:", X_hid.shape)  # 应该是 [m, 64]


class FinalFeatureExtractor(nn.Module):
    def __init__(self, hidden_size=64):
        super(FinalFeatureExtractor, self).__init__()
        # 定义可学习参数
        self.Wc = nn.Parameter(torch.randn(hidden_size, hidden_size))
        self.Wd = nn.Parameter(torch.randn(hidden_size, hidden_size))
        self.We = nn.Parameter(torch.randn(hidden_size, hidden_size))
        self.Wf = nn.Parameter(torch.randn(hidden_size, hidden_size))
    
    def forward(self, X, R1, X_hid, R2):
        # 线性变换
        term1 = torch.mm(X, self.Wc)
        term2 = torch.mm(torch.mm(R1, X), self.Wd)
        term3 = torch.mm(X_hid, self.We)
        term4 = torch.mm(torch.mm(R2, X_hid), self.Wf)
        
        # 组合所有项
        F_last = term1 + term2 + term3 + term4
        
        return F_last

# 初始化最终特征提取器并获取 F_last
final_extractor = FinalFeatureExtractor()
F_last = final_extractor(X, R1, X_hid, R2)

print("Shape of F_last:", F_last.shape)  # 应该是 [m, 64]