import cvxpy as cp
import numpy as np

# 设置参数
n = 5  # 资产数量
T = 10  # 时间段数量

# 生成随机数据
np.random.seed(42)
r = np.random.randn(T, n)  # 资产回报率
gamma = 0.1  # 风险规避参数
phi_hold = np.random.rand(T, n)  # 持有成本
phi_trade = np.random.rand(T, n)  # 交易成本
Sigma = np.random.rand(T, n, n)  # 风险矩阵
Sigma = np.array([np.dot(S, S.T) for S in Sigma])  # 确保风险矩阵为正定

# 初始资产权重
w0 = np.ones(n) / n

# 决策变量
w = cp.Variable((T, n))  # 每个时间段的资产权重
z = cp.Variable((T, n))  # 每个时间段的交易向量

# 目标函数
objective = 0
for t in range(T):
    if t == 0:
        prev_w = w0
    else:
        prev_w = w[t-1]
    objective += r[t] @ w[t] - gamma * cp.quad_form(w[t], Sigma[t]) - phi_hold[t] @ w[t] - phi_trade[t] @ cp.abs(z[t])

# 约束条件
constraints = []
for t in range(T):
    if t > 0:
        constraints.append(w[t] == w[t-1] + z[t])
    constraints.append(cp.sum(w[t]) == 1)  # 资产权重总和为1
    constraints.append(w[t] >= 0)  # 权重非负

# 优化问题
prob = cp.Problem(cp.Maximize(objective), constraints)
prob.solve()

# 输出结果
print("最优资产权重：")
print(w.value)
print("最优交易向量：")
print(z.value)