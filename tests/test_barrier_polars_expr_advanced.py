import polars as pl
import numpy as np
from core.cst import SegmentType
from core.predictor_config import PredictorConfig
import time

# 创建测试数据
data = {
    'code': ['BTCUSDT'] * 10 + ['ETHUSDT'] * 10,
    'open_time': [f'2023-01-{i+1:02d}' for i in range(10)] + [f'2023-02-{i+1:02d}' for i in range(10)],
    'open': [100, 102, 98, 95, 97, 99, 101, 103, 99, 97] + [200, 205, 195, 190, 193, 198, 202, 206, 198, 194],
    'high': [105, 104, 100, 98, 100, 102, 104, 105, 102, 100] + [210, 208, 200, 195, 198, 203, 208, 210, 204, 200],
    'low': [98, 97, 94, 93, 95, 97, 99, 100, 97, 95] + [195, 194, 190, 188, 190, 195, 198, 200, 194, 190],
    'close': [102, 98, 95, 97, 99, 101, 103, 99, 97, 96] + [205, 195, 190, 193, 198, 202, 206, 198, 194, 192]
}

# 创建 DataFrame
df = pl.DataFrame(data)

# 创建配置
cfg = PredictorConfig()
cfg.segment_enum = SegmentType.Barrier
cfg.barrier_range = 0.02  # 设置2%的阈值

def process_barrier_segments_expr_advanced(data):
    """
    使用Polars表达式API的高级实现
    
    这个实现尝试使用Polars的表达式API来处理barrier segments，
    通过创建辅助列和窗口函数来减少Python循环
    """
    # 确保数据按code和open_time排序
    sorted_data = data.sort(['code', 'open_time']).clone()
    
    # 使用自定义的Python函数作为UDF
    def process_code_group(group):
        """使用Polars表达式处理单个code组"""
        # 获取第一行的open价格
        first_open = group['open'][0]
        
        # 创建一个新的DataFrame，添加辅助列
        df = group.with_columns([
            # 添加一个列，表示每行的open价格
            pl.col('open').alias('row_open'),
            
            # 添加一个列，表示第一行的open价格
            pl.lit(first_open).alias('first_open'),
            
            # 添加upper_barrier和lower_barrier列
            (pl.lit(first_open) * (1 + cfg.barrier_range)).alias('initial_upper_barrier'),
            (pl.lit(first_open) * (1 - cfg.barrier_range)).alias('initial_lower_barrier')
        ])
        
        # 检查第一行是否突破了初始barrier范围
        df = df.with_columns([
            ((pl.col('close') >= pl.col('initial_upper_barrier')) | 
             (pl.col('close') <= pl.col('initial_lower_barrier'))).alias('is_initial_break')
        ])
        
        # 使用Python循环处理每一行
        n_rows = len(df)
        is_segment_end = np.zeros(n_rows, dtype=bool)
        segment_ids = np.zeros(n_rows, dtype=int)
        
        # 初始segment的起始open价格
        segment_start_open = first_open
        upper_barrier = segment_start_open * (1 + cfg.barrier_range)
        lower_barrier = segment_start_open * (1 - cfg.barrier_range)
        
        # 当前segment_id
        current_segment_id = 0
        
        # 处理每一行
        for i in range(n_rows):
            close = df['close'][i]
            
            # 判断close是否突破了barrier范围
            is_break = (close >= upper_barrier) or (close <= lower_barrier)
            
            if is_break:
                is_segment_end[i] = True
                segment_ids[i] = current_segment_id
                
                # 更新segment_id，为下一个segment做准备
                current_segment_id += 1
                
                # 如果不是最后一行，更新下一个segment的起始价格
                if i < n_rows - 1:
                    segment_start_open = df['open'][i + 1]
                    upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                    lower_barrier = segment_start_open * (1 - cfg.barrier_range)
            else:
                segment_ids[i] = current_segment_id
        
        # 创建结果DataFrame
        result = pl.DataFrame({
            'code': df['code'],
            'open_time': df['open_time'],
            'is_segment_end': is_segment_end,
            'segment_id': segment_ids
        })
        
        return result
    
    # 对每个code组应用处理函数
    result_dfs = []
    for code, group in sorted_data.group_by('code'):
        result_dfs.append(process_code_group(group))
    
    # 合并所有结果
    if result_dfs:
        return pl.concat(result_dfs)
    return None

def process_barrier_segments_expr_scan(data):
    """
    使用Polars的scan_rows函数处理barrier segments
    
    这个实现尝试使用Polars的scan_rows函数来处理barrier segments，
    通过累积状态来减少Python循环
    """
    # 确保数据按code和open_time排序
    sorted_data = data.sort(['code', 'open_time']).clone()
    
    # 使用自定义的Python函数作为UDF
    def process_code_group(group):
        """使用scan_rows处理单个code组"""
        # 初始状态
        init_state = {
            'segment_id': 0,
            'segment_start_open': group['open'][0],
            'upper_barrier': group['open'][0] * (1 + cfg.barrier_range),
            'lower_barrier': group['open'][0] * (1 - cfg.barrier_range)
        }
        
        # 定义scan函数
        def scan_fn(state, row):
            close = row['close']
            
            # 判断close是否突破了barrier范围
            is_break = (close >= state['upper_barrier']) or (close <= state['lower_barrier'])
            
            if is_break:
                # 更新segment_id，为下一个segment做准备
                next_segment_id = state['segment_id'] + 1
                
                # 更新下一个segment的起始价格
                next_segment_start_open = row['open_next'] if 'open_next' in row else state['segment_start_open']
                next_upper_barrier = next_segment_start_open * (1 + cfg.barrier_range)
                next_lower_barrier = next_segment_start_open * (1 - cfg.barrier_range)
                
                return {
                    'segment_id': next_segment_id,
                    'segment_start_open': next_segment_start_open,
                    'upper_barrier': next_upper_barrier,
                    'lower_barrier': next_lower_barrier,
                    'is_segment_end': True
                }
            else:
                return {
                    'segment_id': state['segment_id'],
                    'segment_start_open': state['segment_start_open'],
                    'upper_barrier': state['upper_barrier'],
                    'lower_barrier': state['lower_barrier'],
                    'is_segment_end': False
                }
        
        try:
            # 添加next_open列
            group = group.with_columns([
                pl.col('open').shift(-1).alias('open_next')
            ])
            
            # 使用scan_rows函数
            result = group.select([
                pl.col('code'),
                pl.col('open_time'),
                pl.struct(['close', 'open_next']).scan_rows(scan_fn, init_state)
            ])
            
            # 展开结果
            result = result.unnest('scan_rows')
            
            return result
        except Exception as e:
            print(f"scan_rows failed: {e}")
            
            # 回退到手动处理
            n_rows = len(group)
            is_segment_end = np.zeros(n_rows, dtype=bool)
            segment_ids = np.zeros(n_rows, dtype=int)
            
            # 初始segment的起始open价格
            segment_start_open = group['open'][0]
            upper_barrier = segment_start_open * (1 + cfg.barrier_range)
            lower_barrier = segment_start_open * (1 - cfg.barrier_range)
            
            # 当前segment_id
            current_segment_id = 0
            
            # 处理每一行
            for i in range(n_rows):
                close = group['close'][i]
                
                # 判断close是否突破了barrier范围
                is_break = (close >= upper_barrier) or (close <= lower_barrier)
                
                if is_break:
                    is_segment_end[i] = True
                    segment_ids[i] = current_segment_id
                    
                    # 更新segment_id，为下一个segment做准备
                    current_segment_id += 1
                    
                    # 如果不是最后一行，更新下一个segment的起始价格
                    if i < n_rows - 1:
                        segment_start_open = group['open'][i + 1]
                        upper_barrier = segment_start_open * (1 + cfg.barrier_range)
                        lower_barrier = segment_start_open * (1 - cfg.barrier_range)
                else:
                    segment_ids[i] = current_segment_id
            
            # 创建结果DataFrame
            return pl.DataFrame({
                'code': group['code'],
                'open_time': group['open_time'],
                'is_segment_end': is_segment_end,
                'segment_id': segment_ids
            })
    
    # 对每个code组应用处理函数
    result_dfs = []
    for code, group in sorted_data.group_by('code'):
        result_dfs.append(process_code_group(group))
    
    # 合并所有结果
    if result_dfs:
        return pl.concat(result_dfs)
    return None

# 测量性能 - 高级表达式方法
start_time = time.time()
result_expr_advanced = process_barrier_segments_expr_advanced(df)
end_time = time.time()
expr_advanced_time = end_time - start_time
print(f"高级表达式方法耗时: {expr_advanced_time:.6f} 秒")

# 测量性能 - scan_rows方法
start_time = time.time()
try:
    result_scan = process_barrier_segments_expr_scan(df)
    end_time = time.time()
    scan_time = end_time - start_time
    print(f"scan_rows方法耗时: {scan_time:.6f} 秒")
    print(f"相比高级表达式方法性能提升: {expr_advanced_time / scan_time:.2f}x")
except Exception as e:
    print(f"scan_rows方法失败: {e}")

# 打印结果数据帧以检查segment_id
print("\n结果数据帧 (高级表达式方法):")
print(result_expr_advanced.select(['code', 'open_time', 'is_segment_end', 'segment_id']))

if 'result_scan' in locals():
    print("\n结果数据帧 (scan_rows方法):")
    print(result_scan.select(['code', 'open_time', 'is_segment_end', 'segment_id']))

# 验证结果是否一致
if 'result_scan' in locals() and result_expr_advanced is not None and result_scan is not None:
    # 按code和open_time排序以便比较
    result_expr_advanced = result_expr_advanced.sort(['code', 'open_time'])
    result_scan = result_scan.sort(['code', 'open_time'])
    
    # 检查segment_id和is_segment_end是否一致
    segment_id_match = (result_expr_advanced['segment_id'] == result_scan['segment_id']).sum() == len(result_expr_advanced)
    segment_end_match = (result_expr_advanced['is_segment_end'] == result_scan['is_segment_end']).sum() == len(result_expr_advanced)
    
    print(f"\n结果验证 (高级表达式 vs scan_rows):")
    print(f"segment_id一致: {segment_id_match}")
    print(f"is_segment_end一致: {segment_end_match}")

# 将结果合并回原始数据
if result_expr_advanced is not None:
    df = df.join(result_expr_advanced.select(['code', 'open_time', 'is_segment_end', 'segment_id']), on=['code', 'open_time'], how='left')

# 对每个分割区间计算开始和结束价格
segment_data = df.group_by(['code', 'segment_id']).agg(
    pl.col('open').first().alias('segment_open'),
    pl.col('close').last().alias('segment_close'),
    pl.col('high').max().alias('segment_high'),
    pl.col('low').min().alias('segment_low'),
).sort(['code', 'segment_id']).with_columns(
    pl.col('segment_close').shift().over('code').alias('segment_close_shift')
)

# 将分割数据合并回原始数据
df = df.join(segment_data, on=['code', 'segment_id'], how='left')

# 计算每个分割区间的收益率作为标签
df = df.with_columns(
    (pl.col('segment_close') / pl.col('segment_close_shift') - 1).alias('segment_return')
)

# 打印结果
print("\n原始数据:")
print(df.select(['code', 'open_time', 'open', 'high', 'low', 'close', 'is_segment_end', 'segment_id']))
print("\n分段数据:")
print(segment_data)
print("\n带标签的数据:")
print(df.select(['code', 'open_time', 'close', 'is_segment_end', 'segment_id', 'segment_open', 'segment_close', 'segment_return']))
