import numpy as np
import polars as pl
from core.predictor_config import PredictorConfig


def get_log_prices(df: pl.DataFrame, price_columns: set[str], resume_label: str, mirror_index: int, with_source: bool = True, is_solo: bool = False) -> pl.DataFrame:
    if is_solo:
        base_price = df[resume_label][mirror_index]

        log_df = df.with_columns([
            np.log(pl.col(col) / base_price).alias(col) for col in price_columns])

        if with_source:
            price_gap = log_df[resume_label][0] + \
                log_df[resume_label][-1]
        else:
            price_gap = 2 * log_df[resume_label][mirror_index]

        log_df = log_df.with_columns([
            (price_gap - pl.col(col)).alias(col) for col in price_columns])
    else:
        group_by_col = 'open_time'

        # interval = df[group_by_col][1] - df[group_by_col][0]
        # time_delta = df[group_by_col][-1] - \
        #     df[group_by_col][0] + interval
        # sign = (-1) ** source_in_front
        base_price_expr = pl.col(resume_label).gather(
            (pl.count(resume_label) if mirror_index < 0 else 0) + mirror_index)
        
        def get_price_gap(srs: pl.Series):
            srs = srs.to_numpy().reshape((2, -1))
            return pl.Series(srs[0] + srs[1]).list


        if with_source:
            # price_gap_expr = log_df[resume_label][0].flatten() + log_df[resume_label][-1].flatten()
            price_gap_expr = pl.first(
                resume_label).list.concat(pl.last(resume_label)).map_elements(get_price_gap, return_dtype=pl.List(pl.Float64))
        else:
            # price_gap_expr = 2 * log_df[resume_label][mirror_index].flatten()
            price_gap_expr = pl.col(
                resume_label).take(mirror_index) * 2

        base_price_str = 'base_price'
        price_gap_str = 'price_gap'
        log_df = (
            df.with_columns(
                df.select(                    
                group_by_col,
                resume_label,
                )
                .group_by(group_by_col)
                .agg(
                    pl.first(resume_label).alias(base_price_str),
                    )
                .sort([group_by_col])
            )
            .with_columns(
                pl.col(base_price_str).first().alias(base_price_str),
            )
        # )
                
        # print(log_df.head(10))
        # log_df = (
        #     log_df

            .explode(pl.exclude([group_by_col])).with_columns(
                [np.log(pl.col(col) / pl.col(base_price_str)).alias(col) for col in price_columns],
            ).group_by(group_by_col).agg(pl.exclude([group_by_col])).sort(group_by_col)
            .with_columns(pl.repeat(price_gap_expr, len(df)).alias(price_gap_str)).explode(pl.exclude([group_by_col]))
            .with_columns([
                (pl.col(price_gap_str) - pl.col(col)).alias(col) for col in price_columns
            ]).group_by(group_by_col).agg(pl.exclude([group_by_col, price_gap_str])).sort(group_by_col)
        )
    return log_df


def resume_prices(df: pl.DataFrame, log_df: pl.DataFrame, price_columns: set[str], resume_label: str, resume_index: int, with_source: bool = True, source_in_front: bool = False, is_solo: bool = False) -> pl.DataFrame:
    group_by_col = 'open_time'

    if is_solo:
        resume_base_price = df[resume_label][resume_index]
        resume_df = log_df.with_columns([
            (np.exp(pl.col(col)) * resume_base_price).alias(col) for col in price_columns])
    else:
        base_price_str = 'base_price'
        # base_price_expr = pl.col(resume_label).take(
        #     (pl.count(resume_label) if resume_index < 0 else 0) + resume_index)
        # base_price_df = df.with_columns(base_price_expr.alias(
        #     base_price_str)).select(pl.col(base_price_str))
        resume_df = log_df.explode(pl.exclude(group_by_col)).with_columns([
            (np.exp(pl.col(col)) * pl.col(base_price_str)).alias(col) for col in price_columns]).drop(
            base_price_str)

    volume_str_list = ['buy_volume', 'sell_volume']
    resume_df = resume_df.with_columns(
        [pl.col(col).alias(volume_str_list[1 - i])
            for i, col in enumerate(volume_str_list)]
    )

    volume_str_list += ['volume']
    quote_str_list = ['buy_quote', 'sell_quote', 'quote']
    resume_df = resume_df.with_columns([
        (pl.col('avg_price') * pl.col(vol)).alias(quo) for vol, quo in zip(volume_str_list, quote_str_list)
    ]).group_by(pl.col(group_by_col)).agg(pl.exclude(group_by_col)).sort(group_by_col)

    if with_source:
        # resume_df = df.vstack(resume_df)
        interval = df[group_by_col][1] - df[group_by_col][0]
        time_delta = df[group_by_col][-1] - \
            df[group_by_col][0] + interval
        sign = (-1) ** source_in_front
        resume_df = resume_df.with_columns(
            (pl.col(group_by_col) +
                sign * time_delta).cast(pl.Datetime('ns')).alias(group_by_col),
        )
        if source_in_front:
            resume_df = pl.concat([df, resume_df])
        else:
            resume_df = pl.concat([resume_df, df])
    return resume_df


def get_upside_down(cfg: PredictorConfig, df: pl.DataFrame, mirror_index=0, resume_index=0, with_source: bool = True, source_in_front: bool = False) -> pl.DataFrame:
    if with_source:
        if source_in_front:
            resume_index = 0
        else:
            resume_index = 0
    
    resume_label = 'close'
    price_columns = {'open', 'high', 'low', 'close', 'avg_price'}
    is_solo = cfg.n_codes == 1 and cfg.is_solo
    log_df = get_log_prices(
        df, price_columns, resume_label, mirror_index, with_source, is_solo)

    resume_df = resume_prices(
        df, log_df, price_columns, resume_label, resume_index, with_source, source_in_front, is_solo)
    return resume_df