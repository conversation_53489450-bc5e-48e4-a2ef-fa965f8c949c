import numpy as np
import pandas as pd
from core.predictor_config import PredictorConfig


def filter_weekends(group: pd.DataFrame, cfg: PredictorConfig) -> np.ndarray:
    open_time = group.index.get_level_values('open_time')
    mask = open_time.dt.weekday.isin([5, 6])
    return mask.to_numpy()


def filter_workdays(group: pd.DataFrame, cfg: PredictorConfig) -> np.ndarray:
    open_time = group.index.get_level_values('open_time').to_series()
    mask = open_time.dt.weekday.isin([0, 1, 2, 3, 4])
    return mask.to_numpy()


def filter_hours(group: pd.DataFrame, cfg: PredictorConfig) -> np.ndarray:
    open_time = group.index.get_level_values('open_time').to_series()
    mask = open_time.dt.hour != 23
    return mask.to_numpy()


def filter_codes(group: pd.DataFrame, cfg: PredictorConfig) -> np.ndarray:
    codes = group.index.get_level_values('code').to_series()
    mask = ~codes.isin(cfg.filter_excluded_codes)
    return mask.to_numpy()


def filter_quantile(group: pd.DataFrame, cfg: PredictorConfig) -> np.ndarray:
    # print(f'{group.columns = }')
    col_name = cfg.filter_cfg.col_name
    zscore = group[f'{col_name}_zscore']    
    qtl_value = group[f'{col_name}_qtl_value']
    mask = zscore > qtl_value
    return mask.to_numpy()