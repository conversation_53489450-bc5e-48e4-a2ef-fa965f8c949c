from ast import alias
from typing import <PERSON><PERSON>
from time import time
import numpy as np
import polars as pl
import pandas as pd
from sklearn.linear_model import LinearRegression
from core.cst import BarType, LabelType, SegmentType
from core.predictor_config import PredictorConfig
from utils.augment import get_upside_down


def process_bar(cfg: PredictorConfig, raw_data: pl.DataFrame, phase: str = 'train') -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    # 如果是EDA模式，直接返回原始数据，跳过特征计算
    if cfg.is_eda:
        # 转换为pandas DataFrame
        data_pd = raw_data.to_pandas()
        # 创建空的特征和标签DataFrame，保持接口一致
        empty_df = pd.DataFrame(index=data_pd.index)
        return data_pd, empty_df, empty_df

    # if phase in ['predict', 'online']:
    #     cols = ["open", "high", "low", "close", "volume", "quote",
    # 	"buy_volume", "buy_quote"]
    #     data[cols] = data[cols].astype(np.float64)

    n_codes = cfg.n_codes
    step = cfg.inner_step
    step_list = cfg.step_list
    # if sum(cfg.augment_data.values()) == 0:
    # max_window = cfg.ta_window_list[-1]
    open_expr = pl.col('open')
    close_expr = pl.col('close')
    high_expr = pl.col('high')
    low_expr = pl.col('low')
    volume_expr = pl.col('volume')
    buy_volume_expr = pl.col('buy_volume')
    sell_volume_expr = volume_expr - buy_volume_expr
    quote_expr = pl.col('quote')
    buy_quote_expr = pl.col('buy_quote')
    sell_quote_expr = quote_expr - buy_quote_expr
    market_quote_expr = pl.col('market_quote')
    market_buy_quote_expr = pl.col('market_buy_quote')

    lot_count_expr = 1 + pl.col('volume') / pl.col('lot_size')
    buy_lot_count_expr = 1 + pl.col('buy_volume') / pl.col('lot_size')
    sell_lot_count_expr = 1 + lot_count_expr - buy_lot_count_expr
    # delta_lot_count_expr = buy_lot_count_expr - sell_lot_count_expr
    step_open_expr = pl.col('open').shift(step - 1).over('code')
    # step_close_expr = pl.col('close')
    step_high_expr = pl.col('high').rolling_max(step).over('code')
    step_low_expr = pl.col('low').rolling_min(step).over('code')
    step_volume_expr = pl.col('volume').rolling_sum(step).over('code')
    step_buy_volume_expr = pl.col('buy_volume').rolling_sum(step).over('code')
    step_sell_volume_expr = step_volume_expr - step_buy_volume_expr
    step_quote_expr = pl.col('quote').rolling_sum(step).over('code')
    step_buy_quote_expr = pl.col('buy_quote').rolling_sum(step).over('code')
    step_sell_quote_expr = step_quote_expr - step_buy_quote_expr

    step_market_quote_expr = market_quote_expr.rolling_sum(step).over('code')

    step_market_buy_quote_expr = market_buy_quote_expr.rolling_sum(step).over('code')

    step_delta_volume_expr = pl.col('delta_volume').rolling_sum(step).over('code')
    macd_cfg = cfg.ta_cfg
    macd_diff_expr = pl.col(f'ewm_close_{macd_cfg.fast}') - pl.col(f'ewm_close_{macd_cfg.slow}')
    cutoff_len = cfg.cutoff_len #+ 2 # significant effect on performance

    # train_data = data[data.open_time < cfg.train_end_date
    group_by_col = 'open_time'
    bar_idx_str = 'bar_idx'
    group_sort_by = 'open_time'

    # data = pl.from_pandas(raw_data, include_index=True)
    # print((data['open_time'].dt.hour()))
    # btc_close_srs = data.filter(pl.col('code') == 'BTCUSDT').select(close_expr.pct_change()).to_series()
    # if phase == 'train':
    #     history_feature = data.select(
    #         pl.col('volume').mean().over('code').alias('volume_mean'),
    #         pl.col('buy_volume').mean().over('code').alias('buy_volume_mean'),
    #         pl.col('quote').mean().over('code').alias('quote_mean'),
    #         pl.col('buy_quote').mean().over('code').alias('buy_quote_mean'),
    #         pl.col('close').pct_change().over('code').std()
    #     )
    #     cfg.history_feature = history_feature
    # else:
    #     history_feature = cfg.history_feature
    # print(history_feature)
    if cfg.sim_base_interval is not None:
        base_interval = cfg.sim_base_interval
        label_interval = base_interval * cfg.sim_label_step
    else:
        base_interval = cfg.interval_cfg.base
        label_interval = cfg.interval_cfg.label
    lot_size_df = pl.read_csv(cfg.trading_pairs_path).filter(pl.col('symbol').is_in(cfg.code_list)).select(
        pl.col('symbol').alias('code'),
        pl.col('lot_size')
    )
    data = raw_data.join(lot_size_df, on='code', how='left')

    data = data.with_columns(
        pl.min_horizontal(pl.col('open'), pl.col('close')).alias('body_low'),
        pl.max_horizontal(pl.col('open'), pl.col('close')).alias('body_high'),
        lot_count_expr.alias('lot_count'),
        buy_lot_count_expr.alias('buy_lot_count'),
        sell_lot_count_expr.alias('sell_lot_count'),
        # delta_lot_count_expr.alias('delta_lot_count'),
        (pl.col('volume') - pl.col('buy_volume')).alias('sell_volume'),
        (2 * pl.col('buy_volume') - pl.col('volume')).alias('delta_volume'),
        step_open_expr.alias('step_open'),
        step_high_expr.alias('step_high'),
        step_low_expr.alias('step_low'),
        step_volume_expr.alias('step_volume'),
        step_buy_volume_expr.alias('step_buy_volume'),
        step_sell_volume_expr.alias('step_sell_volume'),
        step_quote_expr.alias('step_quote'),
        step_buy_quote_expr.alias('step_buy_quote'),
        step_sell_quote_expr.alias('step_sell_quote'),

        # high_expr.rolling_max(step).over('code').fill_null(high_expr).alias('step_high'),
        # low_expr.rolling_min(step).over('code').fill_null(low_expr).alias('step_low'),
        # step_volume_expr.alias('step_volume'),
        # step_buy_volume_expr.alias('step_buy_volume'),
        # step_sell_volume_expr.alias('step_sell_volume'),
        # step_quote_expr.alias('step_quote'),
        # step_buy_quote_expr.alias('step_buy_quote'),
        # step_sell_quote_expr.alias('step_sell_quote'),

        pl.when(step_volume_expr > 0).then(step_quote_expr / step_volume_expr).otherwise(close_expr).alias('avg_price'),
        # pl.when(step_volume_expr > 0).then(step_quote_expr / step_volume_expr).otherwise(close_expr).alias('step_avg_price'),

        close_expr.pct_change().over('code').fill_null(0.).alias('close_roc'),
        close_expr.pct_change(step).over('code').fill_null(0.).alias('step_close_roc'),
        (close_expr / (close_expr.shift(step).over('code') + 1e-12)).fill_null(0.).alias('step_close_ratio'),
    ).with_columns(
        pl.min_horizontal(pl.col('step_open'), close_expr).alias('step_body_low'),
        pl.max_horizontal(pl.col('step_open'), close_expr).alias('step_body_high'),
    )
    # print(f'{data["cycle_idx"].unique() = }')
    # close_str_list = []
    # close_exprs = []
    # close_pct_change_exprs = []
    # for code in cfg.code_list:
    #     close_str = f'{code.replace("USDT", "").lower()}_close'
    #     close_str_list.append(close_str)
    #     close_exprs.append(close_expr.filter(pl.col('code') == code).first().alias(close_str))
    #     close_pct_change_exprs.append(pl.col(close_str).pct_change(step).fill_null(0.).alias(f'{close_str}_roc'))
    market_df = (
        data.group_by(group_by_col).agg(
        pl.col('step_open').filter(pl.col('code') == 'BTCUSDT').first().alias('btc_open'),
        pl.col('step_high').filter(pl.col('code') == 'BTCUSDT').first().alias('btc_high'),
        pl.col('step_low').filter(pl.col('code') == 'BTCUSDT').first().alias('btc_low'),
        close_expr.filter(pl.col('code') == 'BTCUSDT').first().alias('btc_close'),
        pl.col('step_quote').filter(pl.col('code') == 'BTCUSDT').first().alias('btc_quote'),
        pl.col('step_buy_quote').filter(pl.col('code') == 'BTCUSDT').first().alias('btc_buy_quote'),
        # close_expr.filter(pl.col('code') == 'ETHUSDT').first().alias('eth_close'),
        # *close_exprs,
        pl.col('step_quote').sum().alias('step_market_quote'),
        pl.col('step_buy_quote').sum().alias('step_market_buy_quote'),
        # pl.col('step_quote').sum().alias('step_market_quote'),
        # pl.col('step_buy_quote').sum().alias('step_market_buy_quote'),
        (pl.col('step_quote') * pl.col('close_roc')).sum().alias('step_quote_by_close_roc_sum'),
        # (pl.col('step_buy_quote') * pl.col('step_close_roc')).sum().alias('step_quote_by_close_pct_change_sum'),
    ).sort(group_by_col).with_columns(
        # ! no need to over 'code'
        (pl.col('btc_quote') - pl.col('btc_buy_quote')).alias('btc_sell_quote'),
        # pl.col('btc_close').pct_change(step).fill_null(0.).alias('step_btc_close_roc'),
        # pl.col('eth_close').pct_change().fill_null(0.).alias('eth_close_roc'),
        # pl.col('eth_close').pct_change(step).fill_null(0.).alias('step_eth_close_roc'),
        # *close_pct_change_exprs,
        (pl.col('step_quote_by_close_roc_sum') / pl.col('step_market_quote')).alias('step_market_close_roc'),
        # (pl.col('step_quote_by_close_pct_change_sum') / pl.col('step_market_quote')).alias('step_market_close_roc'),

        # pl.col('market_quote').pct_change().fill_null(0.).alias('market_quote_roc'),
        pl.col('step_market_quote').pct_change(step).fill_null(0.).alias('step_market_quote_roc'),
        # pl.col('market_buy_quote').diff().fill_null(0.).alias('market_buy_quote_diff'),
        pl.col('step_market_buy_quote').diff(step).fill_null(0.).alias('step_market_buy_quote_diff'),
        pl.col('btc_close').pct_change().fill_null(0.).alias('btc_close_roc'),
        pl.col('btc_close').pct_change(step).fill_null(0.).alias('step_btc_close_roc'),
        # pl.col('btc_open').shift().fill_null(0.).alias('btc_open_shift')
    )
    # .with_columns(
    #     pl.exclude([group_by_col]).map_elements(lambda x: pl.Series(x * np.ones(n_codes)), return_dtype=pl.List(pl.Float64))
    # )
    # .explode(pl.exclude([group_by_col]))
)



    data = data.join(market_df, on=group_by_col, how='left')
    # data = pl.concat([data, market_df.select(pl.exclude(group_by_col))], how='horizontal')
    # print(f'{market_df.tail(10) = }\n{market_df.shape = }\n{data.shape = }\n{data.tail(10) = }\n')


    if cfg.augment_data[phase]:
        data = data.group_by(group_by_col).agg(pl.exclude(group_by_col)).sort(group_by_col)
        upside_down = get_upside_down(cfg, data)
        if cfg.augment_data.rev:
            rev = upside_down.reverse().with_columns(
                close_expr.alias('open'),
                pl.col('open').alias('close'),
                pl.col('buy_volume').alias('sell_volume'),
                pl.col('buy_quote').alias('sell_quote'),
                pl.col('sell_volume').alias('buy_volume'),
                pl.col('sell_quote').alias('buy_quote'),
                # pl.col('step_buy_volume').alias('step_sell_volume'),
                # pl.col('step_buy_quote').alias('step_sell_quote'),
                # pl.col('step_sell_volume').alias('step_buy_volume'),
                # pl.col('step_sell_quote').alias('step_buy_quote'),
            )
            if cfg.augment_data.rev_first:
                data = pl.concat([upside_down[-cutoff_len:], rev, upside_down])
            else:
                data = pl.concat([rev[-cutoff_len:], upside_down, rev])
        else:
            data = pl.concat([upside_down[-cutoff_len:], upside_down])
    #     data = (
    #         data
    #         .with_row_index(bar_idx_str).explode(pl.exclude([group_by_col, bar_idx_str]))
    #     )
    # else:
    #     data = data.group_by(group_by_col).agg(pl.exclude(group_by_col)).sort(group_by_col).with_row_index(bar_idx_str).explode(pl.exclude([group_by_col, bar_idx_str]))

    # idx_str = 'index'
    # data['open_time'] = pd.to_datetime(data.open_time)
    seq_len = cfg.seq_len
    column_dict = cfg.column_group_dict
    label_step = cfg.label_step[phase]

    image_dict = {}
    for i, n_step in enumerate(step_list):
        if i == 0:
            step_str = ''
            step_macd_fast_str = f'ewm_close_{macd_cfg.fast}{step_str}'
            step_macd_slow_str = f'ewm_close_{macd_cfg.slow}{step_str}'
            step_macd_diff_expr = pl.col(step_macd_fast_str) - pl.col(step_macd_slow_str)
        else:
            step_str = f'_step{n_step}'
            step_macd_fast_str = f'ewm_close_{macd_cfg.fast}{step_str}'
            step_macd_slow_str = f'ewm_close_{macd_cfg.slow}{step_str}'
            step_macd_diff_expr = pl.col(step_macd_fast_str) - pl.col(step_macd_slow_str)
            data = data.with_columns(
                pl.col('open').shift(n_step).over('code').alias(f'open{step_str}'),
                pl.col('high').rolling_max(n_step).over('code').alias(f'high{step_str}'),
                pl.col('low').rolling_min(n_step).over('code').alias(f'low{step_str}'),
                pl.col('close').shift(n_step).over('code').alias(f'close{step_str}'),
                pl.col('close').ewm_mean(com=macd_cfg.fast).over('code').alias(step_macd_fast_str).fill_null(0.),
                pl.col('close').ewm_mean(com=macd_cfg.slow).over('code').alias(step_macd_slow_str).fill_null(0.),
                pl.col('volume').rolling_sum(n_step).over('code').alias(f'volume{step_str}'),
                pl.col('buy_volume').rolling_sum(n_step).over('code').alias(f'buy_volume{step_str}'),
                pl.col('sell_volume').rolling_sum(n_step).over('code').alias(f'sell_volume{step_str}'),
                pl.col('delta_volume').rolling_sum(n_step).over('code').alias(f'delta_volume{step_str}'),
            )
        image_dict.update({
            f'open_{step_str}img': pl.col(f'open{step_str}'),
            f'high_{step_str}img': pl.col(f'high{step_str}'),
            f'low_{step_str}img': pl.col(f'low{step_str}'),
            f'close_{step_str}img': pl.col(f'close{step_str}'),
            f'fast_{step_str}img': pl.col(f'close{step_str}').ewm_mean(com=8).over('code').fill_null(pl.col(f'close{step_str}')),
            f'slow_{step_str}img': pl.col(f'close{step_str}').ewm_mean(com=24).over('code').fill_null(pl.col(f'close{step_str}')),
            f'volume{step_str}_img': pl.col(f'volume{step_str}'),
            f'buy_volume{step_str}_img': pl.col(f'buy_volume{step_str}'),
            f'sell_volume{step_str}_img': -pl.col(f'sell_volume{step_str}'),
            f'delta_volume{step_str}_img': pl.col(f'delta_volume{step_str}'),
            # 'rsi_img': plta.rsi(close_expr, cfg.ta_cfg.rsi).over('code').fill_null(0.),
            # 'rsi_img': pl.col('rsi'),
            # 'stc_rsi_img': (pl.col('rsi') - pl.col('rsi').rolling_min(12).over('code') / (pl.col('rsi').rolling_map(lambda x: x.max() - x.min(), 12).over('code'))).fill_null(0.),
            f'macd_diff{step_str}_img': step_macd_diff_expr,
            f'macd_dea{step_str}_img': step_macd_diff_expr.ewm_mean(com=macd_cfg.dea).over('code').fill_null(0.),
        })

    image_exprs = [v.alias(k) for k, v in image_dict.items()]

    data = data.with_columns(
        pl.col('open_time'),
        ((step_high_expr - step_low_expr) / close_expr).alias('range_ratio'),
        (step_buy_quote_expr / step_quote_expr).fill_null(0.).alias('buy_quote_ratio'),
        pl.when(pl.col('step_volume') > 0).then(pl.col('step_quote') / pl.col('step_volume')).otherwise(close_expr).alias('step_avg_price'),
        # (pl.col('step_buy_quote') / pl.col('step_quote')).fill_null(0.).alias('step_buy_quote_ratio'),
        # pl.when(pl.col('count') > 0).then(quote_expr / pl.col('count')).otherwise(0.).alias('quote_per_trade'),
        pl.col('step_volume').pct_change(step).over('code').fill_null(0.).alias('step_volume_roc'),
        (pl.col('step_volume') / (pl.col('step_volume').shift(step).over('code') + 1e-12)).fill_null(0.).alias('step_volume_ratio'),
        (2 * step_buy_volume_expr - step_volume_expr).alias('step_delta_volume'),
        # (2 * pl.col('step_buy_volume') - pl.col('step_volume')).alias('step_delta_volume'),
        (2 * step_buy_quote_expr - step_quote_expr).alias('step_delta_quote'),
        pl.col('step_volume').diff(step).over('code').alias('step_volume_diff'),
        # (2 * pl.col('step_buy_quote') - pl.col('step_quote')).alias('step_delta_quote'),
        # close_expr.apply(self.kalman_filter.smooth).alias('km_close')

        ((pl.col('step_high') - pl.col('step_high').rolling_mean(seq_len).over('code')) / pl.col('step_high').rolling_std(seq_len).over('code')).alias('high_rolling_zscore'),
        ((pl.col('step_low') - pl.col('step_low').rolling_mean(seq_len).over('code')) / pl.col('step_low').rolling_std(seq_len).over('code')).alias('low_rolling_zscore'),
        ((close_expr - close_expr.rolling_mean(seq_len).over('code')) / close_expr.rolling_std(seq_len).over('code')).alias('close_rolling_zscore'),
        ((pl.col('step_quote') - pl.col('step_quote').rolling_mean(seq_len).over('code')) / pl.col('step_quote').rolling_std(seq_len).over('code')).alias('quote_rolling_zscore'),
        ((pl.col('step_buy_quote') - pl.col('step_buy_quote').rolling_mean(seq_len).over('code')) / pl.col('step_buy_quote').rolling_std(seq_len).over('code')).alias('buy_quote_rolling_zscore'),
        pl.col('close').ewm_mean(com=macd_cfg.fast).over('code').alias(f'ewm_close_{macd_cfg.fast}').fill_null(0.),
        pl.col('close').ewm_mean(com=macd_cfg.slow).over('code').alias(f'ewm_close_{macd_cfg.slow}').fill_null(0.),
        # pl.col('close_roc').rolling_map(lambda x: x.clip(0., None).sum() / x.abs().sum() - 0.5, 12).over('code').alias('rsi'),



        pl.col('high').rolling_max(label_step).over('code').alias('label_high'),
        pl.col('low').rolling_min(label_step).over('code').alias('label_low'),
    ).with_columns(
        (pl.col('step_delta_quote') / pl.col('step_quote')).fill_null(0.).alias('step_delta_quote_ratio'),
        (pl.col('close_roc').abs() / pl.col('volume')).fill_null(0.).alias('roc_div_volume'),
        # pl.col('open').shift(step).over('code').fill_null(0.).alias('step_open_shift')
        # (pl.col('step_delta_quote') / pl.col('step_quote')).fill_null(0.).alias('step_delta_quote_ratio'),
    # fdiff(pl.col('km_close'), n=cfg.fracdiff, axis=0).alias('km_close_fd')
    ).with_row_index()


    #! fracdiff
    # frac_cols = [
    #     # 'open',
    #     'high', 'low', 'close',
    #     'quote', 'buy_quote',
    #     # 'avg_price',
    #     'range_ratio',
    #     'buy_quote_ratio',
    #     'delta_quote',
    #     # 'delta_quote_ratio',
    #     # 'sell_quote',
    #     # 'count',
    #     # 'quote_per_trade',
    # ]

    # if n_codes == 1 and cfg.is_solo:
    #     frac_arr = data[frac_cols].to_numpy()
    #     # if cfg.seq_len > 1:
    #     frac_arr = fdiff(frac_arr, n=cfg.fracdiff, axis=0)
    #     frac_exprs = [f'{col}_fd' for col in frac_cols]
    #     for i, col_name in enumerate(frac_cols):
    #         qdf = pl.Series(f'{col_name}_fd', frac_arr[:, i])
    #         data = data.with_columns(qdf)
    # else:
    #     frac_exprs = [pl.col(frac_str).map_elements(lambda x: multi_code_fdiff(x, cfg), return_dtype=pl.List(pl.Float64)).over('code').alias(f'{frac_str}_fd') for frac_str in frac_cols]
    #!

    # for frac_str in frac_cols:
    #     column_dict.fracdiff.append(f'{frac_str}_fd')
    rolling_exprs = []
    if cfg.feature_cfg.rolling:
        import polars_talib as plta
        rolling_dict = {
            # 'range_ratio_{}': lambda w: ((high_expr.rolling_max(w) - low_expr.rolling_min(w)) / close_expr).over('code'),
            # 'cma_ratio_{}': lambda w: (close_expr.rolling_mean(w) / close_expr - 1).over('code'),
            # 'cema_ratio_{}': lambda w: (close_expr.ewm_mean(w) / close_expr - 1).over('code'),
            # 'vma_ratio_{}': lambda w: (volume_expr.rolling_mean(w) / volume_expr - 1).over('code').fill_null(0.),
            # 'vema_ratio_{}': lambda w: (volume_expr.ewm_mean(w) / volume_expr - 1).over('code').fill_null(0.),
            # 'hema_ratio_{}': lambda w: (high_expr.ewm_mean(w) / close_expr - 1).over('code'),
            # 'lema_ratio_{}': lambda w: (low_expr.ewm_mean(w) / close_expr - 1).over('code'),

            # 'range_ratio_{}': lambda w: ((pl.col('step_high').rolling_max(w) - pl.col('step_low').rolling_min(w)) / close_expr.ewm_mean(com=w)).over('code'),

            # 'vma_ratio_{}': lambda w: (pl.col('step_volume') / pl.col('step_volume').rolling_mean(w)).over('code'),
            # 'vema_ratio_{}': lambda w: (pl.col('step_volume') / pl.col('step_volume').ewm_mean(com=w)).over('code'),
            # 'hema_ratio_{}': lambda w: (pl.col('step_high') / close_expr.ewm_mean(com=w)).over('code'),
            # 'lema_ratio_{}': lambda w: (pl.col('step_low') / close_expr.ewm_mean(com=w)).over('code'),
            # 'cema_ratio_{}': lambda w: (close_expr / close_expr.ewm_mean(com=w)).over('code'),
            # 'croc_estd_{}': lambda w: close_expr.pct_change(step).over('code').ewm_std(com=w).over('code'),

            # 'mom_div_volatility{}': lambda w: (close_expr.pct_change() / (close_expr.rolling_std(w) + 1e-12)).over('code'),
            #                          #
            'roc_{}': lambda w: close_expr.pct_change(w).over('code'),
            'ma_ratio_{}': lambda w: close_expr / close_expr.rolling_mean(w).over('code'),

            # 'croc_std_{}': lambda w: close_expr.pct_change(step).over('code').rolling_std(w).over('code'),

            'slope_{}': lambda w: plta.linearreg_slope(close_expr, w).over('code') / close_expr,
            # # 'intercept_{}': lambda w: plta.linearreg_intercept(close_expr, w).over('code'),
            # # 'resi_{}': lambda w: (close_expr - (pl.col(f'slope_{w}') * w + pl.col(f'intercept_{w}'))).over('code'),
            'resi_{}': lambda w: (close_expr - (plta.linearreg_slope(close_expr, w).over('code') * w + plta.linearreg_intercept(close_expr, w).over('code'))) / close_expr,

            'rsqr_{}': lambda w: (pl.rolling_corr(close_expr, pl.col('bar_idx'), window_size=w).over('code') ** 2),

            'vroc_std_{}': lambda w: pl.col('step_volume_roc').rolling_std(w).over('code'),
            'abs_croc_by_vol_std_div_mean_{}': lambda w: (pl.col('step_close_roc') * pl.col('step_volume')).rolling_std(w).over('code') / (pl.col('step_close_roc') * pl.col('step_volume')).rolling_mean(w).over('code'),

            'rolling_{}_high_div_close': lambda w: (pl.col('step_high').rolling_max(w).over('code') / close_expr),
            'rolling_{}_low_div_close': lambda w: (pl.col('step_low').rolling_min(w).over('code') / close_expr),
            'rolling_{}_close_upper_quantile_ratio': lambda w: (close_expr.rolling_quantile(0.8, window_size=w).over('code') / close_expr),
            'rolling_{}_close_lower_quantile_ratio': lambda w: (close_expr.rolling_quantile(0.2, window_size=w).over('code') / close_expr),
            'rolling_{}_close_rank_ratio': lambda w: close_expr.rolling_map(lambda x: x.rank()[-1] / x.count(), w).over('code'),
            'rsv_{}': lambda w: (close_expr - pl.col('step_low').rolling_min(w).over('code') / (pl.col('step_high').rolling_max(w).over('code') - pl.col('step_low').rolling_min(w).over('code') + 1e-12)),
            'idx_min_{}': lambda w: (close_expr.rolling_map(lambda x: x.arg_min() + 1, w).over('code') / w),
            'idx_max_{}': lambda w: (close_expr.rolling_map(lambda x: x.arg_max() + 1, w).over('code') / w),
            'rolling_{}_close_div_log_volume_corr': lambda w: (plta.correl(close_expr, pl.col('step_volume').log(), w)).over('code'),
            'rolling_{}_close_div_shift_div_log_volume_corr_div_shift': lambda w: (plta.correl(pl.col('step_close_ratio'), (pl.col('step_volume_ratio') + 1).log(), w)).over('code'),
            # 'cntp_{}': lambda w: pl.col('step_close_roc').rolling_map(lambda x: (x > 0).cast(pl.Int64).sum() / x.count(), w).over('code'),
            # 'cntn_{}': lambda w: pl.col('step_close_roc').rolling_map(lambda x: (x < 0).cast(pl.Int64).sum() / x.count(), w).over('code'),
            # 'cntd_{}': lambda w: pl.col('step_close_roc').rolling_map(lambda x: 2 * (x > 0).cast(pl.Int64).sum() / x.count() - 1, w).over('code'),
            # 'sump_{}': lambda w: pl.col('step_close_roc').rolling_map(lambda x: x.clip(0, None).sum() / x.abs().sum(), w).over('code'),
            # 'sumn_{}': lambda w: pl.col('step_close_roc').rolling_map(lambda x: x.clip(None, 0).sum() / x.abs().sum(), w).over('code'),
            # 'sumd_{}': lambda w: pl.col('step_close_roc').rolling_map(lambda x: (x.clip(0, None) - x.clip(None, 0)).sum() / x.abs().sum(), w).over('code'),
            # 'vsump_{}': lambda w: pl.col('step_volume_roc').rolling_map(lambda x: x.clip(0, None).sum() / x.abs().sum(), w).over('code'),
            # 'vsumn_{}': lambda w: pl.col('step_volume_roc').rolling_map(lambda x: x.clip(None, 0).sum() / x.abs().sum(), w).over('code'),
            # 'vsumd_{}': lambda w: pl.col('step_volume_roc').rolling_map(lambda x: (x.clip(0, None) - x.clip(None, 0)).sum() / x.abs().sum(), w).over('code'),

            # 'hl_slope_diff_{}': lambda w: (plta.linearreg_slope(pl.col('step_high'), w) - plta.linearreg_slope(pl.col('step_low'), w)).over('code'),
            # 'hl_slope_sum_{}': lambda w: (plta.linearreg_slope(pl.col('step_high'), w) + plta.linearreg_slope(pl.col('step_low'), w)).over('code'),
            # 'mom_{}': lambda w: (close_expr.pct_change(w)).over('code'),
            'rsi_{}': lambda w: plta.rsi(close_expr, w).over('code'),
            'vrsi_{}': lambda w: plta.rsi(pl.col('step_volume'), w).over('code'),
            # 'estd_{}': lambda w: close_expr.ewm_std(com=w).over('code'),

            # 'bb_upper_{}': lambda w: plta.bbands_upper(close_expr, w).over('code'),
            # 'bb_lower_{}': lambda w: plta.bbands_lower(close_expr, w).over('code'),


            # 'rsi_diff_{}': lambda w: (plta.rsi(close_expr, w).diff()).over('code'), # has nan!
            # 'macd_{}': lambda w: plta.macd(close_expr, w // 2, w, int(w * 0.66)).over('code').struct[2],# has nan! bad
            # 'adx_{}': lambda w: plta.adx(high_expr, low_expr, close_expr, w).over('code'), # has nan! but great
            # 'aroon_{}': lambda w: plta.aroonosc(high_expr, low_expr, w).over('code') # has nan! but good

        }


        # w = 1
        for k, v in rolling_dict.items():
            for i, w in enumerate(windows := cfg.rolling_window_list):
                expr_str = k.format(w * step)
                rolling_exprs.append(v(w).alias(expr_str))
                # column_dict.rolling.append(expr_str)


        # ta_exprs.append(((volume_expr.rolling_max(w) - volume_expr.rolling_min(w)) / volume_expr.rolling_mean(w)).alias(f'vrange_ratio_{w}'))
        # ta_exprs.append(volume_expr.diff().rolling_std(w).alias(f'vstd_{w}'))
        #
        # ((high_expr.rolling_max(w) - low_expr.rolling_min(w)) / close_expr.ewm_mean(w).over('code').alias(f'range_ratio_{w}'))
        # ((volume_expr / volume_expr.rolling_mean(w)).over('code').alias(f'vema_ratio_{w}')) # great!
        # ((volume_expr / volume_expr.ewm_mean(w)).over('code').alias(f'vrolling_ratio_{w}')) # great!

        # ta_exprs.append(volume_expr.ewm_mean(w).pct_change().over('code').alias(f'vema_ratio_{w}'))
        # ta_exprs.append(close_expr.ewm_mean(w).pct_change().over('code').alias(f'cema_ratio_{w}'))
        # ta_exprs.append((close_expr.ewm_mean(w) / close_expr.ewm_mean(w // 2) - 1).over('code').alias(f'cema_hist_{w}'))
        # ta_exprs.append((close_expr.ewm_mean(w) / close_expr.ewm_mean(w // 2) - 1).diff().over('code').alias(f'cema_hist_diff_{w}'))
        # ta_exprs.append((volume_expr.diff() / volume_expr.rolling_mean(w)).alias(f'vdiff_ratio_{w}')) # bad

        # ((high_expr / close_expr.ewm_mean(w)).over('code').alias(f'hema_ratio_{w}')) # -1 +3
        # ((low_expr / close_expr.ewm_mean(w)).over('code').alias(f'lema_ratio_{w}')) # -1 +3
        # ((close_expr / close_expr.ewm_mean(w)).over('code').alias(f'cema_ratio_{w}')) # -1 +3

        # ta_exprs.append(volume_expr.diff().rolling_skew(w).alias(f'vskew_{w}')) # bad
        # ta_exprs.append((high_expr.rolling_max(w) / low_expr.rolling_min(w)).alias(f'high_max_div_low_min_ratio_{w}'))
        # ta_exprs.append((high_expr.rolling_min(w) / low_expr.rolling_max(w)).alias(f'high_min_div_low_max_ratio_{w}'))

        # ta_exprs.append((plta.atr(high_expr, low_expr, close_expr, w)/close_expr).alias(f'atr_{w}')) # bad
        # ta_exprs.append((1 - low_expr.rolling_min(w).shift(1) / close_expr).alias(f'to_low_ratio_{w}')) # bad
        # ta_exprs.append((1 - high_expr.rolling_max(w).shift(1) / close_expr).alias(f'to_high_ratio_{w}')) # bad

        # (close_expr.pct_change().rolling_std(w).over('code').alias(f'rolling_std_{w}'))
        # (close_expr.pct_change().ewm_std(w).over('code').alias(f'ewm_std_{w}'))

        # ta_exprs.append(close_expr.pct_change().rolling_skew(w).alias(f'skew_{w}')) # has nan!
        # ta_exprs.append(close_expr.diff().rolling_apply(lambda x: x.kurtosis(), w).alias(f'kurt_{w}')) # bad

        # slope
        # (plta.linearreg_slope(close_expr, w).over('code').alias(f'slope_{w}'))
        # # ta_exprs.append(plta.linearreg_slope(close_expr.diff(), w).alias(f'diff_slope_{w}')) # bad
        # # ta_exprs.append(plta.linearreg_slope(close_expr, w).diff().over('code').alias(f'slope_diff_{w}'))
        # ((plta.linearreg_slope(high_expr, w) - plta.linearreg_slope(low_expr, w)).over('code').alias(f'hl_slope_diff_{w}'))
        # ((plta.linearreg_slope(high_expr, w) + plta.linearreg_slope(low_expr, w)).over('code').alias(f'hl_slope_sum_{w}'))

        # ta_exprs.append(pl.concat([high_expr, low_expr]).rolling_apply(calculate_beta, w).alias(f'rsrs_{w}')) # too slow!

        # ta_exprs.append((plta.linearreg_slope(high_expr.diff(), w) - plta.linearreg_slope(low_expr.diff(), w)).alias(f'diff_rsrs_{w}')) # bad

        # ta_exprs.append((close_expr.pct_change(w)).over('code').alias(f'mom_{w}'))

        # slow_kd = plta.stoch(high_expr, low_expr, close_expr, w, w // 2, slowd_period=w // 2)
        # fast_kd = plta.stochf(high_expr, low_expr, close_expr, w, w // 2)
        # ta_exprs.extend([
        #     slow_kd.struct[0].alias(f'slowk_{w}'),
        #     slow_kd.struct[1].alias(f'slowd_{w}'),
        #     (slow_kd.struct[0] - slow_kd.struct[1]).alias(f'slowj_{w}'),
        #     fast_kd.struct[0].alias(f'fastk_{w}'),
        #     fast_kd.struct[1].alias(f'fastd_{w}'),
        #     (fast_kd.struct[0] - fast_kd.struct[1]).alias(f'fastj_{w}'),
        #     ]) # has nan!

        # (plta.rsi(close_expr, w).over('code').alias(f'rsi_{w}'))
        # (plta.macd(close_expr, w // 2, w, int(w * 0.66)).over('code').struct[2].alias(f'macd_{w}'))
        # (plta.adx(high_expr, low_expr, close_expr, w).over('code').alias(f'adx_{w}'))
        # (plta.aroonosc(high_expr, low_expr, w).over('code').alias(f'aroonosc_{w}'))


        # ta_exprs.append(plta.cci(high_expr, low_expr, close_expr, w).alias(f'cci_{w}')) # bad
        # ta_exprs.append(plta.mfi(high_expr, low_expr, close_expr, volume_expr, w).alias(f'mfi_{w}')) # bad
        # ta_exprs.append(plta.trix(close_expr, w).alias(f'trix_{w}')) # bad
    if cfg.feature_cfg.original_with_btc:
        original_dict = {
            # 'btc_open_shift': pl.col('btc_open_shift'),
            'btc_open': pl.col('btc_open'),
            'btc_high': pl.col('btc_high'),
            'btc_low': pl.col('btc_low'),
            'btc_close': pl.col('btc_close'),
        }
    else:
        original_dict = {}

    original_dict.update({
        # 'btc_buy_quote': 2 * pl.col('btc_buy_quote'),
        # 'btc_sell_quote': 2 * pl.col('btc_sell_quote'),
        # 'btc_delta_quote': (pl.col('btc_buy_quote') - pl.col('btc_sell_quote')),
        # 'btc_quote': pl.col('btc_quote'),

        # 'open_shift': pl.col('step_open_shift'),
        'open': step_open_expr,
        'high': step_high_expr,
        'low': step_low_expr,
        'close': close_expr,


        # 'buy_lot_count': 2 * pl.col('buy_lot_count').log(),
        # 'sell_lot_count': 2 * pl.col('sell_lot_count').log(),
        # 'delta_lot_count': pl.col('buy_lot_count').log() - pl.col('sell_lot_count').log(),
        # 'lot_count': pl.col('lot_count').log(),

        # pl.col('market_buy_quote'),
        # (pl.col('market_quote') - pl.col('market_buy_quote')).alias('market_sell_quote'),
        # (2 * pl.col('market_buy_quote') - pl.col('market_quote')).alias('market_delta_quote'),
        # pl.col('market_quote'),
    })
    original_exprs = [v.alias(k) for k, v in original_dict.items()]

    # hour_expr = pl.col('open_time').shift(step - 1).over('code').dt.hour() * 2 * np.pi / 24
    # weekday_expr = pl.col('open_time').dt.weekday() * 2 * np.pi / 7
    # if data['code_idx'].max() >= n_codes:
    #     print(f'code_idx max is {data["code_idx"].max()}, but n_codes is {n_codes}')
    cross_dict = {
        # 'sin_code': np.sin(pl.col('code_idx') * 2 * np.pi / n_codes).alias('sin_code'),
        # 'cos_code': np.cos(pl.col('code_idx') * 2 * np.pi / n_codes).alias('cos_code'),
        # 'sin_hour': np.sin(hour_expr).alias('sin_hour'),
        # 'cos_hour': np.cos(hour_expr).alias('cos_hour'),
        # 'sin_day': np.sin(weekday_expr).alias('sin_day'),
        # 'cos_day': np.cos(weekday_expr).alias('cos_day'),


        # pl.when(seq_len > 1).then(pl.col('close_fd').rolling_std(seq_len)).otherwise(pl.lit(0.)).over('code').alias('fd_std'), # cause nan when seq_len == 1
        # pl.col('open').pct_change().over('code').alias('open_roc'),
        'macd': macd_diff_expr - macd_diff_expr.ewm_mean(com=9).over('code').fill_null(0.), # bad,

        'step_high_roc': pl.col('step_high').pct_change(step).over('code').fill_null(0.),
        'step_low_roc': pl.col('step_low').pct_change(step).over('code').fill_null(0.),
        'step_close_roc': close_expr.pct_change(step).over('code').fill_null(0.),
        # 'close_roc_diff_ema': pl.col('close_roc').diff().ewm_mean(com=12).over('code').fill_null(0.), # bad
        'step_quote_roc': pl.col('step_quote').pct_change(step).over('code').fill_null(0.),
        # 'buy_quote_roc': pl.col('buy_quote').pct_change().over('code').fill_null(0.),
        'step_delta_quote_ratio': pl.col('step_delta_quote_ratio'),

        # 'break_up': ((pl.col('close') / pl.max_horizontal(pl.col('step_high').shift(step * 2).over('code'), pl.col('step_high').shift(step).over('code'))) - 1),#.clip(0, None),
        # 'break_down': ((pl.col('close') / pl.min_horizontal(pl.col('step_low').shift(step * 2).over('code'), pl.col('step_low').shift(step).over('code'))) - 1),#.clip(None, 0),


        # 'delta_pct_div_btc': (1 + pl.col('delta_quote') / pl.col('quote') - 2 * pl.col('btc_buy_quote') / pl.col('btc_quote')).fill_null(0.),
        # 'btc_delta_pct': (2 * pl.col('btc_buy_quote') / pl.col('btc_quote') - 1).fill_null(0.),
        # 'delta_pct_diff': (pl.col('delta_quote') / pl.col('quote')).diff().over('code').fill_null(0.),
        # 'delta_quote_roc': pl.col('delta_quote').pct_change().over('code').fill_null(0.),

        # 'open_wick_div_range': (pl.col('open') - pl.when(pl.col('close') >= pl.col('open')).then(pl.col('low')).otherwise(pl.col('high'))) / (pl.col('high') - pl.col('low')).fill_null(0.),
        # 'close_wick_div_range': (pl.col('close') - pl.when(pl.col('close') >= pl.col('open')).then(pl.col('low')).otherwise(pl.col('high'))) / (pl.col('high') - pl.col('low')).fill_null(0.),
        # 'range_dif_open_wick': ((pl.col('high') - pl.col('low')) / (pl.col('open') - pl.when(pl.col('close') >= pl.col('open')).then(pl.col('low')).otherwise(pl.col('high')))).fill_null(0.),
        # 'range_dif_close_wick': ((pl.col('high') - pl.col('low')) / (pl.col('close') - pl.when(pl.col('close') >= pl.col('open')).then(pl.col('low')).otherwise(pl.col('high')))).fill_null(0.),

        # 'up_wick_div_range': (pl.col('high') - pl.max_horizontal('open', 'close')) / (pl.col('high') - pl.col('low')).fill_null(0.),
        # 'down_wick_div_range': (pl.min_horizontal('open', 'close') - pl.col('low')) / (pl.col('high') - pl.col('low')).fill_null(0.),
        # 'wick_diff_div_range': ((pl.col('high') - pl.max_horizontal('open', 'close')) - (pl.min_horizontal('open', 'close') - pl.col('low')) / (pl.col('high') - pl.col('low'))).fill_null(0.),

        # 'up_wick_div_close': (pl.col('high') - pl.max_horizontal('open', 'close')) / pl.col('close').fill_null(0.),
        # 'down_wick_div_close': (pl.min_horizontal('open', 'close') - pl.col('low')) / pl.col('close').fill_null(0.),
        # 'wick_diff_div_close': (pl.col('high') - pl.max_horizontal('open', 'close')) - (pl.min_horizontal('open', 'close') - pl.col('low')) / pl.col('close'),
        # 'range_div_close': (pl.col('high') - pl.col('low')) / pl.col('close').fill_null(0.),

        # 'high_roc': pl.col('step_high').pct_change(step).over('code').fill_null(0.),
        # 'low_roc': pl.col('step_low').pct_change(step).over('code').fill_null(0.),
        # 'close_roc': close_expr.pct_change(step).over('code').fill_null(0.),
        # 'quote_roc': pl.col('step_quote').pct_change(step).over('code').fill_null(0.),

        # pl.col('market_close_roc'), # bad
        # pl.col('market_quote_roc'),
        # 'close_pct_change_diff_div_market_close_roc': (close_expr.pct_change().over('code').fill_null(0.) - pl.col('market_close_roc')), # bad
        # 'buy_quote_ratio': pl.col('buy_quote_ratio'), # bad

        'close_pct_change_diff_sub_btc_close_roc': (close_expr.pct_change(step).over('code').fill_null(0.) - pl.col('step_btc_close_roc')), # good?
        # 'close_pct_change_diff_div_eth_close_roc': (close_expr.pct_change().over('code').fill_null(0.) - pl.col('eth_close_roc')), # bad
        'quote_div_market_quote_diff': (pl.col('step_quote') / pl.col('step_market_quote')).diff(step).over('code').fill_null(0.),
        'buy_quote_div_market_quote_diff': (pl.col('step_buy_quote') / pl.col('step_market_quote')).diff(step).over('code').fill_null(0.),

        # 'quote_diff_div_market_quote': (pl.col('quote').diff().over('code') / pl.col('market_quote')).fill_null(0.),
        # 'buy_quote_diff_div_market_quote': (pl.col('market_buy_quote').diff().over('code') / pl.col('market_quote')).fill_null(0.),

        # 'quote_diff_div_market_quote_1': (quote_expr.diff().over('code') / market_quote_expr.shift().over('code')).fill_null(0.),
        # (quote_expr / market_quote_expr).pct_change().over('code').fill_null(0.).alias('market_quote_ratio_roc'), # bad


        # 'close_pct_change_diff_div_btc_close_roc': (close_expr.pct_change(step).over('code').fill_null(0.) - pl.col('step_btc_close_roc')), # good?
        # 'quote_diff_div_market_quote': (pl.col('step_quote').diff(step).over('code') / pl.col('step_market_quote')).fill_null(0.),
        # 'buy_quote_diff_div_market_quote': (pl.col('step_market_buy_quote').diff(step).over('code') / pl.col('step_market_quote')).fill_null(0.),

        # (pl.col('market_buy_quote_diff') / market_quote_expr).alias('market_buy_quote_diff_div_market_quote'), # bad
        # (pl.col('market_buy_quote_diff') / market_quote_expr.shift().over('code')).fill_null(0.).alias('market_buy_quote_diff_div_market_quote_1'), # bad

        # (pl.col('market_buy_quote') / market_quote_expr).alias('market_buy_quote_ratio'), # bad
        # pl.col('market_buy_quote').pct_change().over('code').alias('market_buy_quote_roc'), # bad
        # (pl.col('market_buy_quote') / market_quote_expr).pct_change().over('code').fill_null(0.).alias('market_buy_quote_ratio_roc'),
        # (pl.col('market_buy_quote') / market_quote_expr).diff().over('code').alias('market_buy_quote_ratio_diff'),

        # (quote_expr / market_quote_expr).alias('market_quote_ratio'), # bad


        # pl.when(delta_volume_expr != 0).then(close_expr.pct_change() / delta_volume_expr).otherwise(0).over('code').alias('close_roc_div_delta'),
        # pl.when(delta_volume_expr.shift() != 0).then(close_expr.pct_change() / delta_volume_expr.shift()).otherwise(0).over('code').alias('close_roc_div_delta_1'),
        # pl.when(delta_volume_expr.diff() != 0).then(close_expr.pct_change() / delta_volume_expr.diff()).otherwise(0).over('code').alias('close_roc_div_delta_diff'),
        # pl.when(delta_volume_expr.diff().shift() != 0).then(close_expr.pct_change() / delta_volume_expr.diff().shift()).otherwise(0).over('code').alias('close_roc_div_delta_diff_1'),

        # (high_expr / low_expr / volume_expr.pct_change()).over('code').alias('high_div_low_div_vol_roc'),
        # 'true_range': (high_expr.rolling_max(2) / low_expr.rolling_min(2) - 1).over('code'),

        # 'body_high_roc': pl.col('step_body_high').pct_change().over('code'),
        # 'body_low_roc': pl.col('step_body_low').pct_change().over('code'),

        'high_div_low': (pl.col('step_high') / pl.col('step_low') - 1).over('code'),
        'high_div_low_roc': (pl.col('step_high') / pl.col('step_low')).pct_change(step).over('code'),
        'high_low_diff_div_close': ((pl.col('step_high') - pl.col('step_low')) / close_expr).over('code'),
        'high_low_diff_div_close_roc': ((pl.col('step_high') - pl.col('step_low')) / close_expr).pct_change(step).over('code'),
        # 'close_sub_open_div_high_sub_low': (close_expr - pl.col('step_open')) / (pl.col('step_high') - pl.col('step_low')),

        # 'avg_div_close': (pl.col('step_avg_price') / close_expr - 1),
        # 'avg_roc_sub_close_roc': (pl.col('step_avg_price').pct_change(step).over('code') - close_expr.pct_change(step).over('code')),
        # 'close_pct_change_div_high_low_diff': (close_expr.pct_change(step) / (pl.col('step_high') - pl.col('step_low'))).over('code'),

        # 'high_low_sum_div_double_close': ((pl.col('high') + pl.col('low')) / 2 / close_expr - 1).over('code'),

        # 'high_div_low': (pl.col('step_high') / pl.col('step_low') - 1).over('code'),
        # 'high_div_low_roc': (pl.col('step_high') / pl.col('step_low')).pct_change(step).over('code').fill_null(0.),
        # 'high_low_diff_div_close': ((pl.col('step_high') - pl.col('step_low')) / close_expr).over('code'),
        # 'high_low_diff_div_close_roc': ((pl.col('step_high') - pl.col('step_low')) / close_expr).pct_change(step).over('code').fill_null(0.),
        # 'body_div_range': ((open_expr - close_expr).abs() / (high_expr - low_expr)).over('code').fill_null(0.),
        # 'wick_div_close_roc': ((high_expr - low_expr - (open_expr - close_expr).abs()) / close_expr).pct_change().over('code')
        # 'high_low_diff_div_volume_sqrt': ((high_expr - low_expr) / volume_expr.sqrt()).over('code').fill_null(0.),
        # 'high_low_diff_div_volume_sqrt_1': ((high_expr - low_expr) / volume_expr.sqrt().shift()).over('code').fill_null(0.),
        # 'high_low_diff_div_volume_diff_sqrt': ((high_expr - low_expr) / volume_expr.diff().sqrt()).over('code').fill_null(0.),
        # 'high_low_diff_div_volume_diff_sqrt_1': ((high_expr - low_expr) / volume_expr.diff().sqrt().shift()).over('code').fill_null(0.),
        # 'high_low_diff_roc_div_volume_sqrt': ((high_expr - low_expr).pct_change() / volume_expr.sqrt()).over('code').fill_null(0.),
        # 'high_low_diff_div_close_div_volume_sqrt': ((high_expr - low_expr) / close_expr / volume_expr.sqrt()).over('code').fill_null(0.),
        # 'high_div_open': (high_expr / open_expr - 1),
        # 'low_div_open': (low_expr / open_expr - 1),
        # 'high_div_close': (high_expr / close_expr - 1),
        # 'low_div_close': (low_expr / close_expr - 1),
        # 'avg_price_div_open': (pl.col('avg_price') / open_expr - 1).fill_null(0.),
        # 'avg_price_div_close': (pl.col('avg_price') / close_expr - 1).fill_null(0.),
        # 'avg_price_roc': pl.col('avg_price').pct_change().over('code').fill_null(0.),


        # ((high_expr.diff() + low_expr.diff()) / close_expr).over('code').alias('high_diff_low_diff_div_close'),

        # (high_expr / close_expr).pct_change().over('code').alias('high_div_close_roc'),

        # (low_expr / close_expr).pct_change().over('code').alias('low_div_close_roc'),
        # (high_expr / low_expr.shift()).over('code').alias('high_div_low_1'),
        # (high_expr / low_expr.shift()).pct_change().over('code').alias('high_div_low_1_roc'),
        # (high_expr.shift() / low_expr).over('code').alias('high_1_div_low'),
        # (high_expr.shift() / low_expr).pct_change().over('code').alias('high_1_div_low_roc'),

        # (np.log(high_expr).diff()).over('code').alias('log_high'),
        # (np.log(low_expr).diff()).over('code').alias('log_low'),
        # pl.col('avg_price').pct_change().over('code').alias('avg_price_roc'),
        # (np.log(close_expr).diff()).over('code').alias('log_close'),

        # high_expr / close_expr.alias('high_div_close'),
        # low_expr / close_expr.alias('low_div_close'),


        # polars.exceptions.ComputeError: Series length 143040 doesn't match the DataFrame height of 286080
        # pl.concat([high_expr, low_expr]).rolling_apply(calculate_beta, 20).alias(f'rsrs_{20}'),
        # (np.log(close_expr).diff()).over('code').alias('log_close'),
        # close_expr.pct_change().over('code').fill_null(0.).alias('roc_close'),
    }
    # other_pct_change_dict = {
    #     f'close_pct_change_diff_div_{close_str}_roc': (close_expr.pct_change() - pl.col(f'{close_str}_roc')).over('code').fill_null(0.) for close_str in close_str_list
    # }
    # rolling_dict.update(other_pct_change_dict)
    extra_dict = {

        'close_roc_div_volume': pl.col('step_close_roc') / pl.col('step_volume'),
        'close_roc_div_volume_1': pl.col('step_close_roc') / pl.col('step_volume').shift(step).over('code'),
        'close_roc_div_volume_diff': pl.col('step_close_roc') / pl.col('step_volume_diff'),
        'close_roc_div_volume_diff_1': pl.col('step_close_roc') / pl.col('step_volume_diff').shift(step).over('code'), # mid-high return low drawdown

        # 'close_roc_div_volume_sqrt': (close_expr.pct_change() / (pl.col('volume').sqrt())).over('code'),
        # 'close_roc_div_volume_sqrt_1': (close_expr.pct_change() / (pl.col('volume').sqrt().shift())).over('code'),
        # 'close_roc_div_volume_sqrt_diff': (close_expr.pct_change() / (pl.col('volume').sqrt().diff())).over('code'),
        # 'close_roc_div_volume_sqrt_diff_1': (close_expr.pct_change() / (pl.col('volume').sqrt().diff().shift())).over('code'), # high return high drawdown

        # 'close_roc_div_volume_sqrt': (close_expr.pct_change(step) / (pl.col('step_volume').sqrt() + 1e-8)).over('code').fill_null(0.),
        # 'close_roc_div_volume_sqrt_1': (close_expr.pct_change(step) / (pl.col('step_volume').sqrt().shift(step) + 1e-8)).over('code').fill_null(0.), # good
        # 'close_roc_div_volume_sqrt_diff': (close_expr.pct_change(step) / (pl.col('step_volume').sqrt().diff(step) + 1e-8)).over('code').fill_null(0.),
        # 'close_roc_div_volume_sqrt_diff_1': (close_expr.pct_change(step) / (pl.col('step_volume').sqrt().diff(step).shift(step) + 1e-8)).over('code').fill_null(0.), # good

        # 'close_roc_div_quote': (pl.when(quote_expr != 0).then(close_expr.pct_change() / quote_expr).otherwise(0)).over('code'),
        # 'close_roc_div_quote_1': (pl.when(quote_expr.shift() != 0).then(close_expr.pct_change() / quote_expr.shift()).otherwise(0)).over('code'),
        # 'close_roc_div_quote_diff': pl.when(quote_expr.diff() != 0).then(close_expr.pct_change() / quote_expr.diff()).otherwise(0).over('code'),
        # 'close_roc_div_quote_diff_1': pl.when(quote_expr.diff().shift() != 0).then(close_expr.pct_change() / quote_expr.diff().shift()).otherwise(0).over('code'),
        # 'close_roc_div_volatility': (close_expr.pct_change().over('code') / close_expr.rolling_std(seq_len).over('code')),
        # 'close_roc_div_volatility': (close_expr.pct_change() / close_expr.rolling_std(seq_len)).over('code'),

        # 'sine_hour_of_day': (np.sin(2 * np.pi * pl.col('open_time').dt.hour() / 24)),
        # 'cosine_hour_of_day': (np.cos(2 * np.pi * pl.col('open_time').dt.hour() / 24)),
    }

    if cfg.feature_cfg.extra:
        cross_dict.update(extra_dict)

    if not cfg.feature_cfg.original_with_btc and 'close_pct_change_diff_sub_btc_close_roc' in cross_dict:
        cross_dict.pop('close_pct_change_diff_sub_btc_close_roc')

    if not cfg.feature_cfg.macd and 'macd' in cross_dict:
        cross_dict.pop('macd')

    if cfg.rolling_zscore:
        cross_dict.update({
        'high_rolling_zscore': pl.col('high_rolling_zscore'),
        'low_rolling_zscore': pl.col('low_rolling_zscore'),
        'close_rolling_zscore': pl.col('close_rolling_zscore'),
        'quote_rolling_zscore': pl.col('quote_rolling_zscore'),
        'buy_quote_rolling_zscore': pl.col('buy_quote_rolling_zscore'),
        })
    cross_exprs = [v.alias(k) for k, v in cross_dict.items()]

    inner_dict = {
        'close_roc_mean': pl.col('close_roc').rolling_mean(step).over('code'),
        'close_roc_std': pl.col('close_roc').rolling_std(step).over('code'),
        'close_roc_skew': pl.col('close_roc').rolling_skew(step).over('code'),
        'close_roc_kurt': pl.col('close_roc').rolling_map(lambda x: x.kurtosis(fisher=False), step).over('code'),
        'rv_up': pl.col('close_roc').rolling_map(lambda x: x.filter(x > 0).std(), step).over('code'),
        'rv_down': pl.col('close_roc').rolling_map(lambda x: x.filter(x < 0).std(), step).over('code'),
        'rv_umd': pl.col('close_roc').rolling_map(lambda x: (x.filter(x > 0).std() - x.filter(x < 0).std()) / (x.std() + 1e-12), step).over('code'),
        'extreme_pos_roc': pl.col('close_roc').rolling_max(step).over('code'),
        'extreme_neg_roc': pl.col('close_roc').rolling_min(step).over('code'),
        'extreme_pos_freq': pl.col('close_roc').rolling_map(lambda x: (x >= x.quantile(0.9)).cast(pl.Int64).sum() / step, step).over('code'),
        'extreme_neg_freq': pl.col('close_roc').rolling_map(lambda x: (x <= x.quantile(0.1)).cast(pl.Int64).sum() / step, step).over('code'),
    }
    inner_exprs = [v.alias(k) for k, v in inner_dict.items()]

    category_dict = {
        'code_index': pl.col('code_idx'),
        'day_of_week': pl.col('open_time').dt.weekday().cast(pl.Int32) - 1,
        'hour_of_day': (pl.col('open_time').dt.hour().cast(pl.Int32) * 60 // base_interval),
    }
    for k in list(category_dict.keys()):
        if k not in cfg.category_size_dict:
            category_dict.pop(k)
    category_exprs = [v.alias(k) for k, v in category_dict.items()]
    # import polars_talib as plta

    filter_dict = {
        # 'range_ratio': pl.col('range_ratio'),
        # 'atr': pl.col('range_ratio').rolling_median(12).over('code'),
        # 'range_ratio_std12': pl.col('range_ratio').rolling_std(12).over('code'),
        'range_div_std': pl.col('range_ratio') / pl.col('range_ratio').rolling_std(12).over('code'),
        'roc_div_volume': pl.col('roc_div_volume'),
        'roc_div_volume_div_std': pl.col('roc_div_volume') / pl.col('roc_div_volume').rolling_std(12).over('code'),
    }
    filter_exprs = [v.alias(k) for k, v in filter_dict.items()]


    group_list = [group_by_col, bar_idx_str, *cfg.sort_by_list]
    exprs = [
            'index',
            *group_list,
            # pl.col(group_idx_str),
            # pl.col('code'),
            # pl.col('code_idx'),
            # pl.col(frac_str).apply(lambda x: multi_code_fdiff(x, cfg)).over('code').alias(f'{frac_str}_fd') for frac_str in ('high', 'low', 'close', 'quote')
        ]

    if cfg.feature_cfg.original:
        exprs.extend(original_exprs)
        cfg.feature_cfg.original_dim = len(original_exprs)
        column_dict.original = list(original_dict.keys())

    # if cfg.feature_cfg.fracdiff:
    #     exprs.extend(frac_exprs)
    #     cfg.feature_cfg.fracdiff_dim = len(frac_exprs)

    if cfg.feature_cfg.cross:
        exprs.extend(cross_exprs)
        cfg.feature_cfg.cross_dim = len(cross_exprs)
        column_dict.cross = list(cross_dict.keys())


    if cfg.feature_cfg.rolling:
        exprs.extend(rolling_exprs)
        cfg.feature_cfg.rolling_dim = len(rolling_exprs)
        column_dict.rolling = list(rolling_dict.keys())

    if cfg.feature_cfg.inner and cfg.inner_step > 1:
        exprs.extend(inner_exprs)
        cfg.feature_cfg.inner_dim = len(inner_exprs)
        column_dict.inner = list(inner_dict.keys())

    if cfg.feature_cfg.category:
        exprs.extend(category_exprs)
        cfg.feature_cfg.category_dim = len(category_exprs)
        column_dict.category = list(category_dict.keys())

    if cfg.feature_cfg.filter:
        exprs.extend(filter_exprs)
        cfg.feature_cfg.filter_dim = len(filter_exprs)
        column_dict.filter = list(filter_dict.keys())

    if cfg.feature_cfg.image:
        exprs.extend(image_exprs)
        cfg.feature_cfg.image_dim = len(image_exprs)
        column_dict.image = list(image_dict.keys())
    if cfg.segment_enum == SegmentType.BarCount:
        if cfg.label_enum == LabelType.LogReturn:

            label_dict = dict(
                close_return=np.log(close_expr / close_expr.shift(label_step).over('code')).fill_null(0.),
                high_div_close=np.log(pl.col('label_high') / close_expr),
                low_div_close=np.log(pl.col('label_low') / close_expr),
                # np.log(close_expr / close_expr.shift(step).over('code')).fill_null(0.).alias('close_return'),
                # np.log(pl.col('step_high') / close_expr).alias('high_div_close'),
                # np.log(pl.col('step_low') / close_expr).alias('low_div_close'),

            )
        elif cfg.label_enum == LabelType.RawNormalized:
            label_dict = dict(
                close_raw=close_expr,
                high_raw=pl.col('label_high'),
                low_raw=pl.col('label_low'),
                # pl.col('step_high').alias('high_norm'),
                # pl.col('step_low').alias('low_norm'),
            )
        elif cfg.label_enum == LabelType.ShiftEmaPercentChange:
            label_dict = dict(
                close_return=close_expr.pct_change().over('code').fill_null(0.),
                ema_roc_1=(close_expr / close_expr.shift().rolling_mean(cfg.label.ema_window) - 1).over('code').fill_null(0.),
                ema_roc_2=(close_expr.shift(-1) / close_expr.shift().rolling_mean(cfg.label.ema_window) - 1).over('code').fill_null(0.),
                # close_expr.pct_change(step).over('code').fill_null(0.).alias('close_return'),
                # (close_expr / close_expr.shift(step).rolling_mean(cfg.label.ema_window) - 1).over('code').fill_null(0.).alias('ema_roc_1'),
                # (close_expr.shift(-1) / close_expr.shift(step).rolling_mean(cfg.label.ema_window) - 1).over('code').fill_null(0.).alias('ema_roc_2'),
            )
        else:
            label_dict = dict(
                close_return=close_expr.pct_change(label_step).over('code').fill_null(0.),
                high_div_close=(pl.col('label_high') / close_expr - 1),
                low_div_close=(pl.col('label_low') / close_expr - 1),
                # close_expr.pct_change(step).over('code').fill_null(0.).alias('close_return'),
                # (pl.col('step_high') / close_expr - 1).alias('high_div_close'),
                # (pl.col('step_low') / close_expr - 1).alias('low_div_close'),
            )
    else:
        exprs += ['is_segment_end']
        if cfg.segment_enum == SegmentType.Barrier:
            barrier_range = cfg.barrier_range

            def process_barrier_segments_udf(data: pl.DataFrame) -> pl.DataFrame:
                """使用Polars的UDF处理barrier segments"""
                # 确保数据按code和open_time排序
                sorted_data = data.sort(['code', 'open_time']).clone()

                # 定义处理单个code组的UDF
                def calculate_segments(group: pl.DataFrame) -> pl.DataFrame:
                    """计算单个code组的segment_id和is_segment_end"""
                    # 获取open和close列
                    opens = group['open'].to_numpy()
                    closes = group['close'].to_numpy()
                    n_rows = len(opens)

                    # 初始化结果数组
                    is_segment_end = np.zeros(n_rows, dtype=bool)
                    segment_ids = np.zeros(n_rows, dtype=int)

                    # 初始segment的起始open价格
                    segment_start_open = opens[0]
                    upper_barrier = segment_start_open * (1 + barrier_range)
                    lower_barrier = segment_start_open * (1 - barrier_range)

                    # 当前segment_id
                    current_segment_id = 0

                    # 处理每一行
                    for i in range(n_rows):
                        close = closes[i]

                        # 判断close是否突破了barrier范围
                        is_break = (close >= upper_barrier) or (close <= lower_barrier)

                        if is_break:
                            is_segment_end[i] = True
                            segment_ids[i] = current_segment_id

                            # 更新segment_id，为下一个segment做准备
                            current_segment_id += 1

                            # 如果不是最后一行，更新下一个segment的起始价格
                            if i < n_rows - 1:
                                segment_start_open = opens[i + 1]
                                upper_barrier = segment_start_open * (1 + barrier_range)
                                lower_barrier = segment_start_open * (1 - barrier_range)
                        else:
                            segment_ids[i] = current_segment_id

                    # 返回包含结果的DataFrame
                    return pl.DataFrame({
                        'code': group['code'],
                        'open_time': group['open_time'],
                        'is_segment_end': is_segment_end,
                        'segment_id': segment_ids
                    })

                return sorted_data.group_by('code').map_groups(calculate_segments)

                # 对每个code组应用UDF
                result_dfs = []
                for code, group in sorted_data.group_by('code'):
                    result_dfs.append(calculate_segments(group))

                # 合并所有结果
                if result_dfs:
                    return pl.concat(result_dfs)
                return None

            # 处理数据并获取结果
            result_df = process_barrier_segments_udf(data)

            # 将结果合并回原始数据
            if result_df is not None:
                data = data.join(result_df.select(['code', 'open_time', 'is_segment_end', 'segment_id']), on=['code', 'open_time'], how='left')
                # 设置segment_end_expr
                segment_end_expr = pl.col('is_segment_end')
            else:
                # 如果没有结果，创建一个默认的segment_end_expr
                segment_end_expr = pl.lit(False)

        elif cfg.segment_enum == SegmentType.Reversal:

            segment_str = 'close_roc'
            data = data.with_columns(
                pl.col(segment_str).shift().over('code').alias(f'{segment_str}_shift')
            )

            segment_end_expr = (pl.col(segment_str) * pl.col(f'{segment_str}_shift') <= 0)

        # 创建一个分组标识符，每当出现突破点或反转点时增加
        # if cfg.segment_enum != SegmentType.Barrier:
            # 对于非Barrier分支，使用原来的逻辑
            data = (
                data.with_row_index('_idx')
                .sort(pl.col('_idx').reverse())
                .with_columns(
                    segment_end_expr.alias('is_segment_end'),
                    segment_end_expr.cum_sum().over('code').alias('segment_id'))
                ).sort('_idx').drop('_idx').with_columns(
                    segment_id = pl.col('segment_id').max() - pl.col('segment_id')
            )
        # 对于Barrier分支，我们已经在前面的处理中创建了segment_id和is_segment_end

        # 对每个分割区间计算开始和结束价格
        segment_data = data.group_by(['code', 'segment_id']).agg(
            pl.col('open').first().alias('segment_open'),
            pl.col('close').last().alias('segment_close'),
            pl.col('high').max().alias('segment_high'),
            pl.col('low').min().alias('segment_low'),
        ).sort(['code', 'segment_id']).with_columns(
            pl.col('segment_close').shift().over('code').alias('segment_close_shift')
        )

        # 将分割数据合并回原始数据
        data = data.join(segment_data, on=['code', 'segment_id'], how='left')

        if cfg.use_segment_open:
            price_expr = pl.col('segment_open')
        else:
            price_expr = pl.col('segment_close_shift')

        if cfg.label_enum == LabelType.LogReturn:
            # 计算每个分割区间的收益率作为标签
            label_dict = dict(
                segment_return=pl.col('segment_close').log() - price_expr.log(),
                segment_high=pl.col('segment_high').log() - price_expr.log(),
                segment_low=pl.col('segment_low').log() - price_expr.log(),
            )
        elif cfg.label_enum == LabelType.RawNormalized:
            # 计算每个分割区间的收益率作为标签
            label_dict = dict(
                segment_return=pl.col('segment_close'),
                segment_high=pl.col('segment_high'),
                segment_low=pl.col('segment_low'),
            )
        else:
            label_dict = dict(
                segment_return=pl.col('segment_close') / price_expr - 1,
                segment_high=pl.col('segment_high') / price_expr - 1,
                segment_low=pl.col('segment_low') / price_expr - 1
            )

    label_exprs = [v.alias(k) for k, v in label_dict.items()]
    exprs += label_exprs

    if cfg.drop_null_rows:
        selected_df = (
            data.select(exprs)
                .with_columns(
                    pl.concat_list(pl.exclude(group_list))
                    .list.eval(pl.element().is_null())
                    .list.sum().alias('null_count'))
                .group_by(group_list).agg(
                    pl.exclude([*group_list, 'null_count']),
                    pl.sum('null_count'))
        )

        null_df = (
            selected_df
                .filter(pl.col('null_count') != 0)
                .sort(by=group_list)
        ).explode(pl.exclude([*group_list, 'null_count']))

        if len(null_df) > 0:
            print(f'{null_df.shape = }')
            print(f"{null_df['bar_idx'].max() = }")
            # print(f"{null_df.tail(10) = }")
        # print(f'{feature_df.null_count().to_numpy().sum() = }')
        feature_df: pl.DataFrame = (
            selected_df
                .filter(pl.col('null_count') == 0)
                .sort(by=group_list)
                .drop('null_count')
        ).explode(pl.exclude(group_list))
    else:
        feature_df: pl.DataFrame = data.select(exprs)

    if cfg.bar_enum == BarType.Time:
        cycle_idx_expr = ((pl.col('open_time').dt.hour().cast(pl.Int32) * 60 + pl.col('open_time').dt.minute().cast(pl.Int32)) % label_interval // base_interval).alias('cycle_idx')
    else:
        cycle_idx_expr = (pl.col('bar_idx') % cfg.label_step[phase]).alias('cycle_idx')
    feature_df = feature_df.with_columns(cycle_idx_expr)
    # print(feature_df.head(10))
    # print(feature_df.tail(10))

    if cfg.label.clip_quantile_classification:
        col_name = f'close_return'
        qdf = feature_df.select(pl.col(col_name))
        start_quantile = cfg.label.start_quantile
        end_quantile = cfg.label.end_quantile
        cfg.label.pos_min = qdf.filter(pl.col(col_name) > 0).quantile(start_quantile).item()
        cfg.label.pos_max = qdf.filter(pl.col(col_name) > 0).quantile(end_quantile).item()
        cfg.label.neg_max = qdf.filter(pl.col(col_name) < 0).quantile(1 - start_quantile).item()
        cfg.label.neg_min = qdf.filter(pl.col(col_name) < 0).quantile(1 - end_quantile).item()


    if cfg.clip_return[phase]:
        quantile = cfg.clip_return.quantile
        negative_quantiles = feature_df.filter(pl.col('close_return') < 0).groupby('code').agg(pl.col('close_return').quantile(quantile).alias('negative_Q1'))
        positive_quantiles = feature_df.filter(pl.col('close_return') > 0).groupby('code').agg(pl.col('close_return').quantile(1 - quantile).alias('positive_Q3'))
        # print(f'{negative_quantiles.head(20) = }')
        feature_df = feature_df.join(negative_quantiles, on='code').join(positive_quantiles, on='code')
        # print(f'{feature_df.head(20) = }')
        feature_df = feature_df.with_columns([
            pl.when(pl.col('close_return') < pl.col('negative_Q1')).then(pl.col('negative_Q1'))
            .when(pl.col('close_return') > pl.col('positive_Q3')).then(pl.col('positive_Q3'))
            .otherwise(pl.col('close_return')).alias('close_return'),
        ]).drop(['negative_Q1', 'positive_Q3'])

    if cfg.feature_cfg.filter:
        for col_name in filter_dict.keys():
            qtl = cfg.filter_cfg.quantile
            if phase == 'train':
                feature_df = (
                    feature_df
                    .with_columns(
                        pl.col(col_name).std().over('code').alias(f'{col_name}_std'))
                    .with_columns(
                        # (pl.col(col_name) / pl.col(f'{col_name}_median')).alias(f'{col_name}_zscore'),
                        (pl.col(col_name) / pl.col(f'{col_name}_std')).alias(f'{col_name}_zscore').fill_null(0.),
                        )
                    .sort(pl.col(f'{col_name}_zscore'))
                    .with_columns(
                        pl.col(f'{col_name}_zscore').quantile(qtl, interpolation="linear").over('code').alias(f'{col_name}_qtl_value'),
                        pl.col(f'{col_name}_zscore').cum_sum().alias(f'{col_name}_cum'),)
                    .with_columns(
                        (pl.col(f'{col_name}_cum') / pl.col(f'{col_name}_cum').max()).alias(f'{col_name}_qtl').fill_null(0.),
                    ).drop(f'{col_name}_cum')
                )
                cfg.qtl_std_dict[col_name] = df = feature_df.select(['code', f'{col_name}_qtl_value', f'{col_name}_std']).unique()
                # print(df)

                # cfg.quantile_bin_dict[col_name] = np.quantile(
                #     feature_df[f'{col_name}_zscore'].to_numpy(),
                #     np.arange(0, 1.01, 0.01),
                #     interpolation='linear',
                # )

                cfg.qtl_bin_dict[col_name] = np.quantile(
                    feature_df[f'{col_name}_zscore'].to_numpy(),
                    np.linspace(0, 1, cfg.filter_cfg.n_quantiles + 1),
                    interpolation='linear'
                )
            else:
                bins = cfg.qtl_bin_dict[col_name]
                feature_df = (
                    feature_df.join(
                        cfg.qtl_std_dict[col_name],
                        on='code'
                    )
                    .with_columns(
                        # (pl.col(col_name) / pl.col(f'{col_name}_median')).alias(f'{col_name}_zscore'),
                        (pl.col(col_name) / pl.col(f'{col_name}_std')).alias(f'{col_name}_zscore'),
                    )
                    .with_columns(
                        pl.col(f'{col_name}_zscore').map_elements(
                            lambda x: np.searchsorted(bins, x, side='right') / cfg.filter_cfg.n_quantiles,
                            return_dtype=pl.Float32
                        ).alias(f'{col_name}_qtl').fill_null(0.)
                    )
                )
        feature_df = feature_df.sort(pl.col('index'))
    # print(f'{feature_df.null_count().to_numpy().sum() = }')

    # if group_sort_by == 'open_time':
    #     start_date = feature_df[group_sort_by][0]
    #     end_date = feature_df[group_sort_by][-1]
    #     data = data.filter((pl.col(group_sort_by) >= start_date) & (pl.col(group_sort_by) <= end_date))
    # else:
    #     start_index = feature_df[group_sort_by][0]
    #     end_index = feature_df[group_sort_by][-1]
    #     data = data.filter((pl.col(group_sort_by) >= start_index) & (pl.col(group_sort_by) <= end_index))
    data = data[feature_df['index']]
    # print(f'{data.tail(10) = }\n{feature_df.columns = }\n{feature_df.tail(10) = }')

    data = data.drop('index').to_pandas().set_index(group_list)
    # if len(cfg.code_list) == 0:
    #     cfg.code_list = data.index.get_level_values('code').unique().tolist()
    #     cfg.n_codes = len(cfg.code_list)
    # feature_index = feature_df.explode(idx_str).to_pandas().set_index([idx_str, 'code']).index
    # start_date = feature_index[0]
    # end_date = feature_index[-1]
    # start_index = data.index.get_loc(start_date)
    # end_index = data.index.get_loc(end_date)

    feature_df = feature_df.drop('index').to_pandas().set_index(group_list).astype(np.float32)
    label_df = feature_df[label_dict.keys()].astype(np.float32)
    feature_df = feature_df.drop(label_dict.keys(), axis=1)
    # feature_df.index = data.index
    print(f'{data.shape = }')
    print(f'{feature_df.shape = }')
    print(f'{label_df.shape = }')
    feature_columns = feature_df.columns.tolist()
    print(f'{feature_columns = }')
    # n_seq_features = 0
    # for f_group in column_dict.keys():
    #     if len(column_dict[f_group]) == 0:
    #         print(f'{f_group} is empty, skipping...')
    #         continue
    #     # cols = list(column_dict[f_type].keys())
    #     cols = column_dict[f_group]
    #     for col_name in cols:
    #         if col_name in feature_columns and f_group in ['original', 'fracdiff', 'cross', 'inner']:
    #             n_seq_features += 1
    #         else:
    #             print(f'{col_name} not valid in feature_columns, being removed...')
    #             column_dict[f_group].remove(col_name)

    if len(cfg.column_idx_dict) == 0:
        column_idx_dict = cfg.column_idx_dict = {
            group: [feature_columns.index(col_name) for col_name in column_dict[group]]
            for group in column_dict.keys()
        }
        cfg.seq_column_idx_list = [idx for group in cfg.seq_group_list for idx in column_idx_dict[group]]
        cfg.non_seq_column_idx_list = [idx for group in cfg.non_seq_group_list for idx in column_idx_dict[group]]
        cfg.category_column_idx_list = list(column_idx_dict['category'])
        cfg.image_column_idx_list = list(column_idx_dict['image'])


    if cfg.n_columns is None:
        cfg.n_labels = len(label_dict)
        cfg.n_all_features = len(feature_columns)
        cfg.n_columns = cfg.n_labels + cfg.n_all_features
        cfg.n_seq_features = len(cfg.seq_column_idx_list)

    print(f'{cfg.n_seq_features = }')
    if cfg.feature_cfg.category:
        for k in category_dict:
            print(f'{k}: {feature_df[k].unique()}')
    return data, feature_df, label_df


def aggregate_bar(cfg: PredictorConfig, raw_data_pd: pd.DataFrame) -> pl.DataFrame:
    idx_str = f'bar_idx'
    group_by_col = 'open_time'
    # use_pandas = True
    use_pandas = False
    use_join = True
    # print(raw_data_pd.columns)
    agg_col = cfg.bar_enum.value
    bar_agg_num = cfg.agg_cfg[agg_col]

    if cfg.bar_enum == BarType.Time:
        raw_data = (
            pl.from_pandas(raw_data_pd, include_index=True)
            .group_by(group_by_col).agg(pl.exclude(group_by_col)).sort(group_by_col).with_row_index(idx_str).explode(pl.exclude([group_by_col, idx_str]))
        )
        # return raw_data
    elif cfg.bar_enum == BarType.Move:
        raw_data: pl.DataFrame = pl.from_pandas(raw_data_pd, include_index=True)
        # 使用lazy evaluation优化性能
        raw_data = pl.LazyFrame(raw_data)
        btc_data = (
            raw_data
            .filter(pl.col('code') == 'BTCUSDT')
            .with_columns(
                (((pl.col('high').log() - pl.col('low').log() + (pl.col('close').log() - pl.col('open').log()).abs()))
            #           ).alias(agg_col),
            # )
            # .with_columns(
            #     (pl.col(agg_col)
                        .cum_sum() / bar_agg_num).cast(pl.Int32).alias(idx_str),
            )
            .select(['open_time', idx_str])
        )
        raw_data = (
            raw_data
            .join(btc_data, on='open_time', how='left')
            .group_by(['code', idx_str])
            .agg(
                pl.col('open_time').first(),
                pl.col('code_idx').first(),
                pl.col('open').first(),
                pl.col('high').max(),
                pl.col('low').min(),
                pl.col('close').last(),
                pl.col('volume').sum(),
                pl.col('quote').sum(),
                pl.col('buy_volume').sum(),
                pl.col('buy_quote').sum()
            )
            .sort(['open_time', 'code'])
            .collect()
        )

    elif cfg.bar_enum == BarType.Quote:

        start = time()
        if use_pandas:
            btc_data = raw_data_pd[raw_data_pd.index.get_level_values('code') == 'BTCUSDT']
            # n = len(btc_data)
            # btc_data['index'] = np.arange(n)
            cum_quote = btc_data[agg_col].cumsum()
            btc_data = btc_data.assign(quote_agg_idx=(cum_quote // bar_agg_num).astype(int))
            open_time_agg_mapping = dict(btc_data.reset_index()[['open_time', idx_str]].values)
            raw_data_pd[idx_str] = raw_data_pd.index.get_level_values('open_time').map(open_time_agg_mapping)
            raw_data_pd = raw_data_pd.reset_index().groupby(['code', idx_str]).aggregate(
                {
                    'open_time': 'first',
                    'code_idx': 'first',
                    'open': 'first',
                    'high': 'max',
                    'low': 'min',
                    'close': 'last',
                    'volume': 'sum',
                    'quote': 'sum',
                    'buy_volume': 'sum',
                    'buy_quote': 'sum',
                }
            )
            raw_data = pl.from_pandas(raw_data_pd, include_index=True)
            # print(raw_data[raw_data.index.get_level_values('code') == 'BTCUSDT'].head(20))
            # print(raw_data[raw_data.index.get_level_values('code') == 'BTCUSDT'].tail(20))

        elif use_join:
            raw_data: pl.DataFrame = pl.from_pandas(raw_data_pd, include_index=True)
            # 使用lazy evaluation优化性能
            raw_data = pl.LazyFrame(raw_data)
            btc_data = (
                raw_data
                .filter(pl.col('code') == 'BTCUSDT')
                .with_columns(
                    (pl.col(agg_col).cum_sum() / bar_agg_num).cast(pl.Int32).alias(idx_str),
                )
                .select(['open_time', idx_str])
            )

            # 直接join避免dict映射
            raw_data = (
                raw_data
                .join(btc_data, on='open_time', how='left')
                .group_by(['code', idx_str])
                .agg(
                    pl.col('open_time').first(),
                    pl.col('code_idx').first(),
                    pl.col('open').first(),
                    pl.col('high').max(),
                    pl.col('low').min(),
                    pl.col('close').last(),
                    pl.col('volume').sum(),
                    pl.col('quote').sum(),
                    pl.col('buy_volume').sum(),
                    pl.col('buy_quote').sum()
                )
                .sort(['open_time', 'code'])
                .collect()
            )

        else:
            raw_data: pl.DataFrame = pl.from_pandas(raw_data_pd, include_index=True)
            btc_data = raw_data.filter(pl.col('code') == 'BTCUSDT')
            btc_data: pl.DataFrame = btc_data.with_columns(
                (pl.col(agg_col).cum_sum() / bar_agg_num).cast(pl.Int32).alias(idx_str),
            )
            agg_mapping = dict(btc_data.select(['open_time', idx_str]).to_pandas().values)
            raw_data = raw_data.with_columns(
                pl.col('open_time').replace_strict(agg_mapping).alias(idx_str)
            ).group_by(['code', idx_str]).agg(
                pl.col('open_time').first(),
                pl.col('code_idx').first(),
                pl.col('open').first(),
                pl.col('high').max(),
                pl.col('low').min(),
                pl.col('close').last(),
                pl.col('volume').sum(),
                pl.col('quote').sum(),
                pl.col('buy_volume').sum(),
                pl.col('buy_quote').sum()
            ).sort(['open_time', 'code'])

        # raw_data: pd.DataFrame = raw_data.set_index(['open_time', 'code']).sort_index()

        elapsed = time() - start
        print(f'{use_pandas = } | {use_join = } | {elapsed = }')
    # print(raw_data.head(20))
    # print(raw_data.tail(20))
    return raw_data


# def multi_code_fdiff(srs: pl.Series, cfg: PredictorConfig) -> pl.Series:
#     frac_arr = srs.to_numpy()
#     frac_arr = fdiff(frac_arr, n=cfg.fracdiff, axis=0)
#     result = pl.Series(f'{srs.name}_fd', frac_arr)
#     return result


def calculate_beta(lst):
    lst = lst.to_numpy()
    x = lst[::2]
    y = lst[1::2]
    # print(f'{x = }\n{y = }')
    lr = LinearRegression().fit(x.reshape(-1, 1), y)
    # y_pred = lr.predict(x.reshape(-1, 1))
    beta = lr.coef_[0]
    # r2 = r2_score(y, y_pred)
    return beta