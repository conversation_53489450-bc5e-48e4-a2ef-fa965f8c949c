import numpy as np
import pandas as pd
from sklearn.metrics import classification_report, confusion_matrix


def report_clf_ic(pred: np.ndarray, label: np.ndarray, title: str = '', n_range_groups: int = 1, day_of_week_arr: np.ndarray = None, hour_of_day_arr: np.ndarray = None, code_arr: np.ndarray = None, fee_ratio: float = 1e-4):
    pos_neg_ratio = label.clip(0).sum() / label.clip(-np.inf, 0).sum()
    pred_category = np.sign(pred) + 1
    label_category = np.sign(label) + 1

    report_str = ''
    net_pnl_sum = (pred * label).sum() - abs(pred).sum() * fee_ratio
    if n_range_groups > 1:
        pos_net_pnl_sum = 0
        for sign, side in zip([-1, 1], ['Short', 'Long']):
            side_pred = pred[pred_category == sign + 1]
            side_label = label[pred_category == sign + 1]
            if len(side_pred) == 0:
                continue
            percentile_list = [i * 100 / n_range_groups for i in range(n_range_groups + 1)]
            
            percentiles = np.percentile(side_pred, percentile_list)

            # 按分位数分组
            group_indices = np.digitize(side_pred, percentiles)

            # 分组结果
            pred_groups = [side_pred[group_indices == i] for i in range(1, len(percentiles))]
            label_groups = [side_label[group_indices == i] for i in range(1, len(percentiles))]
            for i, (p, l) in enumerate(zip(pred_groups, label_groups)):
                if len(p) == 0:
                    continue
                pnl = (p * l)
                pnl_mean = pnl.mean()
                if pnl_mean >= fee_ratio:
                    pos_net_pnl_sum += pnl.sum() - abs(p).sum() * fee_ratio
                p_cat = np.sign(p) + 1
                l_cat = np.sign(l) + 1
                report = get_clf_report(l_cat, p_cat)
                cm = confusion_matrix(l_cat, p_cat)
                p_max = p.min()
                p_min = p.max()
                report_str += (f"\n{side} Group {i + 1} position range: {p_max:.5f} to {p_min:.5f}\n")
                report_str += (f"PNL mean: {pnl_mean:.4f}\nClassification Report:\n{report}\nConfusion Matrix:\n{cm}\n")
    else:
        pos_net_pnl_sum = net_pnl_sum
    
    if day_of_week_arr is not None:
        day_of_week_count = np.unique(day_of_week_arr)
        for i, day_of_week in enumerate(day_of_week_count):
            p = pred[day_of_week_arr == day_of_week]
            l = label[day_of_week_arr == day_of_week]
            pnl = (p * l)
            pnl_mean = pnl.mean()
            p_cat = np.sign(p) + 1
            l_cat = np.sign(l) + 1
            report = get_clf_report(l_cat, p_cat)
            cm = confusion_matrix(l_cat, p_cat)
            report_str += (f"\nDay of week {day_of_week} PNL mean: {pnl_mean:.4f}\nClassification Report:\n{report}\nConfusion Matrix:\n{cm}\n")
    
    if hour_of_day_arr is not None:        
        hour_of_day_count = np.unique(hour_of_day_arr)
        for i, hour_of_day in enumerate(hour_of_day_count):
            p = pred[hour_of_day_arr == hour_of_day]
            l = label[hour_of_day_arr == hour_of_day]
            pnl = (p * l)
            pnl_mean = pnl.mean()
            p_cat = np.sign(p) + 1
            l_cat = np.sign(l) + 1
            report = get_clf_report(l_cat, p_cat)
            cm = confusion_matrix(l_cat, p_cat)
            report_str += (f"\nHour {hour_of_day} of day PNL mean: {pnl_mean:.4f}\nClassification Report:\n{report}\nConfusion Matrix:\n{cm}\n")
    
    if code_arr is not None:
        code_count = np.unique(code_arr)
        for i, code in enumerate(code_count):
            # for sign, side in zip([-1, 1], ['Short', 'Long']):
            #     p = pred[code_arr == code][pred_category == sign + 1]
            #     l = label[code_arr == code][pred_category == sign + 1]
            #     pnl = (p * l)
            #     pnl_mean = pnl.mean()
            #     p_cat = np.sign(p) + 1
            #     l_cat = np.sign(l) + 1
                
            p = pred[code_arr == code]
            l = label[code_arr == code]
            pnl = (p * l)
            pnl_mean = pnl.mean()
            p_cat = np.sign(p) + 1
            l_cat = np.sign(l) + 1
            report = get_clf_report(l_cat, p_cat)
            cm = confusion_matrix(l_cat, p_cat)
            report_str += (f"\nCode {code} PNL mean: {pnl_mean:.4f}\nClassification Report:\n{report}\nConfusion Matrix:\n{cm}\n")

    improved = (pos_net_pnl_sum / net_pnl_sum - 1) if net_pnl_sum > 0 else 0
    pnl_mean = (pred * label).mean()
    report = get_clf_report(label_category, pred_category)
    cm = confusion_matrix(label_category, pred_category)
    ic = np.corrcoef(pred, label)[0, 1]
    report_str += (f"\n{title}\nPNL mean: {pnl_mean:.4f}, Pos Neg Ratio: {pos_neg_ratio:.4f}, Pos PNL Improved: {improved:.4f}, IC: {ic:.4f}\nClassification Report:\n{report}\nConfusion Matrix:\n{cm}\n")
    print(report_str)
    return report_str, ic


def get_category_name_list(category_num):
    if category_num == 1:
        target_names = ['neutral']
    elif category_num == 2:
        target_names = ['down', 'up']
    elif category_num == 3:
        target_names = ['down', 'neutral', 'up']
    elif category_num == 4:
        target_names = ['down', 'neutral', 'up', 'grid']
    else:
        raise ValueError(f'Invalid category_num: {category_num}')
    return target_names


def get_clf_report(label_category, pred_category):
    n_label_category = np.unique(label_category).shape[0]
    n_pred_category = np.unique(pred_category).shape[0]
    category_num = max(n_label_category, n_pred_category)
    if n_label_category <= 1:
        return f'Only #{n_label_category = } number for classification report.'
    target_names = get_category_name_list(category_num)
    try:
        return classification_report(label_category, pred_category, target_names=target_names, digits=4, zero_division=0)
    except ValueError as e:
        return f'Error: {e}'


def get_hour_of_day(index: pd.MultiIndex, hour_shift: int = 0, time_col_name: str = 'open_time'):
    hour_srs = index.get_level_values(time_col_name).to_series() + pd.Timedelta(hours=hour_shift)
    return hour_srs.dt.hour.values