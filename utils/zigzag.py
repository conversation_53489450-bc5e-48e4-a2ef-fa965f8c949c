"""
reference:
https://github.com/jbn/ZigZag.git
"""

from typing import Iterable
import numpy as np
from matplotlib import pyplot as plt
from sklearn.model_selection import train_test_split
from core.dot_dict import DotDict as dd

PEAK = 1
VALLEY = -1

def get_pivots(X: np.ndarray, up_pct: float, down_pct: float = None, delay: int = 0) -> np.ndarray:
	"""
	Using online min-max, get pivots of the whole array
	"""
	if down_pct is None:
		down_pct = -up_pct
	else:
		assert  -1 < down_pct < 0,  'down_pct must be in range [-1,0)'
	
	last_pivot_v = v_0 = min_v = max_v = X[0]
	last_pivot_idx = 0
	l = len(X)
	pivots = np.zeros(l, dtype=np.int_)
	init_done_idx = 0
	for i in range(1, l):
		v_i = X[i]
		init_done_idx = i
		if np.log(v_i / min_v) >= up_pct:
			pivots[0] = VALLEY if min_v == v_0 else PEAK
			break
		if np.log(v_i / max_v) <= down_pct:
			pivots[0] =  PEAK if max_v == v_0 else VALLEY
			break
		max_v = max(v_i, max_v)
		min_v = min(v_i, min_v)
		
	# if pivots[0] == 0:
	else:
		pivots[0] = VALLEY if X[-1] > v_0 else PEAK
	print(f'{init_done_idx = }')
	
	trend = -pivots[0]
	
	for i in range(1, l):
		v_i = X[i]
		delayed_idx = i + delay
		pct_chg = np.log(v_i / last_pivot_v)
		if trend < 0:
			if pct_chg >= up_pct:
				pivots[last_pivot_idx] = trend
				trend = PEAK
				last_pivot_v = v_i
				last_pivot_idx = delayed_idx
			elif v_i < last_pivot_v:
				last_pivot_v = v_i
				last_pivot_idx = delayed_idx
		else:
			if pct_chg <= down_pct:
				pivots[last_pivot_idx] = trend
				trend = VALLEY
				last_pivot_v = v_i
				last_pivot_idx = delayed_idx
			elif v_i > last_pivot_v:
				last_pivot_v = v_i
				last_pivot_idx = delayed_idx
			
		if last_pivot_idx == l - 1:
			pivots[last_pivot_idx] = trend
		elif pivots[-1] == 0:
			pivots[-1] = -trend

	return pivots


def get_label_is_long(pivots: np.ndarray) -> np.ndarray:
	result = pivots.copy()
	for i in range(len(result) - 2, 0, -1):
		if result[i] == 0:
			result[i] = result[i + 1]
	# result[-1] = result[-2]
	result[0] = result[1]
	# result = -result
	result[result == -1] = 0
	# result = np.concatenate([result[1:], [0]])
	return result
	


def min_max_transform(X: np.ndarray, pivots: np.ndarray):
	pvt_indices = np.arange(len(X))[pivots != 0]
	min_max = np.ones_like(X) * 0.5
	high = low = X[0]
	for i, idx in enumerate(pvt_indices):
		if i >= len(pvt_indices) - 1:
			break
		start = idx
		end = pvt_indices[i + 1]
		if i == 0 or i == len(pvt_indices) - 2:
			high = max(X[start: end + 1])
			low = min(X[start: end + 1])
		else:
			if X[start] > X[end]:
				high = X[start]
				low = X[end]
			else:
				high = X[end]
				low = X[start]
		min_max[start:end] = 2 * (X[start:end] - low) / (high - low) - 1
	return min_max


def get_label_is_top(X: np.ndarray, pivots: np.ndarray) -> np.ndarray:
	min_max = min_max_transform(X, pivots)
	return (min_max > 0).astype(np.float32)


def get_label_is_hrz_top(pivots: np.ndarray, hrz_pct: float = .5) -> np.ndarray:
	pvt_indices = np.arange(len(pivots))[pivots != 0]
	result = np.zeros_like(pivots)
	for i, idx in enumerate(pvt_indices):
		if i >= len(pvt_indices) - 2:
			break
		start_idx = idx
		end_idx = pvt_indices[i + 1]
		next_end_idx = pvt_indices[i + 2]
		first_range = int((end_idx - start_idx) * hrz_pct)
		second_range = int((next_end_idx - end_idx) * (1 - hrz_pct))
		mid_idx = end_idx - first_range
		if i == len(pvt_indices) - 3:
			next_mid_idx = end_idx
		else:			
			next_mid_idx = end_idx + second_range
		if pivots[start_idx] == VALLEY and pivots[end_idx] == PEAK:
			result[mid_idx: next_mid_idx + 1] = 1
		elif i == 0:
			result[start_idx: mid_idx] = 1
	return result

	
def get_label_is_seg_end(pivots: np.ndarray) -> np.ndarray:
	pvt_indices = np.arange(len(pivots))[pivots != 0]
	result = np.zeros_like(pivots)
	for i, idx in enumerate(pvt_indices):
		if i >= len(pvt_indices) - 1:
			break
		start_idx = idx
		end_idx = pvt_indices[i + 1]
		mid_idx = (start_idx + end_idx) // 2
		result[mid_idx: end_idx] = 1
	return result


def get_label_is_trend(X: np.ndarray, pivots: np.ndarray, qtl_pct: float = 0.6) -> np.ndarray:
	trend = np.zeros_like(X)
	pvt_indices = np.arange(len(X))[pivots != 0]
	returns = compute_segment_returns(X, pivots)
	# 计算result绝对值在各个分位数的收益率分布
	returns = np.abs(returns)
	qtl = np.quantile(returns, qtl_pct)
	label = np.zeros_like(returns)
	label[returns >= qtl] = 1
	print(f'{qtl = :.4f}')
	for i, idx in enumerate(pvt_indices):
		if i >= len(pvt_indices) - 1:
			break
		start_idx = idx
		end_idx = pvt_indices[i + 1]
		trend[start_idx:end_idx] = label[i]
	trend_pct = np.mean(trend)
	print(f'{qtl_pct = }, {trend_pct = :.4f}\n')		
	return trend

def get_pivots_label(X: np.ndarray, pivots: np.ndarray, label_type: str, hrz_pct: float, qtl_pct: float) -> np.ndarray:
	if label_type == 'is_long':
		return get_label_is_long(pivots)
	elif label_type == 'is_top':
		return get_label_is_top(X, pivots)
	elif label_type == 'is_hrz_top':
		return get_label_is_hrz_top(pivots, hrz_pct)
	elif label_type == 'is_trend':
		return get_label_is_trend(X, pivots, qtl_pct)	
	else:
		raise ValueError(f'Invalid label_type: {label_type}')
	

def get_pivots_label_dict(X: np.ndarray, pivots: np.ndarray, qtl_pct: float) -> dict:
	ld = dd()
	ld['is_long'] = get_label_is_long(pivots)
	ld['is_top'] = get_label_is_top(X, pivots)	
	ld['is_hrz_top'] = get_label_is_hrz_top(pivots)
	ld['is_trend'] = get_label_is_trend(X, pivots, qtl_pct)
	return ld


def get_label_dict(X: np.ndarray, up_pct: float, down_pct: float = None, delay: int = 0, qtl_pct: float = 0.6, **kwargs) -> dict:
	pivots = get_pivots(X, up_pct, down_pct, delay)
	return get_pivots_label_dict(X, pivots, qtl_pct)


def get_label(X: np.ndarray, up_pct: float, down_pct: float = None, delay: int = 0, label_type: str = 'is_long', qtl_pct: float = 0.6, **kwargs) -> np.ndarray:
	pivots = get_pivots(X, up_pct, down_pct, delay)
	return get_pivots_label(X, pivots, label_type, qtl_pct)


def identify_initial_pivot(X, up_thresh, down_thresh):
	x_0 = X[0]
	x_t = x_0

	max_x = x_0
	min_x = x_0

	max_t = 0
	min_t = 0

	up_thresh += 1
	down_thresh += 1

	for t in range(1, len(X)):
		x_t = X[t]

		if x_t / min_x >= up_thresh:
			return VALLEY if min_t == 0 else PEAK

		if x_t / max_x <= down_thresh:
			return PEAK if max_t == 0 else VALLEY

		if x_t > max_x:
			max_x = x_t
			max_t = t

		if x_t < min_x:
			min_x = x_t
			min_t = t

	t_n = len(X)-1
	return VALLEY if x_0 < X[t_n] else PEAK


def peak_valley_pivots(X: np.ndarray, up_thresh: float, down_thresh: float) -> np.ndarray:
	"""
	Find the peaks and valleys of a series.

	:param X: the series to analyze
	:param up_thresh: minimum relative change necessary to define a peak
	:param down_thesh: minimum relative change necessary to define a valley
	:return: an array with 0 indicating no pivot and -1 and 1 indicating
		valley and peak


	The First and Last Elements
	---------------------------
	The first and last elements are guaranteed to be annotated as peak or
	valley even if the segments formed do not have the necessary relative
	changes. This is a tradeoff between technical correctness and the
	propensity to make mistakes in data analysis. The possible mistake is
	ignoring data outside the fully realized segments, which may bias
	analysis.
	"""
	if down_thresh > 0:
		raise ValueError('The down_thresh must be negative.')

	initial_pivot = identify_initial_pivot(X, up_thresh, down_thresh)
	t_n = len(X)
	pivots = np.zeros(t_n, dtype=np.int_)
	trend = -initial_pivot
	last_pivot_t = 0
	last_pivot_x = X[0]

	pivots[0] = initial_pivot

	# Adding one to the relative change thresholds saves operations. Instead
	# of computing relative change at each point as x_j / x_i - 1, it is
	# computed as x_j / x_1. Then, this value is compared to the threshold + 1.
	# This saves (t_n - 1) subtractions.
	up_thresh += 1
	down_thresh += 1

	for t in range(1, t_n):
		x = X[t]
		r = x / last_pivot_x

		if trend == -1:
			if r >= up_thresh:
				pivots[last_pivot_t] = trend
				trend = PEAK
				last_pivot_x = x
				last_pivot_t = t
			elif x < last_pivot_x:
				last_pivot_x = x
				last_pivot_t = t
		else:
			if r <= down_thresh:
				pivots[last_pivot_t] = trend
				trend = VALLEY
				last_pivot_x = x
				last_pivot_t = t
			elif x > last_pivot_x:
				last_pivot_x = x
				last_pivot_t = t

	if last_pivot_t == t_n-1:
		pivots[last_pivot_t] = trend
	elif pivots[t_n-1] == 0:
		pivots[t_n-1] = -trend

	return pivots


def max_drawdown(X: np.ndarray | Iterable) -> float:
	"""
	Compute the maximum drawdown of some sequence.

	:return: 0 if the sequence is strictly increasing.
		otherwise the abs value of the maximum drawdown
		of sequence X
	"""
	mdd = 0
	peak = X[0]

	for x in X:
		if x > peak:
			peak = x

		dd = (peak - x) / peak

		if dd > mdd:
			mdd = dd

	return mdd if mdd != 0.0 else 0.0


def pivots_to_modes(pivots: np.ndarray | Iterable) -> np.ndarray:
	"""
	Translate pivots into trend modes.

	:param pivots: the result of calling ``peak_valley_pivots``
	:return: numpy array of trend modes. That is, between (VALLEY, PEAK] it
	is 1 and between (PEAK, VALLEY] it is -1.
	"""

	modes = np.zeros(len(pivots), dtype=np.int_)
	mode = -pivots[0]

	modes[0] = pivots[0]

	for t in range(1, len(pivots)):
		x = pivots[t]
		if x != 0:
			modes[t] = mode
			mode = -x
		else:
			modes[t] = mode

	return modes


def compute_segment_returns(X: np.ndarray, pivots: np.ndarray) -> np.ndarray:
	"""
	:return: numpy array of the pivot-to-pivot returns for each segment."""
	pivot_points = X[pivots != 0]
	return pivot_points[1:] / pivot_points[:-1] - 1.0


def plot_pivots(X: np.ndarray, pivots: np.ndarray):
	plt.xlim(0, len(X))
	# plt.ylim(X.min()*0.99, X.max()*1.01)
	plt.plot(np.arange(len(X)), X, 'k:', alpha=0.5)
	plt.plot(np.arange(len(X))[pivots != 0], X[pivots != 0], 'k-')
	plt.scatter(np.arange(len(X))[pivots == 1], X[pivots == 1], color='g')
	plt.scatter(np.arange(len(X))[pivots == -1], X[pivots == -1], color='r')
	
	
if  __name__ == '__main__':
	import pandas as pd
	from core.dot_dict import DotDict as dd
	from sklearn.metrics import classification_report, confusion_matrix
	from autogluon.tabular import TabularPredictor	
	df = pd.read_csv('bitstampUSD_1-min_data_2012-01-01_to_2021-03-31.csv')[-100000:]
	values = df.Close.values
	# values = np.random.normal(0, 1, 100) * 100 + 1000
	pct = 0.003
	# pivots_ = peak_valley_pivots(values, pct, -pct)
	# pivots_ = get_pivots(values, pct, delay=1)
	pivots_ = get_pivots(values, pct)
	min_max = min_max_transform(values, pivots_)
	plt.figure(figsize=(20, 8))
	plot_pivots(values, pivots_)
	ax = plt.twinx()	
	ax.plot(np.arange(len(values)), min_max)
	plt.show()
	
	td = dd()
	td['is_top'] = get_label_is_top(values, pivots_)
	td['is_trend'] = get_label_is_trend(values, pivots_)
	td['is_long'] = get_label_is_long(pivots_)
	td['is_seg_end'] = get_label_is_seg_end(pivots_)
		
	# for qtl_pct in [0.6, 0.65, 0.7, 0.75, 0.8]:
	# 	trend = get_label_is_trend(values, pivots_, qtl_pct=qtl_pct)
	# 	trend_pct = np.mean(trend) * 100
	# 	print(f'{qtl_pct = }, {trend_pct = :.4f}\n')
    
	train_set, test_set = train_test_split(df, test_size=0.2, shuffle=False)

	excluded_model_types = ['NN_TORCH', 'RF', 'XT', 'CAT']
	for label_str in ['is_top', 'is_long', 'is_seg_end', 'is_trend']:
		label_train = td[label_str][:len(train_set)]
		label_test = td[label_str][len(train_set):]
		train_set[label_str] = label_train
		test_set[label_str] = label_test

		predictor = TabularPredictor(label=label_str).fit(train_set, excluded_model_types=excluded_model_types)
		predictions = predictor.predict(test_set)

		report = classification_report(label_test, predictions)
		cm = confusion_matrix(test_set[label_str], predictions)
		print(f'{label_str} classification report:\n{report}\n{cm}')
		train_set.drop(columns=label_str, inplace=True)
		test_set.drop(columns=label_str, inplace=True)