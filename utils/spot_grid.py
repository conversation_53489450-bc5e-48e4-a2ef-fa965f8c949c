#%%
from matplotlib import pyplot as plt
import numpy as np
import pandas as pd

def backtest_grid(
    price_arr: np.ndarray,
    init_position: float = 0.5,
    grid_size: float = 0.0001,
    bottom_range: float = 0.5,
    top_range: float = 0.5,
    fee_ratio: float = 0.000,
    plot: bool = False
) -> dict:
    """
    现货网格策略回测
    Args:
        prices: 价格序列
        grid_size: 网格间距比例 (如0.0001表示0.01%)
        bottom_price: 最低价格幅度比例 (相对于初始价格)
        top_price: 最高价格幅度比例
        fee_ratio: 手续费率
    
    Returns:
        包含回测结果的字典
    """
    base_price = 1
    top_price = base_price + top_range
    bottom_price = base_price - bottom_range
    # 初始化参数
    grid_num = 2 * (top_price - bottom_price) / grid_size
    grid_levels = np.linspace(bottom_price, top_price, int(grid_num) + 1)
    position_unit = init_position / grid_num
    

    init_cash = 1 - init_position

    top_position = init_position - (top_price - base_price) / grid_size * position_unit
    bottom_position = init_position + (base_price - bottom_price) / grid_size * position_unit
    price_arr = np.concatenate([[base_price], price_arr / price_arr[0]])
    position_arr = (init_position + ((base_price - price_arr) / grid_size).astype(int) * position_unit).clip(top_position, bottom_position)
    
    buy_grid_price_arr = (base_price + np.floor((price_arr - base_price) / grid_size) * grid_size).clip(bottom_price, top_price)
    sell_grid_price_arr = (base_price + np.ceil((price_arr - base_price) / grid_size) * grid_size).clip(bottom_price, top_price)

    notional = position_arr * price_arr
    # 初始化现金数组（包含初始现金）
    cash_arr = np.zeros_like(position_arr)
    cash_arr[0] = init_cash  # 初始现金
    # 原循环代码段替换为：
    delta = np.diff(position_arr)
    buy_mask = delta > 0
    sell_mask = delta < 0
    buy_prices = buy_grid_price_arr[1:]
    sell_prices = sell_grid_price_arr[1:]

    cash_flow = np.zeros_like(delta)
    cash_flow[buy_mask] = -delta[buy_mask] * buy_prices[buy_mask] * (1 + fee_ratio)
    cash_flow[sell_mask] = -delta[sell_mask] * sell_prices[sell_mask] * (1 - fee_ratio)
    cash_arr = np.concatenate([[init_cash], init_cash + np.cumsum(cash_flow)])

    # for i in range(1, len(position_arr)):
    #     delta = position_arr[i] - position_arr[i-1]  # 持仓变化量       
        
    #     if delta > 0:  # 买入操作
    #         cash_flow = delta * buy_grid_price_arr[i] * (1 + fee_ratio)  # 现金流出（含手续费）
    #         cash_arr[i] = cash_arr[i-1] - cash_flow
    #     elif delta < 0:  # 卖出操作
    #         cash_flow = abs(delta) * sell_grid_price_arr[i] * (1 - fee_ratio)  # 现金流入（扣除手续费）
    #         cash_arr[i] = cash_arr[i-1] + cash_flow
    #     else:  # 无交易
    #         cash_arr[i] = cash_arr[i-1]
    balance = notional + cash_arr
    if not plot:
        return balance[1:]
    # 绘制结果
    plt.figure(figsize=(20, 10))
    
    # 价格和网格线
    plt.subplot(5, 1, 1)
    plt.plot(price_arr, label='Price')
    plt.plot(np.ones(price_arr.shape[0]) * base_price, '--', label='Base Price')
    plt.plot(buy_grid_price_arr, '^', markersize=2, label='Buy Grid Price')
    plt.plot(sell_grid_price_arr, 'v', markersize=2, label='Sell Grid Price')
    for level in grid_levels:
        plt.axhline(level, color='gray', linestyle='--', alpha=0.3)
    plt.axhline(bottom_price, color='red', linestyle='--', label='Grid Range')
    plt.axhline(top_price, color='red', linestyle='--')
    plt.legend(loc='lower left')
    
    # 净值曲线
    plt.subplot(5, 1, 2)
    plt.plot(position_arr, label='Position')
    plt.axhline(bottom_position, color='red', linestyle='--', label='Grid Range')
    plt.axhline(top_position, color='red', linestyle='--')    
    plt.legend()

    plt.subplot(5, 1, 3)
    plt.plot(cash_arr, label='Cash')
    plt.legend()
    
    # 持仓变化
    plt.subplot(5, 1, 4)
    plt.plot(notional, label='Notional')
    plt.legend()

    plt.subplot(5, 1, 5)
    plt.plot(balance, label='Balance')
    plt.legend()
    plt.tight_layout()
    plt.show()

    return balance[1:]

if __name__ == '__main__':

    # 示例用法
    prices = np.cumprod(1 + np.random.randn(1000)*0.02)  # 生成随机价格序列
    results = backtest_grid(
        prices, 
        grid_size=0.0001, 
        bottom_range=0.5, 
        top_range=0.5,
        plot=True
        )
# %%
