import torch

def torch_select(conditions, choices, default=0):
    # 将条件堆叠成 (n_conditions, ...) 形状
    cond_stack = torch.stack(conditions, dim=0)
    n_conditions = len(conditions)
    
    # 确定哪些位置需要默认值（所有条件都不满足）
    default_mask = ~cond_stack.any(dim=0)
    
    # 找到每个位置第一个满足条件的索引（若全为False，返回0）
    index = cond_stack.float().argmax(dim=0)
    
    # 将默认位置的索引设置为 n_conditions（即choices_with_default的最后一个位置）
    index = torch.where(default_mask, n_conditions, index)
    
    # 将默认值添加到结果列表中，并堆叠成张量
    choices_with_default = choices + [default]
    choices_stack = torch.stack(choices_with_default, dim=0)
    
    # 根据索引选择结果
    result = choices_stack.gather(
        0, 
        index.unsqueeze(0).expand_as(choices_stack)
    )[0]
    
    return result


if __name__ == "__main__":
    # 示例用法
    x = torch.tensor([1, 3, 5, 7, 9])

    # 定义条件和结果
    cond1 = x < 3
    cond2 = (x >= 3) & (x < 8)
    cond3 = x >= 8

    result1 = x * 1
    result2 = x * 2
    result3 = x * 3
    default = torch.full_like(x, -1)  # 默认值设为-1

    # 调用函数
    output = torch_select(
        [cond1, cond2, cond3],
        [result1, result2, result3],
        default
    )

    print(output)  # 输出: tensor([ 1,  6, 10, 14, 27])