from calendar import c
import os

from matplotlib import style
from sklearn.metrics import classification_report, confusion_matrix
from torch import long
from core.cst import TaskType, TopkType

import pandas as pd
import polars as pl
import numpy as np
import matplotlib.pyplot as plt
from core.dot_dict import DotDict as dd
from core.predictor_config import PredictorConfig
pl.Config.set_tbl_cols(-1)
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)


def get_row_arr(df):
    balance_arr = df['before_fee'].values + 1
    mdd_arr = get_mdd_arr(balance_arr)[0]
    arr = np.append(df.values[-1], mdd_arr)
    return arr

def get_mdd_arr(balance_arr):
    maximum_arr = np.maximum.accumulate(balance_arr)
    drawdown_arr = maximum_arr - balance_arr
    drawdown_norm_arr = drawdown_arr / maximum_arr
    mdd_arr = np.array([drawdown_arr[-1], drawdown_norm_arr[-1], max(drawdown_arr), max(drawdown_norm_arr)])
    return mdd_arr, drawdown_arr, drawdown_norm_arr


def get_top_pnl(df: pl.DataFrame, top_n=4, side='abs', n_codes=20, stop_loss_ratio=-0.5, with_index=True):
    # sign = np.sign(df.position)
    if top_n == n_codes:
        top_df = df
    if side == 'abs':
        top_df = df.top_k(top_n, by='abs_position')
    elif side == 'long':
        top_df = df.top_k(top_n, by='position').with_columns(pl.col('position').clip(0, None))
    elif side =='short':
        top_df = df.bottom_k(top_n, by='position').with_columns(pl.col('position').clip(None, 0))
    else:
        raise ValueError('side should be "abs", "long", or "short"')
    # print(top_df)
    side_expr = np.sign(pl.col('position'))
    loss_return_expr = pl.when(side_expr > 0).then(pl.col('low_return')).when(side_expr < 0).then(pl.col('high_return')).otherwise(0)
    stop_loss_cond_expr = (loss_return_expr * pl.col('position')) <= stop_loss_ratio
    regret_cond_expr = (pl.col('asset_return') * pl.col('position')) > stop_loss_ratio
    exprs = []
    if with_index:
        exprs.append(pl.col('bar_idx').unique())
    exprs.extend([
        ((pl.col('abs_position') * pl.col('asset_return')).implode().list.sum() * n_codes / top_n).alias(f'{side}_top_{top_n}_baseline'),
        ((pl.col('position') * pl.col('asset_return')).implode().list.sum() * n_codes / top_n).alias(f'{side}_top_{top_n}_pnl'),
        (pl.col('position').clip(0, None).implode().list.sum() * n_codes / top_n).alias(f'{side}_top_{top_n}_position_long'),
        (pl.col('position').clip(None, 0).implode().list.sum() * n_codes / top_n).alias(f'{side}_top_{top_n}_position_short'),
        (pl.col('position').implode().list.sum() * n_codes / top_n).alias(f'{side}_top_{top_n}_position_sum'),
        (pl.col('abs_position').implode().list.sum() * n_codes / top_n).alias(f'{side}_top_{top_n}_position_abs'),
        pl.col("code").implode().alias(f'{side}_top_{top_n}_code_list'),
        pl.col("position").implode().alias(f'{side}_top_{top_n}_position_list'),
        pl.col("asset_return").implode().alias(f'{side}_top_{top_n}_asset_return_list'),
    ])
    result = top_df.select(exprs)
    return result


def get_top_k_in_one_row(df: pl.DataFrame, cfg: PredictorConfig, top_num=4, n_codes=0, group_by_str: str = None)  -> pl.DataFrame:

    # if code_list is None:
    #     code_arr = df.select(pl.col('code').unique().alias('code_list')).to_numpy()
    # else:
    #     code_arr = np.array(code_list)

    # code_eye = np.eye(n_codes, dtype=np.float32)
    topk_enum: TopkType = cfg.topk_enum
    top_egnore_num = cfg.top_egnore_num
    max_num = df.shape[0]

    # 确保top_num + top_egnore_num不超过最大可用数量
    if top_num + top_egnore_num > max_num:
        if top_egnore_num >= max_num:
            # 如果忽略的数量已经超过最大数量，则无法选择任何code
            top_egnore_num = max_num - 1
            top_num = 1
        else:
            # 调整top_num，确保能选到足够的code
            top_num = max_num - top_egnore_num

    # long_df = df.top_k(top_num, by='position').select((pl.col('position').clip(0, None) * pl.col('asset_return')).implode().list.mean().alias(f'long_only_pnl'),)
    # short_df = df.bottom_k(top_num, by='position').select((pl.col('position').clip(None, 0) * pl.col('asset_return')).implode().list.mean().alias(f'short_only_pnl'))
    # btc_series = df.filter(pl.col('code') == 'BTCUSDT')
    if top_num == n_codes and n_codes > 0 and top_egnore_num == 0:
        # 如果选择所有code且不忽略任何code
        top_df = df
    elif topk_enum == TopkType.Abs:
        if top_egnore_num > 0:
            # 先选择前top_num + top_egnore_num个，然后跳过前top_egnore_num个
            temp_df = df.top_k(top_num + top_egnore_num, by='abs_position')
            top_df = temp_df.slice(top_egnore_num, top_num)
        else:
            top_df = df.top_k(top_num, by='abs_position')
        # top_df = top_df.with_columns(pl.col('position').clip(-1e-6, None))
        # top_df = top_df.with_columns(pl.col('position').clip(None, 1e-6))
    elif topk_enum == TopkType.PositionVote:
        if top_egnore_num > 0:
            temp_df = df.top_k(top_num + top_egnore_num, by='abs_position')
            top_df = temp_df.slice(top_egnore_num, top_num)
        else:
            top_df = df.top_k(top_num, by='abs_position')
        side_mean = top_df['position'].mean()
        top_df = top_df.with_columns(position=pl.col('position') * side_mean)
    elif topk_enum == TopkType.SignVote:
        if top_egnore_num > 0:
            temp_df = df.top_k(top_num + top_egnore_num, by='abs_position')
            top_df = temp_df.slice(top_egnore_num, top_num)
        else:
            top_df = df.top_k(top_num, by='abs_position')
        sign_mean = top_df['position'].sign().mean()
        top_df = top_df.with_columns(position=pl.col('position') * sign_mean)
    elif topk_enum == TopkType.Hedge:
        if top_num > max_num // 2:
            top_num = max_num // 2

        # 对于Hedge类型，需要分别处理long和short部分
        if top_egnore_num > 0:
            # 先选择更多，然后跳过前top_egnore_num个
            temp_long_df = df.top_k(top_num + top_egnore_num, by='position')
            temp_short_df = df.bottom_k(top_num + top_egnore_num, by='position')

            # 从每个部分跳过前top_egnore_num个
            long_df = temp_long_df.slice(top_egnore_num, top_num)
            short_df = temp_short_df.slice(top_egnore_num, top_num)
        else:
            long_df = df.top_k(top_num, by='position')
            short_df = df.bottom_k(top_num, by='position')

        top_df = pl.concat([long_df, short_df])
    elif topk_enum == TopkType.EWM_PNL:
        if top_egnore_num > 0:
            temp_df = df.top_k(top_num + top_egnore_num, by='ewm_pnl')
            top_df = temp_df.slice(top_egnore_num, top_num)
        else:
            top_df = df.top_k(top_num, by='ewm_pnl')
    elif topk_enum == TopkType.ABS_EWM_PNL:
        if top_egnore_num > 0:
            temp_df = df.top_k(top_num + top_egnore_num, by='abs_ewm_pnl')
            top_df = temp_df.slice(top_egnore_num, top_num)
        else:
            top_df = df.top_k(top_num, by='abs_ewm_pnl')

        top_df = top_df.with_columns(position = pl.when(pl.col('ewm_pnl') < 0).then(-pl.col('position')).otherwise(pl.col('position')))
    # print(top_df)
    if top_df['abs_position'].sum() > 0:
        if cfg.equal_backtest_weight:
            norm_expr = [
                (pl.col('abs_position').sign() / pl.col('abs_position').sign().sum()).alias('abs_position'),
                (pl.col('position').sign() / pl.col('abs_position').sign().sum()).alias('position'),
            ]
            if 'spread_position' in top_df.columns:
                norm_expr.append(
                    (pl.col('spread_position').sign() / pl.col('abs_position').sign().sum()).alias('spread_position')
                )
        else:
            norm_expr = [
                (pl.col('abs_position') / pl.col('abs_position').sum()).alias('abs_position'),
                (pl.col('position') / pl.col('abs_position').sum()).alias('position'),
            ]
            if 'spread_position' in top_df.columns:
                norm_expr.append(
                    (pl.col('spread_position') / pl.col('abs_position').sum()).alias('spread_position')
                    )
        top_df = top_df.with_columns(norm_expr)
    non_zero_count = (top_df['abs_position'] > 0).count()
    if non_zero_count < top_num:
        print(f'{df["open_time"][0] = }\nWarning: {non_zero_count = } is smaller than {top_num = }')
    exprs = []
    if group_by_str is not None:
        exprs.append(pl.col(group_by_str).unique())
    if 'spread_position' in top_df.columns:
        exprs.append((pl.col('spread_position') * (pl.col('spread') - 2 * 0.0002)).implode().list.sum().alias(f'spread_pnl'))
    elif 'spread_pnl' in top_df.columns:
        exprs.append((pl.col('spread_pnl')).implode().list.mean().alias(f'spread_pnl'))
    pnl_expr = pl.col('position') * pl.col('final_filled_return')
    high_sub_low_return_expr = pl.col('high_return') - pl.col('low_return')
    asset_return_expr = pl.col('asset_return')
    exprs.extend([
        (pl.col('abs_position') * pl.col('final_filled_return')).implode().list.sum().alias(f'baseline'),
        pnl_expr.implode().list.sum().alias(f'pnl'),
        pnl_expr.clip(0, None).implode().list.sum().alias(f'profit'),
        pnl_expr.clip(None, 0).implode().list.sum().alias(f'loss'),
        (pl.col('position').clip(0, None) * pl.col('final_filled_return')).implode().list.sum().alias(f'long_pnl'),
        (pl.col('position').clip(None, 0) * pl.col('final_filled_return')).implode().list.sum().alias(f'short_pnl'),
        pl.col('position').clip(0, None).implode().list.sum().alias(f'position_long'),
        pl.col('position').clip(None, 0).implode().list.sum().alias(f'position_short'),
        pl.col('position').implode().list.sum().alias(f'position_hedge'),
        pl.col('abs_position').implode().list.sum().alias(f'abs_position_sum'),
       high_sub_low_return_expr.implode().list.mean().alias(f'spot_spread'),
        # pl.col("code").implode().alias(f'code_list'),

        # pl.col("code_idx").implode().map_elements(lambda idx_arr: code_eye[idx_arr].sum(axis=0)
        # , return_dtype=pl.Object
        # ).alias(f'code_onehot'),

        # pl.struct('code_idx', 'position').map_elements(lambda struct: get_onehot_like_array(struct, n_codes)
        # , return_dtype=pl.List(pl.Float64)
        # # , return_dtype=pl.Object
        # ).implode()
        # .map_elements(lambda arr_list: np.stack(arr_list).sum(axis=0)
        # , return_dtype=pl.Object
        #               )
        # .alias(f'position_arr'),

        pl.concat_list("code_idx", "position").implode().alias(f'position_list'),
        # pl.col("position").implode().alias(f'position_list'),
        # pl.col("asset_return").implode().alias(f'asset_return_list'),
    ])
    result = top_df.select(exprs).with_columns(
        profit_ratio=2 * pl.col('profit') / (pl.col('profit') - pl.col('loss')),
        # btc_return=btc_series['asset_return'].item(),
        # btc_spread=btc_series['high_return'].item() - btc_series['low_return'].item(),
    )
    if 'spread_pnl' in top_df.columns:
        result = result.with_columns(
            (pl.col('pnl') + pl.col('spread_pnl')).alias(f'pnl')
        )
    # result = pl.concat([result, long_df, short_df], how='horizontal')
    # print(result['position_list'])
    # print(result[f'code_onehot'])
    # print(result[f'position_arr'])

    return result


def get_onehot_like_array(struct_expr: pl.Expr, n: int, value_str='position') -> list:
    arr = np.zeros(n, dtype=np.float32)
    arr[struct_expr['code_idx']] = struct_expr[value_str]
    # print(arr)
    # return arr
    return arr.tolist()


def get_fee(arr_srs: pl.Series, fee_rate=0.0002) -> float:
    position_arr = np.stack(arr_srs)
    init_position = np.zeros(1, position_arr.shape[1], dtype=np.float32)
    prev_position_arr = np.concatenate([init_position, position_arr[:-1]], axis=0)
    position_diff = position_arr - prev_position_arr
    fee_arr = position_diff.abs() * fee_rate
    return fee_arr.sum()


def get_turnover(struct_expr: pl.Expr, list_name: str, prev_list_name: str, top_num: int) -> float:
    cur = dict(struct_expr[list_name])#[list_name])
    prev = dict(struct_expr[prev_list_name])#[prev_list_name])
    n = len(cur)
    prev_n = len(prev)
    # tail or head condition
    if n == prev_n == 0:
        return 0.
    if n == 0 or prev_n == 0:
        return 1.

    indices = set(np.array(list(cur.keys())).astype(int))
    prev_indices = set(np.array(list(prev.keys())).astype(int))
    min_set = indices.intersection(prev_indices)

    min_positions = [min(abs(cur[k]), abs(prev[k])) for k in min_set if cur[k] * prev[k] > 0]
    remaining = np.array(min_positions).sum()

    return 1. - remaining
    # return len(set(lst) - set(prev_lst)) / top_num


if __name__ == '__main__':

    # is_rolling_epoches = True
    is_rolling_epoches = False
    epoch_index = 66    # interval_index = 4
    interval_index = 0
    # side_top_num = 0
    side_top_num = 1
    if is_rolling_epoches:
        from rolling_retraining import pred_cfg
        execute_gap = True
        execute_next = True
    else:
        # from rank import pred_cfg
        from classification_trading import pred_cfg
        # from direct_trading import pred_cfg
        # from portfolio import pred_cfg
        execute_gap = False
        execute_next = False

    # pred_cfg.interval_cfg.base = 120
    # pred_cfg.seq_len = 36
    # pred_cfg.rnn_name = 'lstm'
    # pred_cfg.model_name = 'rnn'
    pred_cfg.start_date.multi = '2021.01.01'
    pred_cfg.train_end_date = '2023.12.01'
    # pred_cfg.train_end_date = '2024.08.01'
    # pred_cfg.shuffling.codewise = True
    # pred_cfg.adapt.in_use = False 2024_0827_145345

    # datetime_str = '2024_0828_193243' # drt 2021.01 gru seq36
    # datetime_str = '2024_0827_145345' # 2022.01 gru seq42 sqrt lgt
    # datetime_str = '2024_0827_140422' # 2021.01 gru seq42 sqrt
    # datetime_str = '2024_0826_113247' # 2021.01 gru seq42 14day
    # datetime_str = '2024_0826_105718' # 2021.01 gru seq42 hsz12
    # datetime_str = '2024_0826_100139' # 2021.01 gru seq84
    # datetime_str = '2024_0826_093036' # 2021.01 lstm seq42
    # datetime_str = '2024_0826_083533' # 2021.01 gru seq42
    # datetime_str = '2024_0903_083534' # 2021.01 gru
    # datetime_str = '2024_0904_114552' # 2021.01 lstm
    # datetime_str = '2024_0904_115055' # 2021.01 lstm
    # datetime_str = '2024_0902_070747' # 2021.01 lstm
    # datetime_str = '2024_0904_143151' # 2021.01 lstm shift0.01
    # datetime_str = '2024_0904_152609' # 2021.01 lstm shift0.003
    # datetime_str = '2024_0904_143910' # 2021.01 lstm shift0.005
    # datetime_str = '2024_0904_145642' # 2021.01 lstm shift0.01
    # datetime_str = '2024_0904_154631' # 2021.01 lstm shift0.002 120min
    # datetime_str = '2024_0904_173450' # 2021.01 lstm shift0.005 drt
    # datetime_str = '2024_0907_133315' # 2021.01 gru clf v2023.08.01
    # datetime_str = '2024_0908_225222' # 2021.01 gru clft smax v2023.08.01
    # datetime_str = '2024_0908_005925' # 2021.01 tcn clft v2023.08.01
    # datetime_str = '2024_0909_091658' # 2021.01 tcn clft smax v2023.08.01 120min
    # datetime_str = '2024_0909_100054' # 2021.01 tcn clft smax norev v2023.08.01
    # datetime_str = '2024_0909_095607' # 2021.01 tcn clft smax0.25 noaug v2023.08.01
    # datetime_str = '2024_0910_232803' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.01.01
    # datetime_str = '2024_0910_234625' # 2021.01 tcn tanh clft smax0.2 logit noaug sl0.01 v2024.01.01
    # datetime_str = '2024_0914_111915' # 2021.01 tcn tanh clft smax0.2 logit noaug v2023.08.01 rr conv5
    # datetime_str = '2024_0913_121934' # 2021.01 tcn tanh clft3 smax logit noaug v2023.08.01 rr conv4
    # datetime_str = '2024_0915_131418' # 2021.01 tcn tanh clft2 smax adv logit noaug v2023.08.01 rr conv4
    # datetime_str = '2024_0915_140852' # 2021.01 tcn tanh clft2 smax logit noaug v2023.08.01 rr conv4
    # datetime_str = '2024_0914_085003' # 2021.01 tcn tanh clft smax0.2 logit noaug v2023.08.01 rr conv5 kan

    # datetime_str = '2024_0915_130335' # 2021.01 tcn tanh clft2 smax adv logit noaug v2023.08.01 conv4
    # datetime_str = '2024_0917_173007' # 2021.01 tcn tanh clft3 smax logit noaug v2023.08.01 conv4 rr
    # datetime_str = '2024_0917_204537' # 2021.01 tcn tanh clft3 smax logit noaug v2023.08.01 conv4 rr
    # datetime_str = '2024_0918_121806' # 2021.01 tcn tanh clft3 smax logit noaug v2023.12.01 conv4 drop0.2
    # datetime_str = '2024_0918_124650' # 2021.01 tcn tanh clft3 smax logit noaug v2023.12.01 conv4 drop0.1
    datetime_str = '2024_0919_095134' # 2021.01 tcn tanh clft3 smax logit noaug v2023.12.01 conv4 drop0.15 77top1 72top1&2
    # datetime_str = '2024_0919_102226' # 2021.01 tcn tanh clft3 smax logit noaug v2023.08.01 conv4 drop0.16 43 58 63
    # datetime_str = '2024_0919_104024' # 2021.01 tcn tanh clft3 smax logit noaug v2023.08.01 conv4 drop0.17 43 60 2024_0919_104024
    # datetime_str = '2024_0917_172342' # 2021.01 tcn tanh clft3 smax logit noaug v2023.08.01 conv4
    # datetime_str = '2024_0917_183409' # 2021.01 tcn tanh clft3 smax logit noaug v2023.08.01 conv5
    # datetime_str = '2024_0916_075826' # 2021.01 tcn tanh rank  noaug v2023.08.01
    # datetime_str = '2024_0917_110723' # 2021.01 tcn tanh inner4  noaug v2023.08.01
     # datetime_str = '2024_0911_110222' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.01.01
    # datetime_str = '2024_0911_193253' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.07.01    2024_0913_102014
    # datetime_str = '2024_0913_102014' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.08.01    2024_0913_102014
    # datetime_str = '2024_0911_191748' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.03.01    2024_0911_191748
    # datetime_str = '2024_0911_190359' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.03.01
    # datetime_str = '2024_0911_162140' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.01.01
    # datetime_str = '2024_0911_135836' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.01.01
    # datetime_str = '2024_0911_124655' # 2021.01 tcn tanh clft smax0.2 logit noaug v2023.08.01    2024_0914_082027
    # datetime_str = '2024_0914_082027' # 2021.01 tcn tanh clft smax0.2 logit noaug v2023.08.01
    # datetime_str = '2024_0911_132400' # 2021.01 seq36 tcn tanh clft smax0.2 logit noaug v2023.08.01
    # datetime_str = '2024_0911_130349' # 2021.01 tcn tanh clft smax0.2 logit noaug v2023.08.01
    # datetime_str = '2024_0911_125519' # 2021.01 tcn tanh clft smax0.2 logit noaug v2023.08.01
    # datetime_str = '2024_0911_121033' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.01.01
    # datetime_str = '2024_0911_115601' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.01.01
    # datetime_str = '2024_0911_114622' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.01.01
    # datetime_str = '2024_0911_114159' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.01.01
    # datetime_str = '2024_0911_113214' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.01.01
    # datetime_str = '2024_0911_112442' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.01.01
    # datetime_str = '2024_0910_141011' # 2021.01 tcn tanh clft smax0.2 logit noaug v2024.01.01
    # datetime_str = '2024_0910_232010' # 2021.01 tcn tanh clft smax0.2 logit noaug v2023.08.01
    # datetime_str = '2024_0910_144706' # 2021.01 tcn tanh clft smax0.2 logit noaug v2023.08.01
    # datetime_str = '2024_0909_205432' # 2021.01 tcn clft  smax0.2 logit noaug v2024.01.01
    # datetime_str = '2024_0909_202447' # 2021.01 tcn clft rr smax0.2 logit noaug v2023.08.01
    # datetime_str = '2024_0909_120102' # 2021.01 tcn clft rr smax0.2 noaug v2023.08.01 #
    # datetime_str = '2024_0909_101559' # 2021.01 tcn clft smax0.2 noaug v2023.08.01 #
    # datetime_str = '2024_0909_114016' # 2021.01 tcn clft smax0.2 noaug v2024.01.01 #
    # datetime_str = '2024_0908_222004' # 2021.01 tcn clft smax v2023.08.01
    # datetime_str = '2024_0908_073801' # 2021.01 tcn clft smax v2023.08.01
    # datetime_str = '2024_0907_124730' # 2021.01 tcn clf v2023.08.01
    # datetime_str = '2024_0905_131123' # 2021.01 tcn drt v2023.08.01
    # datetime_str = '2024_0905_132738' # 2021.01 tcn shift0.005 drt v2023.08.01
    # 2024_0911_110222
    #

    ckpt_folder = pred_cfg.get_ckpt_folder()


    folder = os.path.join(ckpt_folder, datetime_str)
    # iterate through all the folders in the folder
    epoch_dict = dd()
    epoch_sum_dict = dd()
    interval_dict = dd()
    interval_sum_dict = dd()
    val_start_list = []
    end_date = None
    mean_position = 1 / pred_cfg.n_codes
    for epoch_folder in os.listdir(folder):
        if epoch_folder.startswith('epoch'):
            # load the data
            epoch_dict[epoch_folder] = dd()
            epoch_sum_dict[epoch_folder] = dd()
            interval_dict[epoch_folder] = dd()
            interval_sum_dict[epoch_folder] = dd()
            for file in os.listdir(os.path.join(folder, epoch_folder)):
                if file.endswith('.pqt'):
                    if end_date is None:
                        end_date = file.replace('.pqt', '').split('v')[-1]
                    df = pd.read_parquet(os.path.join(folder, epoch_folder, file))
                    df = df.sort_index()

                    # df['position'][(df['position'] < -0.448) & (df['position'] > -0.505)] *= -1.
                    # df['position'][(df['position'] > 0.667) | (df['position'] < 0.5068) & (df['position'] > -0.5365)] = 0.
                    # df['position'][(df['position'] < 0.9) & (df['position'] > -0.9)] = 0.
                    # df['position'][(df['position'] <= -0.012) | (df['position'] >= 0.012)] = -0.2

                    # df['position'][(df['position'] < 0) & (df['position'] > -0.0122)] = -mean_position
                    # df['position'][(df['position'] < 0.0122) & (df['position'] > -0.)] = mean_position
                    df['before_fee'] = df['position'] * df.asset_return

                    df['long_position'] = np.clip(df.position.values, 0, 1)
                    df['long_only'] = np.clip(df.position.values, 0, 1) * df.asset_return
                    df['short_position'] = np.clip(df.position.values, -1, 0)
                    df['short_only'] = np.clip(df.position.values, -1, 0) * df.asset_return
                    df['abs_position'] = df['long_position'] - df['short_position']
                    # print(df.head(30))
                    sum_df = df[['long_position', 'short_position', 'abs_position', 'long_only', 'short_only','before_fee', 'after_fee', 'fee']].groupby('index').agg(np.sum)
                    # sum_df['asset_return'] = df['asset_return'].values.reshape(-1, pred_cfg.n_codes).mean(axis=1)


                    # sum_df[abs_top_str] = df.groupby('index').apply(lambda x: get_top_pnl(x, top_n=side_top_num * 2, side='all'))
                    # sum_df[long_top_str] = df.groupby('index').apply(lambda x: get_top_pnl(x, top_n=side_top_num, side='long'))
                    # sum_df[short_top_str] = df.groupby('index').apply(lambda x: get_top_pnl(x, top_n=side_top_num, side='short'))

                    sum_df['baseline'] = (df['asset_return'] * df['position'].abs()).groupby('index').agg(np.sum)
                    val_start = file.split('v')[0]
                    if val_start not in val_start_list:
                        val_start_list.append(val_start)
                    epoch_dict[epoch_folder][val_start] = df
                    epoch_sum_dict[epoch_folder][val_start] = sum_df

    val_start_list.sort()
    print(f'{val_start_list = }')
    epoch_count = len(epoch_sum_dict)
    results_df = pd.DataFrame(columns=['type', 'index', 'long_position', 'short_position', 'abs_position', 'long_only', 'short_only', 'before_fee', 'after_fee', 'fee', 'baseline', 'drawdown', 'drawdown_norm', 'mdd', 'mdd_norm']).set_index(['type', 'index'])
    interval_df = pd.DataFrame(columns=['epoch', 'train_end', 'val_start_end', 'long_position', 'short_position', 'abs_position', 'long_only', 'short_only', 'before_fee', 'after_fee', 'fee', 'baseline', 'drawdown', 'drawdown_norm', 'mdd', 'mdd_norm']).set_index(['epoch', 'train_end', 'val_start_end'])
    # print(f'{results_df.index = }')
    data_count_per_interval = pred_cfg.rolling_retraining.interval_in_day * 1440 // pred_cfg.interval_cfg.base // pred_cfg.inner_step

    rank_dict = dd(
        val=dd(),
        naive=dd(),
        rolling=dd()
    )
    # rank_dict = dd(
    #     gap=dd(),
    #     next=dd()
    #     )
    rank_naive_list = []
    rank_rolling_list = []
    n_codes = pred_cfg.n_codes
    for i in range(epoch_count):
        epoch_str = f'epoch_{i}'
        for iv, val_start in enumerate(val_start_list):
            epoch_val_sum_df = epoch_sum_dict[epoch_str][val_start]
            epoch_val_df = epoch_dict[epoch_str][val_start]
            # num_intervals = ceil(len(epoch_val_df) / data_count_per_interval)
            num_intervals = len(val_start_list) - iv
            epoch_interval_list = interval_dict[epoch_str][val_start] = [epoch_val_df[n_codes * data_count_per_interval * ni: n_codes * data_count_per_interval * (ni+1)] for ni in range(num_intervals)]
            # print(f'{epoch_str} {len(epoch_interval_list) = }')
            # print(epoch_interval_list[0].head(30))
            epoch_interval_sum_list = interval_sum_dict[epoch_str][val_start] = [epoch_val_sum_df[data_count_per_interval * n: data_count_per_interval * (n+1)] for n in range(num_intervals)]
            # print(epoch_interval_sum_list[0].head(30))
            sub_list = val_start_list[iv:] + [end_date]
            for ii, interval in enumerate(epoch_interval_sum_list):
                train_end = f't{val_start}'
                val_start_end = f'{sub_list[ii]}v{sub_list[ii + 1]}'
                # print(f'{val_start_end = }')
                interval_df.loc[(i, train_end, val_start_end), :] = get_row_arr(interval.cumsum())

        if execute_gap:
            naive_gap = epoch_sum_dict[epoch_str][val_start_list[0]][data_count_per_interval:].cumsum()
            results_df.loc[('naive_gap', i), :] = get_row_arr(naive_gap)

            rolling_gap = []
            for val_start in val_start_list[:-1]:
                val_df = epoch_sum_dict[epoch_str][val_start][data_count_per_interval: 2 * data_count_per_interval]
                rolling_gap.append(val_df)
            rolling_gap = pd.concat(rolling_gap).cumsum()
            results_df.loc[('rolling_gap', i), :] = get_row_arr(rolling_gap)

        if execute_next:
            naive_next = epoch_sum_dict[epoch_str][val_start_list[1]].cumsum()
            results_df.loc[('naive_next', i), :] = get_row_arr(naive_next)

            rolling_next = []
            for val_start in val_start_list[1:]:
                val_df = epoch_sum_dict[epoch_str][val_start][:data_count_per_interval]
                rolling_next.append(val_df)
            rolling_next = pd.concat(rolling_next).cumsum()
            results_df.loc[('rolling_next', i), :] = get_row_arr(rolling_next)

        for vi, val_start in enumerate(val_start_list):
            if val_start not in rank_dict.val:
                rank_dict.val[val_start] = dd()
                # rank_dict.val[val_start] = dd()
            if val_start not in rank_dict.naive:
                rank_dict.naive[val_start] = dd()
                # rank_dict.test[val_start] = dd()
            if val_start not in rank_dict.rolling:
                rank_dict.rolling[val_start] = dd()
            val_df = epoch_sum_dict[epoch_str][val_start]
            rank_dict.val[val_start][epoch_str] = val_df[:data_count_per_interval]
            if vi < len(val_start_list) - 1:
                rank_dict.naive[val_start][epoch_str] = val_df[data_count_per_interval: 2 * data_count_per_interval]


    for i in range(epoch_count):
        epoch_str = f'epoch_{i}'
        for vi, val_start in enumerate(val_start_list[:-1]):
            # 留最后一个interval作为验证集，得到每个epoch的rank_idx
            # 再加入最后一个interval的数据进行训练，取与之前rank_idx相同epoch的model
            val_df = epoch_sum_dict[epoch_str][val_start_list[vi+1]]
            rank_dict.rolling[val_start][epoch_str] = val_df[:data_count_per_interval]

    for val_start in val_start_list[:-1]:
        sorted_val = sorted(rank_dict.val[val_start].items(), key=lambda x: x[1]['before_fee'].sum(), reverse=True)
        val_list = list(sorted_val)
        naive_list = [rank_dict.naive[val_start][k] for k, v in val_list]
        # rank_dict.gap[val_start] = gap_list
        rank_naive_list.append(naive_list)

        rolling_list = [rank_dict.rolling[val_start][k] for k, v in val_list]
        # rank_dict.next[val_start] = next_list
        rank_rolling_list.append(rolling_list)


    for i in range(epoch_count):
        # rank_df = pd.concat([v[i] for v in rank_dict.values()]).cumsum()
        if execute_gap:
            rank_gap = pd.concat([tl[i] for tl in rank_naive_list]).cumsum()
            results_df.loc[('rank_naive', i), :] = get_row_arr(rank_gap)
        if execute_next:
            rank_next = pd.concat([tl[i] for tl in rank_rolling_list]).cumsum()
            results_df.loc[('rank_rolling', i), :] = get_row_arr(rank_next)


    print(f'{datetime_str} results_df: \n{results_df.sort_index()}')

    group_interval = interval_df[['before_fee', 'fee', 'drawdown', 'mdd']].groupby(['val_start_end', 'train_end']).agg(np.mean)
    print(f'\ngroup_interval: \n{group_interval.sort_index()}')


    epoch_interval = interval_df[['before_fee', 'fee', 'drawdown', 'mdd']][interval_df.index.get_level_values(0) == epoch_index].reset_index().set_index(['val_start_end', 'train_end']).sort_index().drop(columns=['epoch'])
    print(f'\nepoch #{epoch_index} interval: \n{epoch_interval}')
    # print(f'{interval_df.sort_index() = }')
    # interval_df.to_csv(os.path.join(folder, 'interval_df.csv'))

    if is_rolling_epoches:
        epoch_list = [interval_dict[f'epoch_{epoch_index}'][val_start][0] for val_start in val_start_list]
        # print(f'{len(epoch_list) = }')
        epoch_df = pd.concat(epoch_list).reset_index()
        epoch_df['index'] = np.arange(len(epoch_df)) // n_codes
        epoch_df = epoch_df.set_index(['index', 'code'])
        # print(f'{epoch_df.head(30) = }')
        # print(epoch_df.head(30))
        epoch_sum_list = [interval_sum_dict[f'epoch_{epoch_index}'][val_start][0] for val_start in val_start_list]
        # print(f'{len(epoch_sum_list) = }')
        epoch_sum_df = pd.concat(epoch_sum_list, ignore_index=True).reset_index().set_index(['index'])
    else:

        epoch_sum_df = epoch_sum_dict[f'epoch_{epoch_index}'][val_start_list[interval_index]]
        epoch_df = epoch_dict[f'epoch_{epoch_index}'][val_start_list[interval_index]]

    # print(epoch_df.head(30))
    position_df: pd.DataFrame = epoch_df[['position', 'asset_return', 'before_fee']].sort_values(by=['position']).reset_index()#, ascending=False)

    # print(f'{position_df.loc[2000].position = }')
    # print(f'{position_df.loc[7000].position = }')
    # print(f'{position_df.loc[15000].position = }')
    # print(f'{position_df.loc[26000].position = }')
    plt.figure(figsize=(20, 8))
    # index = np.arange(len(position_df))
    plt.plot(position_df.index, (np.sign(position_df.position) * position_df.asset_return).cumsum(), label='cum asset return over position')
    plt.plot(position_df.index, position_df.before_fee.cumsum(), label='cum pnl before fee over position')
    plt.plot(position_df.index, (position_df.asset_return * position_df.position).cumsum(), label='cum asset_return x position over position')
    plt.legend(['cum asset return over position', 'cum pnl before fee over position', 'cum asset_return x position over position'])
    zero_value_index = position_df[position_df.position > 0].index[0]
    plt.title(f'cum pnl before fee over position, zero value index: {zero_value_index}')
    plt.grid()
    xticks = position_df.index[::400]
    position_xticks = position_df.position[::400]
    gca = plt.gca()
    ax1 = plt.twiny()
    ax1.set_xlim(gca.get_xlim())
    ax1.set_xticks(xticks)
    ax1.set_xticklabels(round(position_xticks, 5), rotation=45)
    ax1.grid()
    plt.show()
    # code_list = position_df.index.get_level_values(1).unique()
    code_list = []
    for code in code_list:
        code_df = position_df[position_df.code == code]
        plt.figure(figsize=(20, 8))
        index = np.arange(len(code_df))
        plt.plot(index, code_df.asset_return.cumsum(), label='asset_return')
        plt.title(f'{code} cum asset return over position')
        plt.show()

    asset_return_df = epoch_df[['position', 'asset_return']].sort_values(by=['asset_return']).reset_index()
    # print(asset_return_df.head(30))
    # print(asset_return_df.tail(30))
    plt.figure(figsize=(20, 8))

    plt.plot(asset_return_df.index, asset_return_df.position.cumsum(), label='cum position over index')
    plt.legend(['cum position over index'])
    ax2 = plt.twiny()
    # ax2.set_xlim(ax.get_xlim())
    # ax2.set_xticks(asset_return_df.asset_return)
    ax2.plot(asset_return_df.asset_return, asset_return_df.position.cumsum(), label='cum position over asset_return', color='r')
    plt.legend(['cum position over asset_return'])
    plt.title(f'cum position over asset_return')
    plt.grid()
    plt.show()



    other_columns = epoch_sum_df.columns.drop(['long_position', 'short_position', 'abs_position'])
    epoch_sum_df[other_columns] = epoch_sum_df[other_columns].cumsum()
    # epoch_sum_df[~['long_position', 'short_position']] = epoch_sum_df[~['long_position', 'short_position']].cumsum()
    # epoch_sum_df['long_position'] = epoch_sum_df.long_position.diff().fillna(0).clip(lower=0)
    # epoch_sum_df['short_position'] = epoch_sum_df.short_position.diff().fillna(0).clip(upper=0)

    epoch_sum_df['alpha'] = epoch_sum_df.before_fee - epoch_sum_df.baseline
    if side_top_num > 0:
        abs_top_str = f'abs_top_{side_top_num}'
        long_top_str = f'long_top_{side_top_num}'
        short_top_str = f'short_top_{side_top_num}'
        epoch_df = pl.from_pandas(epoch_df, include_index=True)
        epoch_sum_df = pl.from_pandas(epoch_sum_df, include_index=True)
        long_top_df = epoch_df.group_by('index').map_groups(lambda x: get_top_pnl(x, side_top_num, 'long')).sort('index')
        short_top_df = epoch_df.group_by('index').map_groups(lambda x: get_top_pnl(x, side_top_num,'short')).sort('index')
        abs_top_df = epoch_df.group_by('index').map_groups(lambda x: get_top_pnl(x, side_top_num, 'abs')).sort('index')
        # print(f'{len(epoch_df) = }')
        # print(f'{len(epoch_sum_df) = }')
        # print(f'{len(long_top_df) = }')
        # print(f'{len(short_top_df) = }')
        # print(f'{len(abs_top_df) = }')
        # print(f'{long_top_df.head(30) = }')
        # print(f'{short_top_df.head(30) = }')
        # print(f'{abs_top_df.head(30) = }')
        # print(f'{epoch_sum_df.head(30) = }')
        epoch_sum_df = epoch_sum_df.with_columns([
            long_top_df.select(f'{long_top_str}_pnl').to_series().cum_sum(),
            short_top_df.select(f'{short_top_str}_pnl').to_series().cum_sum(),
            abs_top_df.select(f'{abs_top_str}_pnl').to_series().cum_sum(),
            abs_top_df.select(f'{abs_top_str}_baseline').to_series().cum_sum(),
            ])
        # max_fee = len(epoch_sum_df) * 2e-4

        # abs_stop_loss_count = abs_top_df.select(f'{abs_top_str}_stop_loss_count').to_series().cum_sum()
        # abs_regret_count = abs_top_df.select(f'{abs_top_str}_regret_count').to_series().cum_sum()
        epoch_sum_df = epoch_sum_df.with_columns(max_fee=np.arange(1, len(epoch_sum_df) + 1) * 2 * 2e-4 * pred_cfg.n_codes)# + abs_stop_loss_count * 4e-4 * pred_cfg.n_codes / side_top_num)
        epoch_sum_df = epoch_sum_df.to_pandas().set_index('index')
        epoch_sum_df[f'{abs_top_str}_after_fee'] = epoch_sum_df[f'{abs_top_str}_pnl'] - epoch_sum_df['max_fee']

        # print(f'{abs_stop_loss_count[-1] = }\n{abs_regret_count[-1] = }')
    # epoch_sum_df['combo'] = 0.8 * (epoch_sum_df.before_fee + epoch_sum_df.baseline)
    # epoch_sum_df['std'] = epoch_sum_df.baseline.rolling(14).std()
    # print(f'\nrolling epoch #{epoch_index} df: \n{rolling_epoch_df.head(30)}')
    after_fee_arr = epoch_sum_df[f'{abs_top_str}_after_fee'].values
    baseline_arr = epoch_sum_df[f'{abs_top_str}_baseline'].values
    step_baseline_arr = np.concatenate([baseline_arr[:1], np.diff(baseline_arr)])
    step_pnl_arr = np.concatenate([after_fee_arr[:1], np.diff(after_fee_arr)])
    profit_to_loss_ratio = -np.clip(step_pnl_arr, 0, None).sum() / np.clip(step_pnl_arr, None, 0).sum()

    print(f'\n{abs_top_str}_group:\n{profit_to_loss_ratio = }\n{after_fee_arr[-1] = }\n')

    mdd_arr, drawdown_arr, drawdown_norm_arr = get_mdd_arr(after_fee_arr + 1)
    for mi, mdd_str in enumerate(['drawdown', 'drawdown_norm', 'mdd', 'mdd_norm']):
        print(f'{mdd_str}: \t{mdd_arr[mi]/n_codes:.4f}')

    label = np.sign(step_baseline_arr) + 1
    group_side = np.sign(step_pnl_arr * step_baseline_arr)
    pred = np.sign(group_side) + 1
    report = classification_report(label, pred, label_names=['down', 'neutral', 'up'], digits=4, zero_division=0)
    cm = confusion_matrix(label, pred)
    print(f"Classification Report:\n{report}\nConfusion Matrix:\n{cm}\n")
    plt.figure(figsize=(20, 8))
    plt.plot(epoch_sum_df, linewidth=1)
    plt.title(f'epoch #{epoch_index} summary')
    plt.legend(epoch_sum_df.columns)
    plt.grid()
    # gca = plt.gca()
    ax = plt.twinx()
    # ax.set_ylim(None, 0)
    drawdown_line = ax.plot(
        -drawdown_arr,
        color='black',
        linestyle='-.',
        linewidth=1,
        alpha=0.4
        )
    drawdown_norm_line = ax.plot(
        -drawdown_norm_arr,
        color='r',
        # linestyle='-.',
        linewidth=1,
        alpha=0.4
        )
    ax.legend(['drawdown', 'drawdown_norm'])
    # drawdown_line.set_linestyle('g--')
    plt.show()