import json
import time
from datetime import datetime
import numpy as np
import torch
from typing import Callable, <PERSON><PERSON>, Iterable
from pandas import DataFrame
from core.dot_dict import DotDict



def check_nan(result):
	has_nan = False
	if isinstance(result, np.ndarray):
		if np.isnan(result).any():
			print(f'nan in result: {result}')
			has_nan = True	
	elif isinstance(result, Iterable):
		for r in result:
			if isinstance(result, np.ndarray):
				if np.isnan(r).any():
					has_nan = True	
					print(f'nan in result: {r}')	
					break
	return has_nan
		


def nan_checker(np_func):
	def wrapper(*args, **kwargs):
		result = np_func(*args, **kwargs)
		cfg = args[0].cfg
		if cfg.check_nan:
			has_nan = check_nan(result)
			if has_nan:
				print(f'nan in func: {np_func}')	
		return result

	return wrapper


def step_value_decorator(step_func: Callable) -> Callable:
	def wrapper(*args, **kwargs):
		result = step_func(*args, **kwargs)
		cfg = args[0].cfg
		if cfg.dataframe_type.index == 0:  # 'polars'
			if not cfg.is_portfolio:
				if cfg.is_solo:
					if not isinstance(result, (int, float)):
						result = result[0]
				else:
					result = result.to_numpy()
		return result

	return wrapper

def timer(func: Callable) -> Callable:
	def wrapper(*args, **kwargs):
		start = time.time()
		result = func(*args, **kwargs)
		elapsed = time.time() - start
		print(f"{func.__name__} {elapsed = :.3f}s")
		return result
	
	return wrapper

def find_index(lst: list, item):
	if item in lst:
		return lst.index(item)
	else:
		return None


def get_config_dict(filename: str = "train.json"):
	with open(filename, 'r') as fp:
		json_dict = DotDict(json.load(fp))
		# check_none(json_dict)
	return json_dict



def check_none(json_dict):
	if hasattr(json_dict, "__dict__"):
		for k in json_dict.__dict__:
			if k is None:
				print(k, "is None")
			if json_dict.__dict__[k] is None:
				print(k, "value is None")
				json_dict.__dict__.pop(k)
			if hasattr(json_dict.__dict__[k], "__dict__"):
				check_none(json_dict.__dict__[k])
	if isinstance(json_dict, dict):
		for k in json_dict:
			if k is None:
				print(k, "is None")
			if json_dict[k] is None:
				print(k, "is None")
				json_dict.pop(k)
			if isinstance(json_dict[k], dict):
				check_none(json_dict[k])


def report_mean_pnl(config, pnls):
	mean = np.mean(pnls)
	train_val = "train" if config.is_training else "val"
	head = f"\n{train_val} fold#{config.fold_index}, type: {config.fold_type}, mean pnl: {mean}"
	print(head)



class Visualizer:
	def __init__(self, data: DataFrame):
		self.data = data


class MyScatterPoints:
	def __init__(self):
		self.indices = []
		self.items = []

	def add(self, index, item):
		self.indices.append(index)
		self.items.append(item)


class LinearSchedule(object):
	def __init__(self, schedule_timesteps, initial=1., final=0.):
		"""Linear interpolation between initial_p and final_p over
		schedule_timesteps. After this many timesteps pass final_p is
		returned.

		Parameters
		----------
		schedule_timesteps: int
			Number of timesteps for which to linearly anneal initial_p
			to final_p
		initial_p: float
			initial output value
		final_p: float
			final output value
		"""
		self.schedule_timesteps = schedule_timesteps
		self.final = final
		self.initial = initial

	def __call__(self, t):
		"""See Schedule.value"""
		fraction = min(float(t) / self.schedule_timesteps, 1.0)
		return self.initial + fraction * (self.final - self.initial)

def linear_schedule(start_sigma: float, end_sigma: float, duration: int, t: int):
	return end_sigma + (1 - min(t / duration, 1)) * (start_sigma - end_sigma)


def get_datetime_str(timestamp: int = None):
	if timestamp is None:
		timestamp = int(datetime.now().timestamp())
	return datetime.fromtimestamp(timestamp).strftime('%Y_%m%d_%H%M%S')