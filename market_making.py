import os
import joblib
from lightning import LightningModule
from pytorch_lightning.trainer import Trainer
from core.data_module import KlineDataModule
from core.predictor_config import PredictorConfig
from direct_trading import pred_cfg, fit_val_test
import torch
from core.cst import PositionType, TaskType, Optimizers
from core.dot_dict import DotDict as dd
torch.set_float32_matmul_precision('medium') 



pred_cfg.task_enum = TaskType.MarketMaking
pred_cfg.script_name = __file__
pred_cfg.fair_price_shift_spread_for_market_making = False
pred_cfg.with_directional_profit = False
# pred_cfg.with_shift_spread_loss = True
pred_cfg.limit_shift_scale = 0.003
pred_cfg.pnl_loss_with_fee_scale = 2
# pred_cfg.learning_rate = 1e-4
# pred_cfg.batch_size = 128
# pred_cfg.pred_len = 4
pred_cfg.quote_end_date = '2024.08.01'
pred_cfg.train_end_date = '2024.08.01'
pred_cfg.val_start_date = '2024.08.01'
# pred_cfg.model_name = 'tcn'
# pred_cfg.model_name = 'rnn'
# pred_cfg.interval_cfg.base = 120
# pred_cfg.adapt.in_use = True
if pred_cfg.meta_adapt.in_use:
    pred_cfg.task_enum = TaskType.MarketMakingDoubleAdapt
    # pred_cfg.optimizer_enum = Optimizers.RMSPROP
    pred_cfg.optimizer_enum = Optimizers.ADAM
    pred_cfg.sample_pair = True
    # pred_cfg.adapt.transform_x = False
    pred_cfg.meta_adapt.transform_y = False
    pred_cfg.meta_adapt.offline_lr_dict.outer = pred_cfg.meta_adapt.offline_lr_dict.inner = pred_cfg.learning_rate = 1e-4
    pred_cfg.batch_size = 42


# pred_cfg.execute_phase.train = False
# pred_cfg.resume_from_ckpt = True
if pred_cfg.resume_from_ckpt: 
    if pred_cfg.interval_cfg.base == 60:
        pred_cfg.merge_history_data = True
        pred_cfg.history_file_name = '2025_0125_012706_s20210101_crypto30_60min'        
        if pred_cfg.train_end_date == '2024.08.01':
            # pred_cfg.task_folder = '2025_0118_194001_v20250101_train-fee_scale=2.4'
            pred_cfg.task_folder = '2025_0127_173728_v20250101_train'
            pred_cfg.ckpt_file_name = 'epoch=0.ckpt'	    
    if pred_cfg.interval_cfg.base == 240:
        if pred_cfg.train_end_date == '2024.08.01':
            # pred_cfg.task_folder = '2025_0118_194001_v20250101_train-fee_scale=2.4'
            pred_cfg.task_folder = '2025_0129_190138_v20250101_train'
            pred_cfg.ckpt_file_name = 'epoch=0.ckpt'        

if __name__ == '__main__':
    # pred_cfg.with_directional_profit = False
    
    # pred_cfg.episodic_backward = True
    # pred_cfg.learning_rate = 10e-3
    # pred_cfg.interval_cfg.base = 120
    # pred_cfg.limit_margin_per_code = False
    # pred_cfg.shuffling.codewise = True
    # pred_cfg.force_train = False
    # pred_cfg.adapt_distr_shift.val = True
    # pred_cfg.pnl_decay.in_use = True
    # pred_cfg.pnl_decay.threshold = 0.00002
    # pred_cfg.feature_cfg.factor = False
    # pred_cfg.merge_history_data = True
    # pred_cfg.history_file_name = '2024_0827_062913_s20210101'
    fit_val_test(pred_cfg)