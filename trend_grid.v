//@version=5
strategy('Trend Grid v2.2', shorttitle='TGv2.2', overlay=true, close_entries_rule='ANY', precision=5, pyramiding=20, backtest_fill_limits_assumption=1, default_qty_type=strategy.fixed, slippage=1, commission_type=strategy.commission.percent, commission_value=0.05, process_orders_on_close=true, initial_capital=1000, margin_long=1, margin_short=1)

//set time
i_start_time = input.time(defval=timestamp('01 Aug 2021 00:00 +0000'), title = 'Start Time', group='Start/End Time')
i_end_time = input.time(defval=timestamp('01 May 2025 00:00 +0000'), title = 'End Time', group='Start/End Time')
time_cond = (time > i_start_time) and (time < i_end_time)

table = input.bool(true, 'Table', group='Table')
take_type = input.string('Close', title='Accumulate Position Range', options=['Open', 'Close'])
first_quantity = input.float(100, title='First Position', minval=1e-10, step=1e-10)
max_orders = input.int(20, title='Max Orders', minval=1, step=1)

var int max_order_num = 22
on_entry = input.bool(false, title='Box Entry On/Off', group='Entry Outer Indicator')
entry = input.source(close, title='Outer Ref Value', group='Entry Outer Indicator')
entry_trigger_cross = input.string('GT Ref Value', title='Cond', inline='Entry Indicator', group='Entry Outer Indicator', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value'])
entry_trigger = input.float(100, title='Ref Value', inline='Entry Indicator', group='Entry Outer Indicator')
price_reversion_enabled = input.bool(false, title='Price Reversion Box', group='Entry Outer Indicator')

on_exit1 = input.bool(false, title='Box Exit On/Off 1', group='Exit Outer Indicator 1')
exit1 = input.source(close, title='Outer Ref Value 1', group='Exit Outer Indicator 1')
exit_trigger_cross1 = input.string('GT Ref Value', title='Cond', inline='Exit Indicator 1', group='Exit Outer Indicator 1', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value'])
exit_trigger1 = input.float(100, title='Ref Value', inline='Exit Indicator 1', group='Exit Outer Indicator 1')

on_exit2 = input.bool(false, title='Box Exit On/Off 2', group='Exit Outer Indicator 2')
exit2 = input.source(close, title='Outer Ref Value 2', group='Exit Outer Indicator 2')
exit_trigger_cross2 = input.string('GT Ref Value', title='Cond', inline='Exit Indicator 2', group='Exit Outer Indicator 2', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value'])
exit_trigger2 = input.float(100, title='Ref Value', inline='Exit Indicator 2', group='Exit Outer Indicator 2')

long_trend = input.bool(false, title='Set Long Long Trend', group='Long Trend')
is_trend_on = input.bool(false, title='Close Position At Trend End', group='Long Trend')
atr_period = input.int(10, 'ATR Period', group='Long Trend')
factor = input.float(30, 'Factor', step=0.1, group='Long Trend')

avoid_drop = input.bool(false, title='Avoid Drop On/Off', group='Avoid Drop')
candles_to_check = input.int(2, title='Candles to Check', group='Avoid Drop')
drop_threshold = input.float(8, title='Drop Threshold Percentage', group='Avoid Drop')

stop_loss_on = input.bool(false, title='Stop Loss On/Off', group='Stop Loss')
stop_loss_range = input.float(5, title='Stop Loss % After Final Order', minval=0.0001, step=0.001, group='Stop Loss') / 100

trailing_profit = 0.1 / 100
trailing_profit_option = input.string('Fixed', title='Trailing Profit Option', options=['Fixed', 'Trend'], group='Take Profit')
take_profit_range = input.float(.9, title='Profit Range %', minval=0.0001, step=0.001, group='Take Profit') / 100

all_trailing_profit_option = input.string('Off', title='Trailing Profit Option', options=['Off', 'All Fixed', 'All Trend'], group='Take Profit')
trailing_sensitivity = input.string('15m-1h', title='Trailing Sensitivity', options=['1h-1d', '15m-1h', '3m-15m', '1m'], group='Take Profit')


int[] period = array.new_int(max_order_num) //indicator period
bool[] indicator_on = array.new_bool(max_order_num) //indicator on/off
float[] avg_down = array.new_float(max_order_num) //average down
float[] accumulate_scale = array.new_float(max_order_num) //accumulate scale
string[] strategy_option = array.new_string(max_order_num) //strategy option
bool[] trigger_on = array.new_bool(max_order_num) //outer indicator trigger on/off
float[] trigger_source = array.new_float(max_order_num) //outer indicator source
float[] long_trigger_value = array.new_float(max_order_num) //outer indicator long trigger ref value
string[] trigger_cross = array.new_string(max_order_num) //outer indicator trigger default gold cross
int[] macd_fast = array.new_int(max_order_num)
int[] macd_slow = array.new_int(max_order_num)
int[] macd_signal = array.new_int(max_order_num)
float[] macd_hist = array.new_float(max_order_num)

//Strategy Indicator
array.set(indicator_on, 1, input.bool(true, 'Indicator On/Off', group='Buy # 1'))
array.set(strategy_option, 1, input.string('MACD Dead Cross', title='First Order Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], group='Buy # 1'))
array.set(macd_fast, 1, input.int(12, title='MACD Fast', inline='inline 1', group='Buy # 1'))
array.set(macd_slow, 1, input.int(26, title='MACD Slow', inline='inline 1', group='Buy # 1'))
array.set(macd_signal, 1, input.int(9, title='MACD Signal', inline='inline 1', group='Buy # 1'))
array.set(trigger_on, 1, input.bool(false, title='Outer Indicator On/Off', group='Buy # 1'))
array.set(trigger_source, 1, input.source(close, title='Outer Ref Value', inline='inline 2', group='Buy # 1'))
array.set(trigger_cross, 1, input.string('GT Ref Value', title='Cond', inline='inline 2', group='Buy # 1', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 1, input.float(100, title='Ref Value', inline='inline 2', group='Buy # 1'))

//Buy # 2
array.set(avg_down, 2, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 2'))
array.set(accumulate_scale, 2, input.float(1, inline='inline 1', group='Buy # 2'))
array.set(indicator_on, 2, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 2'))
array.set(strategy_option, 2, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 2'))
array.set(macd_fast, 2, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 2'))
array.set(macd_slow, 2, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 2'))
array.set(macd_signal, 2, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 2'))
array.set(trigger_on, 2, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 2'))
array.set(trigger_source, 2, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 2'))
array.set(trigger_cross, 2, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 2', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 2, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 2'))

//Buy # 3
array.set(avg_down, 3, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 3'))
array.set(accumulate_scale, 3, input.float(1, inline='inline 1', group='Buy # 3'))
array.set(indicator_on, 3, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 3'))
array.set(strategy_option, 3, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 3'))
array.set(macd_fast, 3, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 3'))
array.set(macd_slow, 3, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 3'))
array.set(macd_signal, 3, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 3'))
array.set(trigger_on, 3, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 3'))
array.set(trigger_source, 3, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 3'))
array.set(trigger_cross, 3, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 3', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 3, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 3'))

//Buy # 4
array.set(avg_down, 4, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 4'))
array.set(accumulate_scale, 4, input.float(1, inline='inline 1', group='Buy # 4'))
array.set(indicator_on, 4, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 4'))
array.set(strategy_option, 4, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 4'))
array.set(macd_fast, 4, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 4'))
array.set(macd_slow, 4, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 4'))
array.set(macd_signal, 4, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 4'))
array.set(trigger_on, 4, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 4'))
array.set(trigger_source, 4, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 4'))
array.set(trigger_cross, 4, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 4', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 4, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 4'))

//Buy # 5
array.set(avg_down, 5, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 5'))
array.set(accumulate_scale, 5, input.float(1, inline='inline 1', group='Buy # 5'))
array.set(indicator_on, 5, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 5'))
array.set(strategy_option, 5, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 5'))
array.set(macd_fast, 5, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 5'))
array.set(macd_slow, 5, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 5'))
array.set(macd_signal, 5, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 5'))
array.set(trigger_on, 5, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 5'))
array.set(trigger_source, 5, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 5'))
array.set(trigger_cross, 5, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 5', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 5, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 5'))

//Buy # 6
array.set(avg_down, 6, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 6'))
array.set(accumulate_scale, 6, input.float(1, inline='inline 1', group='Buy # 6'))
array.set(indicator_on, 6, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 6'))
array.set(strategy_option, 6, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 6'))
array.set(macd_fast, 6, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 6'))
array.set(macd_slow, 6, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 6'))
array.set(macd_signal, 6, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 6'))
array.set(trigger_on, 6, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 6'))
array.set(trigger_source, 6, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 6'))
array.set(trigger_cross, 6, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 6', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 6, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 6'))

//Buy # 7
array.set(avg_down, 7, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 7'))
array.set(accumulate_scale, 7, input.float(1, inline='inline 1', group='Buy # 7'))
array.set(indicator_on, 7, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 7'))
array.set(strategy_option, 7, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 7'))
array.set(macd_fast, 7, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 7'))
array.set(macd_slow, 7, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 7'))
array.set(macd_signal, 7, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 7'))
array.set(trigger_on, 7, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 7'))
array.set(trigger_source, 7, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 7'))
array.set(trigger_cross, 7, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 7', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 7, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 7'))

//Buy # 8
array.set(avg_down, 8, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 8'))
array.set(accumulate_scale, 8, input.float(1, inline='inline 1', group='Buy # 8'))
array.set(indicator_on, 8, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 8'))
array.set(strategy_option, 8, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 8'))
array.set(macd_fast, 8, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 8'))
array.set(macd_slow, 8, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 8'))
array.set(macd_signal, 8, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 8'))
array.set(trigger_on, 8, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 8'))
array.set(trigger_source, 8, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 8'))
array.set(trigger_cross, 8, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 8', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 8, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 8'))

//Buy # 9
array.set(avg_down, 9, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 9'))
array.set(accumulate_scale, 9, input.float(1, inline='inline 1', group='Buy # 9'))
array.set(indicator_on, 9, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 9'))
array.set(strategy_option, 9, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 9'))
array.set(macd_fast, 9, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 9'))
array.set(macd_slow, 9, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 9'))
array.set(macd_signal, 9, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 9'))
array.set(trigger_on, 9, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 9'))
array.set(trigger_source, 9, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 9'))
array.set(trigger_cross, 9, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 9', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 9, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 9'))

//Buy # 10
array.set(avg_down, 10, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 10'))
array.set(accumulate_scale, 10, input.float(1, inline='inline 1', group='Buy # 10'))
array.set(indicator_on, 10, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 10'))
array.set(strategy_option, 10, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 10'))
array.set(macd_fast, 10, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 10'))
array.set(macd_slow, 10, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 10'))
array.set(macd_signal, 10, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 10'))
array.set(trigger_on, 10, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 10'))
array.set(trigger_source, 10, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 10'))
array.set(trigger_cross, 10, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 10', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 10, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 10'))

//Buy # 11
array.set(avg_down, 11, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 11'))
array.set(accumulate_scale, 11, input.float(1, inline='inline 1', group='Buy # 11'))
array.set(indicator_on, 11, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 11'))
array.set(strategy_option, 11, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 11'))
array.set(macd_fast, 11, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 11'))
array.set(macd_slow, 11, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 11'))
array.set(macd_signal, 11, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 11'))
array.set(trigger_on, 11, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 11'))
array.set(trigger_source, 11, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 11'))
array.set(trigger_cross, 11, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 11', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 11, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 11'))

//Buy # 12
array.set(avg_down, 12, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 12'))
array.set(accumulate_scale, 12, input.float(1, inline='inline 1', group='Buy # 12'))
array.set(indicator_on, 12, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 12'))
array.set(strategy_option, 12, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 12'))
array.set(macd_fast, 12, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 12'))
array.set(macd_slow, 12, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 12'))
array.set(macd_signal, 12, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 12'))
array.set(trigger_on, 12, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 12'))
array.set(trigger_source, 12, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 12'))
array.set(trigger_cross, 12, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 12', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 12, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 12'))

//Buy # 13
array.set(avg_down, 13, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 13'))
array.set(accumulate_scale, 13, input.float(1, inline='inline 1', group='Buy # 13'))
array.set(indicator_on, 13, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 13'))
array.set(strategy_option, 13, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 13'))
array.set(macd_fast, 13, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 13'))
array.set(macd_slow, 13, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 13'))
array.set(macd_signal, 13, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 13'))
array.set(trigger_on, 13, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 13'))
array.set(trigger_source, 13, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 13'))
array.set(trigger_cross, 13, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 13', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 13, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 13'))
//Buy # 14
array.set(avg_down, 14, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 14'))
array.set(accumulate_scale, 14, input.float(1, inline='inline 1', group='Buy # 14'))
array.set(indicator_on, 14, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 14'))
array.set(strategy_option, 14, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 14'))
array.set(macd_fast, 14, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 14'))
array.set(macd_slow, 14, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 14'))
array.set(macd_signal, 14, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 14'))
array.set(trigger_on, 14, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 14'))
array.set(trigger_source, 14, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 14'))
array.set(trigger_cross, 14, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 14', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 14, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 14'))
//Buy # 15
array.set(avg_down, 15, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 15'))
array.set(accumulate_scale, 15, input.float(1, inline='inline 1', group='Buy # 15'))
array.set(indicator_on, 15, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 15'))
array.set(strategy_option, 15, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 15'))
array.set(macd_fast, 15, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 15'))
array.set(macd_slow, 15, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 15'))
array.set(macd_signal, 15, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 15'))
array.set(trigger_on, 15, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 15'))
array.set(trigger_source, 15, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 15'))
array.set(trigger_cross, 15, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 15', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 15, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 15'))
//Buy # 16
array.set(avg_down, 16, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 16'))
array.set(accumulate_scale, 16, input.float(1, inline='inline 1', group='Buy # 16'))
array.set(indicator_on, 16, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 16'))
array.set(strategy_option, 16, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 16'))
array.set(macd_fast, 16, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 16'))
array.set(macd_slow, 16, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 16'))
array.set(macd_signal, 16, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 16'))
array.set(trigger_on, 16, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 16'))
array.set(trigger_source, 16, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 16'))
array.set(trigger_cross, 16, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 16', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 16, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 16'))

//Buy # 17
array.set(avg_down, 17, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 17'))
array.set(accumulate_scale, 17, input.float(1, inline='inline 1', group='Buy # 17'))
array.set(indicator_on, 17, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 17'))
array.set(strategy_option, 17, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 17'))
array.set(macd_fast, 17, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 17'))
array.set(macd_slow, 17, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 17'))
array.set(macd_signal, 17, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 17'))
array.set(trigger_on, 17, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 17'))
array.set(trigger_source, 17, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 17'))
array.set(trigger_cross, 17, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 17', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 17, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 17'))
//Buy # 18
array.set(avg_down, 18, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 18'))
array.set(accumulate_scale, 18, input.float(1, inline='inline 1', group='Buy # 18'))
array.set(indicator_on, 18, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 18'))
array.set(strategy_option, 18, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 18'))
array.set(macd_fast, 18, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 18'))
array.set(macd_slow, 18, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 18'))
array.set(macd_signal, 18, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 18'))
array.set(trigger_on, 18, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 18'))
array.set(trigger_source, 18, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 18'))
array.set(trigger_cross, 18, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 18', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 18, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 18'))
//Buy # 19
array.set(avg_down, 19, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 19'))
array.set(accumulate_scale, 19, input.float(1, inline='inline 1', group='Buy # 19'))
array.set(indicator_on, 19, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 19'))
array.set(strategy_option, 19, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 19'))
array.set(macd_fast, 19, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 19'))
array.set(macd_slow, 19, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 19'))
array.set(macd_signal, 19, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 19'))
array.set(trigger_on, 19, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 19'))
array.set(trigger_source, 19, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 19'))
array.set(trigger_cross, 19, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 19', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 19, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 19'))

// Buy # 20
array.set(avg_down, 20, input.float(0.9, 'Down %', inline='inline 1', group='Buy # 20'))
array.set(accumulate_scale, 20, input.float(1, inline='inline 1', group='Buy # 20'))
array.set(indicator_on, 20, input.bool(true, 'Indicator On/Off', inline='inline 0', group='Buy # 20'))
array.set(strategy_option, 20, input.string('MACD Gold Cross', title='Inner Indicator', options=['MACD Dead Cross', 'MACD Gold Cross'], inline='inline 0', group='Buy # 20'))
array.set(macd_fast, 20, input.int(12, title='MACD Fast', inline='inline 2', group='Buy # 20'))
array.set(macd_slow, 20, input.int(26, title='MACD Slow', inline='inline 2', group='Buy # 20'))
array.set(macd_signal, 20, input.int(9, title='MACD Signal', inline='inline 2', group='Buy # 20'))
array.set(trigger_on, 20, input.bool(false, title='Outer Indicator On/Off',  group='Buy # 20'))
array.set(trigger_source, 20, input.source(close, title='Outer Ref Value', inline='inline 3', group='Buy # 20'))
array.set(trigger_cross, 20, input.string('GT Ref Value', title='Cond', inline='inline 3', group='Buy # 20', options=['GT Ref Value', 'LT Ref Value', 'CO Ref Value', 'CU Ref Value']))
array.set(long_trigger_value, 20, input.float(100, title='Ref Value', inline='inline 3', group='Buy # 20'))


var is_position_open = false

//Buy Signals
bool[] buy = array.new_bool(max_order_num, false)
array.fill(buy, false)

int m_fast = 1
int m_slow = 1
int m_signal = 1

for i = 1 to max_orders-1
    m_fast := array.get(macd_fast, i+1)
    m_slow := array.get(macd_slow, i+1)
    m_signal := array.get(macd_signal, i+1)

fast_ma = ta.sma(close, m_fast)
slow_ma = ta.sma(close, m_slow)
macd_line = fast_ma - slow_ma
signal_line = ta.sma(macd_line, m_signal)
gold_cross = ta.crossover(macd_line, signal_line)
dead_cross = ta.crossunder(macd_line, signal_line)

for i = 1 to max_orders
    if array.get(strategy_option, i) == 'MACD Gold Cross' and gold_cross
        array.set(buy, i, true)
    if array.get(strategy_option, i) == 'MACD Dead Cross' and dead_cross
        array.set(buy, i, true)

for i = 1 to max_orders
    if array.get(trigger_on, i)
		array.set(buy, i, true)

var float[] quantity_arr = array.new_float(max_order_num)
for i = 2 to max_orders
    array.set(quantity_arr, i, array.get(accumulate_scale, i) * array.get(quantity_arr, i-1))

var entry_buy = false
if on_entry and is_position_open
    if entry_trigger_cross == 'GT Ref Value'
		entry_buy := entry > entry_trigger ? true : false
	else if entry_trigger_cross == 'LT Ref Value'
		entry_buy := entry < entry_trigger? true : false
	else if entry_trigger_cross == 'CO Ref Value'
		entry_buy := ta.crossover(entry, entry_trigger) ? true : false
	else if entry_trigger_cross == 'CU Ref Value'
		entry_buy := ta.crossunder(entry, entry_trigger)? true : false
else
	entry_buy := true

bool exit_buy1 = false
bool exit_buy2 = false
if on_exit1
    if exit_trigger_cross1 == 'GT Ref Value'
		exit_buy1 := exit1 > exit_trigger1? true : false
	else if exit_trigger_cross1 == 'LT Ref Value'
		exit_buy1 := exit1 < exit_trigger1? true : false
	else if exit_trigger_cross1 == 'CO Ref Value'
		exit_buy1 := ta.crossover(exit1, exit_trigger1)? true : false
	else if exit_trigger_cross1 == 'CU Ref Value'
		exit_buy1 := ta.crossunder(exit1, exit_trigger1)? true : false

if on_exit2
    if exit_trigger_cross2 == 'GT Ref Value'
		exit_buy2 := exit2 > exit_trigger2? true : false
	else if exit_trigger_cross2 == 'LT Ref Value'
		exit_buy2 := exit2 < exit_trigger2? true : false
	else if exit_trigger_cross2 == 'CO Ref Value'
		exit_buy2 := ta.crossover(exit2, exit_trigger2)? true : false
	else if exit_trigger_cross2 =='CU Ref Value'
		exit_buy2 := ta.crossunder(exit2, exit_trigger2)? true : false

bool[] trigger_buy = array.new_bool(max_order_num)
array.fill(trigger_buy, false)


for i = 0 to max_orders
	if array.get(trigger_on, i)
		if array.get(trigger_cross, i) == 'GT Ref Value'
			array.set(trigger_buy, i, array.get(trigger_source, i) > array.get(long_trigger_value, i) ? true : false)
		else if array.get(trigger_cross, i) == 'LT Ref Value'
			array.set(trigger_buy, i, array.get(trigger_source, i) < array.get(long_trigger_value, i)? true : false)
		else if array.get(trigger_cross, i) == 'CO Ref Value'
			array.set(trigger_buy, i, ta.crossover(array.get(trigger_source, i), array.get(long_trigger_value, i)) ? true : false)
		else if array.get(trigger_cross, i) == 'CU Ref Value'
			array.set(trigger_buy, i, ta.crossunder(array.get(trigger_source, i), array.get(long_trigger_value, i)) ? true : false)
	else
		array.set(trigger_buy, i, true)

//open position conditions
[supertrend, direction] = ta.supertrend(factor, atr_period)

body_middle = plot((open + close) / 2, display=display.none)
up_trend = plot(direction < 0 and is_trend_on ? supertrend : na, 'Up Trend', color=color.green, style=plot.style_linebr)
down_trend = plot(direction > 0 and is_trend_on ? supertrend : na, 'Down Trend', color=color.red, style=plot.style_linebr)

//Buy Signals
buy_trend = true
if is_trend_on and direction > 0
    buy_trend := false


take_type_open = take_type == 'Open' ? open : close

var float[] buy_profit = array.new_float(max_order_num, na)
var float[] trailing_profit_prices = array.new_float(max_order_num, na)
var bool[] position = array.new_bool(max_order_num, true)
int next_open_trades = strategy.opentrades + 1
int now_open_trades = strategy.opentrades

//close position if short trend
if is_trend_on and long_trend and direction > 0
	strategy.close_all('trend is over')
	array.set(position, 1, true)
	entry_buy := false
	is_position_open := true

//open first position
if next_open_trades == 1 and array.get(buy, 1) and time_cond and array.get(trigger_buy, 1) and buy_trend and array.get(position, 1) and entry_buy
	strategy.entry('Buy #1', strategy.long, qty=array.get(quantity_arr, 1), comment='buy #1 $' + str.tostring(array.get(quantity_arr, 1) * close))
	array.set(buy_profit, 1, close)
	array.set(position, 1, false)
	is_position_open := false

for i = 2 to max_orders
    if next_open_trades == i and max_orders >= i and ((array.get(buy_profit, i-1) - take_type_open) / array.get(buy_profit, i-1)) * 100 > array.get(avg_down, i) and array.get(buy, i) and array.get(trigger_buy, i) and buy_trend
		strategy.entry('Buy #' + str.tostring(i), strategy.long, qty=array.get(quantity_arr, i), comment='buy #' + str.tostring(i) + ' $' + str.tostring(array.get(quantity_arr, i) * close))
		array.set(buy_profit, i, close)
		array.set(position, i, false)
		if not price_reversion_enabled
			is_position_open := false

is_sharp_drop() => 
	price_drop_percentage = (close[0] - close[candles_to_check]) / close[candles_to_check] * 100
	price_drop_percentage < -drop_threshold

should_exit = is_sharp_drop()

if should_exit and strategy.opentrades >= max_orders and avoid_drop
	strategy.close_all('avoid sharp drop')
	array.set(position, 1, true)
	entry_buy := false

stop_loss_price = 0.0
if stop_loss_on and strategy.opentrades == max_orders
	stop_loss_price := strategy.opentrades.entry_price(max_orders - 1) * (1 - stop_loss_range / 100)
	if close < stop_loss_price
		strategy.close_all('stop loss')
		array.set(position, 1, true)

get_senstivity(level) =>
	float sensitivity = na
	if level == '1h-1d'
		sensitivity := 0.985
	else if level == '15m-1h'
		sensitivity := 0.99
	else if level == '3m-15m'
		sensitivity := 0.995
	else if level == '1m'
		sensitivity := 0.998
	sensitivity

take_profit_price = strategy.position_avg_price * (1 + take_profit_range)

plot(take_profit_price, title='Take Profit Price', color=color.new(color.green, 50), style=plot.style_linebr)
array.set(trailing_profit_prices, now_open_trades, array.get(buy_profit, now_open_trades) * (1 + take_profit_range))
plot(strategy.opentrades != 0 ? array.get(trailing_profit_prices, now_open_trades) : na, title='Trailing Profit Price', color=color.new(color.yellow, 50), style=plot.style_linebr)

pivot_sizing = 20
pivot_lookup = 1
pivot_high = ta.pivothigh(high, pivot_lookup, pivot_lookup)
top = ta.valuewhen(pivot_high, high[pivot_lookup], 0)
float highest = ta.highest(top, pivot_sizing)
move_high1 = ta.crossunder(close, highest * get_senstivity(trailing_sensitivity))
reversal1 = ta.crossunder(close, array.get(trailing_profit_prices, now_open_trades))
move_high2 = ta.crossunder(close, highest * get_senstivity(trailing_sensitivity))
reversal2 = ta.crossunder(close, take_profit_price)

if exit_buy1 or exit_buy2
	strategy.close_all('Exit Box')
	array.set(position, 1, true)
	entry_buy := false
	is_position_open := true
else
	if trailing_profit_option == 'Fixed'
		if close > array.get(trailing_profit_prices, now_open_trades)
			strategy.close('B' + str.tostring(strategy.opentrades), comment=str.tostring('B' + str.tostring(strategy.opentrades)) + 'Fixed Trailing Profit #' + str.tostring(now_open_trades) + ' Order')
			array.set(position, now_open_trades, true)
	else if trailing_profit_option == 'Trend'
		if all_trailing_profit_option == 'All Trend'
			if close > take_profit_price and move_high2
				strategy.close_all('All Trend Trailing Profit')
				array.set(position, 1, true)
			else if open > take_profit_price and reversal2
				strategy.close_all('All Protective Trailing Profit')
				array.set(position, 1, true)
			else if close > array.get(trailing_profit_prices, now_open_trades) and move_high1
				strategy.close('B' + str.tostring(strategy.opentrades), comment=str.tostring('B' + str.tostring(strategy.opentrades)) +'Trend Trailing Profit #' + str.tostring(now_open_trades) + ' Order')
				array.set(position, 1, true)
			else if open > array.get(trailing_profit_prices, now_open_trades) and reversal1
				strategy.close('B' + str.tostring(strategy.opentrades), comment=str.tostring('B' + str.tostring(strategy.opentrades)) +'Protective Trailing Profit #' + str.tostring(now_open_trades) + ' Order')
		else if all_trailing_profit_option == 'All Fixed'
			if close > take_profit_price
				strategy.close_all('All Fixed Trailing Profit')
				array.set(position, 1, true)
			if close > array.get(trailing_profit_prices, now_open_trades) and move_high1
				strategy.close('B' + str.tostring(strategy.opentrades), comment=str.tostring('B' + str.tostring(strategy.opentrades)) +'Trend Trailing Profit #' + str.tostring(now_open_trades) + ' Order')
				array.set(position, 1, true)
			else if open > array.get(trailing_profit_prices, now_open_trades) and reversal1
				strategy.close('B' + str.tostring(strategy.opentrades), comment=str.tostring('B' + str.tostring(strategy.opentrades)) +'Protective Trailing Profit #' + str.tostring(now_open_trades) + ' Order')
		else if all_trailing_profit_option == 'Off'
			if close > array.get(trailing_profit_prices, now_open_trades) and move_high1
				strategy.close('B' + str.tostring(strategy.opentrades), comment=str.tostring('B' + str.tostring(strategy.opentrades)) +'Trend Trailing Profit #' + str.tostring(now_open_trades) + ' Order')
				array.set(position, 1, true)
			else if open > array.get(trailing_profit_prices, now_open_trades) and reversal1
				strategy.close('B' + str.tostring(strategy.opentrades), comment=str.tostring('B' + str.tostring(strategy.opentrades)) +'Protective Trailing Profit #' + str.tostring(now_open_trades) + ' Order')
				array.set(position, 1, true)

var float all_qty_count = 0
float[] qty_count = array.new_float(max_order_num)
float[] qty_prev = array.new_float(max_order_num)
array.set(qty_count, 1, first_quantity)
array.set(qty_prev, 1, first_quantity)
for i = 2 to max_orders
    array.set(qty_prev, i, array.get(qty_prev, i-1) * array.get(quantity_arr, i))
	array.set(qty_count, i, array.get(qty_prev, i) * array.get(quantity_arr, i) + array.get(qty_count, i-1))

qty_count_str = array.get(qty_count, max_orders)

if table
	var table atr_display = table.new(position.bottom_right, 2, 6, bgcolor=color.white)

	table.cell(atr_display, 0, 0, 'current setup money: ' + str.tostring(qty_count_str) + ' (1x leverage)')
	