# 双向做市商策略 (Two-Way Market Maker Strategy)

这个项目是将TradingView Pine Script策略`two_way_market_maker_limit.pine`转换为Python的向量化实现。

## 策略概述

双向做市商策略是一种在价格波动中通过限价单进行交易的策略，同时管理多头和空头仓位。策略的核心思想是在价格下跌时买入，在价格上涨时卖出，并在价格反转时获利。

### 主要特点

1. **双向交易**：可以同时管理多头和空头仓位，也可以设置为只做多或只做空
2. **基于限价单**：使用限价单进行入场和出场，减少滑点
3. **动态入场水平**：可以基于ATR或固定百分比设置入场价格
4. **加仓机制**：支持在价格继续朝不利方向移动时进行加仓
5. **止盈管理**：为整个仓位设置止盈水平，而不是为每个入场单独设置
6. **库存衰减**：随着加仓次数增加，调整止盈水平
7. **冷却期**：入场后设置冷却期，避免过度交易

## 实现细节

### 核心组件

1. **入场逻辑**：
   - 当价格达到入场水平时，使用限价单入场
   - 入场水平可以基于ATR或固定百分比计算
   - 支持多头和空头入场

2. **加仓逻辑**：
   - 当价格继续朝不利方向移动时，放置加仓限价单
   - 加仓大小基于系数计算，可以设置为递增
   - 设置最大加仓次数和冷却期

3. **出场逻辑**：
   - 使用止盈限价单平仓
   - 止盈水平基于平均入场价格和库存衰减计算
   - 平仓后重置入场水平

4. **仓位管理**：
   - 跟踪平均入场价格和仓位大小
   - 在首次入场时取消反向订单
   - 在平仓后重置所有变量

### 向量化实现

Python实现使用了向量化操作进行数据处理和指标计算，但由于策略的状态管理特性，主要的交易逻辑仍然需要逐条处理。主要的向量化部分包括：

- 使用Polars/Pandas进行数据处理
- 使用TA-Lib计算ATR
- 使用NumPy进行数学计算
- 使用向量化操作计算性能指标

## 使用方法

### 安装依赖

```bash
pip install pandas numpy polars matplotlib talib yfinance seaborn
```

### 基本用法

```python
from two_way_market_maker_limit import TwoWayMarketMakerLimit
import pandas as pd

# 准备数据
data = pd.DataFrame({
    'datetime': [...],  # 日期时间
    'open': [...],      # 开盘价
    'high': [...],      # 最高价
    'low': [...],       # 最低价
    'close': [...],     # 收盘价
    'volume': [...]     # 成交量
})

# 初始化策略
strategy = TwoWayMarketMakerLimit(
    trade_direction="Both",     # 交易方向: "Long Only", "Short Only", "Both"
    use_atr=True,               # 是否使用ATR计算入场水平
    atr_period=14,              # ATR周期
    atr_multiplier=1.0,         # ATR乘数
    fixed_percent=0.01,         # 固定百分比(如果不使用ATR)
    cooldown_period=6,          # 冷却期(K线数)
    initial_position_pct=0.01,  # 初始仓位大小(占总资金百分比)
    scale_in_coef=1.5,          # 加仓系数
    max_scale_in=5,             # 最大加仓次数
    take_profit_pct=0.02,       # 止盈百分比
    inventory_decay=0.95,       # 库存衰减系数
    commission_pct=0.001        # 手续费百分比
)

# 运行回测
results = strategy.run_backtest(data)

# 分析性能
metrics = strategy.analyze_performance(results)
strategy.print_performance_summary(metrics)

# 可视化结果
strategy.plot_results(results)
```

### 示例脚本

项目包含一个示例脚本`example_two_way_market_maker.py`，展示了如何使用真实数据、合成数据和多个交易对进行回测。

运行示例：
```bash
python example_two_way_market_maker.py
```

## 参数调优

策略性能很大程度上取决于参数设置。以下是一些关键参数的调优建议：

1. **入场参数**：
   - `use_atr`和`atr_multiplier`：在波动性较高的市场中，使用ATR可能比固定百分比更有效
   - `fixed_percent`：在波动性较低的市场中，使用较小的固定百分比

2. **仓位管理**：
   - `initial_position_pct`：控制风险敞口，通常建议使用较小的值(1-5%)
   - `scale_in_coef`：控制加仓大小，较大的值会导致加仓规模快速增加
   - `max_scale_in`：限制最大加仓次数，避免过度加仓

3. **出场参数**：
   - `take_profit_pct`：较小的值会导致更频繁的交易，较大的值会增加单笔交易的潜在收益
   - `inventory_decay`：控制加仓后止盈水平的调整，较小的值会导致止盈水平更激进

4. **其他参数**：
   - `cooldown_period`：控制入场后的冷却期，避免过度交易
   - `commission_pct`：设置为与您的交易所一致的手续费率

## 性能分析

策略性能分析包括以下指标：

- 总收益率
- 年化收益率
- 年化波动率
- 夏普比率
- 最大回撤
- 交易次数(多头/空头)
- 胜率

## 注意事项

1. 这个策略主要适用于波动性较高但有一定范围的市场
2. 在强趋势市场中，策略可能会因为持续加仓而面临较大风险
3. 参数需要根据不同的交易品种和时间框架进行调整
4. 实盘交易时，需要考虑流动性和滑点的影响

## 未来改进

1. 添加止损机制，控制最大亏损
2. 实现动态参数调整，根据市场条件自适应
3. 添加更多的入场和出场过滤条件
4. 优化加仓策略，考虑资金管理和风险控制
5. 实现实时交易接口，连接交易所API
