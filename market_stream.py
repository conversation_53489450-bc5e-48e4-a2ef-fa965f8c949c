# -*- coding:utf-8 -*-

"""
Market Server.

Market Server will get market data from Exchange via Websocket or REST as soon as possible, then packet market data into
MarketEvent and publish into EventCenter.

Author: CyberQuant
Date:   2018/05/04
Email:  <EMAIL>
"""

import time
from aed_quant import const
from aed_quant.quant import quant
from aed_quant.config import config
from models.lightning_pfl import LightningPFL
from direct_trading import pred_cfg
from core.predictor_config import PredictorConfig


def initialize():
	"""Initialize Server."""

	for platform in config.market_stream:
		if platform == const.OKEX or platform == const.OKEX_MARGIN:
			from aed_quant.market_stream.okex import OKEx as MarketService
		elif platform == const.OKEX_FUTURE or platform == const.OKEX_SWAP:
			from aed_quant.market_stream.okex_ftu import OKExFuture as MarketService
		elif platform == const.BINANCE:
			from aed_quant.market_stream.binance import Binance as MarketService
		elif platform == const.BINANCE_FUTURE:
			from aed_quant.market_stream.binance_future import BinanceFuture as MarketService
		elif platform == const.DERIBIT:
			from aed_quant.market_stream.deribit import Deribit as MarketService
		elif platform == const.BITMEX:
			from aed_quant.market_stream.bitmex import Bitmex as MarketService
		elif platform == const.HUOBI:
			from aed_quant.market_stream.huobi import Huobi as MarketService
		elif platform == const.COINSUPER:
			from aed_quant.market_stream.coinsuper import CoinsuperMarket as MarketService
		elif platform == const.COINSUPER_PRE:
			from aed_quant.market_stream.coinsuper_pre import CoinsuperPreMarket as MarketService
		elif platform == const.KRAKEN:
			from aed_quant.market_stream.kraken import KrakenMarket as MarketService
		elif platform == const.GATE:
			from aed_quant.market_stream.gate import GateMarket as MarketService
		elif platform == const.GEMINI:
			from aed_quant.market_stream.gemini import GeminiMarket as MarketService
		elif platform == const.COINBASE_PRO:
			from aed_quant.market_stream.coinbase import CoinbaseMarket as MarketService
		elif platform == const.KUCOIN:
			from aed_quant.market_stream.kucoin import KucoinMarket as MarketService
		elif platform == const.HUOBI_FUTURE:
			from aed_quant.market_stream.huobi_future import HuobiFutureMarket as MarketService
		else:
			from aed_quant.utils import logger
			logger.error("platform error! platform:", platform)
			continue
		cc = config.market_stream[platform]
		cc["platform"] = platform
		MarketService(**cc)


def main(pred_cfg: PredictorConfig):
	ckpt_folder = pred_cfg.get_ckpt_folder(make_new_task_folder=False)
	cfg = pred_cfg.load_cfg_from_ckpt(ckpt_folder=ckpt_folder, ckpt_file_name=pred_cfg.ckpt_file_name)
	if pred_cfg.load_all_codes:
		cfg.code_list = cfg.get_all_code_list()
	else:
		# print(cfg.code_list)
		cfg.code_list.extend(cfg.alternate_code_list)
		# cfg.replace_symbol_in_code_list('MATICUSDT', 'POLUSDT')
	print(f'{len(cfg.code_list)} = ')
	# print(cfg.code_list)
	if (sim_base_interval := pred_cfg.sim_base_interval) is not None:
		cfg.sim_base_interval = cfg.interval_cfg.base = sim_base_interval
	market_cfg = cfg.market_stream
	# 周期性重启
	restart_interval = pred_cfg.stream_restart_interval
	quant.initialize(market_cfg)
	initialize()
	quant.start_with_restart(restart_interval)
	# quant.start()


if __name__ == "__main__":
	# pred_cfg.stream_restart_interval = 0.01
	# pred_cfg.log_console.stream = False
	# pred_cfg.sim_base_interval = 3
	main(pred_cfg)
