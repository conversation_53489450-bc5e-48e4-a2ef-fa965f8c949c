from calendar import c
import os

from matplotlib import style
from sklearn.metrics import classification_report, confusion_matrix
from torch import long
from core.cst import TaskType

import pandas as pd
import polars as pl
import numpy as np
import matplotlib.pyplot as plt
from core.dot_dict import DotDict as dd
from models.lightning_drt import save_summary_fig_pqt
from core.predictor_config import PredictorConfig

pl.Config.set_tbl_cols(-1)
pd.set_option('display.max_rows', None)
pd.set_option('display.max_columns', None)


def report_top_epoch(pred_cfg: PredictorConfig, task_folder, is_rolling_epoches, top_num_str, val_start_list, end_date, epoch_index, epoch_dict, interval_dict, interval_index: int = 0):
    epoch_str = f'epoch{epoch_index}'
    if is_rolling_epoches:
        
        rolling_str = 'rolling_'
        val_start_str = val_start_list[0]
        epoch_list = [interval_dict[val_start][top_num_str][epoch_str][0] for val_start in val_start_list]
        # print(f'{len(epoch_list) = }')
        top_epoch_df: pl.DataFrame = pl.concat(epoch_list)
        top_epoch_df.with_columns(index=np.arange(len(top_epoch_df)))
        # epoch_df = epoch_df.set_index(['index', 'code'])
        # print(f'{epoch_df.head(30) = }')
        # print(epoch_df.head(30))
    else:      
        rolling_str = ''  
        val_start_str = val_start_list[interval_index]
        top_epoch_df = epoch_dict[val_start_str][top_num_str][epoch_str]

    save_folder_str = f'{rolling_str}report_{val_start_str}v{end_date}'
    save_summary_fig_pqt(pred_cfg, 'val', task_folder, epoch_str, save_folder_str, top_num_str, top_epoch_df)


def get_epoch_interval_dict(top_num_list, data_count_per_interval, task_folder):
    epoch_dict = dd()
    interval_dict = dd()
    val_start_list = []
    end_date = None
    epoch_count = None

    for folder in os.listdir(task_folder):
        if os.path.isdir(os.path.join(task_folder, folder)) and folder.startswith('20'):
            val_start_list.append(folder.split('v')[0])
            if end_date is None:
                end_date = folder.split('v')[1]
    val_start_list.sort()
    print(f'{val_start_list = }')
    for vi, val_start in enumerate(val_start_list):
        val_start_end = f'{val_start}v{end_date}'
        val_folder = os.path.join(task_folder, val_start_end)
        num_intervals = len(val_start_list) - vi
        epoch_dict[val_start] = dd()
        interval_dict[val_start] = dd()
        for top_num in top_num_list:
            top_str = f'top{top_num}'
            epoch_dict[val_start][top_str] = dd()
            interval_dict[val_start][top_str] = dd()        
            for file in os.listdir(val_folder):
                if file.endswith('.pqt') and top_str in file:
                    epoch_str = file.replace('.pqt', '').split('_')[-1]
                    df = pl.read_parquet(os.path.join(val_folder, file))
                    interval_df_list = [df[data_count_per_interval * ni: data_count_per_interval * (ni + 1)] for ni in range(num_intervals)]
                    epoch_dict[val_start][top_str][epoch_str] = df
                    interval_dict[val_start][top_str][epoch_str] = interval_df_list
                    sub_list = val_start_list[vi:] + [end_date]
    epoch_count = len(epoch_dict[val_start][top_str])
    print(f'{epoch_count = }')

    return epoch_dict,interval_dict,val_start_list,end_date, epoch_count

if __name__ == '__main__':
    
    # is_rolling_epoches = True
    is_rolling_epoches = False

    
    datetime_str = '2024_1003_174534'    
    # datetime_str = '2024_1003_172441-GELU'
    if is_rolling_epoches:
        from rolling_retraining import pred_cfg
        execute_gap = True
        execute_next = True
    else:
        # from rank import pred_cfg
        # from classification_trading import pred_cfg
        from direct_trading import pred_cfg
        # from portfolio import pred_cfg        
        execute_gap = False
        execute_next = False
    pred_cfg.start_date.multi = '2021.01.01'
    pred_cfg.train_end_date = '2023.12.01'  
    ckpt_folder = pred_cfg.get_ckpt_folder()
    data_count_per_interval = pred_cfg.rolling_retraining.interval_in_day * 1440 // pred_cfg.interval_cfg.base // pred_cfg.inner_step
    task_folder = os.path.join(ckpt_folder, datetime_str)
    n_codes = pred_cfg.n_codes
    interval_index = 0
    top_num_list = pred_cfg.top_num_list + [
        n_codes // 2, 
        n_codes
        ]        
    # iterate through all the folders in the folder
    epoch_dict, interval_dict, val_start_list, end_date, epoch_count = get_epoch_interval_dict(top_num_list, data_count_per_interval, task_folder)    

    
    for ti in top_num_list:
        top_num_str = f'top{ti}'
        for epoch_index in range(epoch_count):
            report_top_epoch(pred_cfg, task_folder, is_rolling_epoches, top_num_str, val_start_list, end_date, epoch_index, epoch_dict, interval_dict, interval_index)