"""
Trend Scanning Labels Implementation
基于论文实现趋势扫描标签生成方法
"""

import polars as pl
import pandas as pd
import numpy as np
from typing import List, Tuple, Optional
from dataclasses import dataclass
from sklearn.linear_model import LinearRegression
import warnings
warnings.filterwarnings('ignore')


@dataclass
class TrendScanningConfig:
    """趋势扫描配置"""
    max_lookforward: int = 20  # 最大前瞻窗口|L|max
    min_lookforward: int = 3   # 最小前瞻窗口（论文约束：|L|≥3）
    price_column: str = 'close'  # 用于趋势扫描的价格列


class TrendScanningLabeler:
    """
    趋势扫描标签生成器

    实现论文中的趋势扫描标签方法：
    1. 对每个时间点，在前瞻窗口内拟合多条不同长度的线性回归
    2. 计算每条回归线的T统计量
    3. 选择T值绝对值最大的回归线
    4. 根据该回归线的斜率符号确定标签（上涨/下跌）
    """

    def __init__(self, config: TrendScanningConfig):
        self.config = config

    def generate_labels(self, df: pl.DataFrame, code_column: str = 'code') -> pl.DataFrame:
        """
        生成趋势扫描标签

        Args:
            df: 输入数据，必须包含价格时间序列
            code_column: 代码列名

        Returns:
            包含趋势扫描标签的DataFrame
        """
        print(f"开始生成趋势扫描标签，前瞻窗口: {self.config.min_lookforward}-{self.config.max_lookforward}")

        if self.config.price_column not in df.columns:
            raise ValueError(f"缺少价格列: {self.config.price_column}")

        labels_list = []

        for code in df[code_column].unique():
            code_df = df.filter(pl.col(code_column) == code)

            # 确保时间列的数据类型正确
            try:
                if 'open_time' in code_df.columns:
                    code_df = code_df.with_columns(
                        pl.col('open_time').cast(pl.Datetime)
                    ).sort('open_time')
                else:
                    # 如果没有open_time列，按索引排序
                    code_df = code_df.with_row_index().sort('index').drop('index')
            except Exception as e:
                print(f"排序时出现错误: {e}，跳过排序")

            code_labels = self._generate_labels_for_code(code_df, code)
            if len(code_labels) > 0:
                labels_list.append(code_labels)

        if not labels_list:
            return pl.DataFrame()

        result = pl.concat(labels_list)
        print(f"生成了 {len(result)} 个标签")

        # 尝试排序，如果失败则跳过
        try:
            result = result.with_columns([
                pl.col('open_time').cast(pl.Datetime),
                pl.col('code').cast(pl.Utf8)
            ]).sort(['code', 'open_time'])
        except Exception as e:
            print(f"排序时出现错误: {e}，返回未排序的结果")

        return result

    def _generate_labels_for_code(self, df: pl.DataFrame, code: str) -> pl.DataFrame:
        """为单个代码生成趋势扫描标签"""

        if len(df) < self.config.max_lookforward + 1:
            print(f"代码 {code} 数据不足，需要至少 {self.config.max_lookforward + 1} 条记录")
            return pl.DataFrame()

        price_series = df[self.config.price_column].to_numpy()
        open_time_series = df['open_time'].to_numpy()

        labels = []

        # 为每个可以生成标签的时间点计算趋势扫描标签
        for i in range(len(df) - self.config.max_lookforward):
            current_time = open_time_series[i]

            # 获取前瞻窗口的价格数据
            lookforward_prices = price_series[i:i + self.config.max_lookforward + 1]

            # 计算趋势扫描标签
            label, best_t_stat, best_slope, best_length = self._calculate_trend_scanning_label(lookforward_prices)

            labels.append({
                'open_time': current_time,
                'code': code,
                'tsc_label': label,
                'tsc_t_stat': best_t_stat,
                'tsc_slope': best_slope,
                'tsc_best_length': best_length
            })

        return pl.DataFrame(labels)

    def _calculate_trend_scanning_label(self, lookforward_prices: np.ndarray) -> Tuple[int, float, float, int]:
        """
        计算趋势扫描标签

        Args:
            lookforward_prices: 前瞻窗口的价格序列

        Returns:
            (label, best_t_stat, best_slope, best_length)
            label: 1表示上涨，0表示下跌
            best_t_stat: 最佳T统计量
            best_slope: 最佳斜率
            best_length: 最佳回归长度
        """
        best_t_stat = 0.0
        best_slope = 0.0
        best_length = self.config.min_lookforward

        # 对不同长度的前瞻窗口进行线性回归
        for length in range(self.config.min_lookforward, min(len(lookforward_prices), self.config.max_lookforward + 1)):
            # 获取当前长度的价格序列
            y = lookforward_prices[:length + 1]  # 包含当前点

            # 重新缩放价格到[0,1]区间（论文中提到的重新缩放）
            y_scaled = self._rescale_prices(y)

            # 计算线性回归的T统计量
            t_stat, slope = self._calculate_t_statistic(y_scaled)

            # 选择T值绝对值最大的回归
            if abs(t_stat) > abs(best_t_stat):
                best_t_stat = t_stat
                best_slope = slope
                best_length = length

        # 根据最佳斜率的符号确定标签
        label = 1 if best_slope > 0 else 0

        return label, best_t_stat, best_slope, best_length

    def _rescale_prices(self, prices: np.ndarray) -> np.ndarray:
        """
        重新缩放价格到[0,1]区间

        这是为了满足论文中β0（截距项）设为0的要求
        """
        if len(prices) <= 1:
            return prices

        min_price = np.min(prices)
        max_price = np.max(prices)

        if max_price == min_price:
            return np.zeros_like(prices)

        return (prices - min_price) / (max_price - min_price)

    def _calculate_t_statistic(self, y_scaled: np.ndarray) -> Tuple[float, float]:
        """
        计算线性回归的T统计量（论文公式3-5）

        Args:
            y_scaled: 重新缩放后的价格序列

        Returns:
            (t_statistic, slope)
        """
        n = len(y_scaled)
        if n < 3:  # 需要足够的自由度
            return 0.0, 0.0

        # 创建时间变量（从0开始）
        x = np.arange(n)

        try:
            # 计算斜率β1（论文公式3）
            # β1 = (N∑(l*yl) - ∑l∑yl) / (N∑l² - (∑l)²)
            sum_x = np.sum(x)
            sum_y = np.sum(y_scaled)
            sum_xy = np.sum(x * y_scaled)
            sum_x2 = np.sum(x * x)
            sum_y2 = np.sum(y_scaled * y_scaled)

            denominator = n * sum_x2 - sum_x * sum_x
            if abs(denominator) < 1e-10:
                return 0.0, 0.0

            slope = (n * sum_xy - sum_x * sum_y) / denominator

            # 计算残差方差σε²（论文公式4）
            # σε² = [N∑yl² - (∑yl)²] - β1²[N∑l² - (∑l)²] / [N(N-2)]
            numerator = (n * sum_y2 - sum_y * sum_y) - slope * slope * (n * sum_x2 - sum_x * sum_x)
            if n <= 2:
                return 0.0, slope

            residual_variance = numerator / (n * (n - 2))
            if residual_variance <= 0:
                return 0.0, slope

            # 计算斜率的标准误
            # SE(β1) = sqrt(σε² / (N∑l² - (∑l)²))
            slope_variance = residual_variance / (n * sum_x2 - sum_x * sum_x)
            if slope_variance <= 0:
                return 0.0, slope

            slope_se = np.sqrt(slope_variance)

            # 计算T统计量（论文公式5）
            # t = β1 / SE(β1)
            if slope_se == 0:
                return 0.0, slope

            t_stat = slope / slope_se

            return t_stat, slope

        except Exception as e:
            # 如果计算出错，返回零值
            return 0.0, 0.0

    def analyze_labels(self, labels_df: pl.DataFrame) -> dict:
        """分析标签的统计特性"""
        if len(labels_df) == 0:
            return {}

        analysis = {
            'total_labels': len(labels_df),
            'upward_ratio': labels_df['tsc_label'].mean(),
            'downward_ratio': 1 - labels_df['tsc_label'].mean(),
            't_stat_stats': {
                'mean': labels_df['tsc_t_stat'].mean(),
                'std': labels_df['tsc_t_stat'].std(),
                'min': labels_df['tsc_t_stat'].min(),
                'max': labels_df['tsc_t_stat'].max()
            },
            'slope_stats': {
                'mean': labels_df['tsc_slope'].mean(),
                'std': labels_df['tsc_slope'].std(),
                'min': labels_df['tsc_slope'].min(),
                'max': labels_df['tsc_slope'].max()
            },
            'best_length_stats': {
                'mean': labels_df['tsc_best_length'].mean(),
                'mode': labels_df['tsc_best_length'].mode().to_list()[0] if len(labels_df) > 0 else None
            }
        }

        return analysis


class FixedTimeHorizonLabeler:
    """
    固定时间范围标签生成器（作为对比基准）

    传统的标签方法：基于固定时间范围的价格变化
    """

    def __init__(self, horizon: int = 1, price_column: str = 'close'):
        self.horizon = horizon
        self.price_column = price_column

    def generate_labels(self, df: pl.DataFrame, code_column: str = 'code') -> pl.DataFrame:
        """生成固定时间范围标签"""

        labels_list = []

        for code in df[code_column].unique():
            code_df = df.filter(pl.col(code_column) == code).sort('open_time')

            if len(code_df) <= self.horizon:
                continue

            # 计算未来价格变化
            current_prices = code_df[self.price_column].to_numpy()[:-self.horizon]
            future_prices = code_df[self.price_column].to_numpy()[self.horizon:]
            open_times = code_df['open_time'].to_numpy()[:-self.horizon]

            # 生成标签：1表示上涨，0表示下跌
            labels = (future_prices > current_prices).astype(int)

            code_labels = pl.DataFrame({
                'open_time': open_times,
                'code': [code] * len(labels),
                'fth_label': labels,
                'price_change': future_prices - current_prices,
                'price_change_pct': (future_prices - current_prices) / current_prices
            })

            labels_list.append(code_labels)

        if not labels_list:
            return pl.DataFrame()

        return pl.concat(labels_list).sort(['code', 'open_time'])


if __name__ == "__main__":
    # 测试代码
    print("Trend Scanning Labels 模块已加载")

    # 创建测试配置
    config = TrendScanningConfig()
    labeler = TrendScanningLabeler(config)
    print(f"前瞻窗口范围: {config.min_lookforward}-{config.max_lookforward}")
