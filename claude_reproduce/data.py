import polars as pl
import pandas as pd
import numpy as np
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 暂时使用模拟数据进行测试
# 如果需要使用真实数据，请确保ArcticDB配置正确
# use_real_data = False
use_real_data = True

if use_real_data:
    try:
        from core.data_module import KlineDataModule
        from direct_trading import pred_cfg

        # 配置数据加载参数
        pred_cfg.n_codes = 1
        pred_cfg.symbol = 'ETHUSDT'
        pred_cfg.interval_cfg.base = 1  # 1分钟数据
        pred_cfg.code_sort_by_quote = True
        pred_cfg.start_date.single = '2021.10.01'
        pred_cfg.train_end_date = '2025.01.01'
        # pred_cfg.val_end_date = '2025.05.01'
        pred_cfg.execute_phase.train = True
        pred_cfg.is_eda = True

        print("正在加载数据...")
        dm = KlineDataModule(pred_cfg)

        #%%
        # 获取训练和验证数据
        tr_df = dm.dataset_dict.train.data.reset_index()
        vl_df = dm.dataset_dict.val.data.reset_index()
        tr_df = pl.from_pandas(tr_df)
        vl_df = pl.from_pandas(vl_df)

        print(f"训练数据形状: {tr_df.shape}")
        print(f"验证数据形状: {vl_df.shape}")
        print(f"数据列: {tr_df.columns}")

        # 添加close_roc列（收盘价变化率）
        # data columns:['open_time','open', 'high', 'low', 'close', 'volume', 'buy_volume', 'quote', 'buy_quote']
        tr_df = tr_df.with_columns(
            close_roc=pl.col('close').pct_change().over('code')
        )
        vl_df = vl_df.with_columns(
            close_roc=pl.col('close').pct_change().over('code')
        )

        # 合并训练和验证数据用于完整的时间序列分析
        full_df = pl.concat([tr_df, vl_df]).sort(['code', 'open_time'])
        print(f"完整数据形状: {full_df.shape}")
        print(f"数据时间范围: {full_df['open_time'].min()} 到 {full_df['open_time'].max()}")

        # 显示数据样本
        print("\n数据样本:")
        print(full_df.head(10))

    except Exception as e:
        print(f"无法导入数据模块: {e}")
        print("创建模拟数据用于测试...")
        use_real_data = False

if not use_real_data:
    print("创建模拟数据用于测试...")

    # 创建模拟的1分钟ETHUSDT数据
    start_date = pd.Timestamp('2021-10-01')
    end_date = pd.Timestamp('2024-01-01')

    # 生成时间序列
    time_range = pd.date_range(start=start_date, end=end_date, freq='1min')
    n_points = len(time_range)

    # 模拟价格数据（随机游走）
    np.random.seed(42)
    base_price = 3000.0
    returns = np.random.normal(0, 0.001, n_points)  # 0.1%的标准差
    prices = base_price * np.exp(np.cumsum(returns))

    # 生成OHLCV数据
    data = []
    for i, timestamp in enumerate(time_range):
        if i == 0:
            open_price = base_price
        else:
            open_price = data[i-1]['close']

        close_price = prices[i]
        high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, 0.0005)))
        low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, 0.0005)))

        volume = np.random.lognormal(10, 1)  # 对数正态分布的成交量
        quote = volume * close_price  # 成交额
        buy_volume = volume * np.random.uniform(0.4, 0.6)  # 买入成交量
        buy_quote = buy_volume * close_price  # 买入成交额

        data.append({
            'open_time': timestamp,
            'code': 'ETHUSDT',
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume,
            'buy_volume': buy_volume,
            'quote': quote,
            'buy_quote': buy_quote
        })

    # 转换为polars DataFrame
    full_df = pl.DataFrame(data)

    # 添加close_roc列
    full_df = full_df.with_columns(
        close_roc=pl.col('close').pct_change().over('code')
    )

    print(f"模拟数据形状: {full_df.shape}")
    print(f"数据列: {full_df.columns}")
    print(f"数据时间范围: {full_df['open_time'].min()} 到 {full_df['open_time'].max()}")
    print(f"价格范围: {full_df['close'].min():.2f} - {full_df['close'].max():.2f}")

    # 显示数据样本
    print("\n模拟数据样本:")
    print(full_df.head(10))

print("\n数据加载完成！")
