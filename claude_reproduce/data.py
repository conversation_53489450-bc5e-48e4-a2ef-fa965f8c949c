from core.data_module import KlineDataModule
from direct_trading import pred_cfg

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import polars as pl

pred_cfg.n_codes = 1
pred_cfg.symbol = 'ETHUSDT'
pred_cfg.interval_cfg.base = 1
pred_cfg.code_sort_by_quote = True
pred_cfg.start_date.single = '2021.10.01'
pred_cfg.train_end_date = '2025.04.01'
pred_cfg.val_end_date = '2025.05.01'
pred_cfg.is_eda = True

dm = KlineDataModule(pred_cfg)

#%%
tr_df = dm.dataset_dict.train.data.reset_index()
vl_df = dm.dataset_dict.val.data.reset_index()
tr_df = pl.from_pandas(tr_df)
vl_df = pl.from_pandas(vl_df)

# 添加close_roc列（收盘价变化率）
# columns['open_time','open', 'high', 'low', 'close', 'volume', 'buy_volume', 'quote', 'buy_quote']
tr_df = tr_df.with_columns(
    close_roc=pl.col('close').pct_change().over('code')
)
vl_df = vl_df.with_columns(
    close_roc=pl.col('close').pct_change().over('code')
)

#%%
# 计算每个code的quote_sum
code_quote_sum = tr_df.group_by('code').agg(
    pl.col('quote').sum().alias('quote_sum')
)