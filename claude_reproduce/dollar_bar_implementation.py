"""
Dollar Bar Implementation for Paper Reproduction
基于论文《Deep neural network model enhanced with data preparation for the directional predictability of multi-stock returns》
实现美元K线采样方法
"""

import polars as pl
import pandas as pd
import numpy as np
from typing import List, Tuple, Optional
from dataclasses import dataclass


@dataclass
class DollarBarConfig:
    """Dollar Bar配置参数"""
    threshold_50m: float = 50_000_000  # 5000万美元阈值
    threshold_200m: float = 200_000_000  # 2亿美元阈值
    use_quote_volume: bool = True  # 使用quote列还是计算close*volume


class DollarBarSampler:
    """
    美元K线采样器

    基于累计成交额（CUSUM）的采样方法：
    - 当累计成交额达到预设阈值时，形成一个新的美元K线
    - 重置累加器，开始下一个美元K线的累积
    """

    def __init__(self, config: DollarBarConfig):
        self.config = config

    def create_dollar_bars(self,
                          df: pl.DataFrame,
                          threshold: float,
                          code_column: str = 'code') -> pl.DataFrame:
        """
        创建美元K线

        Args:
            df: 输入的1分钟数据，必须包含 ['open_time', 'open', 'high', 'low', 'close', 'volume', 'quote']
            threshold: 美元阈值（50M或200M）
            code_column: 代码列名

        Returns:
            美元K线数据
        """
        print(f"开始创建美元K线，阈值: ${threshold:,.0f}")

        # 验证必要的列
        required_cols = ['open_time', 'open', 'high', 'low', 'close', 'volume']
        if self.config.use_quote_volume:
            required_cols.append('quote')

        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"缺少必要的列: {missing_cols}")

        # 按代码分组处理
        dollar_bars_list = []

        for code in df[code_column].unique():
            code_df = df.filter(pl.col(code_column) == code).sort('open_time')
            code_dollar_bars = self._create_dollar_bars_for_code(code_df, threshold, code)
            if len(code_dollar_bars) > 0:
                dollar_bars_list.append(code_dollar_bars)

        if not dollar_bars_list:
            return pl.DataFrame()

        result = pl.concat(dollar_bars_list)
        print(f"原始数据: {len(df)} 行，美元K线: {len(result)} 行")

        # 确保数据类型正确后再排序
        try:
            result = result.with_columns([
                pl.col('open_time').cast(pl.Datetime),
                pl.col('code').cast(pl.Utf8)
            ])
            return result.sort(['code', 'open_time'])
        except Exception as e:
            print(f"排序时出现错误: {e}")
            return result

    def _create_dollar_bars_for_code(self,
                                   df: pl.DataFrame,
                                   threshold: float,
                                   code: str) -> pl.DataFrame:
        """为单个代码创建美元K线"""

        # 计算成交额
        if self.config.use_quote_volume and 'quote' in df.columns:
            dollar_volume = df['quote'].to_numpy()
        else:
            dollar_volume = (df['close'] * df['volume']).to_numpy()

        # 转换为numpy数组以提高性能
        open_time = df['open_time'].to_numpy()
        open_price = df['open'].to_numpy()
        high_price = df['high'].to_numpy()
        low_price = df['low'].to_numpy()
        close_price = df['close'].to_numpy()
        volume = df['volume'].to_numpy()

        dollar_bars = []

        # CUSUM逻辑
        cumsum = 0.0
        bar_start_idx = 0

        for i in range(len(df)):
            cumsum += dollar_volume[i]

            # 当累计成交额达到阈值时，形成一个美元K线
            if cumsum >= threshold:
                # 计算该美元K线的OHLCV
                bar_open_time = open_time[bar_start_idx]
                bar_open = open_price[bar_start_idx]
                bar_high = np.max(high_price[bar_start_idx:i+1])
                bar_low = np.min(low_price[bar_start_idx:i+1])
                bar_close = close_price[i]
                bar_volume = np.sum(volume[bar_start_idx:i+1])
                bar_quote = cumsum

                dollar_bars.append({
                    'open_time': bar_open_time,
                    'code': code,
                    'open': float(bar_open),
                    'high': float(bar_high),
                    'low': float(bar_low),
                    'close': float(bar_close),
                    'volume': float(bar_volume),
                    'quote': float(bar_quote),
                    'bar_duration_minutes': int(i - bar_start_idx + 1),
                    'threshold_used': float(threshold)
                })

                # 重置累加器
                cumsum = 0.0
                bar_start_idx = i + 1

        # 处理最后一个未完成的K线（如果有的话）
        if bar_start_idx < len(df) and cumsum > 0:
            bar_open_time = open_time[bar_start_idx]
            bar_open = open_price[bar_start_idx]
            bar_high = np.max(high_price[bar_start_idx:])
            bar_low = np.min(low_price[bar_start_idx:])
            bar_close = close_price[-1]
            bar_volume = np.sum(volume[bar_start_idx:])
            bar_quote = cumsum

            dollar_bars.append({
                'open_time': bar_open_time,
                'code': code,
                'open': float(bar_open),
                'high': float(bar_high),
                'low': float(bar_low),
                'close': float(bar_close),
                'volume': float(bar_volume),
                'quote': float(bar_quote),
                'bar_duration_minutes': int(len(df) - bar_start_idx),
                'threshold_used': float(threshold)
            })

        return pl.DataFrame(dollar_bars)

    def create_both_thresholds(self, df: pl.DataFrame) -> Tuple[pl.DataFrame, pl.DataFrame]:
        """
        同时创建50M和200M两种阈值的美元K线

        Returns:
            (dollar_bars_50m, dollar_bars_200m)
        """
        dollar_bars_50m = self.create_dollar_bars(df, self.config.threshold_50m)
        dollar_bars_200m = self.create_dollar_bars(df, self.config.threshold_200m)

        return dollar_bars_50m, dollar_bars_200m

    def analyze_dollar_bars(self, dollar_bars: pl.DataFrame) -> dict:
        """分析美元K线的统计特性"""
        if len(dollar_bars) == 0:
            return {}

        try:
            # 确保数据类型正确
            if 'bar_duration_minutes' in dollar_bars.columns:
                avg_duration = dollar_bars['bar_duration_minutes'].mean()
                median_duration = dollar_bars['bar_duration_minutes'].median()
                min_duration = dollar_bars['bar_duration_minutes'].min()
                max_duration = dollar_bars['bar_duration_minutes'].max()
            else:
                avg_duration = median_duration = min_duration = max_duration = 0

            analysis = {
                'total_bars': len(dollar_bars),
                'avg_duration_minutes': avg_duration,
                'median_duration_minutes': median_duration,
                'min_duration_minutes': min_duration,
                'max_duration_minutes': max_duration,
                'avg_volume': dollar_bars['volume'].mean(),
                'avg_quote': dollar_bars['quote'].mean(),
            }

            # 尝试计算时间跨度
            try:
                # 转换时间列为datetime类型
                time_col = dollar_bars['open_time']
                if time_col.dtype != pl.Datetime:
                    time_col = time_col.cast(pl.Datetime)

                time_span_seconds = (time_col.max() - time_col.min()).total_seconds()
                analysis['time_span_days'] = time_span_seconds / (24 * 3600)
            except:
                analysis['time_span_days'] = 0

        except Exception as e:
            print(f"分析Dollar Bar时出现错误: {e}")
            analysis = {'total_bars': len(dollar_bars)}

        return analysis


def compare_time_bars_vs_dollar_bars(minute_data: pl.DataFrame,
                                   dollar_bars: pl.DataFrame,
                                   time_intervals: List[int] = [15, 60]) -> dict:
    """
    比较时间K线和美元K线的特性

    Args:
        minute_data: 1分钟原始数据
        dollar_bars: 美元K线数据
        time_intervals: 时间K线间隔（分钟）

    Returns:
        比较结果字典
    """
    results = {}

    # 创建时间K线
    for interval in time_intervals:
        time_bars = create_time_bars(minute_data, interval)
        results[f'time_{interval}m'] = {
            'count': len(time_bars),
            'avg_volume': time_bars['volume'].mean() if len(time_bars) > 0 else 0,
            'price_volatility': time_bars['close'].std() if len(time_bars) > 0 else 0
        }

    # 美元K线统计
    results['dollar_bars'] = {
        'count': len(dollar_bars),
        'avg_volume': dollar_bars['volume'].mean() if len(dollar_bars) > 0 else 0,
        'price_volatility': dollar_bars['close'].std() if len(dollar_bars) > 0 else 0,
        'avg_duration': dollar_bars['bar_duration_minutes'].mean() if len(dollar_bars) > 0 else 0
    }

    return results


def create_time_bars(df: pl.DataFrame, interval_minutes: int) -> pl.DataFrame:
    """
    创建传统的时间K线

    Args:
        df: 1分钟数据
        interval_minutes: 时间间隔（分钟）

    Returns:
        时间K线数据
    """
    # 创建时间分组
    df_with_group = df.with_columns(
        time_group=(pl.col('open_time').dt.timestamp('ms') // (interval_minutes * 60 * 1000)) * (interval_minutes * 60 * 1000)
    ).with_columns(
        time_group=pl.from_epoch(pl.col('time_group'), time_unit='ms')
    )

    # 聚合数据
    time_bars = df_with_group.group_by(['code', 'time_group']).agg([
        pl.col('open_time').first().alias('open_time'),
        pl.col('open').first().alias('open'),
        pl.col('high').max().alias('high'),
        pl.col('low').min().alias('low'),
        pl.col('close').last().alias('close'),
        pl.col('volume').sum().alias('volume'),
        pl.col('quote').sum().alias('quote') if 'quote' in df.columns else pl.lit(0).alias('quote')
    ]).sort(['code', 'time_group'])

    return time_bars.drop('time_group')


if __name__ == "__main__":
    # 测试代码
    print("Dollar Bar Implementation 模块已加载")
