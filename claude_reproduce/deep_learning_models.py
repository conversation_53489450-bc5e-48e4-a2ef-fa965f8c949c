"""
Deep Neural Network Models Implementation
基于论文实现深度神经网络模型
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, TensorDataset
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import warnings
warnings.filterwarnings('ignore')


@dataclass
class DNNConfig:
    """DNN模型配置（基于论文规格）"""
    input_size: int  # 输入特征数量N（PAA特征数）
    hidden_layers: int = 6  # 隐藏层数量NHL
    hidden_size_multiplier: int = 2  # 每层神经元数量 = 2N
    activation: str = 'swish'  # 激活函数：ReLU, Tanh, Swish
    dropout_rate: float = 0.2  # Dropout率（论文中为20%）
    learning_rate: float = 0.001  # 学习率
    batch_size: int = 128  # 批次大小
    max_epochs: int = 200  # 最大训练轮数
    early_stopping_patience: int = 20  # 早停耐心值
    device: str = 'cuda' if torch.cuda.is_available() else 'cpu'


class SwishActivation(nn.Module):
    """Swish激活函数实现"""
    
    def forward(self, x):
        return x * torch.sigmoid(x)


class DNNModel(nn.Module):
    """
    深度神经网络模型（论文架构）
    
    架构规格：
    - 输入层：N个特征
    - 隐藏层：NHL层，每层2N个神经元
    - 激活函数：ReLU/Tanh/Swish
    - 输出层：1个神经元，Sigmoid激活
    - Dropout：20%
    """
    
    def __init__(self, config: DNNConfig):
        super(DNNModel, self).__init__()
        self.config = config
        
        # 计算隐藏层大小
        hidden_size = config.input_size * config.hidden_size_multiplier
        
        # 构建网络层
        layers = []
        
        # 输入层到第一个隐藏层
        layers.append(nn.Linear(config.input_size, hidden_size))
        layers.append(self._get_activation(config.activation))
        layers.append(nn.Dropout(config.dropout_rate))
        
        # 隐藏层
        for _ in range(config.hidden_layers - 1):
            layers.append(nn.Linear(hidden_size, hidden_size))
            layers.append(self._get_activation(config.activation))
            layers.append(nn.Dropout(config.dropout_rate))
        
        # 输出层
        layers.append(nn.Linear(hidden_size, 1))
        layers.append(nn.Sigmoid())
        
        self.network = nn.Sequential(*layers)
        
    def _get_activation(self, activation: str) -> nn.Module:
        """获取激活函数"""
        if activation.lower() == 'relu':
            return nn.ReLU()
        elif activation.lower() == 'tanh':
            return nn.Tanh()
        elif activation.lower() == 'swish':
            return SwishActivation()
        else:
            raise ValueError(f"不支持的激活函数: {activation}")
    
    def forward(self, x):
        return self.network(x)


class EarlyStopping:
    """
    自定义早停机制（基于论文Algorithm 1）
    
    监控训练损失和验证损失的差异以及验证损失本身
    """
    
    def __init__(self, patience: int = 20, min_delta: float = 0.0):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss_diff = float('inf')
        self.best_val_loss = float('inf')
        self.best_epoch = 0
        self.early_stop = False
        
    def __call__(self, train_loss: float, val_loss: float, epoch: int) -> bool:
        """
        检查是否应该早停
        
        基于论文公式9和10：
        - 条件1：min|L_train - L_valid|
        - 条件2：min|L_valid|
        """
        loss_diff = abs(train_loss - val_loss)
        
        # 条件1：训练-验证损失差异
        if loss_diff < self.best_loss_diff - self.min_delta:
            self.best_loss_diff = loss_diff
            self.best_epoch = epoch
            self.counter = 0
        # 条件2：验证损失
        elif val_loss < self.best_val_loss - self.min_delta:
            self.best_val_loss = val_loss
            self.best_epoch = epoch
            self.counter = 0
        else:
            self.counter += 1
        
        if self.counter >= self.patience:
            self.early_stop = True
        
        return self.early_stop


class DNNTrainer:
    """DNN训练器"""
    
    def __init__(self, config: DNNConfig):
        self.config = config
        self.device = torch.device(config.device)
        self.model = None
        self.optimizer = None
        self.criterion = None
        self.history = {'train_loss': [], 'val_loss': [], 'train_acc': [], 'val_acc': []}
        
    def build_model(self) -> DNNModel:
        """构建模型"""
        self.model = DNNModel(self.config).to(self.device)
        
        # 优化器（论文使用Adam）
        self.optimizer = optim.Adam(
            self.model.parameters(), 
            lr=self.config.learning_rate
        )
        
        # 损失函数（二元交叉熵）
        self.criterion = nn.BCELoss()
        
        return self.model
    
    def train(self, X_train: np.ndarray, y_train: np.ndarray,
              X_val: np.ndarray, y_val: np.ndarray) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            X_train: 训练特征
            y_train: 训练标签
            X_val: 验证特征
            y_val: 验证标签
            
        Returns:
            训练历史和结果
        """
        if self.model is None:
            self.build_model()
        
        # 创建数据加载器
        train_dataset = TensorDataset(
            torch.FloatTensor(X_train),
            torch.FloatTensor(y_train.reshape(-1, 1))
        )
        val_dataset = TensorDataset(
            torch.FloatTensor(X_val),
            torch.FloatTensor(y_val.reshape(-1, 1))
        )
        
        train_loader = DataLoader(
            train_dataset, 
            batch_size=self.config.batch_size, 
            shuffle=True
        )
        val_loader = DataLoader(
            val_dataset, 
            batch_size=self.config.batch_size, 
            shuffle=False
        )
        
        # 早停机制
        early_stopping = EarlyStopping(patience=self.config.early_stopping_patience)
        
        print(f"开始训练DNN模型，设备: {self.device}")
        print(f"模型参数: {sum(p.numel() for p in self.model.parameters()):,}")
        
        best_model_state = None
        best_val_loss = float('inf')
        
        for epoch in range(self.config.max_epochs):
            # 训练阶段
            train_loss, train_acc = self._train_epoch(train_loader)
            
            # 验证阶段
            val_loss, val_acc = self._validate_epoch(val_loader)
            
            # 记录历史
            self.history['train_loss'].append(train_loss)
            self.history['val_loss'].append(val_loss)
            self.history['train_acc'].append(train_acc)
            self.history['val_acc'].append(val_acc)
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_state = self.model.state_dict().copy()
            
            # 早停检查
            if early_stopping(train_loss, val_loss, epoch):
                print(f"早停触发，在第 {epoch+1} 轮停止训练")
                break
            
            # 打印进度
            if (epoch + 1) % 10 == 0:
                print(f"Epoch {epoch+1}/{self.config.max_epochs}: "
                      f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, "
                      f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
        
        # 加载最佳模型
        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)
        
        return {
            'history': self.history,
            'best_epoch': early_stopping.best_epoch,
            'final_train_loss': train_loss,
            'final_val_loss': val_loss,
            'best_val_loss': best_val_loss
        }
    
    def _train_epoch(self, train_loader: DataLoader) -> Tuple[float, float]:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        correct = 0
        total = 0
        
        for batch_x, batch_y in train_loader:
            batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            outputs = self.model(batch_x)
            loss = self.criterion(outputs, batch_y)
            
            # 反向传播
            loss.backward()
            self.optimizer.step()
            
            # 统计
            total_loss += loss.item()
            predicted = (outputs > 0.5).float()
            total += batch_y.size(0)
            correct += (predicted == batch_y).sum().item()
        
        avg_loss = total_loss / len(train_loader)
        accuracy = correct / total
        
        return avg_loss, accuracy
    
    def _validate_epoch(self, val_loader: DataLoader) -> Tuple[float, float]:
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for batch_x, batch_y in val_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                
                outputs = self.model(batch_x)
                loss = self.criterion(outputs, batch_y)
                
                total_loss += loss.item()
                predicted = (outputs > 0.5).float()
                total += batch_y.size(0)
                correct += (predicted == batch_y).sum().item()
        
        avg_loss = total_loss / len(val_loader)
        accuracy = correct / total
        
        return avg_loss, accuracy
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """预测"""
        if self.model is None:
            raise ValueError("模型尚未训练")
        
        self.model.eval()
        X_tensor = torch.FloatTensor(X).to(self.device)
        
        with torch.no_grad():
            outputs = self.model(X_tensor)
            predictions = (outputs > 0.5).cpu().numpy().flatten()
        
        return predictions.astype(int)
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """预测概率"""
        if self.model is None:
            raise ValueError("模型尚未训练")
        
        self.model.eval()
        X_tensor = torch.FloatTensor(X).to(self.device)
        
        with torch.no_grad():
            outputs = self.model(X_tensor)
            probabilities = outputs.cpu().numpy().flatten()
        
        # 返回两类概率
        return np.column_stack([1 - probabilities, probabilities])
    
    def evaluate(self, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
        """评估模型"""
        y_pred = self.predict(X)
        y_proba = self.predict_proba(X)[:, 1]
        
        metrics = {
            'accuracy': accuracy_score(y, y_pred),
            'precision': precision_score(y, y_pred, average='weighted', zero_division=0),
            'recall': recall_score(y, y_pred, average='weighted', zero_division=0),
            'f1_score': f1_score(y, y_pred, average='weighted', zero_division=0),
            'auc': roc_auc_score(y, y_proba)
        }
        
        return metrics


def create_dnn_configs() -> List[DNNConfig]:
    """创建不同的DNN配置（用于超参数搜索）"""
    configs = []
    
    # 基于论文表2的超参数搜索空间
    learning_rates = [0.01, 0.001, 0.0001]
    hidden_layers = [3, 4, 5, 6, 7]
    batch_sizes = [32, 64, 128, 256]
    activations = ['relu', 'tanh', 'swish']
    
    # 创建一些代表性配置
    base_configs = [
        {'learning_rate': 0.001, 'hidden_layers': 6, 'batch_size': 128, 'activation': 'swish'},  # 论文最佳配置
        {'learning_rate': 0.01, 'hidden_layers': 4, 'batch_size': 64, 'activation': 'relu'},
        {'learning_rate': 0.0001, 'hidden_layers': 5, 'batch_size': 256, 'activation': 'tanh'},
    ]
    
    for config_dict in base_configs:
        config = DNNConfig(
            input_size=120,  # 将在实际使用时更新
            **config_dict
        )
        configs.append(config)
    
    return configs


if __name__ == "__main__":
    # 测试代码
    print("Deep Learning Models 模块已加载")
    
    # 创建测试配置
    config = DNNConfig(input_size=120)
    trainer = DNNTrainer(config)
    print(f"DNN配置: 输入维度={config.input_size}, 隐藏层={config.hidden_layers}, 激活函数={config.activation}")
