"""
Machine Learning Models Implementation
实现论文中使用的机器学习模型：LightGBM, XGBoost, 线性判别分析器
"""

import polars as pl
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
from sklearn.ensemble import RandomForestClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.svm import SVC
from sklearn.model_selection import GridSearchCV, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from sklearn.preprocessing import StandardScaler
import lightgbm as lgb
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')


@dataclass
class ModelConfig:
    """模型配置"""
    model_name: str
    hyperparameters: Dict[str, Any]
    use_grid_search: bool = True
    cv_folds: int = 5
    random_state: int = 42


class BaseMLModel:
    """机器学习模型基类"""
    
    def __init__(self, config: ModelConfig):
        self.config = config
        self.model = None
        self.scaler = StandardScaler()
        self.is_fitted = False
        self.feature_importance_ = None
        
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'BaseMLModel':
        """训练模型"""
        raise NotImplementedError
        
    def predict(self, X: np.ndarray) -> np.ndarray:
        """预测"""
        if not self.is_fitted:
            raise ValueError("模型尚未训练")
        return self.model.predict(X)
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """预测概率"""
        if not self.is_fitted:
            raise ValueError("模型尚未训练")
        return self.model.predict_proba(X)
    
    def evaluate(self, X: np.ndarray, y: np.ndarray) -> Dict[str, float]:
        """评估模型"""
        y_pred = self.predict(X)
        y_proba = self.predict_proba(X)[:, 1] if hasattr(self.model, 'predict_proba') else None
        
        metrics = {}
        
        # 基本分类指标
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        
        metrics['accuracy'] = accuracy_score(y, y_pred)
        metrics['precision'] = precision_score(y, y_pred, average='weighted', zero_division=0)
        metrics['recall'] = recall_score(y, y_pred, average='weighted', zero_division=0)
        metrics['f1_score'] = f1_score(y, y_pred, average='weighted', zero_division=0)
        
        # AUC
        if y_proba is not None:
            try:
                metrics['auc'] = roc_auc_score(y, y_proba)
            except:
                metrics['auc'] = 0.0
        
        return metrics


class LightGBMModel(BaseMLModel):
    """LightGBM模型"""
    
    def __init__(self, config: ModelConfig = None):
        if config is None:
            config = ModelConfig(
                model_name='lightgbm',
                hyperparameters={
                    'objective': 'binary',
                    'metric': 'binary_logloss',
                    'boosting_type': 'gbdt',
                    'num_leaves': 31,
                    'learning_rate': 0.05,
                    'feature_fraction': 0.9,
                    'bagging_fraction': 0.8,
                    'bagging_freq': 5,
                    'verbose': -1,
                    'random_state': 42
                }
            )
        super().__init__(config)
    
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'LightGBMModel':
        """训练LightGBM模型"""
        
        if self.config.use_grid_search:
            # 网格搜索超参数
            param_grid = {
                'num_leaves': [31, 50, 100],
                'learning_rate': [0.01, 0.05, 0.1],
                'n_estimators': [100, 200, 300]
            }
            
            lgb_model = lgb.LGBMClassifier(**self.config.hyperparameters)
            self.model = GridSearchCV(
                lgb_model, param_grid, 
                cv=self.config.cv_folds, 
                scoring='roc_auc',
                n_jobs=-1
            )
        else:
            self.model = lgb.LGBMClassifier(**self.config.hyperparameters)
        
        self.model.fit(X, y)
        self.is_fitted = True
        
        # 获取特征重要性
        if hasattr(self.model, 'feature_importances_'):
            self.feature_importance_ = self.model.feature_importances_
        elif hasattr(self.model, 'best_estimator_'):
            self.feature_importance_ = self.model.best_estimator_.feature_importances_
        
        return self


class XGBoostModel(BaseMLModel):
    """XGBoost模型"""
    
    def __init__(self, config: ModelConfig = None):
        if config is None:
            config = ModelConfig(
                model_name='xgboost',
                hyperparameters={
                    'objective': 'binary:logistic',
                    'eval_metric': 'logloss',
                    'max_depth': 6,
                    'learning_rate': 0.1,
                    'n_estimators': 100,
                    'subsample': 0.8,
                    'colsample_bytree': 0.8,
                    'random_state': 42
                }
            )
        super().__init__(config)
    
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'XGBoostModel':
        """训练XGBoost模型"""
        
        if self.config.use_grid_search:
            # 网格搜索超参数
            param_grid = {
                'max_depth': [3, 6, 9],
                'learning_rate': [0.01, 0.1, 0.2],
                'n_estimators': [100, 200, 300]
            }
            
            xgb_model = xgb.XGBClassifier(**self.config.hyperparameters)
            self.model = GridSearchCV(
                xgb_model, param_grid,
                cv=self.config.cv_folds,
                scoring='roc_auc',
                n_jobs=-1
            )
        else:
            self.model = xgb.XGBClassifier(**self.config.hyperparameters)
        
        self.model.fit(X, y)
        self.is_fitted = True
        
        # 获取特征重要性
        if hasattr(self.model, 'feature_importances_'):
            self.feature_importance_ = self.model.feature_importances_
        elif hasattr(self.model, 'best_estimator_'):
            self.feature_importance_ = self.model.best_estimator_.feature_importances_
        
        return self


class LinearDiscriminantModel(BaseMLModel):
    """线性判别分析模型"""
    
    def __init__(self, config: ModelConfig = None):
        if config is None:
            config = ModelConfig(
                model_name='lda',
                hyperparameters={
                    'solver': 'svd',
                    'shrinkage': None
                },
                use_grid_search=False  # LDA参数较少，通常不需要网格搜索
            )
        super().__init__(config)
    
    def fit(self, X: np.ndarray, y: np.ndarray) -> 'LinearDiscriminantModel':
        """训练线性判别分析模型"""
        
        # 标准化特征
        X_scaled = self.scaler.fit_transform(X)
        
        if self.config.use_grid_search:
            # 网格搜索超参数
            param_grid = {
                'solver': ['svd', 'lsqr', 'eigen'],
                'shrinkage': [None, 'auto', 0.1, 0.5, 0.9]
            }
            
            lda_model = LinearDiscriminantAnalysis(**self.config.hyperparameters)
            self.model = GridSearchCV(
                lda_model, param_grid,
                cv=self.config.cv_folds,
                scoring='roc_auc',
                n_jobs=-1
            )
        else:
            self.model = LinearDiscriminantAnalysis(**self.config.hyperparameters)
        
        self.model.fit(X_scaled, y)
        self.is_fitted = True
        
        return self
    
    def predict(self, X: np.ndarray) -> np.ndarray:
        """预测（需要标准化）"""
        if not self.is_fitted:
            raise ValueError("模型尚未训练")
        X_scaled = self.scaler.transform(X)
        return self.model.predict(X_scaled)
    
    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """预测概率（需要标准化）"""
        if not self.is_fitted:
            raise ValueError("模型尚未训练")
        X_scaled = self.scaler.transform(X)
        return self.model.predict_proba(X_scaled)


class BenchmarkModels:
    """基准模型集合（论文中提到的对比模型）"""
    
    def __init__(self):
        self.models = {
            'decision_tree': DecisionTreeClassifier(random_state=42),
            'random_forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'svm': SVC(probability=True, random_state=42)
        }
        self.fitted_models = {}
        self.scalers = {}
    
    def fit_all(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """训练所有基准模型"""
        results = {}
        
        for name, model in self.models.items():
            print(f"训练 {name} 模型...")
            
            # 对SVM进行特征标准化
            if name == 'svm':
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X)
                self.scalers[name] = scaler
                model.fit(X_scaled, y)
            else:
                model.fit(X, y)
            
            self.fitted_models[name] = model
            
            # 交叉验证评估
            if name == 'svm':
                cv_scores = cross_val_score(model, X_scaled, y, cv=5, scoring='roc_auc')
            else:
                cv_scores = cross_val_score(model, X, y, cv=5, scoring='roc_auc')
            
            results[name] = {
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'model': model
            }
        
        return results
    
    def evaluate_all(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Dict[str, float]]:
        """评估所有模型"""
        results = {}
        
        for name, model in self.fitted_models.items():
            # 预测
            if name == 'svm':
                X_scaled = self.scalers[name].transform(X)
                y_pred = model.predict(X_scaled)
                y_proba = model.predict_proba(X_scaled)[:, 1]
            else:
                y_pred = model.predict(X)
                y_proba = model.predict_proba(X)[:, 1]
            
            # 计算指标
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
            
            results[name] = {
                'accuracy': accuracy_score(y, y_pred),
                'precision': precision_score(y, y_pred, average='weighted', zero_division=0),
                'recall': recall_score(y, y_pred, average='weighted', zero_division=0),
                'f1_score': f1_score(y, y_pred, average='weighted', zero_division=0),
                'auc': roc_auc_score(y, y_proba)
            }
        
        return results


def create_model_suite() -> Dict[str, BaseMLModel]:
    """创建完整的模型套件"""
    models = {
        'lightgbm': LightGBMModel(),
        'xgboost': XGBoostModel(),
        'lda': LinearDiscriminantModel()
    }
    return models


def compare_models(models: Dict[str, BaseMLModel], 
                  X_train: np.ndarray, y_train: np.ndarray,
                  X_test: np.ndarray, y_test: np.ndarray) -> pd.DataFrame:
    """比较多个模型的性能"""
    
    results = []
    
    for name, model in models.items():
        print(f"训练和评估 {name} 模型...")
        
        # 训练模型
        model.fit(X_train, y_train)
        
        # 评估训练集
        train_metrics = model.evaluate(X_train, y_train)
        
        # 评估测试集
        test_metrics = model.evaluate(X_test, y_test)
        
        # 记录结果
        result = {
            'model': name,
            'train_accuracy': train_metrics['accuracy'],
            'train_precision': train_metrics['precision'],
            'train_recall': train_metrics['recall'],
            'train_f1': train_metrics['f1_score'],
            'train_auc': train_metrics.get('auc', 0.0),
            'test_accuracy': test_metrics['accuracy'],
            'test_precision': test_metrics['precision'],
            'test_recall': test_metrics['recall'],
            'test_f1': test_metrics['f1_score'],
            'test_auc': test_metrics.get('auc', 0.0)
        }
        
        results.append(result)
    
    return pd.DataFrame(results)


if __name__ == "__main__":
    # 测试代码
    print("ML Models 模块已加载")
    
    # 创建模型套件
    models = create_model_suite()
    print(f"可用模型: {list(models.keys())}")
