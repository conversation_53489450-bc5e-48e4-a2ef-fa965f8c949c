"""
工具函数模块
提供数据分析、可视化和结果比较的工具函数
"""

import polars as pl
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Any, Optional
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


def plot_dollar_bar_analysis(dollar_bars_50m: pl.DataFrame, 
                            dollar_bars_200m: pl.DataFrame,
                            save_path: Optional[str] = None) -> None:
    """
    可视化Dollar Bar分析结果
    
    Args:
        dollar_bars_50m: 50M Dollar Bar数据
        dollar_bars_200m: 200M Dollar Bar数据
        save_path: 保存路径
    """
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Dollar Bar 分析', fontsize=16, fontweight='bold')
    
    # 50M Dollar Bar持续时间分布
    if len(dollar_bars_50m) > 0:
        duration_50m = dollar_bars_50m['bar_duration_minutes'].to_numpy()
        axes[0, 0].hist(duration_50m, bins=30, alpha=0.7, color='blue', label='50M')
        axes[0, 0].set_title('50M Dollar Bar 持续时间分布')
        axes[0, 0].set_xlabel('持续时间 (分钟)')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].axvline(np.mean(duration_50m), color='red', linestyle='--', 
                          label=f'均值: {np.mean(duration_50m):.1f}分钟')
        axes[0, 0].legend()
    
    # 200M Dollar Bar持续时间分布
    if len(dollar_bars_200m) > 0:
        duration_200m = dollar_bars_200m['bar_duration_minutes'].to_numpy()
        axes[0, 1].hist(duration_200m, bins=30, alpha=0.7, color='green', label='200M')
        axes[0, 1].set_title('200M Dollar Bar 持续时间分布')
        axes[0, 1].set_xlabel('持续时间 (分钟)')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].axvline(np.mean(duration_200m), color='red', linestyle='--',
                          label=f'均值: {np.mean(duration_200m):.1f}分钟')
        axes[0, 1].legend()
    
    # 价格分布比较
    if len(dollar_bars_50m) > 0 and len(dollar_bars_200m) > 0:
        close_50m = dollar_bars_50m['close'].to_numpy()
        close_200m = dollar_bars_200m['close'].to_numpy()
        
        axes[1, 0].hist(close_50m, bins=50, alpha=0.5, color='blue', label='50M', density=True)
        axes[1, 0].hist(close_200m, bins=50, alpha=0.5, color='green', label='200M', density=True)
        axes[1, 0].set_title('收盘价分布比较')
        axes[1, 0].set_xlabel('收盘价')
        axes[1, 0].set_ylabel('密度')
        axes[1, 0].legend()
    
    # 成交量分布比较
    if len(dollar_bars_50m) > 0 and len(dollar_bars_200m) > 0:
        volume_50m = dollar_bars_50m['volume'].to_numpy()
        volume_200m = dollar_bars_200m['volume'].to_numpy()
        
        axes[1, 1].boxplot([volume_50m, volume_200m], labels=['50M', '200M'])
        axes[1, 1].set_title('成交量分布比较')
        axes[1, 1].set_ylabel('成交量')
        axes[1, 1].set_yscale('log')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {save_path}")
    
    plt.show()


def plot_paa_features_analysis(features_df: pl.DataFrame, 
                              threshold_name: str,
                              save_path: Optional[str] = None) -> None:
    """
    可视化PAA特征分析
    
    Args:
        features_df: PAA特征数据
        threshold_name: 阈值名称（50M或200M）
        save_path: 保存路径
    """
    
    if len(features_df) == 0:
        print(f"警告: {threshold_name} 特征数据为空，无法绘图")
        return
    
    # 获取特征列
    feature_cols = [col for col in features_df.columns if col.startswith('paa_feature_')]
    
    if len(feature_cols) == 0:
        print(f"警告: {threshold_name} 没有找到PAA特征列")
        return
    
    # 转换为numpy数组
    features_array = features_df.select(feature_cols).to_numpy()
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'{threshold_name} PAA特征分析', fontsize=16, fontweight='bold')
    
    # 特征分布热图
    correlation_matrix = np.corrcoef(features_array.T)
    im = axes[0, 0].imshow(correlation_matrix, cmap='coolwarm', aspect='auto')
    axes[0, 0].set_title('特征相关性热图')
    axes[0, 0].set_xlabel('特征索引')
    axes[0, 0].set_ylabel('特征索引')
    plt.colorbar(im, ax=axes[0, 0])
    
    # 特征均值分布
    feature_means = np.mean(features_array, axis=0)
    axes[0, 1].plot(feature_means)
    axes[0, 1].set_title('特征均值分布')
    axes[0, 1].set_xlabel('特征索引')
    axes[0, 1].set_ylabel('均值')
    
    # 特征标准差分布
    feature_stds = np.std(features_array, axis=0)
    axes[1, 0].plot(feature_stds)
    axes[1, 0].set_title('特征标准差分布')
    axes[1, 0].set_xlabel('特征索引')
    axes[1, 0].set_ylabel('标准差')
    
    # 前几个特征的分布
    n_features_to_show = min(5, len(feature_cols))
    for i in range(n_features_to_show):
        axes[1, 1].hist(features_array[:, i], bins=30, alpha=0.5, label=f'特征{i}')
    axes[1, 1].set_title('前几个特征的分布')
    axes[1, 1].set_xlabel('特征值')
    axes[1, 1].set_ylabel('频次')
    axes[1, 1].legend()
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {save_path}")
    
    plt.show()


def plot_model_comparison(ml_results: pd.DataFrame, 
                         dnn_results: Dict[str, Any],
                         threshold_name: str,
                         save_path: Optional[str] = None) -> None:
    """
    可视化模型性能比较
    
    Args:
        ml_results: 机器学习模型结果
        dnn_results: DNN模型结果
        threshold_name: 阈值名称
        save_path: 保存路径
    """
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'{threshold_name} 模型性能比较', fontsize=16, fontweight='bold')
    
    # 准备数据
    models = ml_results['model'].tolist()
    test_accuracy = ml_results['test_accuracy'].tolist()
    test_precision = ml_results['test_precision'].tolist()
    test_recall = ml_results['test_recall'].tolist()
    test_auc = ml_results['test_auc'].tolist()
    
    # 添加DNN结果
    dnn_metrics = dnn_results['test_metrics']
    models.append('DNN')
    test_accuracy.append(dnn_metrics['accuracy'])
    test_precision.append(dnn_metrics['precision'])
    test_recall.append(dnn_metrics['recall'])
    test_auc.append(dnn_metrics['auc'])
    
    # 准确率比较
    bars1 = axes[0, 0].bar(models, test_accuracy, color='skyblue')
    axes[0, 0].set_title('测试准确率比较')
    axes[0, 0].set_ylabel('准确率')
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # 在柱状图上添加数值
    for bar, acc in zip(bars1, test_accuracy):
        axes[0, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                       f'{acc:.3f}', ha='center', va='bottom')
    
    # 精确率比较
    bars2 = axes[0, 1].bar(models, test_precision, color='lightgreen')
    axes[0, 1].set_title('测试精确率比较')
    axes[0, 1].set_ylabel('精确率')
    axes[0, 1].tick_params(axis='x', rotation=45)
    
    for bar, prec in zip(bars2, test_precision):
        axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                       f'{prec:.3f}', ha='center', va='bottom')
    
    # 召回率比较
    bars3 = axes[1, 0].bar(models, test_recall, color='lightcoral')
    axes[1, 0].set_title('测试召回率比较')
    axes[1, 0].set_ylabel('召回率')
    axes[1, 0].tick_params(axis='x', rotation=45)
    
    for bar, rec in zip(bars3, test_recall):
        axes[1, 0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                       f'{rec:.3f}', ha='center', va='bottom')
    
    # AUC比较
    bars4 = axes[1, 1].bar(models, test_auc, color='gold')
    axes[1, 1].set_title('测试AUC比较')
    axes[1, 1].set_ylabel('AUC')
    axes[1, 1].tick_params(axis='x', rotation=45)
    
    for bar, auc in zip(bars4, test_auc):
        axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                       f'{auc:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {save_path}")
    
    plt.show()


def plot_training_history(training_history: Dict[str, List], 
                         threshold_name: str,
                         save_path: Optional[str] = None) -> None:
    """
    可视化DNN训练历史
    
    Args:
        training_history: 训练历史数据
        threshold_name: 阈值名称
        save_path: 保存路径
    """
    
    history = training_history['history']
    
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    fig.suptitle(f'{threshold_name} DNN训练历史', fontsize=16, fontweight='bold')
    
    # 损失曲线
    epochs = range(1, len(history['train_loss']) + 1)
    axes[0].plot(epochs, history['train_loss'], 'b-', label='训练损失')
    axes[0].plot(epochs, history['val_loss'], 'r-', label='验证损失')
    axes[0].set_title('损失曲线')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('损失')
    axes[0].legend()
    axes[0].grid(True)
    
    # 准确率曲线
    axes[1].plot(epochs, history['train_acc'], 'b-', label='训练准确率')
    axes[1].plot(epochs, history['val_acc'], 'r-', label='验证准确率')
    axes[1].set_title('准确率曲线')
    axes[1].set_xlabel('Epoch')
    axes[1].set_ylabel('准确率')
    axes[1].legend()
    axes[1].grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图表已保存到: {save_path}")
    
    plt.show()


def create_interactive_dashboard(experiment_results: Dict[str, Any]) -> None:
    """
    创建交互式仪表板
    
    Args:
        experiment_results: 实验结果数据
    """
    
    # 使用Plotly创建交互式图表
    model_results = experiment_results.get('model_results', {})
    
    if not model_results:
        print("没有模型结果数据，无法创建仪表板")
        return
    
    # 创建子图
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('模型性能比较', 'Dollar Bar分析', 'PAA特征分析', '训练历史'),
        specs=[[{"type": "bar"}, {"type": "scatter"}],
               [{"type": "heatmap"}, {"type": "scatter"}]]
    )
    
    # 模型性能比较
    for threshold_name, results in model_results.items():
        ml_results = results['ml_models']
        models = ml_results['model'].tolist()
        test_auc = ml_results['test_auc'].tolist()
        
        fig.add_trace(
            go.Bar(x=models, y=test_auc, name=f'{threshold_name} AUC'),
            row=1, col=1
        )
    
    # 保存为HTML文件
    output_file = f"claude_reproduce/experiment_dashboard_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.html"
    fig.write_html(output_file)
    print(f"交互式仪表板已保存到: {output_file}")


def generate_summary_report(experiment_results: Dict[str, Any], 
                          save_path: Optional[str] = None) -> str:
    """
    生成实验总结报告
    
    Args:
        experiment_results: 实验结果
        save_path: 保存路径
        
    Returns:
        报告文本
    """
    
    report_lines = []
    report_lines.append("=" * 80)
    report_lines.append("论文复现实验总结报告")
    report_lines.append("=" * 80)
    report_lines.append("")
    
    # Dollar Bar分析
    dollar_bars = experiment_results.get('dollar_bars', {})
    if dollar_bars:
        report_lines.append("1. Dollar Bar分析结果:")
        report_lines.append("-" * 40)
        
        for threshold, data in dollar_bars.items():
            analysis = data.get('analysis', {})
            report_lines.append(f"{threshold.upper()} Dollar Bar:")
            report_lines.append(f"  - 总数: {analysis.get('total_bars', 0)}")
            report_lines.append(f"  - 平均持续时间: {analysis.get('avg_duration_minutes', 0):.1f} 分钟")
            report_lines.append(f"  - 平均成交量: {analysis.get('avg_volume', 0):,.0f}")
            report_lines.append("")
    
    # 模型性能结果
    model_results = experiment_results.get('model_results', {})
    if model_results:
        report_lines.append("2. 模型性能结果:")
        report_lines.append("-" * 40)
        
        for threshold_name, results in model_results.items():
            report_lines.append(f"{threshold_name} 数据集结果:")
            
            # 最佳机器学习模型
            ml_results = results['ml_models']
            best_ml_idx = ml_results['test_auc'].idxmax()
            best_ml_model = ml_results.loc[best_ml_idx]
            
            report_lines.append(f"  最佳传统ML模型: {best_ml_model['model']}")
            report_lines.append(f"    - 测试AUC: {best_ml_model['test_auc']:.4f}")
            report_lines.append(f"    - 测试准确率: {best_ml_model['test_accuracy']:.4f}")
            
            # DNN模型
            dnn_results = results['dnn_model']
            test_metrics = dnn_results['test_metrics']
            report_lines.append(f"  DNN模型:")
            report_lines.append(f"    - 测试AUC: {test_metrics['auc']:.4f}")
            report_lines.append(f"    - 测试准确率: {test_metrics['accuracy']:.4f}")
            report_lines.append("")
    
    # 结论
    report_lines.append("3. 结论:")
    report_lines.append("-" * 40)
    report_lines.append("本实验成功复现了论文中的主要方法，包括:")
    report_lines.append("- Dollar Bar采样方法")
    report_lines.append("- PAA特征提取")
    report_lines.append("- 趋势扫描标签生成")
    report_lines.append("- 多种机器学习模型比较")
    report_lines.append("- 深度神经网络模型")
    report_lines.append("")
    
    report_text = "\n".join(report_lines)
    
    if save_path:
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(report_text)
        print(f"报告已保存到: {save_path}")
    
    return report_text


if __name__ == "__main__":
    print("Utils 模块已加载")
