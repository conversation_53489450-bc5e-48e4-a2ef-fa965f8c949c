"""
主实验脚本 - 论文方法复现
基于论文《Deep neural network model enhanced with data preparation for the directional predictability of multi-stock returns》
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import polars as pl
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from data import full_df  # 从data.py导入数据
from dollar_bar_implementation import DollarBarSampler, DollarBarConfig
from paa_features import PAAFeatureExtractor, PAAConfig
from trend_scanning_labels import TrendScanningLabeler, TrendScanningConfig, FixedTimeHorizonLabeler
from ml_models import create_model_suite, BenchmarkModels, compare_models
from deep_learning_models import DNNTrainer, DNNConfig, create_dnn_configs

import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime


class PaperReproductionExperiment:
    """论文复现实验主类"""
    
    def __init__(self, data_df: pl.DataFrame):
        self.data_df = data_df
        self.results = {}
        self.experiment_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        print(f"实验开始时间: {self.experiment_timestamp}")
        print(f"输入数据形状: {data_df.shape}")
        
    def run_full_experiment(self) -> Dict[str, Any]:
        """运行完整的复现实验"""
        
        print("\n" + "="*60)
        print("开始论文方法复现实验")
        print("="*60)
        
        # 步骤1：创建Dollar Bar
        print("\n步骤1: 创建Dollar Bar...")
        dollar_bars_50m, dollar_bars_200m = self._create_dollar_bars()
        
        # 步骤2：提取PAA特征
        print("\n步骤2: 提取PAA特征...")
        features_50m = self._extract_paa_features(dollar_bars_50m, "50M")
        features_200m = self._extract_paa_features(dollar_bars_200m, "200M")
        
        # 步骤3：生成标签
        print("\n步骤3: 生成趋势扫描标签...")
        labels_50m = self._generate_labels(dollar_bars_50m, "50M")
        labels_200m = self._generate_labels(dollar_bars_200m, "200M")
        
        # 步骤4：准备训练数据
        print("\n步骤4: 准备训练数据...")
        datasets = self._prepare_datasets(features_50m, labels_50m, features_200m, labels_200m)
        
        # 步骤5：训练和评估模型
        print("\n步骤5: 训练和评估模型...")
        model_results = self._train_and_evaluate_models(datasets)
        
        # 步骤6：生成报告
        print("\n步骤6: 生成实验报告...")
        self._generate_report(model_results)
        
        return self.results
    
    def _create_dollar_bars(self) -> Tuple[pl.DataFrame, pl.DataFrame]:
        """创建Dollar Bar"""
        
        config = DollarBarConfig()
        sampler = DollarBarSampler(config)
        
        # 创建50M和200M两种阈值的Dollar Bar
        dollar_bars_50m, dollar_bars_200m = sampler.create_both_thresholds(self.data_df)
        
        # 分析Dollar Bar特性
        analysis_50m = sampler.analyze_dollar_bars(dollar_bars_50m)
        analysis_200m = sampler.analyze_dollar_bars(dollar_bars_200m)
        
        print(f"50M Dollar Bar: {len(dollar_bars_50m)} 条，平均持续时间: {analysis_50m.get('avg_duration_minutes', 0):.1f} 分钟")
        print(f"200M Dollar Bar: {len(dollar_bars_200m)} 条，平均持续时间: {analysis_200m.get('avg_duration_minutes', 0):.1f} 分钟")
        
        self.results['dollar_bars'] = {
            '50m': {'data': dollar_bars_50m, 'analysis': analysis_50m},
            '200m': {'data': dollar_bars_200m, 'analysis': analysis_200m}
        }
        
        return dollar_bars_50m, dollar_bars_200m
    
    def _extract_paa_features(self, dollar_bars: pl.DataFrame, threshold_name: str) -> pl.DataFrame:
        """提取PAA特征"""
        
        if len(dollar_bars) == 0:
            print(f"警告: {threshold_name} Dollar Bar数据为空，跳过特征提取")
            return pl.DataFrame()
        
        config = PAAConfig(window_size=200, segment_size=5)
        extractor = PAAFeatureExtractor(config)
        
        features = extractor.extract_features(dollar_bars)
        
        if len(features) > 0:
            analysis = extractor.analyze_features(features)
            print(f"{threshold_name} PAA特征: {len(features)} 个样本，{analysis.get('n_features', 0)} 个特征")
        else:
            print(f"警告: {threshold_name} PAA特征提取失败")
        
        return features
    
    def _generate_labels(self, dollar_bars: pl.DataFrame, threshold_name: str) -> pl.DataFrame:
        """生成趋势扫描标签"""
        
        if len(dollar_bars) == 0:
            print(f"警告: {threshold_name} Dollar Bar数据为空，跳过标签生成")
            return pl.DataFrame()
        
        # 趋势扫描标签
        tsc_config = TrendScanningConfig(max_lookforward=20, min_lookforward=3)
        tsc_labeler = TrendScanningLabeler(tsc_config)
        tsc_labels = tsc_labeler.generate_labels(dollar_bars)
        
        # 固定时间范围标签（作为对比）
        fth_labeler = FixedTimeHorizonLabeler(horizon=1)
        fth_labels = fth_labeler.generate_labels(dollar_bars)
        
        # 合并标签
        if len(tsc_labels) > 0 and len(fth_labels) > 0:
            labels = tsc_labels.join(fth_labels, on=['open_time', 'code'], how='inner')
            
            # 分析标签分布
            tsc_analysis = tsc_labeler.analyze_labels(tsc_labels)
            print(f"{threshold_name} 趋势扫描标签: {len(labels)} 个，上涨比例: {tsc_analysis.get('upward_ratio', 0):.3f}")
        else:
            labels = pl.DataFrame()
            print(f"警告: {threshold_name} 标签生成失败")
        
        return labels
    
    def _prepare_datasets(self, features_50m: pl.DataFrame, labels_50m: pl.DataFrame,
                         features_200m: pl.DataFrame, labels_200m: pl.DataFrame) -> Dict[str, Dict]:
        """准备训练数据集"""
        
        datasets = {}
        
        # 处理50M数据
        if len(features_50m) > 0 and len(labels_50m) > 0:
            dataset_50m = self._merge_features_labels(features_50m, labels_50m, "50M")
            if dataset_50m is not None:
                datasets['50M'] = dataset_50m
        
        # 处理200M数据
        if len(features_200m) > 0 and len(labels_200m) > 0:
            dataset_200m = self._merge_features_labels(features_200m, labels_200m, "200M")
            if dataset_200m is not None:
                datasets['200M'] = dataset_200m
        
        return datasets
    
    def _merge_features_labels(self, features: pl.DataFrame, labels: pl.DataFrame, 
                              threshold_name: str) -> Dict[str, np.ndarray]:
        """合并特征和标签"""
        
        # 合并特征和标签
        merged = features.join(labels, on=['open_time', 'code'], how='inner')
        
        if len(merged) == 0:
            print(f"警告: {threshold_name} 特征和标签合并后为空")
            return None
        
        # 提取特征矩阵
        feature_cols = [col for col in merged.columns if col.startswith('paa_feature_')]
        if len(feature_cols) == 0:
            print(f"警告: {threshold_name} 没有找到PAA特征列")
            return None
        
        X = merged.select(feature_cols).to_numpy()
        y_tsc = merged['tsc_label'].to_numpy()
        y_fth = merged['fth_label'].to_numpy() if 'fth_label' in merged.columns else y_tsc
        
        # 检查数据质量
        if np.isnan(X).any():
            print(f"警告: {threshold_name} 特征中包含NaN值，进行填充")
            X = np.nan_to_num(X, nan=0.0)
        
        print(f"{threshold_name} 数据集: {X.shape[0]} 个样本，{X.shape[1]} 个特征")
        print(f"{threshold_name} 标签分布 - TSC: {y_tsc.mean():.3f}, FTH: {y_fth.mean():.3f}")
        
        return {
            'X': X,
            'y_tsc': y_tsc,
            'y_fth': y_fth,
            'feature_names': feature_cols,
            'merged_data': merged
        }
    
    def _train_and_evaluate_models(self, datasets: Dict[str, Dict]) -> Dict[str, Any]:
        """训练和评估所有模型"""
        
        results = {}
        
        for threshold_name, dataset in datasets.items():
            print(f"\n训练 {threshold_name} 数据集的模型...")
            
            X = dataset['X']
            y = dataset['y_tsc']  # 使用趋势扫描标签
            
            # 划分训练测试集（80:20）
            split_idx = int(0.8 * len(X))
            X_train, X_test = X[:split_idx], X[split_idx:]
            y_train, y_test = y[:split_idx], y[split_idx:]
            
            # 进一步划分训练验证集（80:20）
            val_split_idx = int(0.8 * len(X_train))
            X_train_final, X_val = X_train[:val_split_idx], X_train[val_split_idx:]
            y_train_final, y_val = y_train[:val_split_idx], y_train[val_split_idx:]
            
            print(f"训练集: {X_train_final.shape}, 验证集: {X_val.shape}, 测试集: {X_test.shape}")
            
            # 训练传统机器学习模型
            ml_results = self._train_ml_models(X_train_final, y_train_final, X_test, y_test)
            
            # 训练深度学习模型
            dnn_results = self._train_dnn_model(X_train_final, y_train_final, X_val, y_val, X_test, y_test)
            
            results[threshold_name] = {
                'ml_models': ml_results,
                'dnn_model': dnn_results,
                'data_info': {
                    'train_size': len(X_train_final),
                    'val_size': len(X_val),
                    'test_size': len(X_test),
                    'n_features': X.shape[1]
                }
            }
        
        self.results['model_results'] = results
        return results
    
    def _train_ml_models(self, X_train: np.ndarray, y_train: np.ndarray,
                        X_test: np.ndarray, y_test: np.ndarray) -> pd.DataFrame:
        """训练传统机器学习模型"""
        
        # 创建模型套件
        models = create_model_suite()
        
        # 比较模型性能
        results_df = compare_models(models, X_train, y_train, X_test, y_test)
        
        # 训练基准模型
        benchmark = BenchmarkModels()
        benchmark_results = benchmark.fit_all(X_train, y_train)
        benchmark_eval = benchmark.evaluate_all(X_test, y_test)
        
        # 添加基准模型结果
        for name, metrics in benchmark_eval.items():
            new_row = {
                'model': name,
                'train_accuracy': 0.0,  # 基准模型没有单独的训练评估
                'train_precision': 0.0,
                'train_recall': 0.0,
                'train_f1': 0.0,
                'train_auc': 0.0,
                'test_accuracy': metrics['accuracy'],
                'test_precision': metrics['precision'],
                'test_recall': metrics['recall'],
                'test_f1': metrics['f1_score'],
                'test_auc': metrics['auc']
            }
            results_df = pd.concat([results_df, pd.DataFrame([new_row])], ignore_index=True)
        
        return results_df
    
    def _train_dnn_model(self, X_train: np.ndarray, y_train: np.ndarray,
                        X_val: np.ndarray, y_val: np.ndarray,
                        X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, Any]:
        """训练深度神经网络模型"""
        
        # 创建DNN配置
        config = DNNConfig(
            input_size=X_train.shape[1],
            hidden_layers=6,
            activation='swish',
            learning_rate=0.001,
            batch_size=128,
            max_epochs=200
        )
        
        # 训练模型
        trainer = DNNTrainer(config)
        training_history = trainer.train(X_train, y_train, X_val, y_val)
        
        # 评估模型
        train_metrics = trainer.evaluate(X_train, y_train)
        val_metrics = trainer.evaluate(X_val, y_val)
        test_metrics = trainer.evaluate(X_test, y_test)
        
        return {
            'config': config,
            'training_history': training_history,
            'train_metrics': train_metrics,
            'val_metrics': val_metrics,
            'test_metrics': test_metrics
        }
    
    def _generate_report(self, model_results: Dict[str, Any]):
        """生成实验报告"""
        
        print("\n" + "="*60)
        print("实验结果报告")
        print("="*60)
        
        for threshold_name, results in model_results.items():
            print(f"\n{threshold_name} Dollar Bar 结果:")
            print("-" * 40)
            
            # 机器学习模型结果
            ml_results = results['ml_models']
            print("\n传统机器学习模型:")
            print(ml_results[['model', 'test_accuracy', 'test_precision', 'test_recall', 'test_f1', 'test_auc']].round(4))
            
            # DNN模型结果
            dnn_results = results['dnn_model']
            test_metrics = dnn_results['test_metrics']
            print(f"\n深度神经网络模型:")
            print(f"测试准确率: {test_metrics['accuracy']:.4f}")
            print(f"测试精确率: {test_metrics['precision']:.4f}")
            print(f"测试召回率: {test_metrics['recall']:.4f}")
            print(f"测试F1分数: {test_metrics['f1_score']:.4f}")
            print(f"测试AUC: {test_metrics['auc']:.4f}")


def main():
    """主函数"""
    
    # 检查数据是否可用
    try:
        from data import full_df
        print(f"成功加载数据，形状: {full_df.shape}")
    except ImportError as e:
        print(f"无法导入数据: {e}")
        print("请确保先运行 data.py 文件加载数据")
        return
    
    # 创建实验实例
    experiment = PaperReproductionExperiment(full_df)
    
    # 运行实验
    try:
        results = experiment.run_full_experiment()
        print("\n实验完成！")
        
    except Exception as e:
        print(f"\n实验过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
