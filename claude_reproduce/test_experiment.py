"""
简化的实验测试脚本
用于快速测试论文复现的各个模块
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import polars as pl
import pandas as pd
import numpy as np
from datetime import datetime

# 导入自定义模块
from data import full_df
from dollar_bar_implementation import DollarBarSampler, DollarBarConfig
from paa_features import PAAFeatureExtractor, PAAConfig
from trend_scanning_labels import TrendScanningLabeler, TrendScanningConfig
from ml_models import LightGBMModel, XGBoostModel, LinearDiscriminantModel


def test_dollar_bar():
    """测试Dollar Bar实现"""
    print("\n" + "="*50)
    print("测试 Dollar Bar 实现")
    print("="*50)
    
    # 使用较小的数据集进行测试
    test_df = full_df.head(50000)  # 使用5万条数据
    print(f"测试数据大小: {len(test_df)} 条")
    
    config = DollarBarConfig()
    sampler = DollarBarSampler(config)
    
    # 创建Dollar Bar
    dollar_bars_50m, dollar_bars_200m = sampler.create_both_thresholds(test_df)
    
    print(f"50M Dollar Bar: {len(dollar_bars_50m)} 条")
    print(f"200M Dollar Bar: {len(dollar_bars_200m)} 条")
    
    if len(dollar_bars_50m) > 0:
        analysis = sampler.analyze_dollar_bars(dollar_bars_50m)
        print(f"50M平均持续时间: {analysis.get('avg_duration_minutes', 0):.1f} 分钟")
    
    return dollar_bars_50m, dollar_bars_200m


def test_paa_features(dollar_bars):
    """测试PAA特征提取"""
    print("\n" + "="*50)
    print("测试 PAA 特征提取")
    print("="*50)
    
    if len(dollar_bars) == 0:
        print("Dollar Bar数据为空，跳过PAA测试")
        return pl.DataFrame()
    
    # 使用较小的窗口进行测试
    config = PAAConfig(window_size=50, segment_size=5)  # 减小窗口大小
    extractor = PAAFeatureExtractor(config)
    
    features = extractor.extract_features(dollar_bars)
    
    if len(features) > 0:
        print(f"提取了 {len(features)} 个特征向量")
        print(f"特征维度: {extractor.feature_dim}")
        
        # 显示特征样本
        feature_cols = [col for col in features.columns if col.startswith('paa_feature_')]
        print(f"特征列数: {len(feature_cols)}")
    else:
        print("PAA特征提取失败")
    
    return features


def test_trend_scanning_labels(dollar_bars):
    """测试趋势扫描标签"""
    print("\n" + "="*50)
    print("测试 趋势扫描标签")
    print("="*50)
    
    if len(dollar_bars) == 0:
        print("Dollar Bar数据为空，跳过标签测试")
        return pl.DataFrame()
    
    config = TrendScanningConfig(max_lookforward=10, min_lookforward=3)  # 减小前瞻窗口
    labeler = TrendScanningLabeler(config)
    
    labels = labeler.generate_labels(dollar_bars)
    
    if len(labels) > 0:
        print(f"生成了 {len(labels)} 个标签")
        analysis = labeler.analyze_labels(labels)
        print(f"上涨标签比例: {analysis.get('upward_ratio', 0):.3f}")
    else:
        print("趋势扫描标签生成失败")
    
    return labels


def test_ml_models(features, labels):
    """测试机器学习模型"""
    print("\n" + "="*50)
    print("测试 机器学习模型")
    print("="*50)
    
    if len(features) == 0 or len(labels) == 0:
        print("特征或标签数据为空，跳过模型测试")
        return
    
    # 合并特征和标签
    merged = features.join(labels, on=['open_time', 'code'], how='inner')
    
    if len(merged) == 0:
        print("特征和标签合并后为空")
        return
    
    print(f"合并后数据: {len(merged)} 条")
    
    # 提取特征矩阵和标签
    feature_cols = [col for col in merged.columns if col.startswith('paa_feature_')]
    if len(feature_cols) == 0:
        print("没有找到PAA特征列")
        return
    
    X = merged.select(feature_cols).to_numpy()
    y = merged['tsc_label'].to_numpy()
    
    print(f"特征矩阵形状: {X.shape}")
    print(f"标签分布: {y.mean():.3f}")
    
    # 检查数据质量
    if np.isnan(X).any():
        print("特征中包含NaN值，进行填充")
        X = np.nan_to_num(X, nan=0.0)
    
    # 划分训练测试集
    split_idx = int(0.8 * len(X))
    X_train, X_test = X[:split_idx], X[split_idx:]
    y_train, y_test = y[:split_idx], y[split_idx:]
    
    print(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
    
    # 测试LightGBM
    try:
        print("\n测试 LightGBM...")
        lgb_model = LightGBMModel()
        lgb_model.config.use_grid_search = False  # 跳过网格搜索以节省时间
        lgb_model.fit(X_train, y_train)
        lgb_metrics = lgb_model.evaluate(X_test, y_test)
        print(f"LightGBM AUC: {lgb_metrics['auc']:.4f}")
    except Exception as e:
        print(f"LightGBM测试失败: {e}")
    
    # 测试XGBoost
    try:
        print("\n测试 XGBoost...")
        xgb_model = XGBoostModel()
        xgb_model.config.use_grid_search = False  # 跳过网格搜索以节省时间
        xgb_model.fit(X_train, y_train)
        xgb_metrics = xgb_model.evaluate(X_test, y_test)
        print(f"XGBoost AUC: {xgb_metrics['auc']:.4f}")
    except Exception as e:
        print(f"XGBoost测试失败: {e}")
    
    # 测试线性判别分析
    try:
        print("\n测试 线性判别分析...")
        lda_model = LinearDiscriminantModel()
        lda_model.fit(X_train, y_train)
        lda_metrics = lda_model.evaluate(X_test, y_test)
        print(f"LDA AUC: {lda_metrics['auc']:.4f}")
    except Exception as e:
        print(f"LDA测试失败: {e}")


def main():
    """主测试函数"""
    print("开始论文复现模块测试")
    print(f"数据总量: {len(full_df)} 条")
    print(f"测试时间: {datetime.now()}")
    
    try:
        # 步骤1: 测试Dollar Bar
        dollar_bars_50m, dollar_bars_200m = test_dollar_bar()
        
        # 选择50M数据进行后续测试
        dollar_bars = dollar_bars_50m
        
        # 步骤2: 测试PAA特征提取
        features = test_paa_features(dollar_bars)
        
        # 步骤3: 测试趋势扫描标签
        labels = test_trend_scanning_labels(dollar_bars)
        
        # 步骤4: 测试机器学习模型
        test_ml_models(features, labels)
        
        print("\n" + "="*50)
        print("所有模块测试完成！")
        print("="*50)
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
