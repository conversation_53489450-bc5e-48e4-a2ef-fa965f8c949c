# **复现《Deep neural network model enhanced with data preparation for the directional predictability of multi-stock returns》论文预测方法的详细计划**

## **1\. 预测框架概述**

### **1.1. 论文核心目标与方法论**

本报告旨在为其他研究人员或自动化代理提供一个详细的计划，以复现论文《Deep neural network model enhanced with data preparation for the directional predictability of multi-stock returns》中提出的股票收益方向性预测方法 1。该论文的核心目标是通过整合一套全面的数据准备流程来增强深度神经网络（DNNs）在预测股票收益方向上的表现 1。关键组成部分包括美元K线（dollar bar）采样、趋势扫描（trend scanning）标签以及分段聚合近似（Piecewise Aggregate Approximation, PAA）特征提取，这些都服务于最终的DNN模型 1。

该研究的创新之处并非仅仅采用这些技术，更在于将这些特定的数据准备技术针对金融时间序列的特性进行整合与优化，以期提升DNN模型的预测准确性 1。论文明确指出，其研究旨在弥补现有研究往往忽视针对性数据准备技术重要性的空白 1。因此，复现工作的核心在于精确理解和执行每一个数据准备步骤，并体会其对整体模型性能的贡献。

### **1.2. 数据准备的重要性**

论文作者将数据准备置于极高的战略地位，反复强调其是构建稳健金融预测模型的基石 1。文中明确指出，“金融预测模型的质量与数据质量密切相关，使得数据准备成为此项任务中一个重要的基础方面” 1，并且“本研究通过强调数据准备在最大化DNNs用于股票市场预测效果方面的关键作用而进一步区别于其他研究” 1。

这种对数据准备的侧重源于金融数据固有的高噪声、非平稳以及易受多种因素影响的特性。通过精心设计的数据准备流程，如美元K线采样旨在降低市场噪音、趋势扫描标签法旨在动态捕捉市场趋势、PAA旨在高效提取特征，该方法试图为DNN模型提供更高质量、更具信息量的输入，从而减轻过拟合风险，提升模型对真实市场动态的学习能力和泛化能力 1。

### **1.3. 复现范围**

本计划的目标是提供一个清晰、可执行的步骤指南，帮助其他研究人员或代理严格复现论文中提出的方法论，特别是针对表现最佳的美元K线结合趋势扫描标签（DB-TSC）的模型配置。成功的复现不仅应能在技术上实现各个模块，更理想的是能够获得与原论文报告相似的性能指标。

论文的核心假设是，经过优化的数据准备流程（美元K线减少噪音、趋势扫描动态打标、PAA高效特征提取）能够显著提升DNN模型的性能（如准确率和泛化能力）1。本复现计划将严格遵循论文的细节，以验证这一核心假设。若能成功复现，该方法论有望为金融市场，特别是高波动性市场中的短期方向性预测提供一个更为稳健和有效的框架，这对于量化交易策略的开发具有重要意义 1。

## **2\. 数据采集与初始设置**

### **2.1. 数据来源与特征**

复现工作的第一步是获取与原始研究一致的基础数据。

* **数据来源**: Barchart.com Inc. ([www.barchart.com](https://www.barchart.com)) 1。  
* **标的资产 (股票代码)**: 选取美国领先的科技股，包括苹果 (AAPL)、谷歌 (GOOGL)、英特尔 (INTC) 和微软 (MSFT) 1。这些高流动性、高关注度的股票通常交易活跃，其价格行为更能体现复杂市场动态，也更能检验数据降噪技术的有效性。  
* **时间范围**: 2012年1月3日至2022年12月30日 1。这一长达11年的时间跨度覆盖了多种市场状况。  
* **原始数据频率**: 1分钟 1。这种高频数据是构建美元K线和进行细致PAA特征提取的基础，能够更精确地捕捉市场微观结构和交易活动。  
* **数据字段**: 必须包含开盘价 (Opening)、收盘价 (Closing)、最高价 (High)、最低价 (Low) 以及交易量 (Trading volumes) 1。  
* **数据集规模**: 原始1分钟数据每只股票约有 1,074,705 条记录 1。如此大规模的数据集为后续的统计分析和深度学习模型训练提供了坚实的基础。

### **2.2. 初始数据处理**

获取数据后，建议进行以下初步处理：

* **数据获取**: 根据Barchart.com Inc.提供的方式（如API或历史数据下载服务）获取指定时间范围和频率的数据。  
* **数据结构化**: 推荐使用如Python Pandas库中的DataFrame结构，为每只股票单独存储和处理数据。  
* **基础数据清洗**:  
  * 检查并处理缺失值（尽管论文未明确提及此问题，但这是标准实践）。  
  * 验证时间戳的连续性和一致性。  
  * 确保价格和交易量字段的数据类型正确无误。

论文假设能够从Barchart.com获取到高质量、高粒度的原始数据。复现方必须确保所用数据的质量和特征与原始研究尽可能一致，任何显著差异都可能影响最终结果的可比性。

**表 2.1: 数据规格摘要**

| 属性 | 规格 |
| :---- | :---- |
| 数据来源 | Barchart.com Inc. ([www.barchart.com](https://www.barchart.com)) |
| 股票代码 | AAPL, GOOGL, INTC, MSFT |
| 开始日期 | 2012年1月3日 |
| 结束日期 | 2022年12月30日 |
| 原始数据频率 | 1分钟 |
| 必需字段 | 开盘价, 收盘价, 最高价, 最低价, 交易量 |

此表格为复现工作提供了清晰的数据起点，确保所有后续步骤都建立在正确的数据基础之上。

## **3\. 详细数据准备工作流**

数据准备是该论文方法论的核心，包含数据采样、特征工程和标签生成三个关键阶段。这些步骤的设计旨在将原始嘈杂的金融时间序列数据转化为适合DNN模型学习的结构化输入。

### **3.1. 数据采样实现**

论文对比了传统的时间K线采样和更为先进的美元K线采样方法。

#### **3.1.1. 时间K线 (Time Bar) 采样**

* **实现**: 从1分钟原始数据生成15分钟和1小时两种时间间隔的K线 1。  
* **聚合逻辑**: 对于每个时间K线周期：  
  * 开盘价 (Open): 周期内第一笔1分钟数据的开盘价。  
  * 最高价 (High): 周期内所有1分钟数据的最高价的最大值。  
  * 最低价 (Low): 周期内所有1分钟数据的最低价的最小值。  
  * 收盘价 (Close): 周期内最后一笔1分钟数据的收盘价。  
  * 交易量 (Volume): 周期内所有1分钟数据的交易量总和。

#### **3.1.2. 美元K线 (Dollar Bar) 采样**

美元K线采样基于累计成交额（CUSUM \- Cumulative Sum）1。

* **实现**: 当累计成交额（每分钟收盘价 \* 每分钟交易量）达到预设阈值时，形成一个新的美元K线 1。论文中使用的阈值为5000万美元 (50M) 和2亿美元 (200M) 1。  
* **CUSUM逻辑**: 遍历1分钟数据，持续累加 收盘价 \* 交易量。一旦累计值达到或超过阈值，则记录一条美元K线，并重置累加器。该美元K线的开盘价、最高价、最低价、收盘价和交易量对应于成交额累积期间的相应值。  
* **理论依据**: 美元K线旨在根据市场活动（信息流）而非固定的时钟时间进行采样。"美元K线提供了对市场活动更准确和平衡的表述……减少了低交易活动时期的影响，并最小化了高波动时期价格运动的扭曲" 1。论文中的图1直观展示了美元K线采样能够使价格分布更接近正态分布，这被认为对DNN模型训练有利，因为DNN通常在处理近似正态分布的数据时表现更佳 1。这种分布特性有助于模型更好地学习模式，减少过拟合，并提高预测准确性 1。

### **3.2. 基于分段聚合近似 (PAA) 的特征工程**

PAA用于从采样后的K线数据中提取特征，旨在降低数据维度，同时保留关键信息并确保特征集大小的一致性 1。

#### **3.2.1. PAA概念**

PAA通过将时间序列（此处为K线序列）分割成等长的片段，并用每个片段的均值（或其他统计量）来代表该片段 1。论文扩展了传统PAA，不仅使用均值，还计算了每个片段的标准差和斜率，以提供更丰富的特征描述 1。

#### **3.2.2. 滑动窗口与分段**

* 定义一个滑动窗口 W（回顾的K线条数）和一个片段大小 S（每个片段包含的K线条数）1。  
* 论文中给出的示例是 |W|=200 根K线，|S|=5 根K线/片段，从而产生 W/S \= 40 个片段 1。最终模型使用的确切 W 和 S 值可能因不同配置而异，但此示例提供了重要指导。特征总数将是 (W/S) \* 3（因为每个片段提取3个统计特征）。

#### **3.2.3. 特征缩放 (公式 1\)**

在计算PAA统计特征之前，对每个滑动窗口 W 内的数据（例如收盘价）进行缩放 1。

* 缩放公式: Xi,scaled​=\[max(W)−min(W)Xi​−min(W)​\](b−a)+a 1。  
* 其中 Xi​ 是窗口内第 i 个K线的数据点， min(W) 和 max(W) 是当前窗口 W 内该数据序列的最小值和最大值。缩放区间 \[a, b\] 在本研究中设为 \`\` 1。  
* **理论依据**: "股价运动缺乏固定中心……每只股票的价格水平处于不同的价格区间" 1。缩放操作旨在标准化这些价格水平的差异，使模型能够关注相对变化而非绝对值。

#### **3.2.4. 统计特征计算**

对于缩放后窗口内的每个片段，计算以下三个统计量：

1. **均值 (Mean)**  
2. **标准差 (Standard Deviation, std)**：衡量片段内价格波动性。  
3. **斜率 (Slope)**：例如，通过对片段内缩放后的数据点进行线性回归拟合，取时间变量的系数作为斜率，代表局部趋势。 这三个值构成了该片段的特征 1。这种包含均值、波动和趋势的特征组合，比仅使用均值的传统PAA能更全面地刻画片段内的价格动态 1。

#### **3.2.5. 构建特征向量**

将窗口内所有片段的 \[均值, 标准差, 斜率\] 特征按顺序连接起来，形成该时间点（即窗口末端K线对应的时间点）的最终特征向量 1。例如，若有40个片段，则特征向量维度为 40×3=120 1。论文图3展示了这种特征向量的示例 1。

### **3.3. 标签生成技术**

论文采用了两种标签生成方法：固定时间范围标签和趋势扫描标签。

#### **3.3.1. 固定时间范围 (Fixed Time Horizon, FTH) 标签**

* **实现**: "传统上，标签依赖于固定时间范围方法，即从采样点开始，延伸到下一个周期，通过比较初始价格和最终价格来计算回报，并据此打标" 1。  
* 标签是二元的（例如，如果 未来某固定时间点的价格 \> 当前价格，则为1，否则为0）。论文未明确FTH的具体“下一周期”长度，复现时应选择一个固定且合理的长度（例如，下一根K线的收盘价）。

#### **3.3.2. 趋势扫描 (Trend Scanning, TSC) 标签 (Prado, 2018\)**

这是论文中更受青睐的复杂方法，旨在基于前瞻窗口内最显著的趋势进行打标 1。

* **核心思想**: 对从时间 t 到 t+∣L∣max​（∣L∣max​ 为最大观察窗口）的一系列价格观测值 {Xt​}，拟合多条不同长度 l（l 从一个最小值到 ∣L∣max​）的线性回归：Xt+l​=β0​+β1​l+ϵt+l​ 1。其中，对于已重新缩放的价格区间，β0​（截距项）设为0 1。  
* **斜率系数 β1​ (公式 3\)**: β1​=N∑l=0n​l2−(∑l=0n​l)2N∑l=0n​lyl​−∑l=0n​l∑l=0n​yl​​，其中 yl​ 是前瞻 l 步的价格，N 是该特定回归中的数据点数 1。  
* **斜率标准误 σβ1​​**: 论文公式4给出了残差方差 σϵ2​=N(N−2)\[N∑l=0n​yl2​−(∑l=0n​yl​)2\]−β12​\[N∑l=0n​l2−(∑l=0n​l)2\]​ 1。通常，β1​ 的标准误 SE(β1​) 可以从 σϵ2​ 导出。  
* **T值计算 (公式 5\)**: tβ1​​=σc2​β1​​ 1。这里的 σc2​ 需要仔细解读。标准的T统计量是系数除以其标准误，即 t=β1​/SE(β1​)。如果 σc2​ 代表 SE(β1​)2（即 β1​ 的方差），那么公式是合理的。复现时，如果结果不符，可能需要测试将 σc2​ 理解为 SE(β1​) 或 SE(β1​)2。  
* **标签确定**: 根据产生最高绝对T值的回归线的斜率 β1​ 的符号来确定标签（上涨或下跌）。  
* **前瞻窗口 ∣L∣ 的约束条件** 1:  
  1. ∣L∣≥3：确保回归和波动率计算（公式4分母为 N−2）有足够的自由度。  
  2. ∣L∣ 理想情况下应至少是美元K线平均持续时间（或等效信息量）的3倍：这确保标签的确定考虑了相对于采样频率而言足够长的未来信息。这一约束体现了数据准备步骤间的内在联系和精心设计。  
  3. ∣L∣ 不应超过特征准备窗口 W：防止标签的确定使用了特征提取时尚未观测到的未来数据，从而避免前瞻偏差。  
* 论文图4形象地展示了此过程：评估多条趋势线，T值最高的趋势线决定了最终标签 1。

这种美元K线（按信息流采样）与趋势扫描（按统计显著趋势打标）的组合，其目的是创建一个特征和标签与真实市场动态（而非固定的时钟周期）更内在同步的数据集。

**表 3.1: 数据准备参数概览**

| 阶段 | 方法/参数 | 描述/公式/约束 |
| :---- | :---- | :---- |
| **采样** | 时间K线 | 15分钟, 1小时间隔；标准OHLCV聚合 |
|  | 美元K线 | $50M, $200M 成交额阈值；CUSUM逻辑 |
| **特征提取 (PAA)** | 滑动窗口 W | 回顾K线条数 (例: 200\) |
|  | 片段大小 S | 每片段K线条数 (例: 5\) |
|  | 特征缩放 | Xscaled​=\[max(W)−min(W)X−min(W)​\](1−0)+0 |
|  | 每片段特征 | 均值, 标准差, 斜率 |
| **标签生成** | 固定时间范围 (FTH) | 基于未来固定周期价格变化 |
|  | 趋势扫描 (TSC) | 公式 2-5；tβ1​​=β1​/σc2​；选择最大 $\\$ |
|  | TSC前瞻窗口 L | L≥3; L≥3×美元K线等效时长; L≤W |

## **4\. 深度神经网络 (DNN) 模型构建**

在数据准备完成之后，下一步是构建深度神经网络模型。论文中的DNN架构设计旨在处理PAA提取的高维特征，并进行二分类预测（股价上涨或下跌）。

### **4.1. 定义DNN架构**

参考论文第3节和图5 1，DNN模型结构如下：

* **输入层 (Input Layer)**: 包含 N 个特征，N 是通过PAA方法从时间序列数据中提取的特征总数（例如，在前述 W=200, S=5 的例子中，N=120）1。  
* **隐藏层 (Hidden Layers)**:  
  * 隐藏层数量 (Number of Hidden Layers, NHL): 这是一个超参数，从集合 中选择 1。  
  * 每层神经元数量: 2N，即输入特征数量的两倍 1。这是一个特定的架构选择，意味着网络的宽度随输入特征维度动态调整。  
* **激活函数 (Activation Functions)**:  
  * 隐藏层: 可选用 ReLU、Tanh 或 Swish 1。论文特别提到，对于使用美元K线数据的模型，Swish激活函数往往出现在表现最佳的模型配置中（见表3-6）1。这可能暗示了美元K线处理后的数据特性与Swish函数的平滑、非单调等特性之间存在某种协同效应。  
  * 输出层: Sigmoid 函数（用于二分类和BCE损失函数）。  
* **输出层 (Output Layer)**:  
  * 包含一个神经元，使用Sigmoid激活函数，输出一个0到1之间的值，代表股价预测为上涨的概率 1。

### **4.2. 配置模型编译**

参考论文第3节 1，模型编译设置如下：

* **损失函数 (Loss Function)**: 二元交叉熵 (Binary Cross Entropy, BCE) 1。论文中的公式8为 Z=Nbatch​α​∑i=1Nbatch​​\[yi​log(y^​i​)+(1−yi​)log(1−y^​i​)\] 1。这里，α 被描述为学习率，而 Nbatch​ (原文为N) 指批次大小。通常学习率是优化器的一部分，而不是直接出现在损失函数公式中。复现时，应使用标准的BCE损失，并将学习率 α 应用于优化器。  
* **优化器 (Optimizer)**: Adam 优化器 1。  
* **正则化 (Regularization)**: 在训练过程中应用 Dropout，丢弃率为20%，以减轻过拟合 1。论文提到“最佳丢弃率是通过经验评估确定的……选择了20%的比率” 1。这直接有助于提升模型在未见数据上的泛化能力。

**表 4.1: DNN架构与编译规格**

| 组件 | 规格 |
| :---- | :---- |
| 输入层大小 | N (PAA特征数) |
| 隐藏层配置 | NHL ∈ ; 每层 2N 个神经元 |
| 隐藏层激活函数 | ReLU, Tanh, Swish (Swish 常用于DB模型) |
| 输出层 | 1个神经元, Sigmoid激活 |
| 损失函数 | 二元交叉熵 (BCE) |
| 优化器 | Adam |
| Dropout率 | 20% |

此表格为复现DNN模型提供了清晰的蓝图，确保模型结构和训练设置与论文保持一致。

## **5\. 综合训练、验证与模型选择协议**

论文采用了一套非常严格的训练和验证流程，以确保模型的稳健性和泛化能力。这套流程结合了前向测试、交叉验证和自定义早停机制。

### **5.1. 实现前向测试 (Walk-Forward Testing) 框架**

参考论文第3.3节、第4节和图8 1。

* **数据划分策略**:  
  * 将整个数据集（2012-2022年）划分为5个重叠的周期，每个周期跨度为7年 1。例如：WF1: 2012-2018, WF2: 2013-2019,..., WF5: 2016-2022 1。  
  * 在每个7年周期内，数据按时间顺序划分为：60% 训练集 (Training)，20% 验证集 (Validation)，20% 测试集 (Test) 1。  
* **流程**:  
  1. 在当前周期的60%训练集上训练模型。  
  2. 使用20%验证集进行超参数调整（通过交叉验证）和早停判断。  
  3. 在选定最佳模型后，于当前周期的20%测试集上评估其最终性能。  
  4. 对所有5个前向测试周期重复此过程。  
* **理论依据**: 这种方法“确保在训练和测试数据上均有良好表现” 1，并且能够“模拟真实世界的交易场景” 1，比简单的静态训练-测试划分更能评估模型在动态市场环境下的表现。

### **5.2. 超参数优化策略**

参考论文表2和第3.3节 1。

* **网格搜索 (Grid Search)**: 采用网格搜索方法系统地探索超参数组合 1。  
* **搜索空间 (源自表2)** 1:  
  * 学习率 (α): \[0.01, 0.001, 0.0001\]  
  * 最大训练轮数 (Epochs for early stopping):  
  * 隐藏层数量 (NHL):  
  * 批次大小 (Batch Size):  
  * 激活函数:  
* **5折交叉验证 (5-Fold Cross-Validation)**:  
  * 论文明确指出“在超参数调整过程中采用了5折交叉验证” 1。这应用于每个前向测试周期的 **60%训练集内部**，目的是为该周期的验证集选择最佳的超参数组合。  
  * 交叉验证中的模型选择标准是“在所有交叉验证折叠中获得最佳平均性能” 1。  
  * 选定超参数后，使用这些参数在完整的60%训练集上重新训练模型，并利用20%验证集进行早停。

### **5.3. 实现自定义早停机制 (算法1, 公式9 & 10, 图6)**

参考论文第3.3节和算法1 1。

* **目标**: “防止过拟合，并增加获得最佳性能模型的机会” 1。  
* **算法1逻辑概述** 1:  
  * 初始化 min\_diff \= infinity (最小训练-验证损失差)，min\_valid\_loss \= infinity (最小验证损失)，best\_epoch \= 0，wait \= 0 (等待轮数)。  
  * 逐轮训练模型。  
  * 从 check\_start\_epoch 轮开始监控：  
    * **条件1 (公式9)**: 如果当前 abs(训练损失 \- 验证损失) \< min\_diff，则更新 min\_diff 和 best\_epoch，重置 wait。目标是 min∣Ltrain​−Lvalid​∣ 1。  
    * **条件2 (公式10)**: 如果当前 验证损失 \< min\_valid\_loss，则更新 min\_valid\_loss 和 best\_epoch，重置 wait。目标是 min∣Lvalid​∣ 1。  
    * 如果当前轮数 epoch \> best\_epoch \+ current\_threshold（current\_threshold 会动态增加），则 wait 加1。  
    * 如果 wait \>= patience (预设的耐心轮数)，则 stop\_flag \= True，停止训练。  
  * **算法1的参数**: total\_epochs (最大轮数), threshold\_start (初始阈值), threshold\_increment (阈值增量因子), check\_start\_epoch (开始检查的轮数), patience (耐心轮数) 1。论文未在表2中提供这些参数的具体值，复现时可能需要根据经验设定（例如，patience=10-20轮，check\_start\_epoch=总轮数的10-20%）。  
* 论文图6展示了基于这些标准进行模型选择的损失曲线示例 1。

这种结合前向测试、内部交叉验证调参和自定义早停的验证策略非常严谨，是论文的一个显著优点。然而，这也意味着巨大的计算开销：每个前向测试周期都需要执行一次包含5折交叉验证的完整网格搜索。复现者需要为此准备充足的计算资源。

**表 5.1: 训练与验证配置**

| 方面 | 配置/参数 |
| :---- | :---- |
| **前向测试** | 5个周期, 每个周期7年数据, 周期之间重叠 |
|  | 每个周期内数据划分: 60%训练, 20%验证, 20%测试 |
| **超参数优化** | 网格搜索: 学习率 (\[0.01, 0.001, 0.0001\]), Epochs (\[100-500\]), NHL (), Batch (), Act. () |
|  | 在每个前向周期的训练集(60%)上进行5折交叉验证 |
| **早停机制** | 自定义算法1: 监控 $min\\$ |
|  | 参数: total\_epochs, threshold\_start, threshold\_increment, check\_start\_epoch, patience (需设定合理值) |

## **6\. 设置基准模型以供比较**

为了评估所提出的DNN模型的性能，论文引入了一系列机器学习模型作为基准 1。这些基准模型为DNN的预测能力提供了一个参照系。

### **6.1. 基准模型套件**

根据论文第3.1节 1，基准模型包括：

* **决策树 (Decision Tree)**  
* **随机森林 (Random Forest)**  
* **支持向量机 (Support Vector Machine, SVM)**  
* **XGBoost (Extreme Gradient Boosting)**

### **6.2. 实现指南**

* **库选择**: 建议使用广泛应用的Python库，如Scikit-learn（用于决策树、随机森林、SVM）和XGBoost官方库。  
* **输入数据**: 为了确保比较的公平性，基准模型应使用与DNN模型完全相同的输入数据，即经过特定数据准备流程（例如，美元K线采样、PAA特征提取、趋势扫描标签）处理后的特征和标签。论文中的表格11-14将DB-TSC DNN模型与基准模型进行比较，这意味着基准模型也使用了DB-TSC方法准备的数据 1。  
* **超参数**: 论文没有详细说明基准模型的超参数调整过程。在复现时，可以考虑以下两种策略：  
  1. 使用这些库中对应模型的默认参数。  
  2. 为每个基准模型执行一个基础的超参数搜索（例如，小范围的网格搜索或随机搜索），以确保它们得到合理的优化，尽管可能不如DNN那样详尽。 这样做是为了避免因基准模型配置不当而夸大DNN模型的优势。

基准模型的引入，其目的是“提供一个基准，用以评估我们提出的DNNs的性能” 1。如果DNN模型显著优于这些成熟的基准模型，则更能证明其方法的有效性。论文结果（表11-14）显示DB-TSC DNNs优于基准模型，这可能归因于DNN架构更善于利用经过复杂预处理的数据，或者基准模型处理高维PAA特征的能力相对较弱，或两者兼而有之 1。

## **7\. 执行与性能评估**

在模型训练和验证完成后，需要对模型的性能进行全面的评估，并与论文报告的结果进行比较。

### **7.1. 性能指标**

根据论文第4.1和4.2节 1，对每个前向测试周期的训练集和测试集，收集以下性能指标：

* **加权平均精确率 (Weighted average Precision)**  
* **加权平均召回率 (Weighted average Recall)**  
* **加权平均F1分数 (Weighted average F1-score)**  
* **ROC曲线下面积 (Area Under the ROC Curve, AUC)**

采用加权平均的原因是“由于每个数据库中上涨和下跌方向的数据量不同”（即类别不平衡问题）1。AUC是一个衡量模型整体判别能力的常用指标，而精确率在交易场景中尤为重要，因为它关系到交易信号的可靠性。

### **7.2. 结果解读与比较**

* 将复现得到的各项指标与论文中报告的相应值进行对比（表7-10展示了不同数据预处理方法下的DNN性能，表11-14对比了最佳DNN模型与基准模型的性能）1。  
* 重点关注DB-TSC模型的性能，因为论文强调这是最有效的方法组合 1。  
* 分析模型性能在不同前向测试周期之间的一致性。  
* 通过比较训练集和测试集上的性能差异来评估模型的泛化能力，差异越小通常表示泛化能力越好。DB-TSC模型通常表现出更高的AUC值以及更小的训练-测试AUC差异，这被认为是数据准备技术有效捕捉真实模式并减少过拟合的结果 1。  
* 注意论文中提到“将训练数据规模从50M增加到200M通常会提高DB-TSC模型的性能” 1，这里的50M和200M指的是美元K线的成交额阈值。

**表 7.1: 关键模型性能目标示例 (AAPL DB-TSC 50M, 测试集)**

| 股票 | 模型类型 | 指标 | 论文报告值 (测试集) |
| :---- | :---- | :---- | :---- |
| AAPL | DB-TSC (50M) | 精确率 | 0.7634 |
| AAPL | DB-TSC (50M) | 召回率 | 0.7536 |
| AAPL | DB-TSC (50M) | F1分数 | 0.7540 |
| AAPL | DB-TSC (50M) | AUC | 0.7668 |

此表格为复现工作设定了明确的量化目标。成功的复现应力求使各项性能指标尽可能接近论文报告的值。

## **8\. 特定案例复现步骤整合 (示例: AAPL DB-TSC 50M)**

为了使整个复现流程更具体化，本节以论文中表现优异的苹果公司股票（AAPL）采用5000万美元阈值的美元K线和趋势扫描标签（DB-TSC 50M）的配置为例，整合关键步骤和参数。

* **8.1. 数据采集**:  
  * 股票代码: AAPL  
  * 数据源: Barchart.com Inc.  
  * 频率: 1分钟  
  * 时间范围: 2012年1月3日至2022年12月30日  
  * 字段: OHLCV (开盘价, 最高价, 最低价, 收盘价, 交易量)  
* **8.2. 数据准备**:  
  * **采样**: 美元K线 (Dollar Bar)，阈值设为 $50M 1。  
  * **PAA特征提取**:  
    * 滑动窗口 W 和片段大小 S (例如，W=200, S=5)。  
    * 特征缩放至 \`\` 区间。  
    * 计算每个片段的均值、标准差和斜率。  
  * **标签生成**: 趋势扫描 (Trend Scanning, TSC)。  
    * 严格遵守前瞻窗口 |L| 的约束条件（例如，如果50M美元K线平均对应 X 分钟，则 |L| 与 X 相关联，且 L≥3×美元K线等效时长，L≤W）。  
    * 使用公式2-5计算T值并确定标签 1。  
* **8.3. DNN模型配置 (AAPL DB-TSC 50M)**: 根据论文表3 1：  
  * 学习率 (α): 0.001  
  * 最大训练轮数 (Epochs for early stopping): 200  
  * 隐藏层数量 (NHL): 6  
  * 批次大小 (Batch Size): 128  
  * 激活函数: Swish  
  * 其他: 输入特征 N (来自PAA)，隐藏层神经元 2N，20% Dropout，Adam优化器，BCE损失函数。  
* **8.4. 训练与验证**:  
  * 采用前向测试框架（5个周期，每个周期7年数据，60:20:20划分）。  
  * 对于此特定案例的复现，直接使用上述给定的超参数（这些是原始研究调优后的结果）。若要复现整个调优过程，则需执行第5.2节的网格搜索。  
  * 应用自定义早停算法 (算法1) 1。  
* **8.5. 性能评估**:  
  * 目标测试集性能指标 (来自论文表7) 1:  
    * 精确率: 0.7634  
    * 召回率: 0.7536  
    * F1分数: 0.7540  
    * AUC: 0.7668

**表 8.1: AAPL DB-TSC 50M 复现参数与步骤清单**

| 步骤 | 具体参数/配置 (AAPL DB-TSC 50M) |
| :---- | :---- |
| 数据采集 | AAPL, 1分钟 OHLCV, 2012/01/03 \- 2022/12/30, Barchart.com |
| 数据采样 | 美元K线, 阈值: $50M |
| PAA特征提取 | 示例: W=200, S=5; 缩放至; 特征: 均值, 标准差, 斜率 |
| 标签生成 | 趋势扫描 (TSC); 遵循L约束; 公式2-5 |
| DNN架构 | 输入N个PAA特征, 6个隐藏层 (每层2N神经元), Swish激活, 输出层1个神经元 (Sigmoid) |
| DNN训练超参数 | 学习率:0.001, 最大Epochs:200, Batch Size:128, Adam优化器, BCE损失, 20% Dropout |
| 验证协议 | 前向测试 (5周期, 7年/周期, 60:20:20); 自定义早停 (算法1) |
| 目标测试性能 | Precision:0.7634, Recall:0.7536, F1:0.7540, AUC:0.7668 |

此清单为复现论文中一个表现最佳的模型提供了一个具体的、端到端的指导。成功的复现高度依赖于对这些特定参数和步骤的严格遵守，因为论文的实验结果表明，最优参数组合在不同股票和不同数据准备方法之间存在显著差异 1。每一个环节——从50M美元K线采样，到PAA特征提取，再到TSC标签，最后到具有特定超参数的DNN模型——都是这条因果链上不可或缺的一环，共同促成了论文所报告的高性能。

## **9\. 实现说明与潜在挑战**

成功复现该论文的方法论需要关注一些技术细节和潜在的难点。

### **9.1. 建议软件栈**

* **编程语言**: Python 是金融量化与机器学习领域的常用语言。  
* **核心库**:  
  * **Pandas**: 用于高效的数据处理和时间序列分析。  
  * **NumPy**: 用于数值计算，特别是数组操作。  
  * **Scikit-learn**: 可用于实现基准模型（决策树、随机森林、SVM）、部分性能指标计算以及PAA的某些组成部分（如线性回归计算斜率）。  
  * **TensorFlow/Keras** 或 **PyTorch**: 用于构建和训练深度神经网络模型。论文未指定具体框架，两者均可。

### **9.2. 计算资源需求**

该研究涉及的计算量相当可观：

* **前向测试**: 包含5个独立的训练-验证-测试周期。  
* **超参数网格搜索**: 每个周期内，为寻找最佳超参数组合，需要遍历大量参数点。  
* **5折交叉验证**: 在网格搜索的每个参数点上，模型需要训练和评估5次。  
* **DNN训练**: 深度学习模型本身训练耗时较长，尤其是在较大的数据集和较多Epochs的情况下。 因此，拥有多核CPU进行数据预处理和部分模型训练，以及GPU加速DNN训练，将极大提高复现效率。

### **9.3. 潜在陷阱与注意事项**

* **前瞻偏差 (Look-ahead Bias)**: 这是金融时间序列建模中最需要警惕的问题之一。  
  * **特征工程**: 确保PAA滑动窗口 W 内的数据严格来自当前及历史K线，缩放操作也仅基于当前窗口内信息。  
  * **标签生成**: 趋势扫描标签法 (TSC) 的前瞻窗口 |L| 虽然使用未来数据点，但其设计是为了标记“未来已实现的趋势”，标签本身是针对当前时间点 t 的。关键在于确保 L 不超过特征提取窗口 W，且特征向量之间用 ∣W∣+∣L∣ 的间隔分开，以防止用于预测的特征“看到”用于打标的未来信息 1。  
* **美元K线CUSUM逻辑的准确性**: 确保正确累积成交额，并在达到阈值时准确切分K线。  
* **PAA缩放范围**: 必须在每个独立的滑动窗口 W 内部进行缩放，而不是对整个数据集进行全局缩放。  
* **TSC中T值的计算**: 论文公式5中 tδ​=σc2​β1​​ 的分母 σc2​ 需要仔细核实。如前所述，标准的T统计量是系数除以其标准误。复现时应首先尝试将其理解为 β1​ 的标准误或其平方，若结果不符，可能需要进一步探究或测试。  
* **前向测试数据管理**: 严格按照时间顺序划分和管理5个周期的训练、验证和测试数据，避免不同集合之间的数据泄露。

### **9.4. 调试与验证策略**

* **模块化实现与测试**: 将整个流程分解为独立的模块（数据采样、PAA特征提取、标签生成、DNN模型构建、训练逻辑等），对每个模块进行单元测试。  
* **小数据集试运行**: 在完整数据集上运行前，先用一小部分数据或单个前向测试周期进行调试，以快速发现和修复问题。  
* **中间数据检查**: 验证中间步骤生成的数据的形状、分布和数值范围是否符合预期（例如，PAA特征的分布情况）。

复现这样一个包含多阶段复杂数据准备流程和严格验证框架的模型并非易事，对细节的极致关注是成功的关键。趋势扫描标签法中的参数，如最大前瞻期 ∣L∣max​ 以及测试的回归线数量（即 l 的取值范围和步长），其具体选择可能对最终标签质量和模型性能有显著影响，而论文对此的描述除了约束条件外不够详尽，这可能是复现过程中的一个探索点。

## **10\. 结论与复现建议**

本报告详细阐述了复现论文《Deep neural network model enhanced with data preparation for the directional predictability of multi-stock returns》所提出预测方法的全面计划。该计划涵盖了从数据采集、多阶段数据准备（包括时间K线与美元K线采样、PAA特征工程、固定时间范围与趋势扫描标签生成），到深度神经网络模型构建、复杂的训练与验证协议（包括前向测试、交叉验证调参、自定义早停机制），以及基准模型比较和性能评估的全过程。

**核心复现要点：**

1. **数据准备的精确性**: 论文的核心贡献在于其精细化的数据准备流程。复现工作的成败极大地依赖于对美元K线采样逻辑、PAA特征提取（特别是滑动窗口缩放和多统计量计算）以及趋势扫描标签（尤其是T值计算和前瞻窗口约束）的准确实现。  
2. **参数的严格遵循**: 对于特定案例（如AAPL DB-TSC 50M），应严格使用论文报告的最优超参数组合。对于一般性复现，则需完整执行论文所述的超参数搜索和验证流程。  
3. **验证框架的完整性**: 必须完整实现前向测试框架，并在每个周期的训练集内部署5折交叉验证进行超参数选择，同时应用自定义的早停算法（Algorithm 1）来防止过拟合和优化模型选择。  
4. **避免前瞻偏差**: 在数据处理的每一步，尤其是特征工程和标签生成环节，都必须高度警惕并采取有效措施防止任何形式的前瞻偏差。  
5. **计算资源规划**: 鉴于所涉及的计算复杂度，复现者应提前规划并准备充足的计算资源。

**对复现代理的建议：**

* **模块化开发与测试**: 建议将复杂的流程分解为可独立测试的模块，逐一验证其正确性。  
* **从小处着手**: 开始时可选用单一股票和单个前向测试周期进行流程打通和初步调试。  
* **关注细节**: 对于论文中可能存在歧义之处（如TSC中T值公式的分母），需仔细研读上下文，或进行小范围实验对比不同理解下的效果。  
* **记录与对比**: 详细记录复现过程中的各项参数设置、中间结果和最终性能指标，并与论文报告值进行细致比较。

成功复现此论文不仅能验证其研究成果的可靠性，更能为量化交易研究者提供一套经过严格测试的、旨在提升金融时间序列预测能力的先进方法论。该方法论通过深度整合信息同步采样、多维度特征表征和动态趋势标签，为应对复杂多变的金融市场提供了有力的分析工具。本计划的遵循将有助于推动机器学习在金融预测领域的可复现性和实践应用。

#### **引用的著作**

1. Deep neural network model enhanced with data preparation for the directional predictability of multi-stock returns.pdf