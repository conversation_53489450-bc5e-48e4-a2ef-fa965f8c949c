# 论文预测方法复现项目

本项目复现了论文《Deep neural network model enhanced with data preparation for the directional predictability of multi-stock returns》中提出的股票收益方向性预测方法。

## 项目结构

```
claude_reproduce/
├── README.md                      # 项目说明文档
├── data.py                        # 数据加载和预处理
├── dollar_bar_implementation.py   # Dollar Bar采样实现
├── paa_features.py               # PAA特征工程
├── trend_scanning_labels.py      # 趋势扫描标签生成
├── ml_models.py                  # 传统机器学习模型
├── deep_learning_models.py       # 深度神经网络模型
├── main_experiment.py            # 主实验脚本
├── utils.py                      # 工具函数和可视化
└── 论文预测方法复现计划_.md        # 详细复现计划
```

## 核心功能模块

### 1. Dollar Bar采样 (`dollar_bar_implementation.py`)

实现基于累计成交额（CUSUM）的美元K线采样方法：
- 支持50M和200M两种阈值
- 使用1分钟quote累加来合成dollar bar
- 提供Dollar Bar特性分析功能

**主要类：**
- `DollarBarSampler`: 美元K线采样器
- `DollarBarConfig`: 配置参数

### 2. PAA特征工程 (`paa_features.py`)

实现分段聚合近似（PAA）特征提取：
- 滑动窗口缩放（论文公式1）
- 分段统计特征计算（均值、标准差、斜率）
- 支持多尺度特征提取

**主要类：**
- `PAAFeatureExtractor`: PAA特征提取器
- `PAAConfig`: 配置参数

### 3. 趋势扫描标签 (`trend_scanning_labels.py`)

实现趋势扫描标签生成方法：
- 基于前瞻窗口的多条线性回归
- T统计量计算（论文公式3-5）
- 固定时间范围标签作为对比

**主要类：**
- `TrendScanningLabeler`: 趋势扫描标签生成器
- `FixedTimeHorizonLabeler`: 固定时间范围标签生成器

### 4. 机器学习模型 (`ml_models.py`)

实现论文中使用的机器学习模型：
- LightGBM
- XGBoost
- 线性判别分析器（LDA）
- 基准模型（决策树、随机森林、SVM）

### 5. 深度神经网络 (`deep_learning_models.py`)

实现论文规格的DNN模型：
- 输入层：N个PAA特征
- 隐藏层：NHL层，每层2N个神经元
- 激活函数：ReLU/Tanh/Swish
- 自定义早停机制（Algorithm 1）

## 使用方法

### 1. 数据准备

首先运行数据加载脚本：

```python
# 运行data.py加载数据
python claude_reproduce/data.py
```

### 2. 运行完整实验

执行主实验脚本：

```python
# 运行完整的复现实验
python claude_reproduce/main_experiment.py
```

### 3. 单独使用各模块

#### Dollar Bar采样示例：

```python
from dollar_bar_implementation import DollarBarSampler, DollarBarConfig

# 创建配置
config = DollarBarConfig(threshold_50m=50_000_000, threshold_200m=200_000_000)
sampler = DollarBarSampler(config)

# 创建Dollar Bar
dollar_bars_50m, dollar_bars_200m = sampler.create_both_thresholds(minute_data)

# 分析特性
analysis = sampler.analyze_dollar_bars(dollar_bars_50m)
print(f"平均持续时间: {analysis['avg_duration_minutes']:.1f} 分钟")
```

#### PAA特征提取示例：

```python
from paa_features import PAAFeatureExtractor, PAAConfig

# 创建配置
config = PAAConfig(window_size=200, segment_size=5)
extractor = PAAFeatureExtractor(config)

# 提取特征
features = extractor.extract_features(dollar_bars)
print(f"特征维度: {extractor.feature_dim}")
```

#### 趋势扫描标签示例：

```python
from trend_scanning_labels import TrendScanningLabeler, TrendScanningConfig

# 创建配置
config = TrendScanningConfig(max_lookforward=20, min_lookforward=3)
labeler = TrendScanningLabeler(config)

# 生成标签
labels = labeler.generate_labels(dollar_bars)
analysis = labeler.analyze_labels(labels)
print(f"上涨标签比例: {analysis['upward_ratio']:.3f}")
```

#### 模型训练示例：

```python
from ml_models import create_model_suite
from deep_learning_models import DNNTrainer, DNNConfig

# 传统机器学习模型
models = create_model_suite()
lgb_model = models['lightgbm']
lgb_model.fit(X_train, y_train)
metrics = lgb_model.evaluate(X_test, y_test)

# 深度学习模型
config = DNNConfig(input_size=X_train.shape[1])
trainer = DNNTrainer(config)
trainer.build_model()
history = trainer.train(X_train, y_train, X_val, y_val)
test_metrics = trainer.evaluate(X_test, y_test)
```

## 实验配置

### 论文中的最佳配置（AAPL DB-TSC 50M）：

- **Dollar Bar阈值**: $50M
- **PAA配置**: 窗口大小W=200, 片段大小S=5
- **DNN配置**: 
  - 学习率: 0.001
  - 隐藏层数: 6
  - 批次大小: 128
  - 激活函数: Swish
  - Dropout率: 20%

### 目标性能指标：

根据论文报告的AAPL DB-TSC 50M测试集性能：
- 精确率: 0.7634
- 召回率: 0.7536
- F1分数: 0.7540
- AUC: 0.7668

## 依赖包

```
polars>=0.20.0
pandas>=1.5.0
numpy>=1.21.0
scikit-learn>=1.0.0
lightgbm>=3.3.0
xgboost>=1.6.0
torch>=1.12.0
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0
```

## 安装依赖

```bash
pip install polars pandas numpy scikit-learn lightgbm xgboost torch matplotlib seaborn plotly
```

## 注意事项

1. **数据质量**: 确保使用高质量的1分钟OHLCV数据
2. **计算资源**: DNN训练需要较多计算资源，建议使用GPU
3. **内存使用**: PAA特征提取可能消耗较多内存，注意监控
4. **前瞻偏差**: 严格避免在特征工程中使用未来信息

## 结果分析

实验完成后，会生成以下分析结果：

1. **Dollar Bar分析**: 持续时间分布、价格分布特性
2. **PAA特征分析**: 特征相关性、分布特性
3. **模型性能比较**: 各模型在测试集上的表现
4. **训练历史**: DNN模型的损失和准确率曲线
5. **综合报告**: 实验总结和结论

## 扩展功能

- 支持多个加密货币品种
- 可配置的前向测试框架
- 交互式结果可视化
- 自动化超参数搜索

## 参考文献

Deep neural network model enhanced with data preparation for the directional predictability of multi-stock returns

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
