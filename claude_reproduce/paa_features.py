"""
Piecewise Aggregate Approximation (PAA) Feature Engineering
基于论文实现PAA特征提取方法
"""

import polars as pl
import pandas as pd
import numpy as np
from typing import List, Tuple, Optional
from dataclasses import dataclass
from sklearn.linear_model import LinearRegression


@dataclass
class PAAConfig:
    """PAA特征提取配置"""
    window_size: int = 200  # 滑动窗口大小W（回顾的K线条数）
    segment_size: int = 5   # 片段大小S（每个片段包含的K线条数）
    scaling_range: Tuple[float, float] = (0.0, 1.0)  # 缩放区间[a, b]
    price_column: str = 'close'  # 用于特征提取的价格列


class PAAFeatureExtractor:
    """
    PAA特征提取器

    实现论文中的分段聚合近似特征提取方法：
    1. 滑动窗口缩放
    2. 分段统计特征计算（均值、标准差、斜率）
    3. 特征向量构建
    """

    def __init__(self, config: PAAConfig):
        self.config = config
        self.n_segments = config.window_size // config.segment_size
        self.feature_dim = self.n_segments * 3  # 每个片段3个特征：均值、标准差、斜率

    def extract_features(self, df: pl.DataFrame, code_column: str = 'code') -> pl.DataFrame:
        """
        提取PAA特征

        Args:
            df: 输入数据，必须包含时间序列价格数据
            code_column: 代码列名

        Returns:
            包含PAA特征的DataFrame
        """
        print(f"开始提取PAA特征，窗口大小: {self.config.window_size}, 片段大小: {self.config.segment_size}")

        if self.config.price_column not in df.columns:
            raise ValueError(f"缺少价格列: {self.config.price_column}")

        features_list = []

        for code in df[code_column].unique():
            code_df = df.filter(pl.col(code_column) == code)

            # 确保时间列的数据类型正确
            try:
                if 'open_time' in code_df.columns:
                    code_df = code_df.with_columns(
                        pl.col('open_time').cast(pl.Datetime)
                    ).sort('open_time')
                else:
                    # 如果没有open_time列，按索引排序
                    code_df = code_df.with_row_index().sort('index').drop('index')
            except Exception as e:
                print(f"排序时出现错误: {e}，跳过排序")

            code_features = self._extract_features_for_code(code_df, code)
            if len(code_features) > 0:
                features_list.append(code_features)

        if not features_list:
            return pl.DataFrame()

        result = pl.concat(features_list)
        print(f"提取了 {len(result)} 个特征向量，每个向量维度: {self.feature_dim}")

        # 尝试排序，如果失败则跳过
        try:
            result = result.with_columns([
                pl.col('open_time').cast(pl.Datetime),
                pl.col('code').cast(pl.Utf8)
            ]).sort(['code', 'open_time'])
        except Exception as e:
            print(f"排序时出现错误: {e}，返回未排序的结果")

        return result

    def _extract_features_for_code(self, df: pl.DataFrame, code: str) -> pl.DataFrame:
        """为单个代码提取PAA特征"""

        if len(df) < self.config.window_size:
            print(f"代码 {code} 数据不足，需要至少 {self.config.window_size} 条记录")
            return pl.DataFrame()

        price_series = df[self.config.price_column].to_numpy()
        open_time_series = df['open_time'].to_numpy()

        features = []

        # 滑动窗口提取特征
        for i in range(self.config.window_size, len(df) + 1):
            window_start = i - self.config.window_size
            window_end = i

            # 获取当前窗口的价格数据
            window_prices = price_series[window_start:window_end]
            current_time = open_time_series[window_end - 1]

            # 特征缩放（公式1）
            scaled_prices = self._scale_window(window_prices)

            # 分段特征提取
            segment_features = self._extract_segment_features(scaled_prices)

            # 构建特征向量
            feature_vector = np.concatenate(segment_features)

            features.append({
                'open_time': current_time,
                'code': code,
                **{f'paa_feature_{j}': feature_vector[j] for j in range(len(feature_vector))}
            })

        return pl.DataFrame(features)

    def _scale_window(self, window_prices: np.ndarray) -> np.ndarray:
        """
        窗口内价格缩放（论文公式1）

        Xi,scaled = [(Xi - min(W)) / (max(W) - min(W))] * (b - a) + a
        """
        min_price = np.min(window_prices)
        max_price = np.max(window_prices)

        # 避免除零
        if max_price == min_price:
            return np.full_like(window_prices, (self.config.scaling_range[0] + self.config.scaling_range[1]) / 2)

        a, b = self.config.scaling_range
        scaled = ((window_prices - min_price) / (max_price - min_price)) * (b - a) + a

        return scaled

    def _extract_segment_features(self, scaled_prices: np.ndarray) -> List[np.ndarray]:
        """
        提取分段特征

        为每个片段计算：
        1. 均值 (Mean)
        2. 标准差 (Standard Deviation)
        3. 斜率 (Slope)
        """
        segment_features = []

        for seg_idx in range(self.n_segments):
            start_idx = seg_idx * self.config.segment_size
            end_idx = start_idx + self.config.segment_size

            # 确保不超出数组边界
            if end_idx > len(scaled_prices):
                end_idx = len(scaled_prices)

            segment_data = scaled_prices[start_idx:end_idx]

            if len(segment_data) == 0:
                # 如果片段为空，使用零值
                features = np.array([0.0, 0.0, 0.0])
            else:
                # 计算统计特征
                mean_val = np.mean(segment_data)
                std_val = np.std(segment_data) if len(segment_data) > 1 else 0.0
                slope_val = self._calculate_slope(segment_data)

                features = np.array([mean_val, std_val, slope_val])

            segment_features.append(features)

        return segment_features

    def _calculate_slope(self, segment_data: np.ndarray) -> float:
        """
        计算片段的斜率

        使用线性回归拟合时间序列，取时间变量的系数作为斜率
        """
        if len(segment_data) <= 1:
            return 0.0

        # 创建时间索引
        x = np.arange(len(segment_data)).reshape(-1, 1)
        y = segment_data

        try:
            # 线性回归
            reg = LinearRegression()
            reg.fit(x, y)
            slope = reg.coef_[0]
        except:
            slope = 0.0

        return slope

    def get_feature_names(self) -> List[str]:
        """获取特征名称列表"""
        feature_names = []
        for seg_idx in range(self.n_segments):
            feature_names.extend([
                f'paa_seg{seg_idx}_mean',
                f'paa_seg{seg_idx}_std',
                f'paa_seg{seg_idx}_slope'
            ])
        return feature_names

    def analyze_features(self, features_df: pl.DataFrame) -> dict:
        """分析特征的统计特性"""
        if len(features_df) == 0:
            return {}

        feature_cols = [col for col in features_df.columns if col.startswith('paa_feature_')]

        analysis = {
            'n_samples': len(features_df),
            'n_features': len(feature_cols),
            'feature_stats': {}
        }

        for col in feature_cols:
            col_data = features_df[col]
            analysis['feature_stats'][col] = {
                'mean': col_data.mean(),
                'std': col_data.std(),
                'min': col_data.min(),
                'max': col_data.max(),
                'null_count': col_data.null_count()
            }

        return analysis


class MultiScalePAAExtractor:
    """
    多尺度PAA特征提取器

    可以同时提取不同窗口大小和片段大小的PAA特征
    """

    def __init__(self, configs: List[PAAConfig]):
        self.configs = configs
        self.extractors = [PAAFeatureExtractor(config) for config in configs]

    def extract_all_features(self, df: pl.DataFrame) -> pl.DataFrame:
        """提取所有尺度的PAA特征"""
        all_features = []

        for i, extractor in enumerate(self.extractors):
            print(f"提取第 {i+1}/{len(self.extractors)} 个尺度的特征...")
            features = extractor.extract_features(df)

            # 重命名特征列以避免冲突
            feature_cols = [col for col in features.columns if col.startswith('paa_feature_')]
            rename_dict = {col: f'scale{i}_{col}' for col in feature_cols}
            features = features.rename(rename_dict)

            all_features.append(features)

        # 合并所有特征
        if len(all_features) == 1:
            return all_features[0]

        result = all_features[0]
        for features in all_features[1:]:
            result = result.join(features, on=['open_time', 'code'], how='inner')

        return result


def create_default_paa_configs() -> List[PAAConfig]:
    """创建默认的PAA配置"""
    configs = [
        PAAConfig(window_size=200, segment_size=5),   # 论文中的示例配置
        PAAConfig(window_size=100, segment_size=5),   # 较短窗口
        PAAConfig(window_size=300, segment_size=10),  # 较长窗口，较大片段
    ]
    return configs


if __name__ == "__main__":
    # 测试代码
    print("PAA Features 模块已加载")

    # 创建测试配置
    config = PAAConfig()
    extractor = PAAFeatureExtractor(config)
    print(f"特征维度: {extractor.feature_dim}")
    print(f"特征名称: {extractor.get_feature_names()[:10]}...")  # 显示前10个特征名
