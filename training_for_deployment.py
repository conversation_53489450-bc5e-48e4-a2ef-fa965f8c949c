import os
from lightning import LightningModule
from core.cst import Optimizers, TaskType
from core.data_module import KlineDataModule
from core.fit_val_test import fit_val_test
from direct_trading import pred_cfg


pred_cfg.script_name = __file__
pred_cfg.for_deployment = True
pred_cfg.start_date.multi = '2021.01.01'
pred_cfg.num_epochs = 10
pred_cfg.save_top_k = -1
pred_cfg.save_every_n_epochs = 1
# pred_cfg.val_check_interval = 0.
# pred_cfg.shuffling.codewise = True
# pred_cfg.rnn_name = 'gru'
# pred_cfg.rnn_name = 'lstm'
pred_cfg.train_end_date = '2024.08.01'
# pred_cfg.val_start_date = '2024.07.01'
pred_cfg.execute_phase.val = False

# pred_cfg.adapt.in_use = True
if pred_cfg.meta_adapt.in_use:
    pred_cfg.task_enum = TaskType.PortfolioDoubleAdapt
    # pred_cfg.optimizer_enum = Optimizers.RMSPROP
    pred_cfg.optimizer_enum = Optimizers.ADAM
    pred_cfg.sample_pair = True
    # pred_cfg.adapt.transform_x = False
    pred_cfg.meta_adapt.transform_y = False
    pred_cfg.meta_adapt.offline_lr_dict.outer = pred_cfg.meta_adapt.offline_lr_dict.inner = pred_cfg.learning_rate = 1e-4
    pred_cfg.batch_size = 42


if __name__ == '__main__':
    # pred_cfg.adapt_distr_shift.val = True
    # pred_cfg.pnl_decay.in_use = True
    # pred_cfg.pnl_decay.threshold = 0.00002
    # pred_cfg.feature_category.factor = False
    pred_cfg.merge_history_data = True
    pred_cfg.history_file_name = '2024_0822_0420_s20210101'
    fit_val_test(pred_cfg) 