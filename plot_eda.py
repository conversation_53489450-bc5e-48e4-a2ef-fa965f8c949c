#%%
from core.data_module import KlineDataModule
from direct_trading import pred_cfg
from core.cst import DatabaseType
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import polars as pl
from sklearn.linear_model import LinearRegression

pred_cfg.database_enum = DatabaseType.Arctic
pred_cfg.n_codes = 1
# pred_cfg.symbol = 'BTCUSDT'
# pred_cfg.symbol = 'ETHUSDT'
# pred_cfg.symbol = 'BNBUSDT'
# pred_cfg.symbol = 'SOLUSDT'
# pred_cfg.symbol = 'XRPUSDT'
# pred_cfg.symbol = 'ADAUSDT'
# pred_cfg.symbol = 'DOGEUSDT'
# pred_cfg.symbol = 'DOTUSDT'
# pred_cfg.symbol = 'UNIUSDT'
# pred_cfg.symbol = 'TRXUSDT'
pred_cfg.symbol = 'AVAXUSDT'
pred_cfg.interval_cfg.base = 240
pred_cfg.code_sort_by_quote = True
# pred_cfg.start_date.multi = '2024.07.01'
pred_cfg.start_date.single = '2021.01.01'
pred_cfg.train_end_date = '2025.04.15'
pred_cfg.val_end_date = '2025.05.01'
pred_cfg.is_eda = True
dm = KlineDataModule(pred_cfg)

#%%
tr_df = dm.dataset_dict.train.data.reset_index()
# vl_df = dm.dataset_dict.val.data.reset_index()
tr_df = pl.from_pandas(tr_df)
# vl_df = pl.from_pandas(vl_df)

ema_span = 10000
# 添加close_roc列（收盘价变化率）
# Prepare data for linear regression
X = np.arange(len(tr_df)).reshape(-1, 1)  # Use index as X for time series
y = tr_df['close'].to_numpy()

# Fit linear regression
tr_linear_reg = LinearRegression().fit(X, y)
tr_linear_fit = tr_linear_reg.predict(X)
slope = tr_linear_reg.coef_[0]
print(f"Slope: {slope}")
tr_df = tr_df.with_columns(
    close_roc=pl.col('close').pct_change().over('code'),
    vwap=(pl.col('quote').cum_sum() / pl.col('volume').cum_sum()).over('code'),
    ema=pl.col('close').ewm_mean(span=ema_span, adjust=False).over('code'),
    linear_reg=pl.Series(tr_linear_fit)
)

#%%
# 计算每个code的quote_sum
# code_quote_sum = tr_df.group_by('code').agg(
#     pl.col('quote').sum().alias('quote_sum')
# )

# Prepare data for plotting
plot_df = tr_df.select(['open_time', 'close', 'vwap', 'ema', 'linear_reg']).to_pandas()
plot_df = plot_df.set_index('open_time')

# Plot
plt.figure(figsize=(20, 10))
plt.plot(plot_df.index, plot_df['close'], label='Close Price', linewidth=1)
plt.plot(plot_df.index, plot_df['vwap'], label='VWAP', alpha=0.7)
plt.plot(plot_df.index, plot_df['ema'], label=f'EMA({ema_span})', alpha=0.7)
plt.plot(plot_df.index, plot_df['linear_reg'], 'r--', label='Linear Regression', linewidth=2)

plt.title('Price Analysis with Linear Regression')
plt.xlabel('Date')
plt.ylabel('Price')
plt.legend(loc='upper left')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()


