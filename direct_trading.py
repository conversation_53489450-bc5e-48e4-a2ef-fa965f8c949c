import torch
from sklearn.preprocessing import RobustScaler, StandardScaler
from core.data_module import KlineDataModule
from core.predictor_config import PredictorConfig
from core.dot_dict import DotDict as dd
from core.cst import ActionType, AllocationType, BarType, DatabaseType, EntryExitLogitsEnum, FC_Type, NormType, Objective, Optimizers, LabelType, SegmentType, TaskType, TopkType
from core.fit_val_test import fit_val_test
from core.scaler import CrossSectionalScaler, ZScoreScaler, QuantileScaler
import warnings

warnings.filterwarnings("ignore", category=UserWarning, module="torch.nn.modules.linear")
torch.set_float32_matmul_precision('medium') 
print('set float32 matmul precision to medium')


pred_cfg = PredictorConfig()
pred_cfg.script_name = __file__
# pred_cfg.database_enum = DatabaseType.Dolphin
pred_cfg.task_enum = TaskType.DirectTrading
# pred_cfg.database_enum = DatabaseType.Dolphin
# pred_cfg.use_exit_idx = True
# pred_cfg.use_entry_idx = True
if pred_cfg.use_entry_idx:
    pred_cfg.limit_shift_scale = 0.005
# pred_cfg.entry_exit_logits_enum = EntryExitLogitsEnum.STE
# pred_cfg.agg_cfg.quote *= 0.5
# pred_cfg.topk_enum = TopkType.EWM_PNL
# pred_cfg.topk_enum = TopkType.PositionVote
# pred_cfg.topk_enum = TopkType.SignVote
# pred_cfg.equal_backtest_weight = True
# pred_cfg.objective_enum = Objective.MSE
# pred_cfg.feature_scaler = QuantileScaler()
pred_cfg.feature_scaler = ZScoreScaler()
# pred_cfg.feature_scaler = StandardScaler()
# pred_cfg.feature_scaler = RobustScaler()
# pred_cfg.label_enum = LabelType.RawNormalized
# pred_cfg.base_output_size = 1
# pred_cfg.base_output_size = 2
pred_cfg.base_output_size = 3
# pred_cfg.base_output_size = 4
# pred_cfg.base_output_size = 5
# top_num = pred_cfg.top_num_list[-1]
top_num = 1
# pred_cfg.top_egnore_num = 2
pred_cfg.monitor = f'top{top_num}_profit_to_loss_ratio'
# pred_cfg.monitor = 'ic'
pred_cfg.drop_null_rows = True
pred_cfg.feature_cfg.normalize_original = True
pred_cfg.feature_cfg.original = True
# pred_cfg.feature_cfg.macd = True
# pred_cfg.feature_cfg.original_with_btc = False
# pred_cfg.feature_cfg.original_group_size = 5
# pred_cfg.feature_cfg.vmd_dim = 6
# pred_cfg.feature_cfg.kalman = True
# pred_cfg.feature_cfg.adapt = True
# pred_cfg.adapter_cfg.in_use = True

# pred_cfg.stop_loss.in_use = True
pred_cfg.stop_loss.std_scale = 2
pred_cfg.stop_loss.learnable = True
# pred_cfg.stop_loss.with_position = True
pred_cfg.stop_loss.scale = 0.035
pred_cfg.stop_loss.min_ratio = 0.005

pred_cfg.take_profit.in_use = True
pred_cfg.take_profit.std_scale = 5
pred_cfg.take_profit.std_window = 12
pred_cfg.take_profit.learnable = True
# pred_cfg.take_profit.with_position = True
pred_cfg.take_profit.scale = 0.025
pred_cfg.take_profit.min_ratio = 0.005


pred_cfg.seed = 0
pred_cfg.set_seed()
pred_cfg.batch_size = 2048
# pred_cfg.batch_size = 256
pred_cfg.learning_rate = 20e-5
# pred_cfg.reduce_lr_on_plateau.in_use = True

# pred_cfg.dropout_rate.backbone = 0.4
pred_cfg.num_epochs = 100
pred_cfg.patience = 80
pred_cfg.n_range_groups = 20
pred_cfg.pnl_loss_with_fee_scale = 1.5
# pred_cfg.inverse_hour_position = True
# pred_cfg.episodic_backward = True # out of memory
# pred_cfg.batch_size = 64
# pred_cfg.learning_rate = 1000e-5

pred_cfg.model_name = 'fusion'
# pred_cfg.model_name = 'tcn'
# pred_cfg.model_name = 'koopa'
# pred_cfg.rnn_name = 'lstm'
pred_cfg.rnn_name = 'gru'
# pred_cfg.model_name = 'rnn'

if pred_cfg.model_name == 'rnn':
    pred_cfg.base_output_size = 2
# pred_cfg.model_name = 'min_gru'
# pred_cfg.model_name = 'grande'
elif pred_cfg.model_name == 'grande':
    # pred_cfg.batch_size = 1024
    pred_cfg.base_output_size = 1
    pred_cfg.objective_enum = Objective.MSE
    # pred_cfg.objective_enum = Objective.BCE
    # pred_cfg.objective_enum = Objective.IC
    pred_cfg.tree_depth = 4
    pred_cfg.n_estimators = 1
    pred_cfg.n_selected_features = 1
    pred_cfg.learning_rate = 30e-5
    # pred_cfg.seq_len_list = [1]
    # pred_cfg.feature_cfg.inner = True


elif pred_cfg.model_name == 'tcn':
    pred_cfg.dropout_rate.backbone = 0.15
    pred_cfg.dropout_rate.fc = 0.15
    pred_cfg.seq_len_dict.conv = 0
    # pred_cfg.tcn_with_ffn = True
    # pred_cfg.embedding_size = 128
    # pred_cfg.rolling_zscore = True
    # pred_cfg.feature_cfg.inner = True
    # pred_cfg.fc_enum == FC_Type.TabM
    # pred_cfg.fc_enum == FC_Type.VMLP
    
# pred_cfg.model_name = 'gru_pfg'
elif pred_cfg.model_name == 'gru_pfg':
    ...
    # pred_cfg.sample_cross_section_in_a_batch = True
    pred_cfg.base_output_size = 1
    pred_cfg.objective_enum = Objective.MSE
# pred_cfg.model_name = 'tabm'
# pred_cfg.model_name = 'mlp'
# pred_cfg.dropout_rate = 0.5
# pred_cfg.num_tabm_blocks = 3

# pred_cfg.model_name = 'dft'
elif pred_cfg.model_name == 'dft':
    pred_cfg.objective_enum = Objective.MSE
    pred_cfg.feature_scaler = RobustScaler()
    # pred_cfg.label_scaler = CrossSectionalScaler('zscore')
    pred_cfg.label_scaler.scaler_type_str = 'robust'
    pred_cfg.dropout_rate.fc = 0.5
    pred_cfg.weight_decay = 0.001
    pred_cfg.base_output_size = 1
    pred_cfg.sample_cross_section_in_a_batch = True
    pred_cfg.cosine_lr_scheduler = True
    # pred_cfg.accumulate_grad_batches = 16
    pred_cfg.shuffling.codewise = True
    pred_cfg.shuffling.batchwise = True    

elif pred_cfg.model_name == 'fusion':    
    # pred_cfg.use_backbone = False
    if pred_cfg.use_backbone:
        # pred_cfg.backbone_name = 'tcn'
        pred_cfg.seq_len_dict.backbone = 84
        # pred_cfg.seq_len_dict.backbone = 42
        pred_cfg.dropout_rate.backbone = 0.15
        # pred_cfg.dropout_rate.fc = 0.15
        # pred_cfg.num_tcn_blocks = 4
        # pred_cfg.num_block_convs = 5
        # pred_cfg.learning_rate = 30e-5
        # pred_cfg.batch_size = 16384    
        pred_cfg.learning_rate = 10e-5
        # pred_cfg.batch_size = 8192
    # else:

    #     pred_cfg.dropout_rate.fc = 0.65

    # pred_cfg.feature_cfg.image = True
    if pred_cfg.feature_cfg.image:
        # pred_cfg.vision_name = 'trn'
        # pred_cfg.seq_len_dict.conv = 96
        # pred_cfg.accumulate_grad_batches = 2
        # pred_cfg.pnl_loss_with_fee_scale = 1
        pred_cfg.learning_rate = 2e-5
        # pred_cfg.batch_size = 256
        # pred_cfg.dropout_rate.fc = 0.6
        # pred_cfg.learning_rate = 1e-5
        pred_cfg.batch_size = 512
        pred_cfg.dropout_rate.fc = 0.6
        pred_cfg.image_cfg.step = 1
        pred_cfg.image_cfg.sign = True
        # pred_cfg.image_cfg.ema = True
        if pred_cfg.image_cfg.step == 1:
            pred_cfg.image_cfg.sign = True
        # pred_cfg.image_cfg.vstack = True
        # pred_cfg.image_cfg.multi_branch = True
        # pred_cfg.image_cfg.n_mixed_channel_layers = 3
        # pred_cfg.image_cfg.plot = True
        pred_cfg.image_cfg.volume = True
        # pred_cfg.image_cfg.buy = True
        pred_cfg.image_cfg.buy_sell = True
        pred_cfg.image_cfg.delta = True    
        pred_cfg.image_cfg.macd = True
        # pred_cfg.image_cfg.diff_as_bar = True
        # pred_cfg.image_cfg.macd_ema_line = True
        # pred_cfg.image_cfg.rsi = True
        # pred_cfg.dropout_rate.conv = 0.15
        # pred_cfg.dropout_rate.fc = 0.15
        pred_cfg.num_conv_channels = 32
        # pred_cfg.num_conv_channels = 64
        pred_cfg.num_conv_layers = 4
    else:
        pred_cfg.seq_len_dict.conv = 0

# pred_cfg.model_name = 'rwkv'
# pred_cfg.model_name = 'tsmx'

pred_cfg.n_head = 4
# pred_cfg.embedding_size = 16
# pred_cfg.flatten_before_fc = False
pred_cfg.channel_size = 16
# pred_cfg.channel_size = 24
pred_cfg.hidden_size = 2
# pred_cfg.pred_len = 1
pred_cfg.pred_len = 4
# pred_cfg.step_list = [1, 4]
# pred_cfg.pred_len = 2
pred_cfg.inverse_image = True
# pred_cfg.inverse_seq = True
# pred_cfg.seq_len_list = [96] #12#24#18#60#30#42
pred_cfg.seq_len_list = [84] #12#24#18#60#30#42
# pred_cfg.seq_len_list = [72] #12#24#18#60#30#42
# pred_cfg.seq_len_list = [60] #12#24#18#60#30#42
# pred_cfg.seq_len_list = [64] #12#24#18#60#30#42
# pred_cfg.seq_len_list = [42] #12#24#18#60#30#42
# pred_cfg.seq_len_list = [30] #12#24#18#60#30#42
# pred_cfg.selected_seq_len_list = [42]
# pred_cfg.mask_zero_quote_code = False
# pred_cfg.range_to_mask = [[-.9, 0]]
# pred_cfg.kan_as_fc = True
pred_cfg.num_block_convs = 4
# pred_cfg.cycle_mask_idx = 0
# pred_cfg.interval_cfg.base = 15
# pred_cfg.interval_cfg.feature = [15]
# pred_cfg.interval_cfg.base = 30
# pred_cfg.interval_cfg.feature = [30]
# pred_cfg.interval_cfg.base = 120
# pred_cfg.interval_cfg.feature = [120]
# pred_cfg.interval_cfg.label = 60
# pred_cfg.interval_cfg.label = 120
# pred_cfg.interval_cfg.base = 60
# pred_cfg.interval_cfg.base = 120
# pred_cfg.interval_cfg.base = 240
# pred_cfg.use_scaler = False

# pred_cfg.label.clip_quantile_classification = True
# pred_cfg.label.start_quantile = 0.25
# pred_cfg.label.end_quantile = 1.
# pred_cfg.label.ema_window = 1

# pred_cfg.start_date.multi = '2021.04.01'
# pred_cfg.quote_start_date = '2021.07.01'
# pred_cfg.quote_end_date = '2024.08.01'
# pred_cfg.quote_end_date = '2024.10.01'
# pred_cfg.quote_end_date = '2025.04.01'
# pred_cfg.train_end_date = '2023.12.01'
# pred_cfg.train_end_date = '2024.02.01'
# pred_cfg.train_end_date = '2024.08.01'
# pred_cfg.train_end_date = '2024.09.01'
# pred_cfg.train_end_date = '2024.08.01'
pred_cfg.train_end_date = '2024.10.01'
# pred_cfg.train_end_date = '2024.11.01'
# pred_cfg.train_end_date = '2024.12.01'
# pred_cfg.train_end_date = '2025.01.01'
# pred_cfg.train_end_date = '2025.02.01'
# pred_cfg.train_end_date = '2025.03.01'
# pred_cfg.train_end_date = '2025.03.16'
# pred_cfg.train_end_date = '2025.04.01'
# pred_cfg.train_end_date = '2025.02.21'
# pred_cfg.train_end_date = '2023.08.01'
# pred_cfg.train_end_date = '2024.05.01'
# pred_cfg.val_end_date = '2024.12.01'
# pred_cfg.val_start_date = '2025.01.01'
# pred_cfg.val_start_date = '2025.02.01'
# pred_cfg.val_end_date = '2025.03.11'
pred_cfg.val_end_date = '2025.06.01'
# pred_cfg.test_end_date = '2024.07.01'
# pred_cfg.task_folder = '2024_1107_190242' '2024_1113_124008'
# pred_cfg.task_folder = '2024_1113_124008' # crypto30
# pred_cfg.ckpt_file_name = '-epoch=42-top1_profit_to_loss_ratio=0.237.ckpt'
# pred_cfg.load_all_codes = True
# pred_cfg.fracdiff = .2
# pred_cfg.cum_feature_num = 7
pred_cfg.execute_phase.test = False
# pred_cfg.augment_data.rev = False
pred_cfg.augment_data.train = False

# pred_cfg.align_pos_neg_return = True

# pred_cfg.merge_history_data = True
pred_cfg.history_file_name = '2025_0524_145533_s20210401_crypto33_60min'

# pred_cfg.backtest_excluded_codes += [
# # pred_cfg.filter_excluded_codes += [
#     # 'BTCUSDT', 
#     'BNBUSDT', 
#     'DOTUSDT', 
#     'FTMUSDT'
#     ]
pred_cfg.n_codes = 40
pred_cfg.earliest_date_before_train_start = False
pred_cfg.group_by_str = 'open_time'

# pred_cfg.filter_mask_fn = filter_codes
# pred_cfg.filter_mask_fn = filter_hours
# pred_cfg.filter_mask_fn = filter_workdays


# pred_cfg.sample_cross_section_in_a_batch = True
# pred_cfg.cosine_lr_scheduler = True
# pred_cfg.accumulate_grad_batches = 16
# pred_cfg.shuffling.codewise = True
# pred_cfg.shuffling.batchwise = True

# pred_cfg.load_all_codes = True
# pred_cfg.quote_quantile = .80
# pred_cfg.sim_base_interval = 3

# pred_cfg.optimize_allocation = True
if pred_cfg.optimize_allocation:
    pred_cfg.earliest_date_before_train_start = True
    pred_cfg.sample_cross_section_in_a_batch = True
    # pred_cfg.resume_from_ckpt = True
    # pred_cfg.merge_history_data = True
    # pred_cfg.allocation_enum = AllocationType.MaxReturn
    # pred_cfg.allocation_enum = AllocationType.MinVar
    pred_cfg.allocation_enum = AllocationType.MaxReturn
    # pred_cfg.score_as_predicted_return = False
    pred_cfg.accumulate_grad_batches = 16

pred_cfg.execute_phase.train = False
pred_cfg.resume_from_ckpt = True
# pred_cfg.code_sort_by_quote = True
# pred_cfg.fine_tune = True
# pred_cfg.lr.fine_tune = 1e-7
if pred_cfg.resume_from_ckpt: 
    # pred_cfg.ckpt_file_name = 'epoch=80.ckpt'
    # pred_cfg.task_folder = '2024_1209_160628-mlp'
    # pred_cfg.ckpt_file_name = 'epoch=99.ckpt'
    # pred_cfg.task_folder = '2024_1213_112530-out3'
    # pred_cfg.ckpt_file_name = 'epoch=59.ckpt'
    # pred_cfg.task_folder = '2024_1208_111537'
    if pred_cfg.interval_cfg.base == 240:
        pred_cfg.task_folder = '2025_0103_192034_v20250101_train-fee_scale=1'
        pred_cfg.ckpt_file_name = 'epoch=24.ckpt'
        # pred_cfg.task_folder = '2025_0103_194659_v20250101_train-fee_scale=.5'
        # pred_cfg.ckpt_file_name = 'epoch=38.ckpt'
        
    elif pred_cfg.interval_cfg.base == 120:
        pred_cfg.task_folder = '2025_0217_190432_v2025_0217_031030_train'
        pred_cfg.ckpt_file_name = 'epoch=12.ckpt'

    elif pred_cfg.interval_cfg.base == 60:
        # pred_cfg.merge_history_data = True
        # pred_cfg.history_file_name = '2025_0211_023725_s20210401_crypto30_60min'
        if pred_cfg.train_start_date == '2021.04.01':	
            if pred_cfg.train_end_date == '2025.04.01':
                if pred_cfg.feature_cfg.image:
                    # pred_cfg.task_folder = '2025_0306_150842_v2025_0303_010356_train-fee_scale=1.3'
                    pred_cfg.task_folder = '2025_0430_115007_v2025_0430_010604_train'
                    pred_cfg.ckpt_file_name = 'epoch=17.ckpt'
                else:
                    pred_cfg.task_folder = '2025_0430_100216_v2025_0430_010604_train'
                    pred_cfg.ckpt_file_name = 'epoch=10.ckpt'
            if pred_cfg.train_end_date == '2025.03.11':
                if pred_cfg.feature_cfg.image:
                    # pred_cfg.task_folder = '2025_0306_150842_v2025_0303_010356_train-fee_scale=1.3'
                    pred_cfg.task_folder = '2025_0301_163711_v2025_0301_082514_train'
                    pred_cfg.ckpt_file_name = 'epoch=13.ckpt'
                else:
                    pred_cfg.task_folder = '2025_0211_084321_v2025_0209_035554_train'
                    pred_cfg.ckpt_file_name = 'epoch=5.ckpt'           
            if pred_cfg.train_end_date == '2025.03.01':
                if pred_cfg.feature_cfg.image:
                    # pred_cfg.task_folder = '2025_0306_150842_v2025_0303_010356_train-fee_scale=1.3'
                    pred_cfg.task_folder = '2025_0505_224449_v2025_0505_070151_train'
                    pred_cfg.ckpt_file_name = 'epoch=14.ckpt'
                else:
                    pred_cfg.task_folder = '2025_0521_110730_v2025_0520_152635_train'
                    pred_cfg.ckpt_file_name = 'epoch=7.ckpt'
            if pred_cfg.train_end_date == '2025.02.21':
                if pred_cfg.feature_cfg.image:
                    pred_cfg.task_folder = '2025_0221_133950_v2025_0221_051533_train'
                    pred_cfg.ckpt_file_name = 'epoch=15.ckpt'
                else:
                    pred_cfg.task_folder = '2025_0211_084321_v2025_0209_035554_train'
                    pred_cfg.ckpt_file_name = 'epoch=5.ckpt'                                    
            if pred_cfg.train_end_date == '2025.02.01':
                if pred_cfg.feature_cfg.image:
                    pred_cfg.task_folder = '2025_0218_151238_v2025_0218_052517_train'
                    pred_cfg.ckpt_file_name = 'epoch=15.ckpt'
                else:
                    pred_cfg.task_folder = '2025_0211_084321_v2025_0209_035554_train'
                    pred_cfg.ckpt_file_name = 'epoch=5.ckpt'       
            elif pred_cfg.train_end_date == '2025.01.01':
                if pred_cfg.feature_cfg.image:
                    pred_cfg.task_folder = '2025_0215_181507_v2025_0215_033309_train_dr.6'
                    pred_cfg.ckpt_file_name = 'epoch=18.ckpt'
                else:
                    pred_cfg.task_folder = '2025_0212_073459_v2025_0211_023725_train'
                    pred_cfg.ckpt_file_name = 'epoch=10.ckpt'                         
            elif pred_cfg.train_end_date == '2024.12.01':
                if pred_cfg.feature_cfg.image:
                    pred_cfg.task_folder = '2025_0219_121035_v2025_0219_024541_train'
                    pred_cfg.ckpt_file_name = 'epoch=15.ckpt'
                else:
                    pred_cfg.task_folder = '2025_0213_134007_v2025_0212_014350_train'
                    pred_cfg.ckpt_file_name = 'epoch=12.ckpt'
            elif pred_cfg.train_end_date == '2024.10.01':
                if pred_cfg.feature_cfg.image:
                    # pred_cfg.task_folder = '2025_0307_103802_v2025_0306_034255_train-fee_scale=1.5'
                    # pred_cfg.ckpt_file_name = 'epoch=15.ckpt'
                    pred_cfg.task_folder = '2025_0430_112236_v2025_0430_010604_train_arctic'
                    pred_cfg.ckpt_file_name = 'epoch=8.ckpt'
                else:
                    pred_cfg.task_folder = '2025_0414_092343_v2025_0404_144356_train'
                    pred_cfg.ckpt_file_name = 'epoch=8.ckpt'
            elif pred_cfg.train_end_date == '2024.09.01':
                pred_cfg.task_folder = '2025_0205_135628_v20250201_train'
                pred_cfg.ckpt_file_name = 'epoch=8.ckpt'
        if pred_cfg.train_start_date == '2021.01.01':	
            if pred_cfg.train_end_date == '2025.03.01':
                if pred_cfg.feature_cfg.image:
                    # pred_cfg.task_folder = '2025_0306_150842_v2025_0303_010356_train-fee_scale=1.3'
                    pred_cfg.task_folder = '2025_0430_115007_v2025_0430_010604_train'
                    pred_cfg.ckpt_file_name = 'epoch=17.ckpt'
                else:
                    pred_cfg.task_folder = '2025_0524_151653_v2025_0523_025218_train'
                    pred_cfg.ckpt_file_name = 'epoch=4.ckpt'
            elif pred_cfg.train_end_date == '2024.10.01':
                if pred_cfg.feature_cfg.image:
                    # pred_cfg.task_folder = '2025_0307_103802_v2025_0306_034255_train-fee_scale=1.5'
                    # pred_cfg.ckpt_file_name = 'epoch=15.ckpt'
                    pred_cfg.task_folder = '2025_0430_112236_v2025_0430_010604_train_arctic'
                    pred_cfg.ckpt_file_name = 'epoch=8.ckpt'
                else:
                    pred_cfg.task_folder = '2025_0524_152857_v2025_0523_025218_train'
                    pred_cfg.ckpt_file_name = 'epoch=5.ckpt'                    
        elif pred_cfg.train_end_date == '2024.10.01':
            # pred_cfg.task_folder = '2025_0118_194001_v20250101_train-fee_scale=2.4'
            pred_cfg.task_folder = '2025_0126_165109_v20250101_train-fee_scale=2.4'
            pred_cfg.ckpt_file_name = 'epoch=8.ckpt'	
        # pred_cfg.task_folder = '2025_0117_105148_v20250101_train-fee_scale=2.1'
        # pred_cfg.ckpt_file_name = 'epoch=10.ckpt'
        elif pred_cfg.train_end_date == '2024.08.01':	
            pred_cfg.task_folder = '2025_0126_150708_v20250101_train-fee_scale=2.4'
            pred_cfg.ckpt_file_name = 'epoch=22.ckpt'

	      
        # pred_cfg.task_folder = '2025_0116_214718_v20250101_train-fee_scale=2.1'
        # pred_cfg.ckpt_file_name = 'epoch=10.ckpt'	 
        # pred_cfg.task_folder = '2025_0116_120727_v20250101_train-fee_scale=2.4'
        # pred_cfg.ckpt_file_name = 'epoch=22.ckpt'	 

# pred_cfg.bar_enum = BarType.Quote
# pred_cfg.bar_enum = BarType.Move
# pred_cfg.norm_enum = NormType.StdOnSides
# pred_cfg.norm_enum = NormType.StdOnAbs
pred_cfg.norm_enum = NormType.StdOnly
# pred_cfg.segment_enum = SegmentType.Barrier
# pred_cfg.barrier_range = 0.02
# pred_cfg.segment_enum = SegmentType.Reversal
if pred_cfg.segment_enum != SegmentType.BarCount:
    # pred_cfg.action_enum = ActionType.Momentum
    # pred_cfg.take_profit.in_use = False
    # pred_cfg.interval_cfg.base = 120
    pred_cfg.pred_len = 1
    label_len = label_step = 1
    pred_cfg.label_step.train = label_step
    pred_cfg.label_step.val = label_step
    pred_cfg.filter_mask_name = 'is_segment_end'

elif pred_cfg.bar_enum == BarType.Time:
    base_interval = pred_cfg.interval_cfg.base
    step_list = pred_cfg.step_list
    # feature_interval = max(pred_cfg.interval_cfg.feature)
    # assert feature_interval >= base_interval
    # feature_step = max(step_list)
    pred_cfg.inner_step = max(step_list) #! will cause zscore overlap if using all indices and inner_step > 1
    label_len = pred_cfg.interval_cfg.label // base_interval    
    pred_cfg.cycle_mask_idx = (label_len - 1) # ! not set if using all indices

    label_step = label_len // pred_cfg.pred_len
    
    pred_cfg.label_step.train = label_step
    pred_cfg.label_step.val = label_step
else:
    pred_cfg.merge_history_data = False
    pred_cfg.interval_cfg.base = 15
    label_len = 4
    label_step = label_len // pred_cfg.pred_len
    pred_cfg.pnl_loss_with_fee_scale = 0
    # pred_cfg.feature_cfg.cross = False
    # pred_cfg.agg_cfg.quote *= 0.5
    pred_cfg.label_step.train = label_step
    # pred_cfg.label_step.val = label_step

if label_len > 0:# and pred_cfg.pred_len == 1:
    pred_cfg.label_enum = LabelType.RawNormalized
# elif label_len == 1 and pred_cfg.pred_len > 1:
#     pred_cfg.label_enum = LabelType.PercentChange
    # pred_cfg.label_enum = LabelType.LogReturn

# pred_cfg.label.csnorm = True
# pred_cfg.label.softmax_advantage_as_position = True

if pred_cfg.interval_cfg.base <= 120:
    pred_cfg.feature_scaler = ZScoreScaler()

pred_cfg.for_deployment = True
if pred_cfg.for_deployment:
    pred_cfg.save_top_k = -1
    pred_cfg.save_every_n_epochs = 1
# pred_cfg.adapt.in_use = True
if pred_cfg.meta_adapt.in_use:
    pred_cfg.task_enum = TaskType.DirectTradingDoubleAdapt
    # pred_cfg.optimizer_enum = Optimizers.RMSPROP
    pred_cfg.optimizer_enum = Optimizers.ADAM
    pred_cfg.base_output_size = 1
    pred_cfg.sample_pair = True
    # pred_cfg.shuffling.samplewise = False
    # pred_cfg.shuffling.batchwise = True
    # pred_cfg.adapt.transform_x = False
    pred_cfg.meta_adapt.transform_y = False
    pred_cfg.meta_adapt.offline_lr_dict.outer = pred_cfg.meta_adapt.offline_lr_dict.inner = pred_cfg.learning_rate = 1e-2
    pred_cfg.batch_size = 42 * pred_cfg.n_codes * 2

    
if __name__ == '__main__':
    # pred_cfg.hyper_connection.in_use = True
    # pred_cfg.shuffling.extra_count = 1
    # pred_cfg.force_train = False
    
    # pred_cfg.resume_from_ckpt = True
    # pred_cfg.task_folder = '2024_1121_140933-last_dropout' # 
    # pred_cfg.task_folder = '2024_1126_135021-standerd'
    # pred_cfg.ckpt_file_name = 'epoch=59.ckpt'
    # pred_cfg.task_folder = '2024_1204_220358-mlp-dropout.15'
    # pred_cfg.task_folder = '2024_1114_115713'
    # pred_cfg.ckpt_file_name = '-epoch=18-top1_profit_to_loss_ratio=0.056.ckpt'
    # pred_cfg.ckpt_file_name = '-epoch=58-top1_profit_to_loss_ratio=0.382.ckpt'
    # pred_cfg.ckpt_file_name = '-epoch=59-top1_profit_to_loss_ratio=0.286.ckpt'
    # pred_cfg.interval_cfg.base = 120
    # pred_cfg.ckpt_file_name = '-epoch=35-top1_profit_to_loss_ratio=0.175.ckpt'
    # pred_cfg.ckpt_file_name = '-epoch=42-top1_profit_to_loss_ratio=0.237.ckpt'
    # pred_cfg.load_all_codes = True
    # pred_cfg.top_num_list.extend([24])
    # pred_cfg.vote_side = True
    # pred_cfg.num_epochs = 100
    
    
    # pred_cfg.optimize_cross_entropy = True
    # pred_cfg.limit_shift_scale = .01
    # pred_cfg.use_scaler = False
    # pred_cfg.feature_cfg.rolling = True
    # pred_cfg.feature_cfg.step = False
    # pred_cfg.feature_cfg.category = True
    # pred_cfg.use_batch_norm = True
    # pred_cfg.use_projection = True
    # pred_cfg.optimize_to_oracle = True
    # pred_cfg.channel_att_after_rnn = True
    # pred_cfg.att_after_tcn = True
    

    # pred_cfg.tcn_before_rnn = True
    # pred_cfg.num_tcn_blocks = 5
    
    # pred_cfg.tcn_kernel_size = 1
    # pred_cfg.model_name = 'rnn'    
    
    # pred_cfg.tcn_fc_dim_list = [128]
    # pred_cfg.channel_dim = 16
    # pred_cfg.dropout_rate = 0.2
    # pred_cfg.learning_rate = 5e-5
    # pred_cfg.ckpt_file_name = '2024_0904_173450-epoch=39-metric=0.908.ckpt'

    # pred_cfg.start_date.multi = '2020.01.01'
    # pred_cfg.start_date.multi = '2022.01.01'
    # pred_cfg.train_end_date = '2024.08.01'
    # pred_cfg.history_prefix = '2024_0828_005253_s20210101'
    # pred_cfg.backtest_excluded_codes += ['MATICUSDT']
    # pred_cfg.merge_history_data = True
    # pred_cfg.history_file_name = '2024_1113_233152_s20210101_240min'
    # pred_cfg.history_file_name = '2024_1209_074248_s20210101_crypto30_240min'
    # pred_cfg.start_date.multi = '2022.01.01'
    # pred_cfg.history_prefix = '2024_0828_004611_s20220101'
    fit_val_test(pred_cfg)