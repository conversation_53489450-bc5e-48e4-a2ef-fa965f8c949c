"""
使用Playwright测试动态均值图表的交互行为
验证图例点击和双击操作的正确性
"""

import asyncio
import os
from playwright.async_api import async_playwright

async def test_dynamic_mean_chart():
    """测试动态均值图表的交互功能"""
    
    # 获取HTML文件的绝对路径
    html_file = os.path.abspath("simple_dynamic_mean.html")
    file_url = f"file://{html_file}"
    
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False)  # 设置为False以便观察
        page = await browser.new_page()
        
        # 打开HTML文件
        await page.goto(file_url)
        
        # 等待图表加载完成
        await page.wait_for_selector('.plotly-graph-div')
        await asyncio.sleep(2)  # 额外等待确保JavaScript执行完成
        
        print("🔍 开始测试动态均值图表...")
        
        # 测试1: 检查初始状态 - 所有线都可见，均值线应该显示
        print("\n📊 测试1: 检查初始状态")
        
        # 获取所有图例项
        legend_items = await page.query_selector_all('.legend .traces .legendtoggle')
        print(f"找到 {len(legend_items)} 个图例项")
        
        # 检查均值线是否可见
        mean_line_visible = await page.evaluate("""
            () => {
                const plotDiv = document.getElementsByClassName('plotly-graph-div')[0];
                const traces = plotDiv.data;
                const meanTrace = traces[traces.length - 1];
                return meanTrace.visible !== false && meanTrace.visible !== 'legendonly';
            }
        """)
        print(f"初始状态均值线可见: {mean_line_visible}")
        assert mean_line_visible, "初始状态下均值线应该可见"
        
        # 测试2: 双击第一个图例项，只显示一条线
        print("\n📊 测试2: 双击图例项，只显示一条线")
        
        if legend_items:
            # 双击第一个图例项
            await legend_items[0].dblclick()
            await asyncio.sleep(1)  # 等待更新
            
            # 检查可见线条数量
            visible_count = await page.evaluate("""
                () => {
                    const plotDiv = document.getElementsByClassName('plotly-graph-div')[0];
                    const traces = plotDiv.data;
                    let count = 0;
                    for (let i = 0; i < traces.length - 1; i++) {
                        if (traces[i].visible !== 'legendonly') {
                            count++;
                        }
                    }
                    return count;
                }
            """)
            print(f"可见线条数量: {visible_count}")
            
            # 检查均值线是否隐藏
            mean_line_visible = await page.evaluate("""
                () => {
                    const plotDiv = document.getElementsByClassName('plotly-graph-div')[0];
                    const traces = plotDiv.data;
                    const meanTrace = traces[traces.length - 1];
                    return meanTrace.visible !== false && meanTrace.visible !== 'legendonly';
                }
            """)
            print(f"只有一条线时均值线可见: {mean_line_visible}")
            assert not mean_line_visible, "只有一条线时均值线应该隐藏"
            
        # 测试3: 单击其他图例项，显示多条线
        print("\n📊 测试3: 单击其他图例项，显示多条线")
        
        if len(legend_items) > 1:
            # 单击第二个图例项
            await legend_items[1].click()
            await asyncio.sleep(1)  # 等待更新
            
            # 检查可见线条数量
            visible_count = await page.evaluate("""
                () => {
                    const plotDiv = document.getElementsByClassName('plotly-graph-div')[0];
                    const traces = plotDiv.data;
                    let count = 0;
                    for (let i = 0; i < traces.length - 1; i++) {
                        if (traces[i].visible !== 'legendonly') {
                            count++;
                        }
                    }
                    return count;
                }
            """)
            print(f"可见线条数量: {visible_count}")
            
            # 检查均值线是否重新显示
            mean_line_visible = await page.evaluate("""
                () => {
                    const plotDiv = document.getElementsByClassName('plotly-graph-div')[0];
                    const traces = plotDiv.data;
                    const meanTrace = traces[traces.length - 1];
                    return meanTrace.visible !== false && meanTrace.visible !== 'legendonly';
                }
            """)
            print(f"多条线时均值线可见: {mean_line_visible}")
            
            if visible_count > 1:
                assert mean_line_visible, "多条线时均值线应该显示"
            else:
                assert not mean_line_visible, "单条线时均值线应该隐藏"
        
        # 测试4: 逐个隐藏线条，测试边界情况
        print("\n📊 测试4: 测试边界情况")
        
        # 先显示所有线条（双击图例区域外的地方）
        await page.evaluate("""
            () => {
                const plotDiv = document.getElementsByClassName('plotly-graph-div')[0];
                const traces = plotDiv.data;
                const updates = {};
                for (let i = 0; i < traces.length - 1; i++) {
                    updates[i] = {visible: true};
                }
                Plotly.restyle(plotDiv, {visible: true}, Array.from({length: traces.length - 1}, (_, i) => i));
            }
        """)
        await asyncio.sleep(1)
        
        # 逐个隐藏线条
        for i in range(len(legend_items) - 1):
            await legend_items[i].click()  # 隐藏一条线
            await asyncio.sleep(0.5)
            
            visible_count = await page.evaluate("""
                () => {
                    const plotDiv = document.getElementsByClassName('plotly-graph-div')[0];
                    const traces = plotDiv.data;
                    let count = 0;
                    for (let i = 0; i < traces.length - 1; i++) {
                        if (traces[i].visible !== 'legendonly') {
                            count++;
                        }
                    }
                    return count;
                }
            """)
            
            mean_line_visible = await page.evaluate("""
                () => {
                    const plotDiv = document.getElementsByClassName('plotly-graph-div')[0];
                    const traces = plotDiv.data;
                    const meanTrace = traces[traces.length - 1];
                    return meanTrace.visible !== false && meanTrace.visible !== 'legendonly';
                }
            """)
            
            print(f"隐藏第{i+1}条线后 - 可见线条: {visible_count}, 均值线可见: {mean_line_visible}")
            
            if visible_count <= 1:
                assert not mean_line_visible, f"只有{visible_count}条线时均值线应该隐藏"
            else:
                assert mean_line_visible, f"有{visible_count}条线时均值线应该显示"
        
        print("\n✅ 所有测试通过！")
        print("\n📋 测试总结:")
        print("✓ 初始状态：所有线可见，均值线显示")
        print("✓ 双击图例：只显示一条线，均值线隐藏")
        print("✓ 单击图例：显示多条线，均值线重新显示")
        print("✓ 边界情况：正确处理线条数量变化")
        
        # 保持浏览器打开一段时间以便观察
        print("\n🔍 浏览器将保持打开5秒钟以便观察...")
        await asyncio.sleep(5)
        
        await browser.close()

async def main():
    """主函数"""
    try:
        await test_dynamic_mean_chart()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
