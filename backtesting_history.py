import sys

import joblib
# from portfolio import pred_cfg
from core.fit_val_test import pred_cfg
from core.data_module import KlineDataModule
from core.predictor_config import PredictorConfig

if __name__ == "__main__":	
	pred_cfg.start_date.multi = '2022.01.01'
	# ckpt_name = 'last-v4.ckpt'
	ckpt_name = None
	cfg: PredictorConfig
	model, cfg = pred_cfg.load_model_cfg_from_ckpt(ckpt_name)
	cfg.is_backtesting_history = True
	cfg.batch_size = 16
	cfg.raw_date_dict.train[1] = '2024.08.03'
	# cfg.adapt.first_order = True
	# cfg.adapt.override_online_lr = True
	# cfg.sample_pair_by_step_on_eval = True
	# cfg.adapt_distr_shift.test = True
	# cfg.adapt_distr_shift.inverse_coef = -.5
	cfg.history_file_name = '2024_0809_0420_s20220101'# start_date.multi = '2022.01.01'
	# history_prefix = '2024_0806_1327' # start_date.multi = '2023.01.01'
	# history_prefix = '2024_0727_0359' # 
	# history_prefix = '2024_0727_0459' # start_date.multi = '2022.01.01'
		
	history_kline_dict = cfg.get_history_kline_dict()
	data_module = KlineDataModule(cfg, history_kline_dict, load_db=False)
	trainer = cfg.get_trainer()
	trainer.test(model, dataloaders=data_module.dataloader_dict.online)

	