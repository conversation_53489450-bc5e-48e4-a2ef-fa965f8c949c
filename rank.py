import os
from lightning import LightningModule
from pytorch_lightning.trainer import Trainer
from core.data_module import KlineDataModule
from core.predictor_config import PredictorConfig
import torch
from core.cst import LabelType, TaskType
from core.fit_val_test import fit_val_test
from direct_trading import pred_cfg
torch.set_float32_matmul_precision('medium') 

pred_cfg.script_name = __file__
pred_cfg.task_enum = TaskType.Rank

# pred_cfg.monitor = 'sharpe'
pred_cfg.stop_loss.learnable = True

# pred_cfg.stop_loss.in_use = True
pred_cfg.stop_loss.scale = 0.01
# pred_cfg.stop_loss.with_position = True

pred_cfg.batch_size = 2048
pred_cfg.learning_rate = 20e-5
# pred_cfg.episodic_backward = True # out of memory
# pred_cfg.batch_size = 64
# pred_cfg.learning_rate = 1000e-5
# pred_cfg.rnn_name = 'lstm'
# pred_cfg.embedding_dim = 16
pred_cfg.channel_size = 16
pred_cfg.hidden_size = 2
pred_cfg.seq_len = 42
# pred_cfg.kan_as_fc = True
# pred_cfg.num_block_convs = 5
# pred_cfg.interval_cfg.base = 120
# pred_cfg.label.clip_quantile_classification = True
# pred_cfg.label.start_quantile = 0.25
# pred_cfg.label.end_quantile = 1.
# pred_cfg.label.ema_window = 1

# pred_cfg.num_epochs = 30
# pred_cfg.stop_loss.in_use = True
# pred_cfg.stop_loss.with_position = True
# pred_cfg.stop_loss.scale = 0.01
# pred_cfg.stop_loss.min_ratio = 0.01

pred_cfg.train_end_date = '2023.08.01'
# pred_cfg.val_end_date = '2024.08.01'
# pred_cfg.test_end_date = '2024.07.01'
# pred_cfg.fracdiff = .2
# pred_cfg.cum_feature_num = 7
pred_cfg.execute_phase.test = False
pred_cfg.augment_data.rev = False
pred_cfg.augment_data.train = False
# pred_cfg.merge_history_data = True

# interval_scale = 2
# pred_cfg.seq_len *= interval_scale
# pred_cfg.interval_cfg.base //= interval_scale    
# pred_cfg.label_len.train *= interval_scale
# pred_cfg.label_len.val *= interval_scale
# pred_cfg.step_size.val *= interval_scale
# pred_cfg.label_enum = LabelType.RawNormalized

# pred_cfg.label.softmax_advantage_as_position = True

if __name__ == '__main__':
    
    # pred_cfg.force_train = False
    # pred_cfg.resume_from_ckpt = True
    # pred_cfg.ckpt_file_name = '2024_0910_223748-epoch=11-metric=1.824.ckpt'
    # pred_cfg.interval_cfg.base = 120



    pred_cfg.n_codes = 20

    
    pred_cfg.num_epochs = 50
    



    # pred_cfg.optimize_cross_entropy = True
    # pred_cfg.limit_shift_scale = 0.005
    # pred_cfg.use_scaler = False
    # pred_cfg.feature_category.normalize_original = True
    # pred_cfg.feature_category.original = True
    # pred_cfg.feature_category.rolling = False
    # pred_cfg.feature_category.temporal = True
    # pred_cfg.use_batch_norm = True
    # pred_cfg.use_projection = True
    # pred_cfg.optimize_to_oracle = True
    # pred_cfg.channel_att_after_rnn = True
    # pred_cfg.channel_att_after_tcn = True
    

    # pred_cfg.tcn_before_rnn = True
    # pred_cfg.num_tcn_blocks = 5
    
    # pred_cfg.tcn_kernel_size = 1
    # pred_cfg.model_name = 'rnn'    
    pred_cfg.model_name = 'tcn'   
    # pred_cfg.tcn_fc_dim_list = [128]
    # pred_cfg.channel_dim = 16
    # pred_cfg.dropout_rate = 0.2
    # pred_cfg.learning_rate = 5e-5
    # pred_cfg.ckpt_file_name = '2024_0904_173450-epoch=39-metric=0.908.ckpt'
    # pred_cfg.merge_history_data = True
    # pred_cfg.start_date.multi = '2021.01.01'
    # pred_cfg.start_date.multi = '2022.01.01'
    # pred_cfg.train_end_date = '2024.08.01'
    # pred_cfg.history_prefix = '2024_0828_005253_s20210101'
    pred_cfg.history_file_name = '2024_0911_054649_s20210101'
    # pred_cfg.start_date.multi = '2022.01.01'
    # pred_cfg.history_prefix = '2024_0828_004611_s20220101'
    fit_val_test(pred_cfg)