dbName = "dfs://monthly"
// dbName = "dfs://monthTest"
// tbName = "day_ohlc"
// tbName = "ohlc_1min"
tbName = "ohlc_1min"
dir = "/root/python-rl-trading/backUp/"
// backup(backupDir=dir, dbPath=dbName)
// backup(backupDir=dir, dbPath=dbName, force=true, parallel=true, snapshot=true, tableName=tbName)
getBackupStatus()
// getBackupMeta(dir, dbName, partition, tbName)
// backupTable(dir, dbName, tbName)
// checkBackup(backupDir=dir, dbPath=dbName, tableName=tbName)


newTbName = tbName
// restoreTable(dir, dbName, tbName, newTableName=newTbName)
migrate(dir, dbName, tbName)