COL_NAMES = ["open_time","open","high","low","close","volume","interval","quote_volume","count","taker_buy_volume","taker_buy_quote_volume","code"]	
folder = "/home/<USER>/crypto/futures/um/monthly/klines/"
interval = "1m"
dbName = "dfs://monthly"
// tbName = "day_ohlc"
tbName = "ohlc_1min"
tbFunc = loadTable{dbName, tbName}
tmpTB = tbFunc()
start_date = 2024.03.01 // NOTE: don't change most of case

codeTB = select sum(quote) as sum_quote, min(open_time), size(code) as code_size, size(code)/1440 as actual_days, 1 + (max(open_time) - min(open_time))/60000/1440 as required_days, int(sum(quote)/size(code)) as quote_size_ratio
from tmpTB group by code having min(open_time) <= start_date order by quote_size_ratio desc
// max_actual_days = max(codeTB.actual_days)
// print("max_actual_days = ", max_actual_days)
codeTB

missTB = select * from codeTB where actual_days < required_days
missTB

missing_days = dict(SYMBOL, ANY)
for (cd in missTB.code){
    tmp = []
    orderedTB = select code, open_time from tmpTB where cd == code order by open_time//, open_time >= start_date//, code like "%USDT"
    diffTB = select *, open_time.deltas() as time_diff from orderedTB
    msTB = select *, time_diff/1440/60000 as days from diffTB where time_diff > 60000
    // print(msTB)    
    for (r in msTB){
        for (d in 1..r.days){
            msStamp = temporalAdd(r.open_time, -d, "d")
            tmp.append!(msStamp)          
        }
    }
    if(tmp.size() > 0){
        missing_days[cd] = tmp
    } 
}
missing_days


def transType(code, interval, colNames, mutable memTB){
	memTB.rename!(colNames)
	memTBschema = memTB.schema().colDefs
	timeCol = timestamp(memTB[memTBschema.name[0]])
	
	print(code)
	n = size(timeCol)
	m = size(memTBschema.name)
	newOrder = memTBschema.name.copy()
	newOrder[1] = 'code'
	newOrder[2] = 'interval'
	newOrder[3] = 'count'
	newOrder[4: 9] = memTBschema.name[1: 6]
	newOrder[9] = 'quote_volume'
	newOrder[10:] = memTBschema.name[9: 11]
	print("n=%i, m=%i".stringFormat(n,m))
	intervalCol = array(SYMBOL, n, n, interval)
	codeCol = array(SYMBOL, n, n, code)
	return memTB.replaceColumn!(memTBschema.name[0], timeCol).replaceColumn!('interval', intervalCol).replaceColumn!('code', codeCol).reorderColumns!(newOrder)
}


for (cd in missing_days.keys()){
    days = missing_days[cd]
    for (day in days){
        tmpPath = "/%W/%W-%W-%W.csv".stringFormat(day.format("yyyy-MM"), cd, interval, day.format("yyyy-MM-dd"))
        filePath = folder + cd + "/" + interval + tmpPath
        if (!exists(filePath)) {
            print("file %W not exist!".stringFormat(filePath))        
            continue        
        }
        print("loading %W".stringFormat(filePath))
        // continue
        dbh = dbName.database()
        dbh.loadTextEx(tableName=tbName, partitionColumns=`code`open_time, filename=filePath, 
        transform=transType{cd, interval, COL_NAMES,})  
    }
}
// (2020.02.01).format("yyyy-MM")