interval = "1m"
dbName = "dfs://monthly"
// tbName = "day_ohlc"
tbName = "ohlc_1min"
tbFunc = loadTable{dbName, tbName}
tmpTB = tbFunc()
end_date = 2025.03.01 // NOTE: don't change most of case

codeTB = select sum(quote) as sum_quote, min(open_time), size(code) as code_size, size(code)/1440 as actual_days, 1 + (max(open_time) - min(open_time))/60000/1440 as required_days, int(sum(quote)/size(code)) as quote_size_ratio
from tmpTB group by code having min(open_time) <= end_date order by quote_size_ratio desc
// max_actual_days = max(codeTB.actual_days)
// print("max_actual_days = ", max_actual_days)
codeTB

missTB = select * from codeTB where actual_days < required_days
missTB

missing_days = dict(SYMBOL, ANY)
for (cd in missTB.code){
    tmp = []
    orderedTB = select code, open_time from tmpTB where cd == code order by open_time//, open_time >= end_date//, code like "%USDT"
    diffTB = select *, open_time.prev() as prev_time, open_time.deltas() as time_diff from orderedTB
    msTB = select *, time_diff/1440/60000 as days from diffTB where time_diff > 60000
    print(msTB)    
    for (r in msTB){
        for (d in 1..r.days){
            msStamp = temporalAdd(r.open_time, -d, "d")
            tmp.append!(msStamp)          
        }
    }
    if(tmp.size() > 0){
        missing_days[cd] = tmp
    }    
}
missing_days
// print(missing_days.keys())
// select top 10 * from DB_NAME.loadTable("ohlc_1min") where code == 'TLMUSDT' order by open_time //desc