//建库
if (existsDatabase("dfs://sh_entrust"))
{
	dropDatabase("dfs://sh_entrust")
}
create database "dfs://sh_entrust" partitioned by VALUE(2022.01.01..2022.01.03), HASH([SYMBOL, 10]), engine='TSDB'

//建表
create table "dfs://sh_entrust"."entrust"(
	SecurityID SYMBOL,
	TransactTime TIMESTAMP,
	valOrderNoue INT,
	Price DOUBLE,
	Balance INT,
	OrderBSFlag SYMBOL,
	OrdType SYMBOL,
	OrderIndex INT,
	ChannelNo INT,
	BizIndex INT)
partitioned by TransactTime,SecurityID,
sortColumns = [`SecurityID,`TransactTime]

//导入文件数据
db = database("dfs://sh_entrust")
def transType(mutable memTable)
{
	return memTable.replaceColumn!(`col0,string(memTable.col0)).replaceColumn!(`col1,datetimeParse(string(memTable.col1),"yyyyMMddHHmmssSSS")).replaceColumn!(`col5,string(memTable.col5)).replaceColumn!(`col6,string(memTable.col6))
}
filePath = "E:/Github/DolphinDB/Tutorials_CN-master/data/LoadDataForPoc/Entrust.csv"
loadTextEx(dbHandle = db, tableName = `entrust, partitionColumns = `col1`col0, filename = filePath, skipRows = 0,transform = transType)

//查看数据
select top 10 * from loadTable("dfs://sh_entrust",`entrust)

//使用cutPoints(X, N, [freq])将数据均匀分区
dates=2020.10.01..2020.10.29;
syms="A"+string(1..13);
syms.append!(string('B'..'Z'));
buckets=cutPoints(syms,5);//cutpoints
t1=table(take(syms,10000) as stock, rand(dates,10000) as date, rand(10.0,10000) as x);
dateDomain = database("", VALUE, dates);
symDomain = database("", RANGE, buckets);
stockDB = database("dfs://stockDBTest", COMPO, [dateDomain, symDomain]);
pt = stockDB.createPartitionedTable(t1, `pt, `date`stock).append!(t1);
// select top 30 * from pt
select top 30 * from loadTable("dfs://stockDBTest",`pt)

//查看文件表结构和数据类型
filePath = "G:/future_min.csv"
schemaTB = extractTextSchema(filename = filePath, skipRows = 0)
schemaTB
extractTextSchema(filename = filePath)
t = ploadText(filePath)
t

tmp = select distinct datetime, code from t where regexFind(code, 'L[789]$') > 0
tmp = select distinct datetime, code, type from t where regexFind(code, 'L8$') > 0 and type='1min'
tmp_ = select datetime, code from t where regexFind(code, 'L8$') > 0
cnt = select count(*) from tmp group by tmp.code
t_with_L = select datetime, code from t where code like '%L%'
t_with_L

// string('B'..'Z')
// string(1..13)
// syms="A"+string(1..13)
// syms
// cutPoints(syms,4)

dataFilePath = "G:/data/futures/um/monthly/klines/BTCUSDT/1d/BTCUSDT-1d-2020-01.csv"
dataFilePath.split("klines/")[1].split("/")
dbPath = "dfs://monthTest"
tbPath = "day_ohlc"
if (existsDatabase(dbPath))
{
	dropDatabase(dbPath)
}
create database "dfs://monthTest" partitioned by VALUE(2020.01M..2022.02M), HASH([SYMBOL, 10]), engine='TSDB'
create table "dfs://monthTest"."day_ohlc"(
	open_time TIMESTAMP,
	open DOUBLE,
	high DOUBLE,
	low DOUBLE,
	close DOUBLE,
	volume DOUBLE,
	interval SYMBOL,// close_time TIMESTAMP,
	quote DOUBLE,
	count INT,
	buy_volume DOUBLE,
	buy_quote DOUBLE,
	code SYMBOL,// ignore
	)
partitioned by open_time,code,
sortColumns = [`code, `open_time]


pt = dbPath.loadTable(tbPath)
folder = "G:/data/futures/um/monthly/klines/"
codes = folder.files().filename
// currencies[11]
interval = "1d"
// stringFormat("%W", interval)
for (code in codes){
	codePath = folder + code + "/" + interval
	// codePath = folder + code + "/%W".stringFormat(interval)
	files = codePath.files().filename
	for (file in files){
		filePath = codePath + "/" + file
		fileTB = filePath.ploadText()
		// fileTB
		fileTBschema = fileTB.schema().colDefs
		// print(fileTBschema)
		// for (i in [0, 6]){
		// 	newCol = timestamp(fileTB[fileTBschema.name[i]])
		// 	fileTB.replaceColumn!(fileTBschema.name[i], newCol)
		// }
		newCol = timestamp(fileTB[fileTBschema.name[0]])
		fileTB.replaceColumn!(fileTBschema.name[0], newCol)
		n = count(fileTB)
		m = count(fileTBschema.name)
		intervalCol = array(SYMBOL, n, n, interval)
		codeCol = array(SYMBOL, n, n, code)
		fileTB.replaceColumn!(fileTBschema.name[6], intervalCol)
		// fileTB[`interval] = intervalCol
		fileTB.replaceColumn!(fileTBschema.name[m - 1], codeCol)
		fileTBschema = fileTB.schema().colDefs
		print(fileTBschema)
		pt.append!(fileTB)
	}
}
select top 100 * from pt
select count(*) from pt

def writeData(db,file){
	loop(loadTextEx{db,`tb,`id,},file)
}
parallelLevel=10
for(x in dataFilePath.cut(100/parallelLevel)){
	submitJob("loadData"+parallelLevel,"loadData",writeData{db,x})
};

timestamp(1577836800000)// + 1
tmpTB = loadText(dataFilePath)
tmpTBSchema = tmpTB.schema().colDefs
schemaTB=extractTextSchema(dataFilePath)
select top 5 * from tmpTB;

// update schemaTB set type="TIMESTAMP" where name = "col0"
// update schemaTB set type="TIMESTAMP" where name = "col6"
// update schemaTB set type="SYMBOL" where name = "col11";
// schemaTB
tmpTB = loadText(filename=dataFilePath,schema=schemaTB);
// tmpTB[`col0] = datetime(tmpTB.col0)
// tmpTB[`col6] = datetime(tmpTB.col6)
// tmpTB[`col11] = "BTCUSDT"
tmpTB.addColumn(`code, SYMBOL)
// addColumn(tmpTB, `code, SYMBOL)
// addColumn(tmpTB, `asset, SYMBOL)
addColumn(tmpTB, `interval, SYMBOL)
tmpTB[`code] = "BTC"
tmpTB[`asset] = "USDT"
tmpTB[`open_time] = timestamp(tmpTB.col0)
tmpTB[`interval] = "1d"
tmpTB.schema().colDefs


dbh = database(dbPath)
dbh.schema()
schema(dbh).partitionSchema

// def transType(code, mutable memTable){
// 	return memTable.replaceColumn!(`col0,timestamp(memTable.col0)).replaceColumn!(`col6,timestamp(memTable.col6))
// }
// loadTextEx(dbHandle = dbh, tableName = tbPath, partitionColumns = `col1`col0, filename = dataFilePath, skipRows = 0,transform = transType{code,})
// select top 30 * from loadTable("dfs://monthTest",`day_ohlc)

