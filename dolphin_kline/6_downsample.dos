EXCLUDED_CODES = ['BTCSTUSDT', 'FTTUSDT', 'GALUSDT', 'SCUSDT', 'CVCUSDT', 'HNTUSDT', 'SRMUSDT', 'TOMOUSDT', 'BTSUSDT', 'ANTUSDT', 'BLUEBIRDUSDT', 'COCOSUSDT', 'AUDIOUSDT', 'FOOTBALLUSDT', 'MBLUSDT', 'RNDRUSDT']
INTERVALS = [3, 5, 15, 30, 60, 120, 240, 360, 480, 720, 1440]
DB_NAME = "dfs://monthly"
TABLE_NAME = "ohlc_1min"
// START_DATE = 2020.01.01
START_DATE = 2025.02.01
CHECK_DATE_0 = 2020.12.01
CHECK_DATE_1 = temporalAdd(CHECK_DATE_0, 1, "M")
END_DATE = 2025.03.01 - 1
// from scratch DROP_TABLE = true
// DROP_TABLE = true
DROP_TABLE = false
ALL_IN_ONE = true
dbh = DB_NAME.database()
// dbh.getTables()
tbFunc = loadTable{DB_NAME, TABLE_NAME}
template = select top 1 * from tbFunc()
// template.dropColumns!(`interval)

def monthRange(sd, ed): month(sd)..month(ed)
def dateRange(sd): sd..(temporalAdd(sd, 1, "M") - 1)
mr = monthRange(START_DATE, END_DATE)
d = date(mr[0])

codeData = select sum(quote)/size(code) as quote_ratio
from tbFunc() 
// where open_time >= CHECK_DATE_0 //and code like "%USDT"  
group by code 
// having min(open_time) <= CHECK_DATE_1 
order by quote_ratio desc
print(codeData.code.size())

droppedSet = set(array(STRING, 0))
codes = codeData.code
// codes_ = codeData.code
// codes_ = ['BNXUSDT']
// codes = ['APTUSDT', 'QNTUSDT',]
// NOTE: don't change most of case
if (ALL_IN_ONE) codes = codes[:1]

for (cd in codes){
    for (m in mr) {
        dr = dateRange(date(m))
        if (ALL_IN_ONE) {
            mtb = select * from tbFunc() where date(open_time) in dr order by code, open_time          
        } else {
            mtb = select * from tbFunc() where code == cd, date(open_time) in dr order by code, open_time 
        }
        // cds = select distinct(code) from mtb
        // print(cds)
        // continue
        if (mtb.size() == 0) continue

        for (i in INTERVALS) {
            intervalStr = "%im".stringFormat(i)
            interval = i * 60000
            if (ALL_IN_ONE) {
                intervalCodeStr = "_%imin".stringFormat(i)
            } else {
                intervalCodeStr = "_%i_%W".stringFormat(i, cd)
            }            
            tbName = 'ohlc' + intervalCodeStr
            if (DB_NAME.existsTable(tbName)){
                if (DROP_TABLE and not intervalCodeStr.in(droppedSet)) {
                    dbh.dropTable(tbName)
                    print("dropping table: %W".stringFormat(tbName))
                    droppedSet.append!(intervalCodeStr)
                    dbh.createPartitionedTable(table=template, tableName=tbName, partitionColumns=`open_time`code, sortColumns=`code`open_time, keepDuplicates=FIRST)
                }
            }
            else{
                droppedSet.append!(intervalCodeStr)
                print("creating table: %W".stringFormat(tbName))
                dbh.createPartitionedTable(table=template, tableName=tbName, partitionColumns=`open_time`code, sortColumns=`code`open_time, keepDuplicates=FIRST)
            } 

            mtb[`min_group] = mtb.open_time / interval
            ntb = select
            first(open_time) as open_time,
            code,            
            intervalStr as interval,            
            sum(count) as count,                       
            first(open) as open, 
            max(high) as high, 
            min(low) as low, 
            last(close) as close, 
            sum(volume) as volume,            
            sum(quote) as quote,
            sum(buy_volume) as buy_volume,
            sum(buy_quote) as buy_quote 
            from mtb group by code, min_group order by code, min_group
            ntb.dropColumns!(`min_group)
            // ntb[`interval] = invertal

            print(ntb.size(), ntb.cols(), m, tbName)
            // ntb.dropColumns!(`interval)
            tbh = DB_NAME.loadTable(tbName)
            // print(tbh.schema().colDefs)
            tbh.append!(ntb)
        }
    }
}

// tbs = dbh.getTables()
dbh.getTables()
// select count(*) from tbh
// tb = select top 10 * from tbh
// select top 10 * from DB_NAME.loadTable("ohlc_1440min") where code == 'WAVESUSDT' order by open_time

