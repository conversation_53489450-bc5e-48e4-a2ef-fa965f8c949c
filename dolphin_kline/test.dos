folder = "G:/data/futures/um/monthly/klines/"
interval = "15m"
dbName = "dfs://monthly"
// dbName = "dfs://monthTest"
// tbName = "day_ohlc"
// tbName = "ohlc_1min"
symbol = 'ETHBUSD'
tbName = "ohlc_1min"
// dir = "G:/DolphinDB_Win64_V2.00.9_JIT/server/local8848/backUp"
// backupTable(dir, dbName, tbName)
// tbName = "since_2021_" + interval
tbFunc = loadTable{dbName, tbName}
tmpTB = tbFunc()
start_date = 2021.01.01

select * from tbFunc() where code == symbol, volume > 0, open_time >= start_date

start_date = 2021.01.01
DB_NAME = "dfs://monthly"
TABLE_STR = "since_2020_"
dbh = DB_NAME.database()
tbFunc = loadTable{DB_NAME, TABLE_STR + "60m"}

high_div = select (next(high) / close - 1) as value from tbFunc() where  open_time >= start_date
low_div = select (1 - next(low) / close) as value from tbFunc() where  open_time >= start_date

for (i in 1..10) {
    base_ratio = i * 0.0001
    high_ratio = sum(high_div < base_ratio) / double(size(high_div))
    low_ratio = sum(low_div < base_ratio) / double(size(low_div))
    print('%F high_ratio = %F'.stringFormat(base_ratio, high_ratio.value))
    print('%F low_ratio = %F'.stringFormat(base_ratio, low_ratio.value))
}    
