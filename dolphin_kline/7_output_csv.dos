INTERVALS = [30, 60, 120, 240, 360, 480, 720, 1440]

DB_NAME = "dfs://monthly"
TABLE_STR = "since_2020_"
dbh = DB_NAME.database()
codeData = select sum(quote)/size(code) as quote_ratio
from loadTable(DB_NAME, TABLE_STR + "240m") group by code order by quote_ratio desc
print(codeData.code.size())
codeData
folder = "G:/crypto/"
if (!folder.exists()) {        
    folder.mkdir()        
}
for (i in INTERVALS) {
    interval_str = "%im".stringFormat(i)
    tbName = TABLE_STR + interval_str
    tbFunc = loadTable{DB_NAME, tbName}   
    savePath = folder + tbName
    if (!savePath.exists()) {        
        savePath.mkdir()        
    }
    for (cd in codeData.code) {
        print(cd)
        tb = (select *, volume - buy_volume as sell_volume, quote - buy_quote as sell_quote from tbFunc() where code = cd)
        saveText(tb, savePath + "/%W.csv".stringFormat(cd))     
        // break
    } 
    // break
}
// select top 100 * from tbFunc()

