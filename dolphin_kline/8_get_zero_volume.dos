EXCLUDED_CODES = ['BTCSTUSDT', 'FTTUSDT', 'GALUSDT', 'SCUSDT', 'CVCUSDT', 'HNTUSDT', 'SRMUSDT', 'TOMOUSDT', 'BTSUSDT', 'ANTUSDT', 'BLUEBIRDUSDT', 'COCOSUSDT', 'AUDIOUSDT', 'FOOTBALLUSDT', 'MBLUSDT', 'RNDRUSDT', 'WAVESUSDT']
// code_list = ['ATOMUSDT', 'APTUSDT', 'QNTUSDT']
interval = "240m"
dbName = "dfs://monthly"
// tbName = "day_ohlc"
tbName = "ohlc_240min"
tbFunc = loadTable{dbName, tbName}
tmpTB = tbFunc()
start_date = 2021.01.01
end_date = 2024.10.01 // NOTE: don't change most of case
codeTB = select top 50 sum(quote)/size(code) as quote_ratio
from tbFunc() where code like "%USDT", code not in EXCLUDED_CODES, open_time >= start_date group by code
having min(open_time) <= start_date order by quote_ratio desc
codeTB

selectedTB = select * from tbFunc() where code in codeTB.code, volume == 0
// selectedTB = select * from tbFunc() where code in code_list, open_time >= start_date, volume == 0
selectedTB


