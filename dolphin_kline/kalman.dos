// 假设你有一个数据表叫做data，其中有两列：'time'和'value'
// 这个数据表包含了你想要进行卡曼滤波处理的时间序列数据
DB_NAME = "dfs://monthly"
TABLE_NAME = "since_2020_1440m"
ltb = loadTable(DB_NAME, TABLE_NAME)
tb = select open_time, close from ltb where code = 'BTCUSDT'

// 定义卡曼滤波函数
def kalmanSmooth(data, Q, R){
    n = size(data)
    x_est = array(DOUBLE, n)
    P = array(DOUBLE, n)
    x_est[0] = data[0]
    P[0] = 1.0
    for (i in 1:n-1){
        // 预测
        x_pred = x_est[i-1]
        P_pred = P[i-1] + Q

        // 更新
        K_gain = P_pred / (P_pred + R)
        x_est[i] = x_pred + K_gain * (data[i] - x_pred)
        P[i] = (1 - K_gain) * P_pred
    }
    return x_est;
}
Q=0.001; R=0.01;
kms = kalmanSmooth(tb.close,Q,R)
kms
// 调用卡曼滤波函数进行平滑处理，并将结果添加到原始数据表中

// tb.update!(kalmanSmooth(tb.close,Q,R) as smoothValue).sort!("open_time")
