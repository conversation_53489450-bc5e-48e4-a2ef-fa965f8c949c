// dbName = "dfs://monthTest"
dbName = "dfs://monthly"
// tbName = "day_ohlc"
tbName = "ohlc_1min"
tbFunc = loadTable{dbName, tbName}

if (existsDatabase(dbName))
{
	dropDatabase(dbName)
}
if (dbName.existsTable(tbName))
{
	dbName.database().dropTable(tbName)
}

create database "dfs://monthly"
// create database "dfs://monthTest" 
partitioned by VALUE(2020.01M..2025.01M), HASH([SYMBOL, 10]), engine='TSDB'

create table "dfs://monthly"."ohlc_1min"(
// create table "dfs://monthly"."day_ohlc"(
	open_time TIMESTAMP,
	code SYMBOL,// ignore	
	interval SYMBOL,// close_time TIMESTAMP,	
	count INT,
	open DOUBLE,
	high DOUBLE,
	low DOUBLE,
	close DOUBLE,
	volume DOUBLE,
	quote DOUBLE,
	buy_volume DOUBLE,
	buy_quote DOUBLE,
	)
partitioned by open_time,code,
sortColumns = [`code, `open_time],
keepDuplicates = FIRST
select top 10 * from tbFunc()
// select count(code) from tbFunc()