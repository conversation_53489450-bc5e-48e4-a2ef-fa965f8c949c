COL_NAMES = ["open_time","open","high","low","close","volume","interval","quote_volume","count","taker_buy_volume","taker_buy_quote_volume","code"]	
EXCLUDED_CODES = ['BTCSTUSDT', 'FTTUSDT', 'GALUSDT', 'SCUSDT', 'CVCUSDT', 'HNTUSDT', 'SRMUSDT', 'TOMOUSDT', 'BTSUSDT', 'ANTUSDT', 'BLUEBIRDUSDT', 'COCOSUSDT', 'AUDIOUSDT', 'FOOTBALLUSDT', 'MBLUSDT', 'RNDRUSDT', 'WAVESUSDT']
dbName = "dfs://monthly"
// tbName = "day_ohlc"
tbName = "ohlc_1min"
tbFunc = loadTable{dbName, tbName}
IS_UPDATING = true
// IS_UPDATING = false
SKIPPING_WHEN_UPDATING = false
// SKIPPING_WHEN_UPDATING = true
// START_DATE = 2020.01M
// END_DATE = 2020.12M
START_DATE = 2025.02M
END_DATE = 2025.03M
SKIP_FIRST_MONTH = true
pt = dbName.loadTable(tbName)
folder = "/home/<USER>/crypto/futures/um/monthly/klines/"
filenames = folder.files().filename
// filenames = []
// codes = ['APTUSDT', 'QNTUSDT']
codes = []
for (code in filenames) {
	if (code.endsWith('USDT') and not code in EXCLUDED_CODES) {
		codes.append!(code)
	}
}
print(codes.size())
interval = "1m"
// interval = "1d"
// stringFormat("%W", interval)
useLoadTextEx = true
// useLoadTextEx = false

firstMonths = dict(SYMBOL, ANY)
for (code in codes){
	codePath = folder + code + "/" + interval
	// codePath = folder + code + "/%W".stringFormat(interval)
	files = codePath.files()
	firstMonth = END_DATE
	for (file in files){
		if (file.isDir){
			print(file.filename)
			continue
		}
		filePath = codePath + "/" + file.filename
		mth_date = temporalParse(filePath.strReplace(".csv", "").right(7), "yyyy-MM")
		firstMonth = min(mth_date, firstMonth)
	}
	firstMonths[code] = firstMonth
}
firstMonths
firstMonths['ICPUSDT'] = 2022.09M
firstMonths['TLMUSDT'] = 2023.03M
firstMonths['BNXUSDT'] = 2023.02M
def transType(code, interval, colNames, mutable memTB){
	memTB.rename!(colNames)
	memTBschema = memTB.schema().colDefs
	timeCol = timestamp(memTB[memTBschema.name[0]])
	
	// print(code)
	n = size(timeCol)
	m = size(memTBschema.name)
	newOrder = memTBschema.name.copy()
	newOrder[1] = 'code'
	newOrder[2] = 'interval'
	newOrder[3] = 'count'
	newOrder[4: 9] = memTBschema.name[1: 6]
	newOrder[9] = 'quote_volume'
	newOrder[10:] = memTBschema.name[9: 11]
	print("%Wn=%i, m=%i".stringFormat(code, n, m))
	intervalCol = array(SYMBOL, n, n, interval)
	codeCol = array(SYMBOL, n, n, code)
	return memTB.replaceColumn!(memTBschema.name[0], timeCol).replaceColumn!('interval', intervalCol).replaceColumn!('code', codeCol).reorderColumns!(newOrder)
}

for (code in codes){
	codePath = folder + code + "/" + interval
	// codePath = folder + code + "/%W".stringFormat(interval)
	files = codePath.files()
	// SKIP first month data
	if ((not IS_UPDATING) and SKIP_FIRST_MONTH) {
		first_month = firstMonths[code]
		print("skipping %W first month: %W".stringFormat(code, first_month.string()))
		start_date = temporalAdd(first_month, 1M)
	} else {
		start_date = START_DATE
	}	
	
	for (file in files){
		filePath = codePath + "/" + file.filename
		if (file.isDir){
			print("skipping dir: %W".stringFormat(filePath))
			continue
		}

		mth_date = temporalParse(filePath.strReplace(".csv", "").right(7), "yyyy-MM")
		// print(start_date, mth_date, END_DATE)
		// if (SKIPPING_WHEN_UPDATING and (start_date <= mth_date <= END_DATE)){
		if (not start_date <= mth_date <= END_DATE) {
			// print("second month: %M".stringFormat(secondMonth))
			print("skipping file: %W".stringFormat(filePath))
			continue
		}
		// continue
		print("loading file: %W".stringFormat(filePath))
			
		//关闭代理才能正常写入
		if (useLoadTextEx){
			dbh = dbName.database()
			dbh.loadTextEx(tableName=tbName, partitionColumns=`code`open_time, filename=filePath, 
			transform=transType{code, interval, COL_NAMES, })
		}else{
			fileTB = filePath.ploadText()
			fileTBschema = fileTB.schema().colDefs
			// print(fileTBschema)
			// for (i in [0, 6]){
			// 	newCol = timestamp(fileTB[fileTBschema.name[i]])
			// 	fileTB.replaceColumn!(fileTBschema.name[i], newCol)
			// }
			timeCol = timestamp(fileTB[fileTBschema.name[0]])
			fileTB.replaceColumn!(fileTBschema.name[0], timeCol)
			n = count(fileTB)
			m = count(fileTBschema.name)
			intervalCol = array(SYMBOL, n, n, interval)
			codeCol = array(SYMBOL, n, n, code)
			fileTB.replaceColumn!(fileTBschema.name[6], intervalCol)
			// fileTB[`interval] = intervalCol
			fileTB.replaceColumn!(fileTBschema.name[m - 1], codeCol)
			fileTBschema = fileTB.schema().colDefs
			print(fileTBschema)
			pt.append!(fileTB)
		}
	}
}
select top 10 * from tbFunc() where code == 'ETHUSDT', open_time > date(START_DATE)
select count(*) from pt