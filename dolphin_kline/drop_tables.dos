DB_NAME = "dfs://monthly"
dbh = DB_NAME.database()
for (tb in dbh.getTables()) {
    print(tb)
    if (tb == "ohlc_1min") continue
    dbh.dropTable(tb)
}

tbFunc = loadTable{DB_NAME,}
startMonths = dict(SYMBOL, ANY)
// drop data before start_date:
startMonths['ICPUSDT'] = 2022.10M
startMonths['TLMUSDT'] = 2023.04M
startMonths['BNXUSDT'] = 2023.03M
codes = startMonths.keys()
for (tb in dbh.getTables()) {
    print(tb)    
    for (cd in codes) {
        start_date = date(startMonths[cd])
        print(start_date)
        delete from tbFunc(tb) where open_time < start_date, code = cd
    }
}