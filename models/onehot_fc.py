import torch
import torch.nn as nn

class OneHotFC(nn.Module):
    def __init__(self, input_size, onehot_dim, output_size):
        super(OneHotFC, self).__init__()
        self.mulitple = 16
        self.fc0 = nn.Linear(input_size, output_size)
        self.bypass_onehot = False
        # self.bypass_onehot = True
        self.fc1 = nn.Linear(input_size, onehot_dim * self.mulitple)
        self.act = nn.Mish()
        self.fc2 = nn.Linear(onehot_dim * self.mulitple, output_size)
        self.onehot_dim = onehot_dim
        

    def forward(self, x, onehot):
        x = torch.cat([x, onehot], dim=1)
        if self.bypass_onehot:
            x = self.fc1(x)
            x = self.act(x)
            # n = x.size(1) // self.onehot_dim
            onehot = onehot.repeat(1, self.mulitple)
            x = x * onehot
            x = self.fc2(x)
        else:
            x = self.fc0(x)
        return x