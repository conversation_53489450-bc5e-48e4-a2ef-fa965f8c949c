from ast import alias
from copy import deepcopy
from math import e
import os
from shutil import copyfile
from matplotlib import pyplot as plt, dates as mdates
import numpy as np
import pandas as pd
import polars as pl
from sklearn.metrics import classification_report, confusion_matrix
import torch
from torch import Tensor, corrcoef
from torch.nn import functional as F
from utils.analyze_results import get_fee, get_mdd_arr, get_top_k_in_one_row, get_top_pnl, get_turnover
from core.backtest import backtest
from core.cst import ActionType, Objective, PositionType, LabelType, TaskType, EntryExitLogitsEnum
from models.lightning_base import LightningBase
from core.predictor_config import PredictorConfig
from core.dot_dict import DotDict as dd
from utils.report_clf_ic import get_hour_of_day, report_clf_ic
from utils.spot_grid import backtest_grid
from utils.torch_select import torch_select


class LightningDRT(LightningBase):

    def __init__(
        self,
        cfg: PredictorConfig,
    ):
        super().__init__(cfg)
        self.position = 0.
        self.pos_neg_return_ratio_dict = dd()


    def on_train_epoch_start(self):        
        self.position = 0.


    def on_validation_epoch_start(self):        
        self.position = 0.

    
    def on_test_epoch_start(self):        
        self.position = 0.


    def calc_pred_loss(self, pred: Tensor, batch_x: Tensor, batch_y: Tensor, phase: str = 'train', save_to_step_dict: bool = True):
        cfg = self.cfg
        direction = (batch_x[:, -1, 7] - batch_x[:, -2, 7]).sign()
        direction_shift = (batch_x[:, -2, 7] - batch_x[:, -3, 7]).sign()
        self.direction = torch.where(direction == 0, direction_shift, direction)
        self.rolling_std = (batch_x[:, -cfg.take_profit.std_window:, 7].diff(1) + 1).log().std(dim=1)
        logits, score_position, shift_ratio, stop_loss_ratio, take_profit_ratio, entry_logits, exit_logits = self.calc_pred(pred)
        # if len(batch_y.shape) != 2:
        #     batch_y = batch_y.reshape(batch_y.shape[0], -1)

        if self.cfg.optimize_allocation:
            history_return = (batch_x[..., 7].diff(1) + 1).log()
            score_position = self.get_allocation(logits, score_position, history_return)
            # asset_return: Tensor = batch_y[..., 0].flatten()
        #     loss = -(score_position * asset_return).sum()
        #     _ = self.calc_loss(logits, score_position, batch_y, phase, shift_ratio, stop_loss_ratio, take_profit_ratio, save_to_step_dict)
        # else:
        loss = self.calc_loss(logits, score_position, batch_y, phase, shift_ratio, stop_loss_ratio, take_profit_ratio, entry_logits, exit_logits, save_to_step_dict)
        return logits, score_position, loss


    def calc_pred(self, pred):
        cfg = self.cfg
        shape = pred.shape
        batch_size = shape[0]
        if len(shape) != 2:
            pred = pred.reshape(shape[0], -1)
        if (shift_scale := cfg.limit_shift_scale) != 0:
            shift_ratio = F.sigmoid(pred[:, 0]) * self.rolling_std * shift_scale
            pred = pred[:, 1:]
        else:
            shift_ratio = torch.zeros(shape[0], device=cfg.device, dtype=torch.float32)

        
        if cfg.stop_loss.in_use:
            stop_loss_min_ratio = cfg.stop_loss.min_ratio
            stop_loss_std_scale = cfg.stop_loss.std_scale
            if cfg.stop_loss.learnable:
                if stop_loss_std_scale > 0:
                    stop_loss_scale = self.rolling_std * stop_loss_std_scale
                else:
                    stop_loss_scale = cfg.stop_loss.scale + stop_loss_min_ratio
                stop_loss_ratio = F.sigmoid(pred[:, 0]) * stop_loss_scale
                pred = pred[:, 1:]
            else:
                stop_loss_ratio = stop_loss_min_ratio * torch.ones(batch_size, device=cfg.device, dtype=torch.float32)
        else:            
            stop_loss_ratio = torch.ones(batch_size, device=cfg.device, dtype=torch.float32)

        if cfg.take_profit.in_use:
            take_profit_min_ratio = cfg.take_profit.min_ratio
            take_profit_std_scale = cfg.take_profit.std_scale
            if take_profit_std_scale > 0:
                take_profit_scale = self.rolling_std * take_profit_std_scale
            else:
                take_profit_scale = cfg.take_profit.scale + take_profit_min_ratio
            if cfg.take_profit.learnable:
                take_profit_ratio = F.sigmoid(pred[:, 0]) * take_profit_scale
                pred = pred[:, 1:]
            else:
                take_profit_ratio = take_profit_min_ratio * torch.ones(batch_size, device=cfg.device, dtype=torch.float32)
        else:
            take_profit_ratio = torch.ones(batch_size, device=cfg.device, dtype=torch.float32)

        if cfg.use_entry_idx:
            entry_logits = pred[:, :cfg.label_len]
            pred = pred[:, cfg.label_len:]
        else:
            entry_logits = torch.full((batch_size, cfg.label_len), -np.inf, device=cfg.device, dtype=torch.float32)
            entry_logits[:, 0] = 0
        
        if cfg.use_exit_idx:
            exit_logits = pred[:, :cfg.label_len]
            pred = pred[:, cfg.label_len:]
        else:
            exit_logits = torch.full((batch_size, cfg.label_len), -np.inf, device=cfg.device, dtype=torch.float32)
            exit_logits[:, -1] = 0

        # logits = pred
        logits_shape = pred.shape
        objective_enum = cfg.objective_enum
        if logits_shape[1] == 1:
            if objective_enum in [Objective.PNL, Objective.Sharpe, Objective.Sortino]:
                if cfg.position_enum == PositionType.Long:
                    score_position = F.sigmoid(pred)
                else:
                    score_position = F.tanh(pred)
            elif objective_enum == Objective.BCE:
                score_position = F.sigmoid(pred)
                if cfg.position_enum == PositionType.Long:
                    ...
                else:
                    score_position = score_position * 2 - 1
            else:
                if cfg.position_enum == PositionType.Long:
                    score_position = pred.clamp(min=0)
                else:
                    score_position = pred
            score_position = score_position.flatten()
        elif logits_shape[1] == 2:
            side = torch.argmax(pred, dim=1) * (2 ** (logits_shape[1] == 2)) - 1
            max_position = torch.softmax(pred, dim=1).max(dim=1)[0]
            
            # if logits_shape[1] == 2:
            if cfg.label.softmax_advantage_as_position:
                max_position = 2 * max_position - 1
                score_position = side * max_position
            elif cfg.label.diff_as_position:
                score_position = F.tanh(pred[:, 0] - pred[:, 1]).abs() / 2 + 0.5
            else:
                if cfg.label.min_max_softmax:
                    softmax_min = 1 / logits_shape[1]
                    max_position = (max_position - softmax_min) / (1 - softmax_min)
                score_position = side * max_position

        else:
            side = torch.zeros_like(pred[:, 0], device=cfg.device, dtype=torch.float32)
            max_idx = torch.argmax(pred, dim=1)
            side[max_idx == 0] = -1
            side[max_idx == logits_shape[1] - 1] = 1
            max_position = torch.softmax(pred, dim=1).max(dim=1)[0]
            if cfg.label.min_max_softmax:
                softmax_min = 1 / logits_shape[1]
                max_position = (max_position - softmax_min) / (1 - softmax_min)
            score_position = side * max_position

        if cfg.action_enum == ActionType.Momentum:
            score_position = score_position * self.direction

        return pred, score_position, shift_ratio, stop_loss_ratio, take_profit_ratio, entry_logits, exit_logits


    def calc_loss(self, logits, score_position, batch_y, phase, shift_ratio, stop_loss_ratio, take_profit_ratio, entry_logits, exit_logits, save_to_step_dict=True):
        cfg = self.cfg
        result = self.direct_loss(logits, score_position, batch_y, shift_ratio, stop_loss_ratio, take_profit_ratio, entry_logits, exit_logits, phase, self.position)
        loss, pnl, fee, filled_position, final_filled_return, final_position, is_market, is_filled, is_take_profit, is_stop_loss, asset_return, high_return, low_return = result
        if not cfg.shuffling.train:
            self.position = final_position[-1:].detach()
        if save_to_step_dict:
            self.step_dict[phase].pnl.append(pnl.detach().cpu().numpy())
            self.step_dict[phase].fee.append(fee.detach().cpu().numpy())
            self.step_dict[phase].asset_return.append(asset_return.detach().cpu().numpy())
            self.step_dict[phase].high_return.append(high_return.detach().cpu().numpy())
            self.step_dict[phase].low_return.append(low_return.detach().cpu().numpy()) 
            self.step_dict[phase].filled_position.append(filled_position.detach().cpu().numpy())
            self.step_dict[phase].final_position.append(final_position.detach().cpu().numpy())
            self.step_dict[phase].final_filled_return.append(final_filled_return.detach().cpu().numpy())
            self.step_dict[phase].stop_loss_ratio.append(stop_loss_ratio.detach().cpu().numpy())        
            self.step_dict[phase].is_market.append(is_market.detach().cpu().numpy())
            self.step_dict[phase].is_filled.append(is_filled.detach().cpu().numpy())
            self.step_dict[phase].is_take_profit.append(is_take_profit.detach().cpu().numpy())
            self.step_dict[phase].is_stop_loss.append(is_stop_loss.detach().cpu().numpy())

        return loss
    

    def process_epoch(self, loss: np.ndarray, pred: np.ndarray, actual: np.ndarray, phase: str, epoch_str: str) -> None:
        cfg = self.cfg
        n_codes = cfg.n_codes
        
        pnl_arr = np.concatenate(self.step_dict[phase].pnl, axis=None)
        if cfg.load_all_codes:
            ...
            # pnl_df = pd.DataFrame(self.step_dict[phase].pnl, index=cfg.valid_index_dict[phase])
            # pnl_arr = pnl_df.groupby(level='group_idx').mean().values
        else:
            # pnl_arr = pnl_arr.reshape(n_codes, -1).mean(axis=0)
            ...
        pnl_mean = pnl_arr.mean()
        pnl_std = pnl_arr.std()
        asset_return_arr = np.concatenate(self.step_dict[phase].asset_return, axis=None)
        high_return_arr = np.concatenate(self.step_dict[phase].high_return, axis=None)
        low_return_arr = np.concatenate(self.step_dict[phase].low_return, axis=None)
        filled_position_arr = np.concatenate(self.step_dict[phase].filled_position, axis=None)
        final_position_arr = np.concatenate(self.step_dict[phase].final_position, axis=None)
        final_filled_return_arr = np.concatenate(self.step_dict[phase].final_filled_return, axis=None)
        is_market_arr = np.concatenate(self.step_dict[phase].is_market, axis=None)
        is_filled_arr = np.concatenate(self.step_dict[phase].is_filled, axis=None)
        is_stop_loss_arr = np.concatenate(self.step_dict[phase].is_stop_loss, axis=None)
        stop_loss_ratio_arr = np.concatenate(self.step_dict[phase].stop_loss_ratio, axis=None)
        if len(label_list := self.step_dict[phase].label) > 0:
            label = np.concatenate(label_list, axis=None)
        else:
            label = None
        # abs_position_sum = abs(position_arr).mean()
        position_max = final_position_arr.max()
        position_min = final_position_arr.min()
        position_mean = final_position_arr.mean()     
        sharpe = (pnl_mean / pnl_std)
        pos_neg_count_ratio = (asset_return_arr > 0).sum() / (asset_return_arr < 0).sum()
        if phase not in self.pos_neg_return_ratio_dict:
            pos_neg_return_ratio = asset_return_arr.clip(0).sum() / -asset_return_arr.clip(-np.inf, 0).sum()
            self.pos_neg_return_ratio_dict[phase] = pos_neg_return_ratio
            
        else:
            pos_neg_return_ratio = self.pos_neg_return_ratio_dict[phase]

        print(f'{epoch_str} {phase} {pos_neg_return_ratio = :.5f},\t{pos_neg_count_ratio = :.5f}\n{sharpe = :.5f},\t{pnl_mean = :.5f},\t{pnl_std = :.5f}\n{position_min = :.5f},\t{position_max = :.5f},\t{position_mean = :.5f}\n')
        
        # if not self.cfg.shuffling.train or phase in [
        if phase in [
            'val', 
            'test',
            ]:
            if 'top' in cfg.monitor:
                epoch_dict = dd()
                for col in ['pnl', 'fee', 'asset_return', 'high_return', 'low_return', 'filled_position', 'final_position', 'final_filled_return', 'stop_loss_ratio', 'is_market', 'is_filled', 'is_take_profit', 'is_stop_loss', 'label']:
                    col_list = self.step_dict[phase].get(col)
                    if col_list is not None and len(col_list) > 0:
                        epoch_dict[col] = np.concatenate(col_list, axis=None).flatten()
                if 'high_return' not in epoch_dict:
                    epoch_dict['high_return'] = epoch_dict['asset_return']
                if 'low_return' not in epoch_dict:
                    epoch_dict['low_return'] = epoch_dict['asset_return']
                epoch_dict['position'] = cfg.get_range_masked_position(epoch_dict.filled_position)#('final_position', epoch_dict.filled_position)
                if 'final_filled_return' not in epoch_dict:
                    epoch_dict['final_filled_return'] = epoch_dict['asset_return']
                epoch_df = pd.DataFrame(epoch_dict)
                
                epoch_df['abs_position'] = epoch_df.position.abs()
                if cfg.fine_tune and cfg.fine_tune_idx_filter is None:
                    epoch_df.index = cfg.valid_index_dict.train
                    cfg.fine_tune_idx_filter = (epoch_df.pnl.values < 0)
                else:
                    epoch_df.index = cfg.valid_index_dict[phase]
                group_by_str = cfg.group_by_str     
                if cfg.inverse_hour_position:
                    position = epoch_df['position'].values
                    hour_srs: pd.Series = epoch_df.index.get_level_values('open_time').to_series()
                    hour_cond = hour_srs.dt.hour.isin([16, 20])
                    position = np.where(hour_cond, abs(position), position)
                    epoch_df['position'] = position
                # if cfg.load_all_codes:
                #     epoch_df.index = cfg.valid_index_dict[phase]
                #     group_by_str = cfg.group_by_str
                # else:
                #     seq_step_count = len(epoch_df) // n_codes
                #     epoch_df['code'] = np.array(cfg.code_list).reshape(1, -1).repeat(seq_step_count)
                #     epoch_df['code_idx'] = np.arange(len(epoch_df), dtype=np.int32) % n_codes
                #     epoch_df['group_idx'] = np.arange(len(epoch_df), dtype=np.int32) // n_codes
                #     group_by_str = 'group_idx'
                
                # print(f'{epoch_df.tail(10) = }')
                # ic = np.corrcoef(epoch_df.position.values, epoch_df.asset_return.values)
                
                self.backtest_top_k(epoch_df, phase, group_by_str)
                return
            else:            
                interval = cfg.interval_cfg.base
                save_folder = os.path.join(self.ckpt_folder, cfg.datetime_str, f'epoch_{self.current_epoch}')
                if not os.path.exists(save_folder):
                    os.makedirs(save_folder)
                if phase == 'val':
                    save_path = os.path.join(self.ckpt_folder, cfg.datetime_str, cfg.script_name.split('/')[-1])
                    if not os.path.exists(save_path):
                        # copy portfolio.py into save_path
                        copyfile(cfg.script_name, save_path)
                date_dict = self.date_dict
                date_str = f'{date_dict.val[0]}v{date_dict.val[1]}'
                quote_str = f'_qql{cfg.quote_quantile}' if cfg.load_all_codes else ''
                save_str = f'{date_str}_{phase}_crypto{(n_codes)}{quote_str}_{interval}min_epc{self.current_epoch}_{cfg.task_enum.value}'
                mean_pnl_before_fee, fee_mean, end_pnl_before_fee, fee_sum = backtest(
                    filled_position_arr, 
                    asset_return_arr, 
                    final_position_arr, 
                    final_filled_return_arr, 
                    high_return_arr, 
                    low_return_arr, 
                    is_stop_loss=is_stop_loss_arr, 
                    stop_loss_ratio_per_code=stop_loss_ratio_arr, 
                    fee_ratio_dict=self.cfg.fee_ratio, 
                    save_folder=save_folder, 
                    save_str=save_str, 
                    n_codes=n_codes, 
                    code_list=cfg.code_list, 
                    is_portfolio=False,
                    label=label
                )
        else:
            mean_pnl_before_fee = fee_mean = end_pnl_before_fee = fee_sum = 0
        
        # metric = -loss
        if phase == 'val':
            metric = end_pnl_before_fee
            if self.cfg.task_enum == TaskType.Rank:
                metric = sharpe
            self.log('pnl_mean', pnl_mean)
            self.log('sharpe', sharpe)
            self.log('mean_pnl_before_fee', mean_pnl_before_fee)
            self.log('mean_pnl_after_fee', mean_pnl_before_fee - fee_mean)
            # self.log('cum_acc_bias_mean', cum_acc_bias_mean)
            self.log('metric', metric)


    def backtest_top_k(self, epoch_df: pd.DataFrame, phase: str, group_by_str: str = None) -> pd.DataFrame:
        
        cfg = self.cfg
        if group_by_str is None:
            group_by_str = cfg.group_by_str
        n_codes = 0 if cfg.load_all_codes else cfg.n_codes

        date_dict = self.date_dict
        val_start_end_str = f'{date_dict.val[0]}v{date_dict.val[1]}'
        task_folder = self.ckpt_folder
        if not os.path.exists(task_folder):
            os.makedirs(task_folder)
        epoch_str = f'epoch{self.current_epoch}'
        title = f'{epoch_str} {phase} all '

        if cfg.task_enum == TaskType.DirectTrading:
            n_range_groups = cfg.n_range_groups
            week_of_day_arr = epoch_df.index.get_level_values('open_time').to_series().dt.day_of_week.values
            hour_interval = cfg.interval_cfg.base // 60
            hour_idx_shift = 0
            hour_shift = hour_interval * hour_idx_shift
            hour_of_day_arr = get_hour_of_day(epoch_df.index, hour_shift)
            code_arr = epoch_df.index.get_level_values('code').values
        else:
            n_range_groups = 1
            week_of_day_arr = None
            hour_of_day_arr = None
            code_arr = None
        report_str, ic = report_clf_ic(epoch_df.position.values, epoch_df.final_filled_return.values, title, n_range_groups, week_of_day_arr, hour_of_day_arr, code_arr)
        file_name_str = title[:-1].replace(' ', '_')
        save_folder = os.path.join(task_folder, val_start_end_str)
        if not os.path.exists(save_folder):
            os.makedirs(save_folder)
        log_path = os.path.join(save_folder, f"ic{ic:.4f}_{file_name_str}.log")
        with open(log_path, 'w') as f:
            f.write(f'{report_str}')


        if phase == 'val':
            self.log(f'ic', ic)
            script_path = os.path.join(task_folder, cfg.script_name.split('/')[-1])
            if not os.path.exists(script_path):
                # copy portfolio.py into save_path
                copyfile(cfg.script_name, script_path)


        epoch_df: pl.DataFrame = (
            pl.from_pandas(epoch_df, include_index=True)
            .with_columns(
                ewm_pnl=pl.col('pnl').ewm_mean(com=2).shift().over('code').fill_null(0.)
            ).with_columns(
                abs_ewm_pnl=pl.col('ewm_pnl').abs(),
                )
            )
        # code_group_df = epoch_df.groupby('code').map_groups()
        # summary_dict = {}
        top_num_list = [n for n in cfg.top_num_list if n < n_codes]
        top_num_list.append(n_codes)        
        # btc_high_sub_low_expr = pl.col('cum_btc_return').max() - pl.col('cum_btc_return').min()
        btc_high_sub_low_expr = 1
        for i, top_num in enumerate(top_num_list):            
            top_str = f'top{top_num}'
            top_df = (
                epoch_df.group_by(group_by_str).map_groups(lambda x: get_top_k_in_one_row(x, cfg, top_num, n_codes, group_by_str)).sort(group_by_str).with_columns(
                # pl.col('index'),
                # pl.col(f'position_long'),
                # pl.col(f'position_short'),
                # pl.col(f'abs_position_sum'),
                pl.col('profit_ratio'),
                pl.col(f'pnl').cum_sum().alias(f'cum_pnl'),
                pl.col(f'long_pnl').cum_sum().alias(f'cum_long_pnl'),
                pl.col(f'short_pnl').cum_sum().alias(f'cum_short_pnl'),
                # pl.col(f'long_only_pnl').cum_sum().alias(f'cum_long_only_pnl'),
                # pl.col(f'short_only_pnl').cum_sum().alias(f'cum_short_only_pnl'),
                pl.col(f'baseline').cum_sum().alias(f'cum_baseline'),
                # pl.col(f'btc_return').cum_sum().alias(f'cum_btc_return'),
                # pl.col(f'btc_spread').cum_sum().alias(f'cum_btc_spread'),
                # ((pl.col('index') + 1).cast(pl.Float32) * cfg.fee_ratio.limit * 2).alias(f'cum_fee'),
                # pl.col(f'position_arr').implode().map_elements(lambda arr_list: get_fee(arr_list, cfg.fee_ratio.limit)
                # , return_dtype=pl.Float32
                # ).explode().cum_sum().alias(f'cum_fee'),
                # pl.col(f'position_list'),
                
                pl.col(f'position_list').shift().fill_null([]).alias(f'prev_position_list'),
                (pl.col('spread_pnl') if 'spread' in epoch_df.columns else pl.lit(0.)).cum_sum().alias(f'cum_spread_pnl')
                ).with_columns(
                    # (0.5 * cfg.spot_grid.position * pl.col('cum_btc_spread') / btc_high_sub_low_expr).alias(f'cum_btc_spread_pnl'),
                    # (cfg.spot_grid.position * pl.col('cum_btc_return') * pl.col('cum_btc_return').abs() / btc_high_sub_low_expr).alias('cum_btc_directional_pnl'),

                    pl.struct([f'position_list', f'prev_position_list'])#.alias('position_struct')
                    .map_elements(lambda expr: get_turnover(expr, f'position_list', f'prev_position_list', top_num), return_dtype=pl.Float32).fill_null(0.).alias('turnover')
                )
                .with_columns(
                    (pl.col('turnover') * cfg.fee_ratio.limit * 2.)
                    .alias(f'fee'),  
                )
                .with_columns(
                    # (pl.col(f'pnl') - pl.col(f'fee')).alias(f'pnl_after_fee'),
                    pl.col(f'fee').cum_sum().alias(f'cum_fee'),
                    (pl.col(f'cum_pnl') - pl.col(f'fee').cum_sum()).alias(f'cum_pnl_after_fee'),

                )
                .with_columns(
                    pl.col('cum_pnl_after_fee').ewm_mean(com=2).alias('cum_pnl_ewm2'),
                    pl.col('cum_pnl_after_fee').ewm_mean(com=3).alias('cum_pnl_ewm3'), 
                    # pl.col('cum_pnl_after_fee').ewm_mean(com=8).alias('cum_pnl_ewm8'),
                    pl.col('cum_pnl_after_fee').ewm_mean(com=4).alias('cum_pnl_ewm4'),                  
                    pl.col('cum_pnl_after_fee').ewm_mean(com=5).alias('cum_pnl_ewm5'),
                    # pl.col('cum_pnl_after_fee').ewm_mean(com=12).alias('cum_pnl_ewm12'),
                    # pl.col('cum_pnl_after_fee').ewm_mean(com=6).alias('cum_pnl_ewm6'),    
                    # (pl.col(f'cum_btc_spread_pnl') + pl.col('cum_btc_directional_pnl') + pl.col('cum_pnl_after_fee')).alias('cum_pnl_after_fee_with_btc_grid'),
                )
            )
            
            # print(f"{top_df['position_struct']} = ")
            # top_df = top_df.with_columns(
            #     pl.col('position_struct')
            #      .map_elements(lambda expr: get_turnover(expr, f'position_list', f'prev_position_list', top_num), return_dtype=pl.Float32).alias('turnover')
            #      # * cfg.fee_ratio.limit * 2.)
            #     # .fill_null(0.)
            #     # .alias(f'fee'),
            #     )
            # print(f"{top_df['turnover']} = ")
            # top_df = top_df.with_columns(
            #     (pl.col('turnover').fill_null(0.) * cfg.fee_ratio.limit * 2.)
            #     .alias(f'fee'),
            # )
            
            # top_df = top_df.with_columns(
            #         # (pl.col(f'pnl') - pl.col(f'fee')).alias(f'pnl_after_fee'),
            #         pl.col(f'fee').cum_sum().alias(f'cum_fee'),
            #         (pl.col(f'cum_pnl') - pl.col(f'fee').cum_sum()).alias(f'cum_pnl_after_fee'),
            #     )#.drop('index')
            sub_expr0 = (pl.col(f'cum_pnl_ewm2') - pl.col(f'cum_pnl_ewm3')) * 20                     
            sub_expr1 = (pl.col(f'cum_pnl_ewm3') - pl.col(f'cum_pnl_ewm4')) * 20
            # sub_expr2 = (pl.col(f'cum_pnl_ewm4') - pl.col(f'cum_pnl_ewm5')) * 20
            # sub_expr2 = (pl.col(f'cum_pnl_ewm5') - pl.col(f'cum_pnl_ewm6')) * 20            
            # sub_expr3 = (pl.col(f'cum_pnl_ewm8') - pl.col(f'cum_pnl_ewm12')) * 10

            fig_df = top_df.select(
                pl.col(group_by_str),
                pl.col(f'abs_position_sum'),                  
                pl.col(f'position_long'),
                pl.col(f'position_short'),


                # pl.col(f'profit_ratio').rolling_mean(15).alias('profit_ratio_ma15'),
                # pl.col(f'profit_ratio').rolling_mean(30).alias('profit_ratio_ma30'),
                # pl.col(f'cum_pnl_ewm12'),
                # (1 + 10 * pl.col(f'cum_pnl_ewm12').diff()).alias('cum_pnl_ewm12_dif') ,
                # pl.col(f'cum_pnl_ewm6'),
                # (1 + 10 * pl.col(f'cum_pnl_ewm6').diff()).alias('cum_pnl_ewm6_dif') ,               

                # (1 + sub_expr2).alias('cum_pnl_emw5sub6'),
                # (1 + sub_expr2).alias('cum_pnl_emw6sub8'),
                # (1 + sub_expr3).alias('cum_pnl_emw8sub12'),
                # (1 + diff_expr.ewm_mean(com=9)).alias('cum_pnl_dea'),
                # (1 + diff_expr - diff_expr.ewm_mean(com=9)).alias('cum_pnl_macd'),
                pl.col(f'cum_pnl'), 
                pl.col(f'cum_long_pnl'),
                pl.col(f'cum_short_pnl'), 
                pl.col(f'cum_spread_pnl'),
                # pl.col(f'cum_long_only_pnl'),
                # pl.col(f'cum_short_only_pnl'),              
                pl.col(f'cum_fee'),
                pl.col(f'cum_baseline'),
                pl.col(f'cum_pnl_after_fee'),
                # pl.col('cum_btc_return'),
                # pl.col('cum_btc_directional_pnl'),
                # pl.col('cum_btc_spread_pnl'),
                # pl.col('cum_pnl_after_fee_with_btc_grid'),
                

                (sub_expr0 - 1).alias('cum_pnl_emw2sub3'),
                (sub_expr1 - 1).alias('cum_pnl_emw3sub4'),
            )
            # if i == len(top_num_list) - 1:
            #     cum_btc_grid = backtest_grid(top_df['cum_btc_return'].to_numpy() + 1, plot=True) - 1
            #     fig_df = fig_df.with_columns(
            #         cum_btc_grid=cum_btc_grid,
            #     ).with_columns(
            #         cum_pnl_after_fee_with_btc_grid=pl.col('cum_btc_grid') + pl.col('cum_pnl_after_fee'),
            #     )

            step_pnl_arr = top_df[f'pnl'].to_numpy()
            cum_pnl_after_fee_arr = top_df[f'cum_pnl_after_fee'].to_numpy()            
            
            sortino, profit_to_loss_ratio = save_summary_fig_pqt(cfg, phase, task_folder, epoch_str, val_start_end_str, top_str, top_df, fig_df, step_pnl_arr, cum_pnl_after_fee_arr, save_pqt=True)

            if phase == 'val':
                self.log(f'{top_str}_pnl_mean', step_pnl_arr.mean())
                self.log(f'{top_str}_end_pnl_before_fee', step_pnl_arr.sum())
                self.log(f'{top_str}_end_pnl_after_fee', cum_pnl_after_fee_arr[-1])
                self.log(f'{top_str}_sortino', sortino)
                self.log(f'{top_str}_profit_to_loss_ratio', profit_to_loss_ratio)



    def predict(self, batch: tuple[torch.Tensor | np.ndarray]) -> tuple:
        cfg = self.cfg
        seq, category, image = batch
        if isinstance(seq, np.ndarray):
            seq = torch.from_numpy(seq)
        if isinstance(category, np.ndarray):
            category = torch.from_numpy(category)
        if isinstance(image, np.ndarray):
            image = torch.from_numpy(image)
        pred = self.model(seq, category, image)
        if isinstance(pred, tuple):
            pred = pred[0]
        self.rolling_std = (seq[:, -cfg.take_profit.std_window:, 7].diff(1) + 1).log().std(dim=1)
        _, score_position, shift_ratio, stop_loss_ratio, take_profit_ratio, entry_logits, exit_logits = self.calc_pred(pred)
        
        # shift_ratio = shift_ratio * top_k_mask
        # stop_loss_ratio = stop_loss_ratio * top_k_mask
        # take_profit_ratio = take_profit_ratio * top_k_mask
        return score_position.detach().cpu().numpy(), shift_ratio.detach().cpu().numpy(), stop_loss_ratio.detach().cpu().numpy(), take_profit_ratio.detach().cpu().numpy(), entry_logits.detach().cpu().numpy(), exit_logits.detach().cpu().numpy()




    def direct_loss(self, logits: Tensor, advised_position: Tensor, batch_y: Tensor, shift_ratio: Tensor = 0., stop_loss_ratio: Tensor = 1., take_profit_ratio: Tensor = 1., entry_logits: Tensor = None, exit_logits: Tensor = None, phase: str = 'train', init_position: Tensor = 0., directional_balance: bool = None) -> tuple:
        cfg = self.cfg
        asset_return_seq = batch_y[..., 0]
        high_to_close_seq = batch_y[..., 1]
        low_to_close_seq = batch_y[..., 2]
        if len(logits.shape) == 2:
            if logits.shape[1] == 1:
                logits = logits.squeeze(1)
        if isinstance(init_position, float):
            init_position = torch.tensor(init_position, device=advised_position.device).reshape(1)
        if isinstance(shift_ratio, float):
            shift_ratio = shift_ratio * torch.ones_like(advised_position, device=advised_position.device)
        if isinstance(stop_loss_ratio, float):
            stop_loss_ratio = stop_loss_ratio * torch.ones_like(advised_position, device=advised_position.device)
        if isinstance(take_profit_ratio, float):
            take_profit_ratio = take_profit_ratio * torch.ones_like(advised_position, device=advised_position.device)
        
        fee_ratio_dict = cfg.fee_ratio
        if cfg.label_enum == LabelType.RawNormalized:
            cum_return_arr = asset_return_seq
            cum_high_arr = high_to_close_seq
            cum_low_arr = low_to_close_seq
        elif cfg.label_enum == LabelType.LogReturn:
            cum_return_arr = asset_return_seq.cumsum(dim=1)
            cum_high_arr = cum_return_arr + high_to_close_seq
            cum_low_arr = cum_return_arr + low_to_close_seq
        elif cfg.label_enum == LabelType.PercentChange:
            cum_return_arr = (asset_return_seq + 1).cumprod(dim=1) - 1
            cum_high_arr = (cum_return_arr + 1) * (1 + high_to_close_seq) - 1
            cum_low_arr = (cum_return_arr + 1) * (1 + low_to_close_seq) - 1
        else:
            cum_high_arr = cum_low_arr = cum_return_arr = (asset_return_seq + 1).cumprod(dim=1) - 1

        batch_size, pred_len = cum_return_arr.shape
        zero_batch = torch.zeros_like(advised_position)
        
        # 初始化标志和索引的默认值
        is_market = torch.ones_like(advised_position, dtype=torch.bool) if shift_ratio.sum() == 0 else (shift_ratio == 0)
        is_entry_exit = (cfg.use_entry_idx or cfg.use_exit_idx) and (entry_logits is not None) and (exit_logits is not None)

        # 传统的shift_ratio逻辑
        side = torch.sign(advised_position)
        signed_shift_ratio = -side * shift_ratio
        signed_shift_arr = signed_shift_ratio.unsqueeze(1).repeat(1, pred_len)
        
        long_filled_arr = (0 >= signed_shift_arr) & (signed_shift_arr > cum_low_arr)
        short_filled_arr = (0 <= signed_shift_arr) & (signed_shift_arr < cum_high_arr)
        is_filled_arr = long_filled_arr | short_filled_arr
        
        is_filled = is_filled_arr.any(dim=1) | is_market
        filled_idx = torch.where(is_filled, is_filled_arr.float().argmax(dim=1), pred_len)
        
        # 创建时间维度索引矩阵和填充掩码
        t_indices = torch.arange(pred_len, device=is_filled.device).unsqueeze(0)
        filled_mask = t_indices >= filled_idx.unsqueeze(1)
        is_filled_arr = is_filled_arr | filled_mask
        
        filled_position = is_filled.float() * advised_position
        if cfg.optimize_cross_entropy:
            filled_position = filled_position.sign()
        
        filled_position_arr = filled_position.unsqueeze(1).repeat(1, pred_len)
        signed_filled_advantage = torch.sign(filled_position) * shift_ratio            
        
        entry_price = cum_return_arr[torch.arange(batch_size), torch.clamp(filled_idx, 0, pred_len-1)]
        exit_price = cum_return_arr[:, -1]  # 默认到期出场  

        # 入场和出场索引计算
        if is_entry_exit:
            if cfg.entry_exit_logits_enum == EntryExitLogitsEnum.GUMBEL_SOFTMAX:
                # 添加Gumbel噪声并温度参数
                tau = cfg.gumbel_tau
                # 生成Gumbel噪声
                gumbel_noise_entry = -torch.log(-torch.log(torch.rand_like(entry_logits) + 1e-10) + 1e-10)
                gumbel_noise_exit = -torch.log(-torch.log(torch.rand_like(exit_logits) + 1e-10) + 1e-10)
                
                # 应用Gumbel-Softmax获取入场概率分布
                entry_gumbel_logits = (entry_logits + gumbel_noise_entry) / tau
                entry_probs = F.softmax(entry_gumbel_logits, dim=1)
                
                # 创建入场掩码(one-hot)
                entry_idx = torch.argmax(entry_probs, dim=1)

                exit_gumbel_logits = (exit_logits + gumbel_noise_exit) / tau
                exit_probs = F.softmax(exit_gumbel_logits, dim=1)
                exit_idx = torch.argmax(exit_probs, dim=1)
                
                # 使用连续近似计算收益
                # 通过概率加权计算入场和出场价格
                entry_price = torch.sum(entry_probs * cum_return_arr, dim=1)
                exit_price = torch.sum(exit_probs * cum_return_arr, dim=1)
                
            elif cfg.entry_exit_logits_enum == EntryExitLogitsEnum.STE:
                # STE方法处理入场和出场
                entry_idx = torch.argmax(entry_logits, dim=1)
                exit_idx = torch.argmax(exit_logits, dim=1)
                
                # 直接选择对应的价格
                entry_price = cum_return_arr[torch.arange(batch_size), entry_idx]
                exit_price = cum_return_arr[torch.arange(batch_size), exit_idx]
                
                # 在反向传播时，梯度从硬索引传递到概率分布
                # 需要自定义autograd函数
                class StraightThroughEstimator(torch.autograd.Function):
                    @staticmethod
                    def forward(ctx, inputs, indices):
                        ctx.save_for_backward(inputs)
                        return inputs.gather(1, indices.unsqueeze(1)).squeeze(1)
                    
                    @staticmethod
                    def backward(ctx, grad_output):
                        inputs, = ctx.saved_tensors
                        grad_inputs = torch.zeros_like(inputs)
                        grad_indices = None  # 索引不需要梯度
                        grad_inputs.scatter_(1, torch.argmax(inputs, dim=1, keepdim=True), grad_output.unsqueeze(1))
                        return grad_inputs, grad_indices
                
                # 使用STE确保梯度回传
                ste = StraightThroughEstimator.apply
                entry_probs = F.softmax(entry_logits, dim=1)
                exit_probs = F.softmax(exit_logits, dim=1)
                entry_price_diff = ste(entry_probs, filled_idx)
                exit_price_diff = ste(exit_probs, exit_idx)
                
                # 保持前向传播的硬索引，但允许梯度通过概率传递
                entry_price = entry_price_diff.detach() + entry_price - entry_price.detach()
                exit_price = exit_price_diff.detach() + exit_price - exit_price.detach()
            
            is_entry = entry_idx < filled_idx
            filled_idx = torch.min(filled_idx, entry_idx)
        else:
            is_entry = torch.zeros_like(is_filled, dtype=torch.bool)


        
        # 处理止损和止盈逻辑（对所有策略都适用）
        if cfg.stop_loss.in_use:
            # if cfg.stop_loss.with_position:
            #     signed_stop_loss_range = -(filled_position * stop_loss_ratio)
            # else:
            signed_stop_loss_range = -torch.sign(filled_position) * stop_loss_ratio
            stop_loss_line_arr = -torch.ones_like(cum_return_arr) * signed_stop_loss_range.unsqueeze(1)
            
            long_hit_loss_arr = cum_low_arr
            short_hit_loss_arr = cum_high_arr
            
            if is_entry_exit:
                # 在入场索引之后应用止损
                t_indices = torch.arange(pred_len, device=is_filled.device).unsqueeze(0)
                entry_mask = t_indices >= filled_idx.unsqueeze(1)
                
                # 对于入场后的时间点检查止损条件
                is_stop_loss_arr = ((short_hit_loss_arr > stop_loss_line_arr) & (filled_position.unsqueeze(1) < 0) | 
                                  (long_hit_loss_arr < stop_loss_line_arr) & (filled_position.unsqueeze(1) > 0))
                is_stop_loss_arr = is_stop_loss_arr & entry_mask
            else:
                is_stop_loss_arr = ((short_hit_loss_arr > stop_loss_line_arr) & (filled_position_arr < 0) | 
                                  (long_hit_loss_arr < stop_loss_line_arr) & (filled_position_arr > 0))
                is_stop_loss_arr = is_stop_loss_arr & filled_mask
            
            is_stop_loss = is_stop_loss_arr.any(dim=1)
            stop_loss_idx = torch.where(is_stop_loss, is_stop_loss_arr.float().argmax(dim=1), pred_len + 1)
            stop_loss_return = signed_stop_loss_range
        else:
            is_stop_loss = torch.zeros_like(advised_position, dtype=torch.bool)
            stop_loss_idx = torch.full_like(is_stop_loss, pred_len + 1, dtype=torch.int64)
            stop_loss_return = zero_batch
        
        if cfg.take_profit.in_use:
            # if cfg.take_profit.with_position:
            #     signed_take_profit_range = (filled_position * take_profit_ratio)
            # else:
            signed_take_profit_range = torch.sign(filled_position) * take_profit_ratio
            take_profit_line_arr = torch.ones_like(cum_return_arr) * signed_take_profit_range.unsqueeze(1)
            
            long_hit_profit_arr = cum_high_arr
            short_hit_profit_arr = cum_low_arr
            
            if is_entry_exit:
                # 在入场索引之后应用止盈
                t_indices = torch.arange(pred_len, device=is_filled.device).unsqueeze(0)
                entry_mask = t_indices >= filled_idx.unsqueeze(1)
                
                # 对于入场后的时间点检查止盈条件
                is_take_profit_arr = ((long_hit_profit_arr > take_profit_line_arr) & (filled_position.unsqueeze(1) > 0) | 
                                    (short_hit_profit_arr < take_profit_line_arr) & (filled_position.unsqueeze(1) < 0))
                is_take_profit_arr = is_take_profit_arr & entry_mask
            else:
                is_take_profit_arr = ((long_hit_profit_arr > take_profit_line_arr) & (filled_position_arr > 0) | 
                                    (short_hit_profit_arr < take_profit_line_arr) & (filled_position_arr < 0))
                is_take_profit_arr = is_take_profit_arr & filled_mask
            
            is_take_profit = is_take_profit_arr.any(dim=1)
            take_profit_idx = torch.where(is_take_profit, is_take_profit_arr.float().argmax(dim=1), pred_len + 2)
            take_profit_return = signed_take_profit_range
        else:
            is_take_profit = torch.zeros_like(advised_position, dtype=torch.bool)
            take_profit_idx = torch.full_like(is_take_profit, pred_len + 2, dtype=torch.int64)
            take_profit_return = zero_batch
        
        # 最终结果确定逻辑：比较止损、止盈和出场索引
        if is_entry_exit:
            # 使用出场索引作为默认退出点
            exit_price_arr = cum_return_arr[torch.arange(batch_size), torch.clamp(exit_idx, 0, pred_len-1)]
            final_filled_return = exit_price - entry_price
            
            # 如果有止损/止盈，检查哪个先发生
            if cfg.take_profit.in_use and cfg.stop_loss.in_use:
                # 确定各种情况和退出条件的先后顺序
                filled_stop_loss = (stop_loss_idx < take_profit_idx) & (stop_loss_idx < exit_idx) & is_filled
                filled_take_profit = (take_profit_idx < stop_loss_idx) & (take_profit_idx < exit_idx) & is_filled
                filled_to_end = (~filled_stop_loss) & (~filled_take_profit) & is_filled
                
                # 根据不同情况选择最终收益
                conds = [filled_stop_loss, filled_take_profit, filled_to_end]
                returns = [stop_loss_return, take_profit_return, final_filled_return]
                positions = [zero_batch, zero_batch, zero_batch]  # 所有情况都平仓
                close_fees = [
                    filled_position * fee_ratio_dict.stop, 
                    filled_position * fee_ratio_dict.limit, 
                    filled_position * fee_ratio_dict.limit
                ]
                
                final_filled_return = torch_select(conds, returns, zero_batch)
                final_position = torch_select(conds, positions, zero_batch)
                close_fee = torch_select(conds, close_fees, zero_batch)
                
            elif cfg.take_profit.in_use:
                # 只有止盈
                filled_take_profit = (take_profit_idx < exit_idx) & is_filled
                final_filled_return = torch.where(filled_take_profit, take_profit_return, final_filled_return)
                close_fee = torch.where(filled_take_profit, 
                                        fee_ratio_dict.limit * abs(filled_position), 
                                        fee_ratio_dict.limit * abs(filled_position))
                final_position = zero_batch
                
            elif cfg.stop_loss.in_use:
                # 只有止损
                filled_stop_loss = (stop_loss_idx < exit_idx) & is_filled
                final_filled_return = torch.where(filled_stop_loss, stop_loss_return, final_filled_return)
                close_fee = torch.where(filled_stop_loss, 
                                        fee_ratio_dict.stop * abs(filled_position), 
                                        fee_ratio_dict.limit * abs(filled_position))
                final_position = zero_batch
                
            else:
                # 没有止损止盈，只使用出场索引
                close_fee = fee_ratio_dict.limit * abs(filled_position)
                final_position = zero_batch
            
            # 计算最终收益
            pnl = filled_position * final_filled_return
            
            # 计算开仓手续费
            open_fee = fee_ratio_dict.limit * abs(filled_position)
            
        else:
            # 原始直接交易逻辑
            asset_return = cum_return_arr[:, -1]
            high_return = cum_high_arr[:, -1]
            low_return = cum_low_arr[:, -1]
            filled_asset_return = is_filled.float() * asset_return + (torch.sign(filled_position) * shift_ratio)
            
            if cfg.take_profit.in_use and cfg.stop_loss.in_use:
                filled_stop_loss = (stop_loss_idx <= take_profit_idx) & (stop_loss_idx < pred_len) & is_filled
                filled_take_profit = (take_profit_idx < stop_loss_idx) & (take_profit_idx < pred_len) & is_filled
                filled_to_end = (take_profit_idx >= pred_len) & (stop_loss_idx >= pred_len) & is_filled
                
                conds = [filled_stop_loss, filled_take_profit, filled_to_end]
                returns = [stop_loss_return, take_profit_return, filled_asset_return]
                positions = [zero_batch, zero_batch, zero_batch]  # 所有情况都平仓
                close_fees = [
                    filled_position * fee_ratio_dict.stop, 
                    filled_position * fee_ratio_dict.limit, 
                    zero_batch,
                ]
                
                final_filled_return = torch_select(conds, returns, zero_batch)
                final_position = torch_select(conds, positions, zero_batch)
                close_fee = torch_select(conds, close_fees, zero_batch)
                
            elif cfg.take_profit.in_use:
                final_filled_return = torch.where(is_take_profit, take_profit_return, filled_asset_return)
                close_fee = torch.where(is_take_profit, fee_ratio_dict.limit * abs(filled_position), zero_batch)
                final_position = torch.where(is_take_profit, zero_batch, filled_position)
                
            elif cfg.stop_loss.in_use:
                final_filled_return = torch.where(is_stop_loss, stop_loss_return, filled_asset_return)
                close_fee = torch.where(is_stop_loss, fee_ratio_dict.stop * abs(filled_position), zero_batch)
                final_position = torch.where(is_stop_loss, zero_batch, filled_position)
                
            else:
                is_take_profit = torch.zeros_like(is_filled, device=is_filled.device)
                is_stop_loss = torch.zeros_like(is_filled, device=is_filled.device)
                final_position = filled_position
                final_filled_return = filled_asset_return
                close_fee = zero_batch
            
            pnl = filled_position * final_filled_return - close_fee
            
            if not (cfg.shuffling.train and phase == 'train'):
                last_position = torch.cat([init_position, final_position])[:-1]
                position_change = filled_position - last_position
                open_fee = fee_ratio_dict.limit * abs(position_change)
                open_fee[is_market] = fee_ratio_dict.market * abs(position_change[is_market])
            else:
                fee_scale = cfg.pnl_loss_with_fee_scale
                open_fee = fee_scale * fee_ratio_dict.limit * abs(filled_position)
        
        # 最终损失计算
        net_pnl = pnl - open_fee
        
        # 根据优化目标选择损失函数
        objective_enum = cfg.objective_enum
        if objective_enum == Objective.IC:
            loss = ic_loss(logits, asset_return)
        elif objective_enum == Objective.MSE:
            loss = F.mse_loss(logits, asset_return)
        elif objective_enum == Objective.Sharpe:
            sharpe = (net_pnl.mean() / net_pnl.std())
            loss = -sharpe
        elif objective_enum == Objective.Oracle:
            oracle = asset_return.abs()
            loss = (oracle.sum(dim=1) - net_pnl).mean()
        elif objective_enum == Objective.BCE:
            loss = F.binary_cross_entropy(F.sigmoid(logits), (asset_return.sign() > 0).float())
        elif objective_enum == Objective.PNL:
            loss = -net_pnl.mean()
            if cfg.align_pos_neg_return and {'train', 'val'}.issubset(set(self.pos_neg_return_ratio_dict)) and phase == 'train':
                extra_neg_pnl = (filled_position * final_filled_return.clip(None, 0)) * (self.pos_neg_return_ratio_dict.train / self.pos_neg_return_ratio_dict.val - 1)
                loss -= extra_neg_pnl.mean()
        elif cfg.position_punishment.in_use:
            punish_ratio = abs(final_position ** cfg.position_punishment.exponent)
            loss = -(torch.clamp_max(net_pnl, 0) * (1 + punish_ratio) + torch.clamp_min(net_pnl, 0) * (1 - punish_ratio / 2)).mean()
        
        if directional_balance is None:
            directional_balance = cfg.directional_balance
        if directional_balance:
            loss = loss * (1 - advised_position.mean().abs())
        
        # 如果在入场出场模式且需要返回原始指标，填充asset_return等
        if is_entry_exit and not 'asset_return' in locals():
            asset_return = cum_return_arr[:, -1] 
            high_return = cum_high_arr[:, -1]
            low_return = cum_low_arr[:, -1]
        
        result = (loss, pnl, open_fee, filled_position, final_filled_return, final_position, 
                 is_market, is_filled, is_take_profit, is_stop_loss, 
                 asset_return, high_return, low_return)
        
        for i, arr in enumerate(result):
            if torch.isnan(arr).any():
                print(f'{i}th array has {torch.isnan(arr).int().sum()} nan values\n{arr = }')
        
        return result


        # 初始化标志和索引的默认值
        is_market = torch.ones_like(advised_position, dtype=torch.bool) if shift_ratio.sum() == 0 else (shift_ratio == 0)
        is_entry_exit = (cfg.use_entry_idx or cfg.use_exit_idx) and (entry_logits is not None) and (exit_logits is not None)
        
        # 入场和出场索引计算
        if is_entry_exit:
            if cfg.entry_exit_logits_enum == EntryExitLogitsEnum.GUMBEL_SOFTMAX:
                # 添加Gumbel噪声并温度参数
                tau = cfg.gumbel_tau
                # 生成Gumbel噪声
                gumbel_noise_entry = -torch.log(-torch.log(torch.rand_like(entry_logits) + 1e-10) + 1e-10)
                gumbel_noise_exit = -torch.log(-torch.log(torch.rand_like(exit_logits) + 1e-10) + 1e-10)
                
                # 应用Gumbel-Softmax获取入场概率分布
                entry_gumbel_logits = (entry_logits + gumbel_noise_entry) / tau
                entry_probs = F.softmax(entry_gumbel_logits, dim=1)
                
                # 创建入场掩码(one-hot)
                entry_idx = torch.argmax(entry_probs, dim=1)
                
                # 创建出场掩码确保exit_idx > entry_idx
                exit_masks = torch.zeros_like(exit_logits)
                for i in range(batch_size):
                    exit_masks[i, entry_idx[i]+1:] = 1.0  # 只允许在entry_idx之后的位置出场
                
                # 将掩码应用到出场logits上
                masked_exit_logits = exit_logits + (1 - exit_masks) * (-1e9)  # 将无效位置设为极小值
                exit_gumbel_logits = (masked_exit_logits + gumbel_noise_exit) / tau
                exit_probs = F.softmax(exit_gumbel_logits, dim=1)
                exit_idx = torch.argmax(exit_probs, dim=1)
                
                # 使用连续近似计算收益
                # 通过概率加权计算入场和出场价格
                entry_price = torch.sum(entry_probs * cum_return_arr, dim=1)
                exit_price = torch.sum(exit_probs * cum_return_arr, dim=1)
                
            elif cfg.entry_exit_logits_enum == EntryExitLogitsEnum.STE:
                # STE方法处理入场和出场
                entry_idx = torch.argmax(entry_logits, dim=1)
                
                # 创建出场掩码
                exit_masks = torch.zeros_like(exit_logits)
                for i in range(batch_size):
                    exit_masks[i, entry_idx[i]+1:] = 1.0
                
                # 应用掩码并选择出场索引
                masked_exit_logits = exit_logits + (1 - exit_masks) * (-1e9)
                exit_idx = torch.argmax(masked_exit_logits, dim=1)
                
                # 直接选择对应的价格
                entry_price = cum_return_arr[torch.arange(batch_size), entry_idx]
                exit_price = cum_return_arr[torch.arange(batch_size), exit_idx]
                
                # 在反向传播时，梯度从硬索引传递到概率分布
                # 需要自定义autograd函数
                class StraightThroughEstimator(torch.autograd.Function):
                    @staticmethod
                    def forward(ctx, inputs, indices):
                        ctx.save_for_backward(inputs)
                        return inputs.gather(1, indices.unsqueeze(1)).squeeze(1)
                    
                    @staticmethod
                    def backward(ctx, grad_output):
                        inputs, = ctx.saved_tensors
                        grad_inputs = torch.zeros_like(inputs)
                        grad_indices = None  # 索引不需要梯度
                        grad_inputs.scatter_(1, torch.argmax(inputs, dim=1, keepdim=True), grad_output.unsqueeze(1))
                        return grad_inputs, grad_indices
                
                # 使用STE确保梯度回传
                ste = StraightThroughEstimator.apply
                entry_probs = F.softmax(entry_logits, dim=1)
                exit_probs = F.softmax(masked_exit_logits, dim=1)
                entry_price_diff = ste(entry_probs, entry_idx)
                exit_price_diff = ste(exit_probs, exit_idx)
                
                # 保持前向传播的硬索引，但允许梯度通过概率传递
                entry_price = entry_price_diff.detach() + entry_price - entry_price.detach()
                exit_price = exit_price_diff.detach() + exit_price - exit_price.detach()
            
            # 初始化入场和出场状态
            is_filled = torch.ones_like(advised_position, dtype=torch.bool)
            filled_position = advised_position
        else:
            # 传统的shift_ratio逻辑
            side = torch.sign(advised_position)
            signed_shift_ratio = -side * shift_ratio
            signed_shift_arr = signed_shift_ratio.unsqueeze(1).repeat(1, pred_len)
            
            long_filled_arr = (0 >= signed_shift_arr) & (signed_shift_arr > cum_low_arr)
            short_filled_arr = (0 <= signed_shift_arr) & (signed_shift_arr < cum_high_arr)
            is_filled_arr = long_filled_arr | short_filled_arr
            
            is_filled = is_filled_arr.any(dim=1) | is_market
            filled_idx = torch.where(is_filled, is_filled_arr.float().argmax(dim=1), pred_len)
            
            # 创建时间维度索引矩阵和填充掩码
            t_indices = torch.arange(pred_len, device=is_filled.device).unsqueeze(0)
            filled_mask = t_indices >= filled_idx.unsqueeze(1)
            is_filled_arr = is_filled_arr | filled_mask
            
            filled_position = is_filled.float() * advised_position
            if cfg.optimize_cross_entropy:
                filled_position = filled_position.sign()
            
            filled_position_arr = filled_position.unsqueeze(1).repeat(1, pred_len)
            signed_filled_advantage = torch.sign(filled_position) * shift_ratio
            
            # 没有入场/出场索引时，使用filled_idx作为入场点
            entry_idx = filled_idx
            entry_price = cum_return_arr[torch.arange(batch_size), torch.clamp(entry_idx, 0, pred_len-1)]
            exit_price = cum_return_arr[:, -1]  # 默认到期出场
        
        # 处理止损和止盈逻辑（对所有策略都适用）
        if cfg.stop_loss.in_use:
            # if cfg.stop_loss.with_position:
            #     signed_stop_loss_range = -(filled_position * stop_loss_ratio)
            # else:
            signed_stop_loss_range = -torch.sign(filled_position) * stop_loss_ratio
            stop_loss_line_arr = -torch.ones_like(cum_return_arr) * signed_stop_loss_range.unsqueeze(1)
            
            long_hit_loss_arr = cum_low_arr
            short_hit_loss_arr = cum_high_arr
            
            if is_entry_exit:
                # 在入场索引之后应用止损
                t_indices = torch.arange(pred_len, device=is_filled.device).unsqueeze(0)
                entry_mask = t_indices >= entry_idx.unsqueeze(1)
                
                # 对于入场后的时间点检查止损条件
                is_stop_loss_arr = ((short_hit_loss_arr > stop_loss_line_arr) & (filled_position.unsqueeze(1) < 0) | 
                                  (long_hit_loss_arr < stop_loss_line_arr) & (filled_position.unsqueeze(1) > 0))
                is_stop_loss_arr = is_stop_loss_arr & entry_mask
            else:
                is_stop_loss_arr = ((short_hit_loss_arr > stop_loss_line_arr) & (filled_position_arr < 0) | 
                                  (long_hit_loss_arr < stop_loss_line_arr) & (filled_position_arr > 0))
                is_stop_loss_arr = is_stop_loss_arr & filled_mask
            
            is_stop_loss = is_stop_loss_arr.any(dim=1)
            stop_loss_idx = torch.where(is_stop_loss, is_stop_loss_arr.float().argmax(dim=1), pred_len + 1)
            stop_loss_return = signed_stop_loss_range
        else:
            is_stop_loss = torch.zeros_like(advised_position, dtype=torch.bool)
            stop_loss_idx = torch.full_like(is_stop_loss, pred_len + 1, dtype=torch.int64)
            stop_loss_return = zero_batch
        
        if cfg.take_profit.in_use:
            # if cfg.take_profit.with_position:
            #     signed_take_profit_range = (filled_position * take_profit_ratio)
            # else:
            signed_take_profit_range = torch.sign(filled_position) * take_profit_ratio
            take_profit_line_arr = torch.ones_like(cum_return_arr) * signed_take_profit_range.unsqueeze(1)
            
            long_hit_profit_arr = cum_high_arr
            short_hit_profit_arr = cum_low_arr
            
            if is_entry_exit:
                # 在入场索引之后应用止盈
                t_indices = torch.arange(pred_len, device=is_filled.device).unsqueeze(0)
                entry_mask = t_indices >= entry_idx.unsqueeze(1)
                
                # 对于入场后的时间点检查止盈条件
                is_take_profit_arr = ((long_hit_profit_arr > take_profit_line_arr) & (filled_position.unsqueeze(1) > 0) | 
                                    (short_hit_profit_arr < take_profit_line_arr) & (filled_position.unsqueeze(1) < 0))
                is_take_profit_arr = is_take_profit_arr & entry_mask
            else:
                is_take_profit_arr = ((long_hit_profit_arr > take_profit_line_arr) & (filled_position_arr > 0) | 
                                    (short_hit_profit_arr < take_profit_line_arr) & (filled_position_arr < 0))
                is_take_profit_arr = is_take_profit_arr & filled_mask
            
            is_take_profit = is_take_profit_arr.any(dim=1)
            take_profit_idx = torch.where(is_take_profit, is_take_profit_arr.float().argmax(dim=1), pred_len + 2)
            take_profit_return = signed_take_profit_range
        else:
            is_take_profit = torch.zeros_like(advised_position, dtype=torch.bool)
            take_profit_idx = torch.full_like(is_take_profit, pred_len + 2, dtype=torch.int64)
            take_profit_return = zero_batch
        
        # 最终结果确定逻辑：比较止损、止盈和出场索引
        if is_entry_exit:
            # 使用出场索引作为默认退出点
            exit_price_arr = cum_return_arr[torch.arange(batch_size), torch.clamp(exit_idx, 0, pred_len-1)]
            final_filled_return = exit_price - entry_price
            
            # 如果有止损/止盈，检查哪个先发生
            if cfg.take_profit.in_use and cfg.stop_loss.in_use:
                # 确定各种情况和退出条件的先后顺序
                filled_stop_loss = (stop_loss_idx < take_profit_idx) & (stop_loss_idx < exit_idx) & is_filled
                filled_take_profit = (take_profit_idx < stop_loss_idx) & (take_profit_idx < exit_idx) & is_filled
                filled_to_end = (~filled_stop_loss) & (~filled_take_profit) & is_filled
                
                # 根据不同情况选择最终收益
                conds = [filled_stop_loss, filled_take_profit, filled_to_end]
                returns = [stop_loss_return, take_profit_return, final_filled_return]
                positions = [zero_batch, zero_batch, zero_batch]  # 所有情况都平仓
                close_fees = [
                    filled_position * fee_ratio_dict.stop, 
                    filled_position * fee_ratio_dict.limit, 
                    filled_position * fee_ratio_dict.limit
                ]
                
                final_filled_return = torch_select(conds, returns, zero_batch)
                final_position = torch_select(conds, positions, zero_batch)
                close_fee = torch_select(conds, close_fees, zero_batch)
                
            elif cfg.take_profit.in_use:
                # 只有止盈
                filled_take_profit = (take_profit_idx < exit_idx) & is_filled
                final_filled_return = torch.where(filled_take_profit, take_profit_return, final_filled_return)
                close_fee = torch.where(filled_take_profit, 
                                        fee_ratio_dict.limit * abs(filled_position), 
                                        fee_ratio_dict.limit * abs(filled_position))
                final_position = zero_batch
                
            elif cfg.stop_loss.in_use:
                # 只有止损
                filled_stop_loss = (stop_loss_idx < exit_idx) & is_filled
                final_filled_return = torch.where(filled_stop_loss, stop_loss_return, final_filled_return)
                close_fee = torch.where(filled_stop_loss, 
                                        fee_ratio_dict.stop * abs(filled_position), 
                                        fee_ratio_dict.limit * abs(filled_position))
                final_position = zero_batch
                
            else:
                # 没有止损止盈，只使用出场索引
                close_fee = fee_ratio_dict.limit * abs(filled_position)
                final_position = zero_batch
            
            # 计算最终收益
            pnl = filled_position * final_filled_return
            
            # 计算开仓手续费
            open_fee = fee_ratio_dict.limit * abs(filled_position)
            
        else:
            # 原始直接交易逻辑
            asset_return = cum_return_arr[:, -1]
            high_return = cum_high_arr[:, -1]
            low_return = cum_low_arr[:, -1]
            filled_asset_return = is_filled.float() * asset_return + (torch.sign(filled_position) * shift_ratio)
            
            if cfg.take_profit.in_use and cfg.stop_loss.in_use:
                filled_stop_loss = (stop_loss_idx <= take_profit_idx) & (stop_loss_idx < pred_len) & is_filled
                filled_take_profit = (take_profit_idx < stop_loss_idx) & (take_profit_idx < pred_len) & is_filled
                filled_to_end = (take_profit_idx >= pred_len) & (stop_loss_idx >= pred_len) & is_filled
                
                conds = [filled_stop_loss, filled_take_profit, filled_to_end]
                returns = [stop_loss_return, take_profit_return, filled_asset_return]
                positions = [zero_batch, zero_batch, zero_batch]  # 所有情况都平仓
                close_fees = [
                    filled_position * fee_ratio_dict.stop, 
                    filled_position * fee_ratio_dict.limit, 
                    zero_batch,
                ]
                
                final_filled_return = torch_select(conds, returns, zero_batch)
                final_position = torch_select(conds, positions, zero_batch)
                close_fee = torch_select(conds, close_fees, zero_batch)
                
            elif cfg.take_profit.in_use:
                final_filled_return = torch.where(is_take_profit, take_profit_return, filled_asset_return)
                close_fee = torch.where(is_take_profit, fee_ratio_dict.limit * abs(filled_position), zero_batch)
                final_position = torch.where(is_take_profit, zero_batch, filled_position)
                
            elif cfg.stop_loss.in_use:
                final_filled_return = torch.where(is_stop_loss, stop_loss_return, filled_asset_return)
                close_fee = torch.where(is_stop_loss, fee_ratio_dict.stop * abs(filled_position), zero_batch)
                final_position = torch.where(is_stop_loss, zero_batch, filled_position)
                
            else:
                is_take_profit = torch.zeros_like(is_filled, device=is_filled.device)
                is_stop_loss = torch.zeros_like(is_filled, device=is_filled.device)
                final_position = filled_position
                final_filled_return = filled_asset_return
                close_fee = zero_batch
            
            pnl = filled_position * final_filled_return - close_fee
            
            if not (cfg.shuffling.train and phase == 'train'):
                last_position = torch.cat([init_position, final_position])[:-1]
                position_change = filled_position - last_position
                open_fee = fee_ratio_dict.limit * abs(position_change)
                open_fee[is_market] = fee_ratio_dict.market * abs(position_change[is_market])
            else:
                fee_scale = cfg.pnl_loss_with_fee_scale
                open_fee = fee_scale * fee_ratio_dict.limit * abs(filled_position)
        
        # 最终损失计算
        net_pnl = pnl - open_fee
        
        # 根据优化目标选择损失函数
        objective_enum = cfg.objective_enum
        if objective_enum == Objective.IC:
            loss = ic_loss(logits, asset_return)
        elif objective_enum == Objective.MSE:
            loss = F.mse_loss(logits, asset_return)
        elif objective_enum == Objective.Sharpe:
            sharpe = (net_pnl.mean() / net_pnl.std())
            loss = -sharpe
        elif objective_enum == Objective.Oracle:
            oracle = asset_return.abs()
            loss = (oracle.sum(dim=1) - net_pnl).mean()
        elif objective_enum == Objective.BCE:
            loss = F.binary_cross_entropy(F.sigmoid(logits), (asset_return.sign() > 0).float())
        elif objective_enum == Objective.PNL:
            loss = -net_pnl.mean()
            if cfg.align_pos_neg_return and {'train', 'val'}.issubset(set(self.pos_neg_return_ratio_dict)) and phase == 'train':
                extra_neg_pnl = (filled_position * final_filled_return.clip(None, 0)) * (self.pos_neg_return_ratio_dict.train / self.pos_neg_return_ratio_dict.val - 1)
                loss -= extra_neg_pnl.mean()
        elif cfg.position_punishment.in_use:
            punish_ratio = abs(final_position ** cfg.position_punishment.exponent)
            loss = -(torch.clamp_max(net_pnl, 0) * (1 + punish_ratio) + torch.clamp_min(net_pnl, 0) * (1 - punish_ratio / 2)).mean()
        
        if directional_balance is None:
            directional_balance = cfg.directional_balance
        if directional_balance:
            loss = loss * (1 - advised_position.mean().abs())
        
        # 如果在入场出场模式且需要返回原始指标，填充asset_return等
        if is_entry_exit and not 'asset_return' in locals():
            asset_return = cum_return_arr[:, -1] 
            high_return = cum_high_arr[:, -1]
            low_return = cum_low_arr[:, -1]
        
        result = (loss, pnl, open_fee, filled_position, final_filled_return, final_position, 
                 is_market, is_filled, is_take_profit, is_stop_loss, 
                 asset_return, high_return, low_return)
        
        for i, arr in enumerate(result):
            if torch.isnan(arr).any():
                print(f'{i}th array has {torch.isnan(arr).int().sum()} nan values\n{arr = }')
        
        return result


def save_summary_fig_pqt(cfg: PredictorConfig, phase: str, task_folder: str, epoch_str: str, save_folder_str: str, top_str: str, top_df: pl.DataFrame, fig_df: pl.DataFrame = None, step_pnl_arr: np.ndarray = None, cum_pnl_after_fee_arr: np.ndarray = None, save_pqt: bool = False):
    n_codes = cfg.n_codes
    abs_position_sum_arr = top_df[f'abs_position_sum'].to_numpy()
    position_hedge_arr = top_df[f'position_hedge'].to_numpy()
    baseline_arr = top_df[f'baseline'].to_numpy()
    title = f'{epoch_str} {phase} {top_str} '
    report_str, ic = report_clf_ic(position_hedge_arr, baseline_arr, title)

    
    if step_pnl_arr is None:
        step_pnl_arr = top_df[f'pnl'].to_numpy()
    if cum_pnl_after_fee_arr is None:
        cum_pnl_after_fee_arr = (top_df[f'pnl'] - top_df[f'fee']).cum_sum().to_numpy()
    fee_arr = top_df[f'fee'].to_numpy()
    pnl_after_fee = (step_pnl_arr - fee_arr)
    pnl_after_fee_mean = pnl_after_fee.mean()
    neg_pnl_after_fee = pnl_after_fee[pnl_after_fee < 0]
    sharpe = (pnl_after_fee_mean / pnl_after_fee.std())
    sortino = (pnl_after_fee_mean / neg_pnl_after_fee.std())
    
    profit_to_loss_ratio = -pnl_after_fee.clip(0).sum() / neg_pnl_after_fee.sum() * sortino

    end_pnl_before_fee = step_pnl_arr.sum()
    end_pnl_after_fee = cum_pnl_after_fee_arr[-1]
    (drawdown, drawdown_norm, mdd, mdd_norm), drawdown_arr, drawdown_norm_arr = get_mdd_arr(cum_pnl_after_fee_arr + 1)
    summary_str = (f"{title}Backtest Result:\nMDD: {mdd:.5f}, MDD Norm: {mdd_norm:.5f}\nDrawdown: {drawdown:.5f}, Drawdown Norm: {drawdown_norm:.5f}\nProfit / Loss Ratio: {profit_to_loss_ratio:.5f}, Sharpe Ratio: {sharpe:.5f}, Sortino Ratio: {sortino:.5f}\nPnl Before Fee Mean: {step_pnl_arr.mean():.5f}, Pnl After Fee Mean: {pnl_after_fee_mean:.5f}\nEnd PnL Before Fee: {end_pnl_before_fee:.5f}, End PnL After Fee: {end_pnl_after_fee:.5f}\nAbs Position Mean: {abs_position_sum_arr.mean():.5f}, Position Mean: {position_hedge_arr.mean():.5f}\n")
    print(summary_str)

    file_name_str = title[:-1].replace(' ', '_')
    save_folder = os.path.join(task_folder, save_folder_str)
    if not os.path.exists(save_folder):
        os.makedirs(save_folder)

    # save summary_str to file
    log_path = os.path.join(save_folder, f"metric{profit_to_loss_ratio:.5f}_ic{ic:.5f}_{file_name_str}.log")
    with open(log_path, 'w') as f:
        f.write(f'{report_str}\n{summary_str}')

    if save_pqt:
        pqt_df = top_df.select([
            cfg.group_by_str,
            'position_long', 
            'position_short', 
            'position_hedge', 
            'abs_position_sum', 
            'profit_ratio',
            'cum_pnl_ewm3',
            'cum_pnl_ewm4',
            'cum_pnl_ewm5',
            # 'cum_pnl_ewm6',
            'pnl', 
            'long_pnl', 
            'short_pnl', 
            # 'long_only_pnl', 
            # 'short_only_pnl', 
            'fee', 
            'baseline',
            ])
        pqt_path = os.path.join(save_folder, f"{file_name_str}.pqt")
        pqt_df.write_parquet(pqt_path)

    interval = cfg.interval_cfg.base
    quote_str = f'_qql{cfg.quote_quantile}' if cfg.load_all_codes else ''
    title_str = f'{save_folder_str}_{cfg.task_enum.value}_{interval}min_crypto{n_codes}{quote_str}_s{cfg.seq_len}p{cfg.pred_len}_{file_name_str}'
    fig_path = os.path.join(save_folder, f"{file_name_str}.png")
    if fig_df is None:
        sub_expr0 = (pl.col(f'cum_pnl_ewm2') - pl.col(f'cum_pnl_ewm3')) * 20                     
        sub_expr1 = (pl.col(f'cum_pnl_ewm3') - pl.col(f'cum_pnl_ewm4')) * 20                                
        # sub_expr2 = (pl.col(f'cum_pnl_ewm5') - pl.col(f'cum_pnl_ewm6')) * 20   
        fig_df = top_df.select(
            cfg.group_by_str,
            pl.col('position_long'),
            pl.col('position_short'),
            pl.col('abs_position_sum'),
            (1 + sub_expr0).alias('cum_pnl_emw2sub3'),
            (1 + sub_expr1).alias('cum_pnl_emw3sub4'),
            # (1 + sub_expr2).alias('cum_pnl_emw4sub5'),
            # (1 + sub_expr3).alias('cum_pnl_emw5sub6'),
            pl.col('pnl').cum_sum().alias('cum_pnl'),
            pl.col('long_pnl').cum_sum().alias('cum_long_pnl'),
            pl.col('short_pnl').cum_sum().alias('cum_short_pnl'),
            # pl.col('long_only_pnl').cum_sum().alias('cum_long_only_pnl'),
            # pl.col('short_only_pnl').cum_sum().alias('cum_short_only_pnl'),            
            pl.col('fee').cum_sum().alias('cum_fee'), 
            pl.col('baseline').cum_sum().alias('cum_baseline'),
            (pl.col('pnl') - pl.col('fee')).cum_sum().alias('cum_pnl_after_fee'),
        )
    fig_df = fig_df.to_pandas().set_index(cfg.group_by_str)
    index: pd.DatetimeIndex = fig_df.index
    plt.figure(figsize=(20, 11))
    if cfg.group_by_str == 'open_time':
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%dh%H'))
    plt.plot(fig_df, linewidth=1)
    plt.title(f"{title_str} Backtest Result")
    plt.legend(fig_df.columns)
    plt.grid()
    ax = plt.twinx()
    ax.plot(
            index, 
            -drawdown_arr, 
            color='black', 
            linestyle='-.', 
            linewidth=1,
            alpha=0.4
        )
    ax.plot(
            index, 
            -drawdown_norm_arr, 
            color='red', 
            linestyle='-.', 
            linewidth=1,  
            alpha=0.4
        )
    ax.legend(['drawdown', 'drawdown_norm'])
    ax.grid()
    plt.savefig(fig_path)
    plt.close()

    return sortino, profit_to_loss_ratio


def get_top_k_mask(score_position: Tensor | np.ndarray, top_num: int, dim: int = 1) -> Tensor:
    if isinstance(score_position, np.ndarray):
        score_position = torch.from_numpy(score_position)
    top_indices = score_position.abs().topk(top_num, dim=dim, largest=True, sorted=True).indices
    top_k_mask = torch.zeros_like(score_position, device=score_position.device)
    top_k_mask.scatter_(dim, top_indices, 1)
    return top_k_mask


def ic_loss(pred, label):
    return -torch.dot(
            (pred - pred.mean()) / np.sqrt(pred.shape[0]) / pred.std(),
            (label - label.mean()) / np.sqrt(label.shape[0]) / label.std(),
        )