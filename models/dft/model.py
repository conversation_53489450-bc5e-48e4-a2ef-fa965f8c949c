import torch
import torch.nn as nn
from core.predictor_config import PredictorConfig
from models.dft.DFT import DFT


class Model(nn.Module):
    def __init__(self, cfg: PredictorConfig):
        super(Model, self).__init__()
        self.cfg = cfg
        n_head = cfg.n_head        
        sn_head = n_head // 2
        self.backbone = DFT(cfg.input_size, cfg.embedding_size, n_head, sn_head, cfg.seq_len, cfg.dropout_rate, cfg.feature_cfg.market_dim * cfg.feature_cfg.original, cfg.output_size)
    

    def forward(self, x, onehot=None):
        x = self.backbone(x)
        return x