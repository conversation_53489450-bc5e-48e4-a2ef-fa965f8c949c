import torch
import torch.nn as nn
import torch.nn.functional as F

from models.tabm.tabm import TabM
from core.predictor_config import PredictorConfig


class Model(nn.Module):
    def __init__(self, cfg: PredictorConfig, input_size=None, num_blocks=None, output_size=None):
        super().__init__()
        self.cfg = cfg
        num_blocks = num_blocks or cfg.num_tabm_blocks
        backbone_cfg = dict(
            type='MLP',
            n_blocks=num_blocks,
            d_block=cfg.embedding_size,
            dropout=cfg.dropout_rate.fc,
            activation='Tanh',
            # activation='Mish',
        )
        input_size = input_size or cfg.input_size * cfg.seq_len
        output_size = output_size or cfg.output_size
        self.backbone = TabM(
            n_num_features=input_size,
            backbone=backbone_cfg,
            n_classes=output_size,
            arch_type="tabm",
            k=32,  # 无需集成 
        )
    

    def forward(self, x, onehot=None):
        x = x.flatten(1)
        return self.backbone(x, onehot).mean(1)
    

TabMModel = Model
        
        