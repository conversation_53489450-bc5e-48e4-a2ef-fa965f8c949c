import numpy as np
import torch
from torch import nn

from core.dot_dict import DotDict as dd
from core.predictor_config import PredictorConfig
from models.tcn.tcn import TCNLayer


class Backbone(nn.Module):
    def __init__(self, cfg: PredictorConfig):
        super().__init__()
        self.cfg = cfg
        self.seq_len = cfg.seq_len_dict.backbone
        self.branch_list = nn.ModuleList()
        for _ in range(cfg.branch_dict.backbone):
            tcn = TCNLayer(cfg, self.seq_len)
            self.branch_list.append(tcn)
        self.input_size = self.branch_list[0].input_size
        self.output_size = sum([np.prod(b.output_size) for b in self.branch_list]).astype(int)

    def forward(self, seq: torch.Tensor):
        shape = seq.shape
        if len(shape) == 3:
            seq = seq.unsqueeze(1)
        seq = seq[:, :, -self.seq_len:]
        return torch.cat([b(seq[:, i]).flatten(1) for i, b in enumerate(self.branch_list)], dim=1)


if __name__ == '__main__':
    cfg = PredictorConfig()
    cfg.n_seq_features = 2
    model = Backbone(cfg)
    x = torch.randn(2, cfg.seq_len_dict.backbone, model.input_size)
    print(model(x).shape)