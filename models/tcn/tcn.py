
import numpy as np
import torch
from einops import rearrange
from torch import nn
from core.dot_dict import DotDict as dd
from layers.feed_forward import FeedForward
from layers.hyper_connection import HyperConnection
from core.predictor_config import PredictorConfig


class Chomp1d(nn.Module):
    def __init__(self, chomp_size):
        super(Chomp1d, self).__init__()
        self.chomp_size = chomp_size

    def forward(self, x):
        """
        其实这就是一个裁剪的模块，裁剪多出来的padding
        tensor.contiguous()会返回有连续内存的相同张量
        有些tensor并不是占用一整块内存，而是由不同的数据块组成
        tensor的view()操作依赖于内存是整块的，这时只需要执行
        contiguous()函数，就是把tensor变成在内存中连续分布的形式
        本函数主要是增加padding方式对卷积后的张量做切边而实现因果卷积
        """
        return x[:, :, :-self.chomp_size].contiguous()


class TemporalBlock(nn.Module):
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, padding, dropout=0., n_convs=1, with_outer_activation=True, hyper_connection_cfg: dd = None, block_idx=1):
        """
        残差模块，其中有两组一维卷积与恒等映射

        :param n_inputs: int, 输入通道数
        :param n_outputs: int, 输出通道数
        :param kernel_size: int, 卷积核尺寸
        :param stride: int, 步长，一般为1
        :param dilation: int, 膨胀系数
        :param padding: int, 填充系数
        :param dropout: float, dropout比率
        """
        super(TemporalBlock, self).__init__()
        self.network = []
        self.convs = []
        # self.convs = [nn.Identity()] * n_convs
        for i in range(n_convs):
            n_inputs_var = n_inputs if i == 0 else n_outputs
            self.convs.append(
                # nn.utils.weight_norm(  # sacd 不能使用
                nn.Conv1d(n_inputs_var, n_outputs, kernel_size,
                          stride=stride, padding=padding, dilation=dilation))
            self.network += [
                    self.convs[i], 
                    Chomp1d(padding), 
                    # nn.LeakyReLU(),
                    nn.Mish(),
                    # nn.GELU(),
                    nn.BatchNorm1d(n_outputs) if block_idx == i == 0 else nn.Identity(),
                    # nn.Mish(),
                    nn.Dropout(dropout)
                ]
        # 根据第一个卷积层的输出与padding大小实现因果卷积
        # 经过conv1，输出的size其实是(Batch, input_channel, seq_len + padding)
        # 裁剪掉多出来的padding部分，维持输出时间步为seq_len, 保证了输入序列与输出序列的长度相等，
        # 但卷积前的通道数与卷积后的通道数不一定一样。
        # self.block_idx = block_idx
        # if block_idx == 0:
        #     self.conv1d = nn.Conv1d(n_inputs, n_inputs, 1)
        # elif block_idx == -1:
        #     self.conv1d = nn.Conv1d(n_outputs, n_outputs, 1)
        self.network = nn.Sequential(*self.network)
        if hyper_connection_cfg.in_use:
            self.hyper_connection = HyperConnection( **hyper_connection_cfg)
        else:
            self.hyper_connection = None
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        # self.activation = nn.Mish()
        self.activation = nn.Tanh() if with_outer_activation else nn.Identity()
        self.init_weights()
        # 正如先前提到的卷积前与卷积后的通道数不一定相同
        # 所以如果通道数不一样，那么需要对输入x做一个逐元素的一维卷积
        # 以使得它的维度与前面两个卷积相等。

    def init_weights(self):
        """
        参数初始化

        :return:
        """
        for conv in self.convs:
            conv.weight.data.normal_(0, 0.01)
        if self.downsample is not None:
            self.downsample.weight.data.normal_(0, 0.01)

    def forward(self, x):
        """
        :param x: size of (Batch, input_channel, seq_len)
        :return:
        """
        x = rearrange(x, 'n t c -> n c t')
        
        # if self.block_idx == 0:
        #     x = self.conv1d(x)            
        out = self.network(x)
        if self.hyper_connection is not None:
            out = self.hyper_connection(out)
            out = self.activation(out)
        else:
            # res = self.downsample(x)
            res = x if self.downsample is None else self.downsample(x)
            out = self.activation(out + res)
        # if self.block_idx == -1:
        #     out = self.conv1d(out)
        out = rearrange(out, 'n c t -> n t c')
        return out


class TCNLayer(nn.Module):
    def __init__(self, cfg: PredictorConfig, seq_len=None, input_size=None):
        super(TCNLayer, self).__init__()
        self.cfg = cfg
        block_list = []
        if seq_len is None:
            seq_len = self.seq_len = cfg.seq_len
        else:
            self.seq_len = seq_len
        if input_size is None:
            input_size = self.input_size = cfg.input_size
        else:
            self.input_size = cfg.input_size

        num_channel_list = [cfg.channel_size] * cfg.num_tcn_blocks
        num_levels = len(num_channel_list)
        hyper_connection_cfg = cfg.hyper_connection        
        for i in range(num_levels):
            dilation_size = 2 ** i  # 膨胀系数：1，2，4，8……随着网络层级的增加而成指数增加，可以增大感受野并不丢弃任何输入序列的元素
            in_channels = self.input_size if i == 0 else num_channel_list[i - 1]  # 确定每一层的输入通道数
            out_channels = num_channel_list[i]  # 确定每一层的输出通道数
            with_outer_activation = True #if i < num_levels - 1 else False  # 最后一层不用激活函数
            # with_outer_activation = False #if i < num_levels - 1 else True
            hyper_connection_cfg.dim = out_channels
            if not hyper_connection_cfg.fixed_layer_idx:
                hyper_connection_cfg.layer_idx = i + 1
            # block_idx = -1 if i == num_levels - 1 else 1
            block_list.append(TemporalBlock(in_channels, out_channels, kernel_size=cfg.tcn_kernel_size,
                                         stride=cfg.num_tcn_stride, dilation=dilation_size,
                                         padding=(cfg.tcn_kernel_size - 1) * dilation_size, dropout=cfg.dropout_rate.backbone,
                                         n_convs=cfg.num_block_convs, with_outer_activation=with_outer_activation, hyper_connection_cfg=hyper_connection_cfg, block_idx=i))
            
            if i < num_levels - 1 and cfg.tcn_with_ffn:
                block_list.append(FeedForward(out_channels, out_channels, dropout=cfg.dropout_rate.backbone))
        self.network = nn.Sequential(*block_list)

        if cfg.tcn_layer_with_temporal_att:
            self.att_fc1 = nn.Linear(seq_len, seq_len)
            self.tanh1 = nn.Tanh()
            self.att_fc2 = nn.Linear(seq_len, seq_len)

        else:
            self.att_fc1 = nn.Identity()
            self.tanh1 = nn.Identity()
            self.att_fc2 = nn.Identity()
        self.output_size = num_channel_list[-1]


    def forward(self, x):
        """
        :param x: size of (Batch, input_channel, seq_len)
        :return: size of (Batch, output_channel, seq_len)
        """
        if isinstance(x, np.ndarray):
            x = torch.from_numpy(x).to(self.cfg.device)

        
        if self.cfg.tcn_layer_with_temporal_att:
            att = self.att_fc1(x)
            att = self.tanh1(att)
            att = self.att_fc2(x)
            att = att.softmax(dim=1)
        x = self.network(x)
        
        return x

