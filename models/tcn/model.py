import numpy as np
import torch
from einops import rearrange
from torch import nn

from core.dot_dict import DotDict as dd
from core.cst import EmbeddingOperation, FC_Type, TaskType
from layers.cheby_kan import ChebyKANLayer
from layers.multi_embedding import MultiEmbedding
from models.lightning_pflda import FeatureAdapter, LabelAdapter
from core.predictor_config import PredictorConfig
from models.tabm.model import TabMModel

from models.tcn.tcn import TCNLayer
from models.vmlp.vmlp import VariationalMLP


class Model(nn.Module):
    def __init__(self, cfg: PredictorConfig, gru=None):
        super().__init__()
        self.cfg = cfg
        num_channel_list = [cfg.embedding_size] * cfg.num_tcn_blocks
        self.seq_len_list = cfg.seq_len_list
        # seq_len = self.seq_len = cfg.seq_len
        self.seq_len_sum = sum(self.seq_len_list)
        self.para_net_list = nn.ModuleList()
        for seq_len in self.seq_len_list:
            tcn = TCNLayer(cfg, seq_len)
            self.para_net_list.append(tcn)
        self.tanh = nn.Tanh()
        
        self.flatten_before_fc = cfg.flatten_before_fc
        att_dim = tcn.output_size
        output_size = cfg.output_size
        # att_dim = seq_len
        if cfg.att_after_tcn:
            self.att_fc1 = nn.Linear(att_dim, att_dim)
            self.tanh1 = nn.Tanh()
            self.att_fc2 = nn.Linear(att_dim, att_dim)
        else:
            self.att_fc1 = nn.Identity()
            self.tanh1 = nn.Identity()
            self.att_fc2 = nn.Identity()

        if cfg.tcn_with_rnn:
            from models.rnn.model import Model as GRU
            self.gru = GRU(cfg, input_size=num_channel_list[-1])
        else:
            self.gru = None            
            self.emb_op_enum = cfg.emb_op_enum
            
            num_categories = len(cfg.non_seq_column_idx_list)
            self.with_non_seq = num_categories > 0
            flatten_size = tcn.output_size * self.seq_len_sum
            if self.with_non_seq:
                self.embedding = MultiEmbedding(cfg.category_size_dict)
                embedding_size = self.embedding.output_size
            else:
                self.embedding = None
                embedding_size = 0
            
            
            if cfg.flatten_before_fc:
                self.fc_input_dim = flatten_size
                if self.with_non_seq:
                    if self.emb_op_enum == EmbeddingOperation.Concat:
                        self.fc_input_dim += embedding_size
            else:
                self.fc_input_dim = tcn.output_size
                self.fc1 = nn.Linear(seq_len, output_size)
                self.mish = nn.Mish()
                self.fc2 = nn.Linear(self.fc_input_dim, 1)
                
            
            self.batch_norm = nn.BatchNorm1d(self.fc_input_dim) if cfg.use_batch_norm else nn.Identity()
            
            
            fc_dim_list = cfg.tcn_fc_size_list + [output_size]
            self.fcs = nn.Sequential()
            for i, dim in enumerate(fc_dim_list):
                input_dim = self.fc_input_dim if i == 0 else fc_dim_list[i - 1]
                # self.fcs.append(nn.LayerNorm(input_dim))
                if cfg.fc_enum == FC_Type.KAN:
                    # self.fcs.append(FastKAN([input_dim, d]))
                    self.fcs.append(ChebyKANLayer(input_dim, dim))
                    if i < len(fc_dim_list) - 1:
                        self.fcs.append(nn.LayerNorm(dim))
                elif cfg.fc_enum == FC_Type.TabM:
                    self.fcs.append(TabMModel(cfg, input_dim, dim, 1))
                elif cfg.fc_enum == FC_Type.VMLP:
                    self.fcs.append(VariationalMLP(input_dim, dim))    
                else:
                    self.fcs.append(nn.Linear(input_dim, dim))

            self.dropout = nn.Dropout(cfg.dropout_rate.fc) if cfg.dropout_rate.fc > 0 else nn.Identity()
            # self.dropout = nn.Identity()
            # self.dropout = nn.Dropout(0.5)
                
                # if i == 0:ChebyKANLayer
                #     self.fcs.append(nn.Tanh())                
        factor_num = cfg.n_seq_features * cfg.seq_len
        num_heads = cfg.meta_adapt.num_heads
        temperature = cfg.meta_adapt.temperature
        if cfg.meta_adapt.in_use:
            self.mapper_x = FeatureAdapter(cfg, factor_num, num_heads, temperature)
            self.mapper_y = LabelAdapter(cfg, factor_num, num_heads, temperature)
        elif cfg.feature_cfg.adapt:
            self.mapper_x = FeatureAdapter(cfg, factor_num, num_heads, temperature)
        

    def forward(self, x: torch.Tensor, non_seq: torch.Tensor = None, image: torch.Tensor = None):
        """
        输入x的结构不同于RNN，一般RNN的size为(Batch, seq_len, channels)或者(seq_len, Batch, channels)，
        这里把seq_len放在channels后面，把所有时间步的数据拼起来，当做Conv1d的输入尺寸，实现卷积跨时间步的操作，
        很巧妙的设计。

        :param x: size of (Batch, input_channel, seq_len)
        :return: size of (Batch, output_channel, seq_len)
        """
        # if self.pre_conv2d_embedding:
        #     x = self.pre_conv2d_embedding(x)
        if isinstance(x, np.ndarray):
            x = torch.from_numpy(x).to(self.cfg.device)
        x_list = []
        for i, seq_len in enumerate(self.seq_len_list):
            x_i = x[:, -seq_len:]
            x_i = self.para_net_list[i](x_i)
            x_list.append(x_i)
            # if self.cfg.batch_att_after_tcn:
            #     att = self.att_fc1(x_i)
            #     att = self.tanh1(att)
            #     att = self.att_fc2(x_i)
            #     att = att.softmax(dim=1)
            #     x_i = x_i * att
            # else:
            #     # x_i = self.mish(x_i)
            #     ...
        x = torch.cat(x_list, dim=1)
        x = self.tanh(x)

        # x = self.tcn(x)
        if self.cfg.att_after_tcn:
            x = x.permute(1, 0, 2)
            # x = x.permute(0, 2, 1)
            att = self.att_fc1(x)
            att = self.tanh1(att)
            att = self.att_fc2(x)
            att = att.softmax(dim=1)
            x = x * att
            x = x.permute(1, 0, 2)
            # x = x.permute(0, 2, 1)
        else:
            # x = self.mish(x)
            ...
        # x = self.tanh(x)
        if self.with_non_seq and non_seq is not None:
            embedded = self.embedding(non_seq.int())
        else:
            embedded = None
        if self.gru is not None:
            x = self.gru(x)
        else:
            if self.flatten_before_fc:
                
                if embedded is not None:
                    if self.emb_op_enum == EmbeddingOperation.Add:
                        x = x + embedded.unsqueeze(1).repeat(1, x.size(1), 1)
                        x = rearrange(x, 'n t c -> n (c t)')
                    elif self.emb_op_enum == EmbeddingOperation.Concat:
                        x = rearrange(x, 'n t c -> n (c t)')
                        x = torch.cat([x, embedded], dim=1)
                
                else:
                    x = rearrange(x, 'n t c -> n (c t)')
                    
                x = self.batch_norm(x)
                for fc in self.fcs:
                    x = fc(x)                
            else:
                x = rearrange(x, 'n t c -> n c t')
                x = self.fc1(x)
                # x = self.mish(x)
                x = rearrange(x, 'n c t -> n t c')
                x = self.fc2(x).squeeze(-1)
        if isinstance(x, tuple):
            x, loss = x
        else:
            loss = 0
        x = self.dropout(x)

        return x, loss