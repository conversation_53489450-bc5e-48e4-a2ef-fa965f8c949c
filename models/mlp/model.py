import torch
import torch.nn as nn


class Model(nn.Module):
    def __init__(self, cfg):
        if __name__ == '__main__':
            from core.predictor_config import PredictorConfig
            cfg: PredictorConfig = cfg
        super(Model, self).__init__()
        embedding_dimension = cfg.embedding_size
        dropout_rate = cfg.dropout_rate
        hidden_size = cfg.hidden_size
        output_size = cfg.output_size
        self.block = nn.Sequential(
            nn.Linear(cfg.input_size * cfg.seq_len, embedding_dimension),
            # nn.BatchNorm1d(embedding_dimension) if batch_norm else nn.Identity(),
            nn.Tanh(),
            nn.Dropout(dropout_rate.fc) if dropout_rate.fc > 0. else nn.Identity(),
            nn.Linear(embedding_dimension, embedding_dimension),
            nn.BatchNorm1d(embedding_dimension) if cfg.use_batch_norm else nn.Identity(),
            nn.Tanh(),
            nn.Dropout(dropout_rate.fc) if dropout_rate.fc > 0. else nn.Identity(),
            nn.Linear(embedding_dimension, output_size),
        )


    def forward(self, x: torch.Tensor, onehot=None) -> torch.Tensor:
        x = x.reshape(x.shape[0], -1)
        return self.block(x)