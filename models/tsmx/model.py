from einops import rearrange
import torch
import torch.nn as nn
import torch.nn.functional as F
from core.predictor_config import PredictorConfig

class TSLinear(nn.Module):
    def __init__(self, L, T):
        super(TSLinear, self).__init__()
        self.fc = nn.Linear(L, T)

    def forward(self, x):
        return self.fc(x)


class TSMixer(nn.Module):
    def __init__(self, seq_len, in_channel, pred_len, n_mixer, dropout, out_hidden=128, out_channel=1, n_paralell=1, n_codes=1, use_projection=False, with_inverse=False, cfg: PredictorConfig = None):
        super(TSMixer, self).__init__()
        if cfg is not None:
            seq_len = cfg.seq_len
            in_channel = cfg.input_size
            pred_len = cfg.pred_len
            out_channel = 1
            n_codes = 1
        self.seq_len = seq_len
        self.in_channel = in_channel
        self.in_channel_by_code = in_channel * n_codes
        self.pred_len = pred_len
        self.n_codes = n_codes
        self.mixer_layers = []
        self.inverse_layers = []
        self.n_mixer = n_mixer
        self.n_paralell = n_paralell
        if n_paralell > 1:
            self.with_inverse = with_inverse = False
        self.temp_proj = TemporalProj(seq_len * n_paralell * 2 ** with_inverse, pred_len)
        if out_channel == 1:
            self.out_layer = nn.Sequential(
                nn.Linear(seq_len * self.in_channel_by_code * n_paralell * 2 ** with_inverse, out_hidden), 
                nn.Mish(), 
                nn.Dropout(dropout),
                # nn.Linear(out_hidden, out_hidden), 
                # nn.Mish(),          
                # nn.Dropout(dropout),       
                nn.Linear(out_hidden, pred_len)
                )
        self.paralell_proj = self.paralell_layers = None
        if n_paralell > 1:
            if use_projection:
                self.paralell_proj = TemporalProj(seq_len, seq_len * n_paralell)
            self.paralell_layers = nn.ModuleList()
            for i in range(self.n_mixer):
                self.paralell_layers.append(nn.ModuleList([MixerLayer(seq_len, self.in_channel_by_code, dropout) for _ in range(n_paralell)]))
            return
        
        for i in range(self.n_mixer):
            self.mixer_layers.append(MixerLayer(seq_len, self.in_channel_by_code, dropout))
            if with_inverse:
                self.inverse_layers.append(MixerLayer(seq_len, self.in_channel_by_code, dropout, True))            
        self.mixer_layers = nn.ModuleList(self.mixer_layers)
        if with_inverse:
            self.inverse_layers = nn.ModuleList(self.inverse_layers)


    def forward(self, x: torch.Tensor):
        batch_size = x.shape[0]
        if len(x.shape) == 2:
            x = rearrange(x, 'b (ncd l c) -> b l (ncd c)', b=batch_size, ncd=self.n_codes, l=self.seq_len, c=self.in_channel)
        
        if self.paralell_layers is not None:
            if self.paralell_proj is not None:                
                para_x = list(self.paralell_proj(x).chunk(self.n_paralell, 1))
            else:
                para_x = [x.clone() for _ in range(self.n_paralell)]
            for i in range(self.n_mixer):
                for j, p in enumerate(self.paralell_layers):
                    para_x[j] = p[i](para_x[j]) # type: ignore
            
            x = torch.cat(para_x, dim=1)

        else:
            if self.with_inverse:
                inv_x = x.clone()
            else:
                inv_x = None      

            for i in range(self.n_mixer):
                x = self.mixer_layers[i](x)        
                if self.with_inverse:
                    inv_x = self.inverse_layers[i](inv_x)

            if inv_x is not None:
                x = torch.cat([x, inv_x], dim=1)

        if self.out_layer is not None:
            x = self.out_layer(x.reshape(batch_size, -1))
        else:
            x = self.temp_proj(x)
            
        return x

class MLPTime(nn.Module):
    def __init__(self, T, dropout_rate=0.1):
        super(MLPTime, self).__init__()
        self.fc = nn.Linear(T, T)
        self.relu = nn.Mish()
        self.dropout = nn.Dropout(p=dropout_rate)

    def forward(self, x):
        x = self.fc(x)
        x = self.relu(x)
        x = self.dropout(x)
        return x

class MLPFeat(nn.Module):
    def __init__(self, C, dropout_rate=0.1):
        super(MLPFeat, self).__init__()
        self.fc1 = nn.Linear(C, C)
        self.dropout1 = nn.Dropout(p=dropout_rate)
        self.relu = nn.Mish()
        self.fc2 = nn.Linear(C, C)
        self.dropout2 = nn.Dropout(p=dropout_rate)

    def forward(self, x):
        x = self.fc1(x)
        x = self.relu(x)
        x = self.dropout1(x)
        x = self.fc2(x)
        x = self.dropout2(x)
        return x

class MixerLayer(nn.Module):
    def __init__(self, L, C, dropout, inverse=False):
        super(MixerLayer, self).__init__()
        self.mlp_time = MLPTime(L, dropout)
        self.mlp_feat = MLPFeat(C, dropout)
        self.inverse = inverse
        
    def batch_norm_2d(self, x):
        """ x has shape (B, L, C) """
        return (x - x.mean()) / x.std()
    
    def forward(self, x):
        """ x has shape (B, L, C) """
        if self.inverse:
            res_x = x
            x = self.batch_norm_2d(x)
            x = self.mlp_feat(x) + res_x            
            res_x = x
            x = self.batch_norm_2d(x)
            x = x.transpose(1, 2)
            x = self.mlp_time(x)
            x = x.transpose(1, 2) + res_x            
        else:
            res_x = x
            x = self.batch_norm_2d(x)
            x = x.transpose(1, 2)
            x = self.mlp_time(x)
            x = x.transpose(1, 2) + res_x
            res_x = x
            x = self.batch_norm_2d(x)
            x = self.mlp_feat(x) + res_x
        return x

class TemporalProj(nn.Module):
    def __init__(self, L, T):
        super(TemporalProj, self).__init__()
        self.fc = nn.Linear(L, T)

    def forward(self, x):
        x = x.transpose(1, 2)
        x = self.fc(x)
        x = x.transpose(1, 2)
        return x


class Model(nn.Module):
    def __init__(self, cfg: PredictorConfig):
        super(Model, self).__init__()
        self.model = TSMixer(
                seq_len=cfg.seq_len,
                in_channel=cfg.input_size,
                pred_len=cfg.output_size,
                n_mixer=1,
                dropout=cfg.dropout_rate,
                out_hidden=cfg.hidden_size,
                out_channel=cfg.label_dim,
                n_paralell=8,
                n_codes=1,
                # use_projection=False,
                # with_inverse=False,
                cfg=cfg,
            )
        
    def forward(self, x, onehot=None):
        return self.model(x)
    

if __name__ == '__main__':
    seq_len = 10
    input_dim = 8
    pred_len = 5
    hidden_size = 16
    label_dim = 1
    model = TSMixer(
                seq_len=seq_len,
                in_channel=input_dim,
                pred_len=pred_len,
                n_mixer=1,
                dropout=0.6,
                out_hidden=hidden_size,
                out_channel=label_dim,
                n_paralell=8,
                n_codes=1,
                # use_projection=False,
                # with_inverse=False,
            )
    model.eval()
    print(model)
    x = torch.randn(2, 10, 8)
    y = model(x)
    print(y.shape)