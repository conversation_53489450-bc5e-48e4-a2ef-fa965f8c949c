import os
from shutil import copyfile
import numpy as np
import torch
from torch import Tensor
from torch.nn import functional as F
from core.backtest import backtest
from core.cst import PositionType, TaskType
from core.predictor_config import PredictorConfig
from models.lightning_drt import LightningDRT



class LightningCTA(LightningDRT):

    def __init__(
        self,
        cfg: PredictorConfig,
    ):
        super().__init__(cfg)


    def calc_loss(self, pred, batch_y, phase, shift_ratio, stop_loss_ratio, take_profit_ratio):
        cfg = self.cfg
        asset_return = batch_y[..., 0]
        loss, pnl, fee, end_position, filled_position, stop_loss_position, stop_loss_range, profit_position, profit_range, is_market, is_filled, is_stop_loss, is_profit = self.cta_loss(pred, asset_return, self.position, shift_ratio, stop_loss_ratio, take_profit_ratio, phase)
        if not cfg.shuffling.train:
            self.position = end_position[-1:].detach()
        self.step_dict[phase].pnl.append(pnl.detach().cpu().numpy())
        self.step_dict[phase].filled_position.append(filled_position.detach().cpu().numpy())
        self.step_dict[phase].asset_return.append(asset_return.detach().cpu().numpy())
        self.step_dict[phase].stop_loss_position.append(stop_loss_position.detach().cpu().numpy())
        self.step_dict[phase].stop_loss_range.append(stop_loss_range.detach().cpu().numpy())
        self.step_dict[phase].profit_position.append(profit_position.detach().cpu().numpy())
        self.step_dict[phase].profit_range.append(profit_range.detach().cpu().numpy())
        self.step_dict[phase].is_market.append(is_market.detach().cpu().numpy())
        self.step_dict[phase].is_filled.append(is_filled.detach().cpu().numpy())
        self.step_dict[phase].is_stop_loss.append(is_stop_loss.detach().cpu().numpy())
        self.step_dict[phase].is_profit.append(is_profit.detach().cpu().numpy())
        self.step_dict[phase].stop_loss_ratio.append(stop_loss_ratio.detach().cpu().numpy())
        self.step_dict[phase].take_profit_ratio.append(take_profit_ratio.detach().cpu().numpy())

        return loss


    def process_epoch(self, loss: np.ndarray, pred: np.ndarray, actual: np.ndarray, phase: str, epoch_str: str) -> None:
        cfg = self.cfg
        pnl_arr = np.concatenate(self.step_dict[phase].pnl, axis=None).flatten()
        pnl_mean = pnl_arr.mean()
        pnl_std = pnl_arr.std()
        asset_return_arr = np.concatenate(self.step_dict[phase].asset_return, axis=None)
        filled_position_arr = np.concatenate(self.step_dict[phase].filled_position, axis=None)
        stop_loss_position_arr = np.concatenate(self.step_dict[phase].stop_loss_position, axis=None)
        final_filled_return_arr = np.concatenate(self.step_dict[phase].final_filled_return, axis=None)
        is_market_arr = np.concatenate(self.step_dict[phase].is_market, axis=None)
        is_filled_arr = np.concatenate(self.step_dict[phase].is_filled, axis=None)
        is_stop_loss_arr = np.concatenate(self.step_dict[phase].is_stop_loss, axis=None)
        stop_loss_ratio_arr = np.concatenate(self.step_dict[phase].stop_loss_ratio, axis=None)
        # abs_position_sum = abs(position_arr).mean()
        position_max = stop_loss_position_arr.max()
        position_min = stop_loss_position_arr.min()
        position_mean = stop_loss_position_arr.mean()     
        sharpe = (pnl_mean / pnl_std)
        print(f'{epoch_str} {phase} {sharpe = :.5f},\t{pnl_mean = :.5f},\t{pnl_std = :.5f}\n{position_min = :.5f},\t{position_max = :.5f},\t{position_mean = :.5f}\n')
        # if not self.cfg.shuffling.train or phase in [
        if phase in [
            'val', 
            'test',
            ]:
            interval = cfg.interval_cfg.base
            save_folder = os.path.join(self.ckpt_folder, cfg.datetime_str, f'epoch_{self.current_epoch}')
            if not os.path.exists(save_folder):
                os.makedirs(save_folder)
            if phase == 'val':
                save_path = os.path.join(self.ckpt_folder, cfg.datetime_str, cfg.script_name.split('/')[-1])
                if not os.path.exists(save_path):
                    # copy portfolio.py into save_path
                    copyfile(cfg.script_name, save_path)
            date_dict = self.date_dict
            date_str = f'{date_dict.val[0]}v{date_dict.val[1]}'
            save_str = f'{date_str}_{phase}_crypto{(n_codes := cfg.n_codes)}_{interval}min_epc{self.current_epoch}_{cfg.task_enum.value}'
            mean_pnl_before_fee, fee_mean, end_pnl_before_fee, fee_sum = backtest(
                filled_position_arr, 
                asset_return_arr, 
                stop_loss_position_arr, 
                final_filled_return_arr, 
                is_market_arr, 
                is_filled_arr, 
                is_stop_loss_arr, 
                stop_loss_ratio_arr, 
                self.cfg.fee_ratio, 
                save_folder=save_folder, 
                save_str=save_str, 
                n_codes=n_codes, 
                code_list=self.cfg.code_list, 
                is_portfolio=False
            )
        else:
            mean_pnl_before_fee = fee_mean = end_pnl_before_fee = fee_sum = 0
        metric = end_pnl_before_fee
        # metric = -loss
        if phase == 'val':
            if self.task_enum == TaskType.DirectTrading:
                self.log('pnl_mean', pnl_mean)
                self.log('sharpe', sharpe)
                self.log('mean_pnl_before_fee', mean_pnl_before_fee)
                self.log('mean_pnl_after_fee', mean_pnl_before_fee - fee_mean)
            # self.log('cum_acc_bias_mean', cum_acc_bias_mean)
            self.log('metric', metric)


    def preidct(self, x: torch.Tensor | np.ndarray) -> tuple:
        cfg = self.cfg
        if isinstance(x, np.ndarray):
            x = torch.from_numpy(x, device=cfg.device, dtype=torch.float32)
        pred = self.model(x)

        # if (shift_scale := cfg.order_shift_scale) != 0:
        #     shift_ratio = F.sigmoid(pred[:, -1]) * shift_scale
        # else:
        #     shift_ratio = torch.zeros(pred.shape[0], device=cfg.device, dtype=torch.float32)
        if cfg.stop_loss.in_use:
            if cfg.stop_loss.learnable:
                stop_loss_ratio = F.sigmoid(pred[:, 1]) * cfg.stop_loss.scale
                pred = pred[:, 0]
            else:
                pred = pred
                stop_loss_ratio = cfg.stop_loss.min_ratio * torch.ones_like(pred)
        else:
            pred = pred
            stop_loss_ratio = torch.ones_like(pred.shape[0])
        if cfg.position_enum == PositionType.Long:
            pred = F.sigmoid(pred)
        else:
            pred = F.tanh(pred)
        return pred, stop_loss_ratio



    def cta_loss(self, advised_position: Tensor, asset_return_arr: Tensor, init_position: Tensor = 0, shift_ratio: Tensor = 0., stop_loss_ratio: Tensor = 1., take_profit_ratio: Tensor = 1., phase: str = 'train', directional_balance: bool = None) -> tuple:
        if isinstance(init_position, float):
            init_position = torch.tensor(init_position, device=advised_position.device).reshape(1)
        if isinstance(shift_ratio, float):
            shift_ratio = shift_ratio * torch.ones_like(advised_position, device=advised_position.device)
        if isinstance(stop_loss_ratio, float):
            stop_loss_ratio = stop_loss_ratio * torch.ones_like(advised_position, device=advised_position.device)
        if isinstance(take_profit_ratio, float):
            take_profit_ratio = take_profit_ratio * torch.ones_like(advised_position, device=advised_position.device)
        
        fee_ratio_dict = self.cfg.fee_ratio
        asset_cum_return_arr = asset_return_arr.cumsum(dim=1)
        pred_len = asset_cum_return_arr.shape[1]
        is_market = shift_ratio == 0
        is_filled_arr = torch.sign(advised_position.unsqueeze(1)) * asset_cum_return_arr  < -shift_ratio.unsqueeze(1).repeat(1, pred_len)
        is_filled = is_filled_arr.any(dim=1) | is_market
        filled_idx = torch.where(is_filled, is_filled_arr.float().argmax(dim=1), pred_len)

        filled_position = is_filled.float() * advised_position
        asset_filled_return_arr = is_filled.float().unsqueeze(1) * asset_cum_return_arr + (torch.sign(filled_position) * shift_ratio).unsqueeze(1).repeat(1, pred_len)
        filled_pnl_arr = filled_position.unsqueeze(1) * asset_filled_return_arr

        is_stop_loss_arr = filled_pnl_arr < -stop_loss_ratio.unsqueeze(1).repeat(1, pred_len)
        is_stop_loss = is_stop_loss_arr.any(dim=1)
        stop_loss_idx = torch.where(is_stop_loss, is_stop_loss_arr.float().argmax(dim=1), pred_len + 1)
        filled_first = filled_idx <= stop_loss_idx
        is_stop_loss[~filled_first] = False
        return_idx = torch.where(is_stop_loss, is_stop_loss_arr.float().argmax(dim=1), pred_len - 1)

        filled_final_filled_return = asset_filled_return_arr[torch.arange(len(is_stop_loss)), return_idx]
        fee_after_stop = torch.where(is_stop_loss, fee_ratio_dict.stop * abs(filled_position), torch.zeros_like(filled_position))
        position_after_stop = torch.where(is_stop_loss, torch.zeros_like(filled_position), filled_position)
        pnl = filled_position * filled_final_filled_return - fee_after_stop

        fee = torch.zeros_like(pnl, device=advised_position.device)
        if not (self.cfg.shuffling.train
                 and phase == 'train'
                 ):
            last_position = torch.cat([init_position, position_after_stop])[:-1]
            position_change = filled_position - last_position
            fee = fee_ratio_dict.limit * abs(position_change)
            fee[is_market] = fee_ratio_dict.market * abs(position_change[is_market])
        net_pnl = pnl - fee
        if self.cfg.optimize_sharpe:
            sharpe = (net_pnl.mean() / net_pnl.std())
            loss = -sharpe
        else:            
            loss = -(
                torch.clamp_max(net_pnl, 0) * (1 + (punish_ratio := abs(self.cfg.position_punishment.in_use * position_after_stop ** self.cfg.position_punishment.exponent))) + 
            torch.clamp_min(net_pnl, 0) * (1 - punish_ratio / 2)
            ).mean()
        if directional_balance is None:
            directional_balance = self.cfg.directional_balance
        if directional_balance:
            loss = loss * (1 - advised_position.mean().abs())
        result = (loss, pnl, fee, filled_position, filled_final_filled_return, position_after_stop, is_market, is_filled, is_stop_loss)
        for arr in result:
            if torch.isnan(arr).any():
                print(f'{torch.isnan(arr).int().sum() = }\n{arr = }')
        return result
