import torch
import torch.nn as nn
import torch.nn.functional as F
from models.gru_pfg.gru_pfg import GRU_PFG
from core.predictor_config import PredictorConfig

class Model(nn.Module):
    def __init__(self, cfg: PredictorConfig, input_size=24):
        super().__init__()
        self.cfg = cfg
        hidden_size = cfg.hidden_size * cfg.n_codes
        self.backbone = GRU_PFG(
            input_size=cfg.input_size or input_size, 
            hidden_size=hidden_size, 
            num_layers=cfg.num_rnn_layers,
            )
        self.activation = nn.Tanh()
        # self.activation = nn.Identity()
        self.fc = nn.Linear(hidden_size, cfg.output_size)

    
    def forward(self, X: torch.Tensor, onehot=None) -> torch.Tensor:
        X = self.backbone(X)
        X = self.activation(X)
        X = self.fc(X)
        return X
    

if __name__ == '__main__':
    from direct_trading import pred_cfg
    # 使用示例
    m = 10  # 股票数量
    n = 24  # 因子数量
    t = 42  # 时间长度
    X = torch.randn(m, t, n)  # 输入数据
    model = Model(pred_cfg)
    last = model(X)
    print("Shape of last:", last.shape)  # 应该是 [m, 64]    