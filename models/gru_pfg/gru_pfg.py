import torch
import torch.nn as nn
import torch.nn.functional as F

class GRU_PFG(nn.Module):
    def __init__(self, input_size=6, hidden_size=64, num_layers=2):
        super(GRU_PFG, self).__init__()

        
        # GRU 网络
        self.gru = nn.GRU(input_size=input_size, hidden_size=hidden_size, num_layers=num_layers, batch_first=True)
        # self.projector = nn.Linear(hidden_size, hidden_size)  # 可选：用于进一步处理 GRU 输出
        self.Wa = nn.Parameter(torch.randn(1))
        self.Wb = nn.Parameter(torch.randn(1))        
        self.final_extractor = FinalFeatureExtractor(hidden_size)
        self.projector = nn.Identity()

    def forward(self, X: torch.Tensor) -> torch.Tensor:
        # 使用 GRU 网络处理 Alpha360 因子数据
        X = self.process_data(X)
        
        # 计算 X− 和 X|
        X_minus, X_pipe = self.compute_X_minus_and_X_pipe(X)
        
        # 计算 R_xy 矩阵
        R1 = self.compute_R_xy_optimized(X_minus, X_pipe)
        
        # 加权 X 得到初步关系提取特征
        weighted_X = torch.mm(R1, X)
        
        # 计算 X_hid
        X_hid, R2 = self.compute_X_hid(X, X_minus, X_pipe, weighted_X)
        
        # 初始化最终特征提取器并获取 F_last
        
        F_last = self.final_extractor(X, R1, X_hid, R2)
        
        return F_last

    def process_data(self, x):
        # GRU 处理
        out, _ = self.gru(x)

        # 获取最后一个时间步的输出
        out = out[:, -1, :]

        # 通过全连接层进一步处理
        out = self.projector(out)

        return out

    def compute_X_minus_and_X_pipe(self, X):
        # 沿着每一行应用 softmax，得到 X− (每个股票内部不同特征的重要性)
        X_minus = F.softmax(X, dim=1)

        # 沿着每一列应用 softmax，得到 X| (同一特征在不同股票间的相对重要性)
        X_pipe = F.softmax(X, dim=0)

        return X_minus, X_pipe

    def compute_pearson_corr_coefficient(self, x, y):
        """计算两个向量 x 和 y 之间的 Pearson 相关系数"""
        x_centered = x - x.mean()
        y_centered = y - y.mean()
        cov = torch.dot(x_centered, y_centered) / (len(x) - 1)
        std_x = torch.std(x, unbiased=True)
        std_y = torch.std(y, unbiased=True)
        if std_x == 0 or std_y == 0:
            return 0
        else:
            return cov / (std_x * std_y)

    def compute_R_xy(self, X_minus, X_pipe):
        """计算 R_xy 矩阵"""
        num_stocks = X_minus.shape[0]
        R_xy = torch.zeros((num_stocks, num_stocks))

        for i in range(num_stocks):
            for j in range(num_stocks):
                F_x = X_minus[i, :]
                F_y = X_pipe[j, :]
                R_xy[i, j] = self.compute_pearson_corr_coefficient(F_x, F_y)

        return R_xy

    def compute_R_xy_optimized(self, X_minus, X_pipe):
        """使用矩阵运算优化计算 R_xy 矩阵"""
        X_minus_centered = X_minus - X_minus.mean(dim=1, keepdim=True)
        X_pipe_centered = X_pipe - X_pipe.mean(dim=0, keepdim=True)

        cov_matrix = torch.mm(X_minus_centered, X_pipe_centered.T) / (X_minus.shape[1] - 1)
        
        std_X_minus = torch.std(X_minus, dim=1, unbiased=True)
        std_X_pipe = torch.std(X_pipe, dim=1, unbiased=True)

        D_minus = torch.diag(1 / std_X_minus)
        D_pipe = torch.diag(1 / std_X_pipe)

        corr_matrix = torch.mm(D_minus, torch.mm(cov_matrix, D_pipe))

        return corr_matrix

    def compute_X_hid(self, X, X_minus, X_pipe, weighted_X):
        """计算 X_hid"""

        X_hid = X - self.Wa * X_minus - self.Wb * X_pipe

        R2 = self.compute_R_xy_optimized(X_minus, X_pipe)

        weighted_X_hid = torch.mm(R2, X_hid)

        return weighted_X_hid, R2

class FinalFeatureExtractor(nn.Module):
    def __init__(self, hidden_size=64):
        super(FinalFeatureExtractor, self).__init__()
        # 定义可学习参数
        self.Wc = nn.Parameter(torch.randn(hidden_size, hidden_size))
        self.Wd = nn.Parameter(torch.randn(hidden_size, hidden_size))
        self.We = nn.Parameter(torch.randn(hidden_size, hidden_size))
        self.Wf = nn.Parameter(torch.randn(hidden_size, hidden_size))


    def forward(self, X, R1, X_hid, R2):
        term1 = torch.mm(X, self.Wc)
        term2 = torch.mm(torch.mm(R1, X), self.Wd)
        term3 = torch.mm(X_hid, self.We)
        term4 = torch.mm(torch.mm(R2, X_hid), self.Wf)

        F_last = term1 + term2 + term3 + term4
        
        return F_last


if __name__ == '__main__':
    # 使用示例
    m = 10  # 股票数量
    n = 6  # 因子数量
    t = 60  # 时间长度
    X = torch.randn(m, t, n)  # 输入数据
    gru_pfg = GRU_PFG()
    F_last = gru_pfg(X)
    print("Shape of F_last:", F_last.shape)  # 应该是 [m, 64]
