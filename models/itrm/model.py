import torch
import torch.nn as nn
import torch.nn.functional as F
from layers.Transformer_EncDec import Encoder, EncoderLayer
from layers.SelfAttention_Family import FullAttention, AttentionLayer
from layers.Embed import DataEmbedding_inverted
import numpy as np




class Model(nn.Module):
    """
    Paper link: https://arxiv.org/abs/2310.06625
    """

    def __init__(self, cfg):
        if __name__ == '__main__':
            from core.predictor_config import PredictorConfig
            cfg: PredictorConfig = cfg
        super(Model, self).__init__()
        cfg.backbone_with_fc = False
        self.seq_len = cfg.seq_len
        self.pred_len = cfg.pred_len
        self.output_attention = cfg.output_attention
        # Embedding
        self.enc_embedding = DataEmbedding_inverted(cfg.seq_len, cfg.d_model, cfg.embed, cfg.freq,
                                                    cfg.dropout_rate)
        # self.class_strategy = cfg.class_strategy
        # Encoder-only architecture
        self.encoder = Encoder(
            [
                EncoderLayer(
                    AttentionLayer(
                        FullAttention(False, cfg.factor, attention_dropout=cfg.dropout_rate,
                                      output_attention=cfg.output_attention), cfg.d_model, cfg.n_heads),
                    cfg.d_model,
                    cfg.d_ff,
                    dropout=cfg.dropout_rate,
                    activation=cfg.activation
                ) for l in range(cfg.e_layers)
            ],
            norm_layer=torch.nn.LayerNorm(cfg.d_model)
        )
        self.projection = nn.Linear(cfg.d_model, cfg.pred_len, bias=True)        
        print('iTransformer ...')


    def forecast(self, x_enc, x_mark_enc, x_dec, x_mark_dec):
        # Normalization from Non-stationary Transformer
        means = x_enc.mean(1, keepdim=True).detach()
        x_enc = x_enc - means
        stdev = torch.sqrt(torch.var(x_enc, dim=1, keepdim=True, unbiased=False) + 1e-5)
        x_enc /= stdev

        _, _, N = x_enc.shape

        # Embedding
        enc_out = self.enc_embedding(x_enc, x_mark_enc)
        enc_out, attns = self.encoder(enc_out, attn_mask=None)

        dec_out = self.projection(enc_out).permute(0, 2, 1)[:, :, :N]
        # De-Normalization from Non-stationary Transformer
        dec_out = dec_out * (stdev[:, 0, :].unsqueeze(1).repeat(1, self.pred_len, 1))
        dec_out = dec_out + (means[:, 0, :].unsqueeze(1).repeat(1, self.pred_len, 1))
        return dec_out


    def forward(self, x_enc, x_mark_enc=None, x_dec=None, x_mark_dec=None, mask=None):
        dec_out = self.forecast(x_enc, x_mark_enc, x_dec, x_mark_dec)
        return dec_out[:, -self.pred_len:, :]  # [B, L, D]


if __name__ == '__main__':
    from core.predictor_config import PredictorConfig
    cfg = PredictorConfig()
    model = Model(cfg)
    x_enc = torch.rand(16, cfg.seq_len, 3)
    out = model(x_enc)
    print(out.shape)