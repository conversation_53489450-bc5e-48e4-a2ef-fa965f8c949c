from collections import deque
import os
from shutil import copyfile
from einops import rearrange
import numpy as np
import polars as pl
import torch
from torch import Tensor, long
from torch.nn import functional as F
from core.backtest import backtest
from core.cst import PositionType, TaskType
from models.lightning_pfl import LightningPFL
from core.predictor_config import PredictorConfig



class LightningMKMPFL(LightningPFL):

    def __init__(
        self,
        cfg: PredictorConfig,
    ):
        super().__init__(cfg)


    def calc_pred_loss(self, pred: Tensor, batch_y: Tensor, phase: str, save_to_step_dict=True) -> Tensor:
        pred = self.calc_pred(pred)
        loss = self.calc_loss(pred, batch_y, phase, save_to_step_dict)
        return pred, loss
    
    
    def calc_pred(self, pred: Tensor) -> Tensor:
        cfg = self.cfg
        n_codes = cfg.n_codes
        margin_coef = cfg.margin_scale
        if len(pred.shape) != 2:
            pred = pred.reshape(pred.shape[0], -1)
        if cfg.limit_margin_per_code:
            margin_ratio_arr = torch.clamp_min(F.sigmoid(pred[:, :n_codes]), margin_coef / n_codes)
            pred = pred[:, n_codes:]
        else:
            margin_ratio_arr = torch.ones(pred.shape[0], n_codes, device=cfg.device, dtype=torch.float32)
        shift_scale = cfg.limit_shift_scale
        pred = F.tanh(pred)
        if cfg.fair_price_shift_spread_for_market_making:
            position = pred[:, :n_codes]
            long_position = position / 2 + 0.5
            short_position = -long_position
            shift = (pred[:, n_codes: 2*n_codes]) * shift_scale
            spread = (pred[:, 2*n_codes: 3*n_codes] / 2 + 0.5) * shift_scale
            long_shift = (shift - spread).clamp(None, 0)
            short_shift = (shift + spread).clamp(0, None)
        else:
            long_position = pred[:, :n_codes] / 2 + 0.5
            long_shift = (pred[:, n_codes:2*n_codes] / 2 - 0.5) * shift_scale
            short_position = pred[:, 2*n_codes:3*n_codes] / 2 - 0.5
            short_shift = (pred[:, 3*n_codes:4*n_codes] / 2 + 0.5) * shift_scale
            

        if cfg.use_softmax:
            long_position = margin_ratio_arr * F.softmax(long_position, dim=1)
            short_position = margin_ratio_arr * F.softmax(short_position, dim=1)
        else:
            if cfg.limit_margin_per_code:
                long_position = margin_ratio_arr * long_position / margin_ratio_arr.sum(dim=1, keepdim=True)
                short_position = margin_ratio_arr * short_position / margin_ratio_arr.sum(dim=1, keepdim=True)
            else:
                long_position = margin_ratio_arr * long_position / long_position.sum(dim=1, keepdim=True)
                short_position = margin_ratio_arr * short_position / short_position.abs().sum(dim=1, keepdim=True)

        pred = torch.stack([long_position, long_shift, short_position, short_shift], dim=0)            
        return pred


    def calc_loss(self, pred: Tensor, batch_y: Tensor, phase: str, save_to_step_dict=True) -> Tensor:
        cfg = self.cfg
        # n_codes = cfg.n_codes        
        if torch.isnan(pred).any():
            print(f"pred is nan in {phase}\n{pred = }")
        asset_return_arr = batch_y[..., 0] # [B, T, S]
        high_to_close_arr = batch_y[..., 1]
        low_to_close_arr = batch_y[..., 2]
        loss, pnl, fee_arr, spread_pnl_arr, spread_position_arr, spread_fee_arr, asset_return, long_position_arr, short_position_arr, position_arr, position_change_arr = self.spread_loss(pred, asset_return_arr, high_to_close_arr, low_to_close_arr, self.position)

        if not cfg.shuffling.train:
            self.position = position_arr[-1:].detach()
        if save_to_step_dict:
            self.step_dict[phase].pred.append(position_arr.detach().cpu().numpy())
            self.step_dict[phase].pnl.append(pnl.detach().cpu().numpy())
            self.step_dict[phase].fee.append(fee_arr.detach().cpu().numpy())
            self.step_dict[phase].filled_position.append(position_arr[:, -1].detach().cpu().numpy())
            self.step_dict[phase].asset_return.append(asset_return.detach().cpu().numpy())
            self.step_dict[phase].spread_pnl.append(spread_pnl_arr.detach().cpu().numpy())
            self.step_dict[phase].spread_fee.append(spread_fee_arr.detach().cpu().numpy())
            self.step_dict[phase].spread_position.append(spread_position_arr.detach().cpu().numpy())            

        return loss
    

    def process_epoch(self, loss: np.ndarray, pred: np.ndarray, actual: np.ndarray, phase: str, epoch_str: str) -> None:
        cfg = self.cfg
        pnl_mean, pnl_std, sign = self.calc_mean_std_sign(phase)
        fee_arr = np.concatenate(self.step_dict[phase].fee, axis=0)
        filled_position_arr = (np.concatenate(self.step_dict[phase].filled_position, axis=0)).T.flatten()
        asset_return_arr = np.concatenate(self.step_dict[phase].asset_return, axis=0).T.flatten()
        spread_pnl_arr = np.concatenate(self.step_dict[phase].spread_pnl, axis=0).T.flatten()
        spread_position_arr = np.concatenate(self.step_dict[phase].spread_position, axis=0).T.flatten()
        spread_fee_arr = np.concatenate(self.step_dict[phase].spread_fee, axis=0).T.flatten()
        position_max = filled_position_arr.max()
        position_min = filled_position_arr.min()
        position_mean = filled_position_arr.mean()
        sharpe = (pnl_mean / pnl_std) if pnl_std != 0 else 0
        print(f'{epoch_str} {phase} {sharpe = :.5f},\t{pnl_mean = :.5f},\t{pnl_std = :.5f}\n{position_max = :.5f},\t{position_min = :.5f},\t{position_mean = :.5f}\n')
        if phase in [
            'val', 
            'test',
            ]:
            interval = cfg.interval_cfg.base
            save_folder = os.path.join(self.ckpt_folder, cfg.datetime_str, f'epoch_{self.current_epoch}')
            if not os.path.exists(save_folder):
                os.makedirs(save_folder)
            if phase == 'val':
                save_path = os.path.join(self.ckpt_folder, cfg.datetime_str, cfg.script_name.split('/')[-1])
                if not os.path.exists(save_path):
                    # copy portfolio.py into save_path
                    copyfile(cfg.script_name, save_path)
            date_dict = self.date_dict
            date_str = f'{date_dict.val[0]}v{date_dict.val[1]}'
            save_str = f'{date_str}_{phase}_crypto{(n_codes := cfg.n_codes)}_{interval}min_epc{self.current_epoch}_{cfg.task_enum.value}'
            mean_pnl_before_fee, fee_mean, end_pnl_before_fee, fee_sum = backtest(
                filled_position_arr, 
                asset_return_arr, 
                fee_ratio_dict=cfg.fee_ratio, save_folder=save_folder, 
                save_str=save_str, 
                n_codes=n_codes, 
                code_list=cfg.code_list, 
                is_market_making=True, spread_pnl=spread_pnl_arr, 
                spread_fee=spread_fee_arr, 
                spread_position=spread_position_arr, 
                )
        else:
            mean_pnl_before_fee = fee_mean = end_pnl_before_fee = fee_sum = 0
        # metric = -loss
        metric = end_pnl_before_fee
        if phase == 'val' or cfg.for_deployment and phase == 'train' and not cfg.execute_phase.val:
            if self.task_enum in [TaskType.Portfolio, TaskType.PortfolioDoubleAdapt, TaskType.MarketMakingPortfolio]:
                self.log('pnl_mean', pnl_mean)
                self.log('sharpe', sharpe)
                self.log('mean_pnl_before_fee', mean_pnl_before_fee)
                self.log('mean_pnl_after_fee', mean_pnl_before_fee - fee_mean)
            # self.log('cum_acc_bias_mean', cum_acc_bias_mean)
            self.log('metric', metric)


    def spread_loss(self, order_action: Tensor, asset_return_arr: Tensor, high_to_close_arr: Tensor, low_to_close_arr: Tensor, init_position: Tensor = 0, phase: str = 'train') -> Tensor:
        cfg = self.cfg
        if isinstance(init_position, int) and init_position == 0:
            init_position = torch.zeros(cfg.n_codes, device=order_action.device, dtype=torch.float32)
        elif len(init_position.shape) != 2:
            init_position = init_position.reshape(1, -1)        
        long_position = order_action[0]
        long_shift = order_action[1]
        short_position = order_action[2]
        short_shift = order_action[3]
        cum_return_arr = asset_return_arr.cumsum(dim=1)
        cum_high_arr = high_to_close_arr + cum_return_arr
        cum_low_arr = low_to_close_arr + cum_return_arr
        
        asset_return = cum_return_arr[:, -1]
        batch_size, pred_len, n_codes = asset_return_arr.shape
        idx_arr = torch.arange(pred_len, device=asset_return_arr.device).unsqueeze(1).unsqueeze(1).repeat(batch_size, 1, n_codes)
        long_shift_arr = long_shift.unsqueeze(1).repeat(1, pred_len, 1)
        long_filled_arr = cum_low_arr.lt(long_shift_arr)
        long_filled = long_filled_arr.any(dim=1, keepdim=True) | (long_shift_arr == 0)
        long_first_idx = long_filled_arr.argmax(dim=1, keepdim=True)

        long_filled_arr[long_first_idx >= idx_arr] = long_filled[long_first_idx >= idx_arr]
        long_position_arr = long_filled_arr * long_position.unsqueeze(1).repeat(1, pred_len, 1)
        
        short_shift_arr = short_shift.unsqueeze(1).repeat(1, pred_len, 1)
        short_filled_arr = cum_high_arr.gt(short_shift_arr)
        short_filled = short_filled_arr.any(dim=1, keepdim=True)  | (short_shift_arr == 0)
        short_first_idx = short_filled_arr.argmax(dim=1, keepdim=True) 
        short_filled_arr[short_first_idx >= idx_arr] = short_filled[short_first_idx >= idx_arr]
        short_position_arr = short_filled_arr * short_position.unsqueeze(1).repeat(1, pred_len, 1)
        fee_ratio = cfg.fee_ratio.limit
        position_arr = long_position_arr + short_position_arr

        if not (cfg.shuffling.train
                 and phase == 'train'
                 ):
            seq_position = position_arr.reshape(-1, n_codes)
            prev_seq_position = torch.cat([init_position, seq_position], dim=0)[:-1]
            position_change = seq_position - prev_seq_position
            position_change_arr = position_change.reshape(batch_size, pred_len, n_codes)
        else:
            init_position_arr = torch.zeros(batch_size, 1, n_codes, device=asset_return_arr.device)
            prev_position_arr = torch.cat([init_position_arr, position_arr], dim=1)[:, :-1]
            position_change_arr = position_arr - prev_position_arr

        fee_arr = fee_ratio * torch.abs(position_change_arr)
        pnl_arr = position_arr * asset_return_arr - fee_arr
        # is_filled = long_filled | short_filled
        # asset_filled_return_arr = 
        both = long_filled & short_filled
        # dual = (long_position + short_position == 0)
        # longer = ~dual & (long_position > short_position.abs())
        # shorter = ~dual & (short_position.abs() > long_position)        
        spread_position_arr = torch.min(long_position - short_position.abs()) * both[:, -1].float()
        spread = (short_shift - long_shift)
        spread_pnl_arr = (spread_position_arr * spread)
        spread_fee_arr = (fee_ratio * 2 * spread_position_arr)
        # remained_ratio = init_position + long_position * long_filled.float() + short_position * short_filled.float()
        # latest_position = position_arr[:, -1]
        sum_pnl_arr = pnl_arr.sum(dim=1)
        sum_fee_arr = fee_arr.sum(dim=1)
        direction_pnl_arr = sum_pnl_arr - spread_pnl_arr
        if cfg.with_directional_profit:
            loss = -sum_pnl_arr.mean()
        else:
            loss = -(spread_pnl_arr + torch.clamp(direction_pnl_arr, min=-torch.inf, max=0) - fee_arr).sum(dim=1).mean()
        
        return loss, sum_pnl_arr.sum(dim=1), sum_fee_arr, spread_pnl_arr, spread_position_arr, spread_fee_arr, asset_return, long_position_arr, short_position_arr, position_arr, position_change_arr

    
        neither = ~(long_filled | short_filled)
        both = long_filled & short_filled
        only_long = long_filled & ~short_filled
        only_short = short_filled & ~long_filled
        long_pnl = long_filled.float() * long_position * (asset_return - long_shift)
        short_pnl = short_filled.float() * short_position * (asset_return - short_shift)
        position_pnl = init_position * asset_return

        dual = (long_position + short_position == 0)
        longer = ~dual & (long_position > short_position.abs())
        shorter = ~dual & (short_position.abs() > long_position)
        
        spread_position_arr, _ = torch.min(long_position, short_position.abs()) * both.float()
        spread = (short_shift - long_shift)
        spread_pnl_arr = spread_position_arr * spread
        latest_position = init_position + long_position + short_position
        direction_pnl_arr = latest_position * (asset_return - (longer.float() * long_shift + shorter.float() * short_shift))

        fee_ratio = self.cfg.fee_ratio
        fee_arr = (long_filled.float() * long_position + short_filled.float() * short_position.abs()) * fee_ratio
        sum_pnl_arr = position_pnl + long_pnl + short_pnl - fee_arr
        loss = spread_pnl_arr + torch.clamp(direction_pnl_arr, min=-torch.inf, max=0) - fee_arr
        return -loss.mean(), loss    
    
