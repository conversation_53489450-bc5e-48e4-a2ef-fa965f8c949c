
import torch
import torch.nn as nn


class LSTM(nn.Module):
    def __init__(self, input_size, seq_len, hidden_size, num_layers, output_size, batch_norm=True, batch_first=True, dropout_rate=0.):
        super(LSTM, self).__init__()
        self.seq_len = seq_len
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.rnn = nn.LSTM(input_size, hidden_size, num_layers, batch_first=batch_first)
        self.dropout = nn.Dropout(dropout_rate)
        self.batch_norm = nn.BatchNorm1d(hidden_size) if batch_norm else nn.Identity()
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        out, _ = self.rnn(x)
        out = self.dropout(out[:, -1, :])#.reshape(x.shape[0], -1)
        out = self.batch_norm(out)
        out = self.fc(out)
        return out