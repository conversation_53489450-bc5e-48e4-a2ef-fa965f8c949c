import numpy as np
import torch
from torch import Tensor, tensor
from torch.nn import functional as F
from cst import TaskType
from models.lightning_base import LightningBase
from core.predictor_config import PredictorConfig



class LightningREG(LightningBase):

    def __init__(
        self,
        cfg: PredictorConfig,
    ):
        super().__init__(cfg)


    def calc_pred_loss(self, pred: Tensor, batch_y: Tensor, phase: str = 'train'):
        cfg = self.cfg
        pred = pred.reshape(pred.shape[0], -1)
        # drt_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        mad_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        mse_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        bce_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        cum_mad_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        cum_mse_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        cum_bce_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        if not self.cfg.pred_multi_step:
            pred = pred[..., -1]
        if batch_y.shape != pred.shape:
            batch_y = batch_y.reshape(pred.shape)
        if cfg.use_mad_loss:
            mad_loss = self.mad_loss(pred, batch_y)
        if cfg.use_mse_loss:
            mse_loss = self.reg_loss_fn(pred, batch_y)
        if cfg.use_bce_loss:
            bce_loss = self.bce_loss_fn(pred.flatten(), batch_y.gt(0).flatten().float())
        if cfg.pred_multi_step:
            output_cum = torch.cumsum(pred, dim=1)  # [:, :,1:]
            y_cum = torch.cumsum(batch_y, dim=1)  # [:, :,1:]
            if cfg.use_cum_mad_loss:
                cum_mad_loss = self.mad_loss(output_cum, y_cum)
            if cfg.use_cum_mse_loss:
                cum_mse_loss = self.reg_loss_fn(output_cum, y_cum)
            if cfg.use_cum_bce_loss:
                cum_label = y_cum.gt(0).flatten().float()
                cum_bce_loss = self.bce_loss_fn(output_cum.flatten(), cum_label)

        loss = mad_loss + mse_loss + bce_loss + cum_mad_loss + cum_mse_loss + cum_bce_loss
        self.loss_dict.mse.append(mse_loss.item())
        self.loss_dict.bce.append(bce_loss.item())
        self.loss_dict.cum_mse.append(cum_mse_loss.item())
        self.loss_dict.cum_bce.append(cum_bce_loss.item())
        self.loss_dict.all.append(loss.item())
        return loss, pred
    

    def process_epoch(self, loss: np.ndarray, pred: np.ndarray, actual: np.ndarray, phase: str, epoch_str: str) -> None:
        pred = pred.reshape(-1, pred.shape[-1])
        acc_arr = ((pred * actual) > 0).mean(axis=0)
        acc_dict = {i + 1: round(acc, 4) for i, acc in enumerate(acc_arr)}
        self.metric_dict[phase].acc.append(acc_dict)
        acc_bias_mean = abs(acc_arr - 0.5).mean()
        print(f'{epoch_str} {phase} {acc_dict = }\n{acc_bias_mean = :.5f}')
        cum_acc_arr = ((pred.cumsum(axis=1) * actual.cumsum(axis=1)) > 0).mean(axis=0)
        cum_acc_dict = {i + 1: round(cum_acc, 4) for i, cum_acc in enumerate(cum_acc_arr)}
        self.metric_dict[phase].cum_acc.append(cum_acc_dict)
        cum_acc_bias_mean = abs(cum_acc_arr - 0.5).mean()
        print(f'{epoch_str} {phase} {cum_acc_dict = }\n{cum_acc_bias_mean = :.5f}')
        metric = loss - acc_bias_mean + cum_acc_bias_mean
        print(f'{epoch_str} {phase} loss: {loss:.5f}\n')
        if phase == 'val':
            # self.log('cum_acc_bias_mean', cum_acc_bias_mean)
            self.log('metric', metric)