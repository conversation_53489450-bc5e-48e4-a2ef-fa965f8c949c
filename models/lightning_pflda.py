from calendar import c
from collections import deque, OrderedDict
from copy import deepcopy
import math
import os
from shutil import copyfile
from einops import rearrange
import higher
import numpy as np
import polars as pl
import torch
import torch.nn as nn
from torch.optim import Adam
from torch import Tensor
from torch.nn import functional as F, init
from core.backtest import backtest
from core.cst import PositionType, TaskType
from models.lightning_base import LightningBase
from core.predictor_config import PredictorConfig
from models.lightning_pfl import LightningPFL



def has_rnn(module: nn.Module):
    for module in module.modules():
        if isinstance(module, nn.RNNBase):
            return True
    return False


def cosine(x1, x2, eps=1e-8):
    x1 = x1 / (torch.norm(x1, p=2, dim=-1, keepdim=True) + eps)
    x2 = x2 / (torch.norm(x2, p=2, dim=-1, keepdim=True) + eps)
    return x1 @ x2.transpose(0, 1)


class LabelAdaptHeads(nn.Module):
    def __init__(self, num_head):
        super().__init__()
        self.weight = nn.Parameter(torch.empty(1, num_head))
        self.bias = nn.Parameter(torch.ones(1, num_head) / 8)
        init.uniform_(self.weight, 0.75, 1.25)

    def forward(self, y, inverse=False):
        if inverse:
            return (y.view(-1, 1) - self.bias) / (self.weight + 1e-9)
        else:
            return (self.weight + 1e-9) * y.view(-1, 1) + self.bias
        

class LabelAdapter(nn.Module):
    def __init__(self, cfg: PredictorConfig, x_dim: int, num_head=4, temperature=4, hid_dim=32):
        super().__init__()
        self.cfg = cfg
        self.s = cfg.n_codes if 'pfl' in cfg.task_enum.value else 1
        self.num_head = num_head
        self.x_dim = x_dim
        self.linear = nn.Linear(x_dim, hid_dim, bias=False)
        self.P = nn.Parameter(torch.empty(num_head, hid_dim))
        init.kaiming_uniform_(self.P, a=math.sqrt(5))
        # self.heads = nn.ModuleList([LabelAdaptHead() for _ in range(num_head)])
        self.heads = LabelAdaptHeads(num_head)
        self.temperature = temperature

    def forward(self, x, y, inverse=False):
        x_shape = x.shape
        x = rearrange(x, 'b t (s c) -> (b s) (t c)', b=x_shape[0], t=x_shape[1], s=self.s)
        v = self.linear(x)
        # v = self.linear(x.reshape(len(x), -1))
        y_shape = y.shape
        # y = rearrange(x, 'b t s -> (b s) t')
        gate = cosine(v, self.P)
        gate = torch.softmax(gate / self.temperature, -1)
        # return sum([gate[:, i] * self.heads[i](y, inverse=inverse) for i in range(self.num_head)])
        y_heads = self.heads(y, inverse=inverse)
        return (gate * y_heads).sum(-1).reshape(y_shape)


class FiLM(nn.Module):
    def __init__(self, in_dim):
        super().__init__()
        self.scale = nn.Parameter(torch.empty(in_dim))
        nn.init.uniform_(self.scale, 0.75, 1.25)

    def forward(self, x):
        return x * self.scale


class FeatureAdapter(nn.Module):
    def __init__(self, cfg: PredictorConfig, in_dim: int, num_head=4, temperature=4):
        super().__init__()
        self.cfg = cfg
        self.s = cfg.n_codes if 'pfl' in cfg.task_enum.value else 1
        self.num_head = num_head
        self.P = nn.Parameter(torch.empty(num_head, in_dim))
        init.kaiming_uniform_(self.P, a=math.sqrt(5))
        self.heads = nn.ModuleList([nn.Linear(in_dim, in_dim, bias=True) for _ in range(num_head)])
        self.temperature = temperature

    def forward(self, x):
        shape = x.shape
        x = rearrange(x, 'b t (s c) -> (b s) (t c)', b=shape[0], t=shape[1], s=self.s)
        # s_hat = torch.cat([cosine(x, self.P[i]).unsqueeze(-1) for i in range(self.num_head)], -1)
        s_hat = torch.cat(
            [torch.cosine_similarity(x, self.P[i], dim=-1).unsqueeze(-1) for i in range(self.num_head)], -1,
        )
        # s_hat = cosine(x, self.P)
        s = torch.softmax(s_hat / self.temperature, -1).unsqueeze(-1)
        x = x + sum([s[..., i, :] * self.heads[i](x) for i in range(self.num_head)])
        return rearrange(x, '(b s) (t c) -> b t (s c)', b=shape[0], t=shape[1], s=self.s)


class LightningPFLDA(LightningPFL):
    def __init__(
        self,
        cfg: PredictorConfig,
    ):
        super().__init__(cfg)

    def set_after_optimizers(self):
        super().set_after_optimizers()
        cfg = self.cfg


    def set_before_optimizers(self, cfg: PredictorConfig = None):
        super().set_before_optimizers(cfg)
        self.adapt_cfg = cfg.meta_adapt
        self.offline_lr_dict = cfg.meta_adapt.offline_lr_dict
        self.online_lr_dict = cfg.meta_adapt.online_lr_dict        
        # input_dim = cfg.input_dim
        self.best_ckpt = None
        self.old_fmodel = None
        self.old_diffopt = None
        self.old_batch = None
        self.has_rnn = has_rnn(self.model)
        # self.has_rnn = True
        

    def common_step(self, batch: tuple[tuple, tuple], phase: str = 'train') -> Tensor:
        support, query = batch
        adapt_cfg = self.adapt_cfg
        # self.mapper_opt.zero_grad()
        support_x, support_y, support_z = self.unpack(support)
        query_x, query_y, query_z = self.unpack(query)

        if adapt_cfg.double_adapt:
            """ Incremental data adaptation & Model adaptation """
            is_osaka_eval = (adapt_cfg.is_osaka and phase != 'train')
            okasa_gamma = adapt_cfg.okasa_gamma
            # if is_osaka_eval:
            #     track_higher_grads = False
            # else:
            #     track_higher_grads = not adapt_cfg.first_order

            if is_osaka_eval:
                loss = torch.tensor(0.0, device=self.device)
                with higher.innerloop_ctx(
                    self.model,
                    # self.inner_opt,
                    self.optimizer,
                    copy_initial_weights=adapt_cfg.inner_copy_initial_weights,
                    track_higher_grads=False,
                    override={'lr': [self.offline_lr_dict.inner]}
                ) as (fmodel, diffopt):                
                
                    if self.old_fmodel is None:
                    # with higher.innerloop_ctx(
                    #     self,
                    #     # self.inner_opt,
                    #     self.optimizer,
                    #     copy_initial_weights=adapt_cfg.inner_copy_initial_weights,
                    #     track_higher_grads=False,
                    #     override={'lr': [self.offline_lr_dict.inner]}
                    # ) as (fmodel, diffopt):                    
                        self.old_fmodel = fmodel
                        self.old_diffopt = diffopt

                        query_output, query_x_adapted = self.get_output_adapted(query_x, model=self.old_fmodel, transform=adapt_cfg.transform_x)
                        # pred, shift_ratio, stop_loss_ratio = self.calc_pred(query_output, query_y, phase)
                        pred, _ = self.calc_pred_loss(query_output, query_y, phase, save_to_step_dict=True)

                    else:
                        x = torch.cat([support_x, query_x], 0)
                        y = torch.cat([support_y, query_y], 0)
                        # x = support_x
                        # y = support_y
                        with torch.backends.cudnn.flags(enabled=adapt_cfg.first_order or not self.has_rnn):
                  
                            output_old, _ = self.get_output_adapted(x, model=self.old_fmodel, transform=adapt_cfg.transform_x)
                            pred_old, inner_loss_old = self.calc_pred_loss(output_old, y, phase, save_to_step_dict=False)

                        # with higher.innerloop_ctx(
                        #     self, 
                        #     self.optimizer, 
                        #     copy_initial_weights=adapt_cfg.inner_copy_initial_weights, 
                        #     track_higher_grads=False,
                        #     override={'lr': [self.offline_lr_dict.inner]}
                        # ) as (fmodel, diffopt):

                            output, _ = self.get_output_adapted(x, model=fmodel, transform=adapt_cfg.transform_x)
                        

                        _, inner_loss_new = self.calc_pred_loss(output, y, phase, save_to_step_dict=False)
                        
                        diffopt.step(inner_loss_new, first_order=True)


                        query_output, query_x_adapted = self.get_output_adapted(query_x, model=self.old_fmodel, transform=adapt_cfg.transform_x)
                        # pred, shift_ratio, stop_loss_ratio = self.calc_pred(query_output, query_y, phase)
                        pred, _ = self.calc_pred_loss(query_output, query_y, phase, save_to_step_dict=True)

                        if inner_loss_old.detach() - inner_loss_new.detach() < okasa_gamma:           
                            self.old_diffopt.step(inner_loss_old)
                            self.old_fmodel.update_params([p.detach().requires_grad_() for p in self.old_fmodel.fast_params])
                        else:
                            loss += self.consolidate()
                            with higher.innerloop_ctx(
                                self.model, 
                                self.optimizer, 
                                copy_initial_weights=False, 
                                track_higher_grads=False,
                                override={'lr': [self.offline_lr_dict.inner]}
                            ) as (fmodel, diffopt):
                                _, inner_loss = self.calc_pred_loss(output, y, phase, save_to_step_dict=False)
                                diffopt.step(inner_loss, first_order=True)
                                self.old_fmodel = fmodel
                                self.old_diffopt = diffopt
                            
                if self.old_batch is None:
                    loss_count = 0
                else:
                    loss_count = len(self.old_batch[1][0])

                self.old_batch = ((support_x, support_y, support_z), (query_x, query_y, query_z))

            else:
                # inner loop
                with higher.innerloop_ctx(
                    self.model,
                    # self.inner_opt,
                    self.optimizer,
                    copy_initial_weights=adapt_cfg.inner_copy_initial_weights,
                    track_higher_grads=not adapt_cfg.first_order,
                    override={'lr': [self.offline_lr_dict.inner]}
                ) as (fmodel, diffopt):
                    with torch.backends.cudnn.flags(enabled=adapt_cfg.first_order or not self.has_rnn):
                        support_output, _ = self.get_output_adapted(support_x, model=fmodel, transform=adapt_cfg.transform_x)

                if adapt_cfg.transform_y:
                    support_y_adapted = self.mapper_y(support_x, support_y, inverse=False)
                else:
                    support_y_adapted = support_y

                support_pred, inner_loss  = self.calc_pred_loss(support_output, support_y_adapted, phase, save_to_step_dict=False)
                diffopt.step(inner_loss)

                """ Online inference """            
                query_output, query_x_adapted = self.get_output_adapted(query_x, model=fmodel, transform=adapt_cfg.transform_x)
                pred, shift_ratio, stop_loss_ratio = self.calc_pred(query_output, query_y, phase)
                if adapt_cfg.transform_y:
                    pred = self.model.mapper_y(query_x_adapted, pred, inverse=True)
                    pred_clamped = pred / (pred.abs().sum(dim=-1, keepdim=True) + 1e-9)
                    pred = torch.where(pred.abs().sum(dim=-1, keepdim=True) < 1, pred, pred_clamped)

                """ Optimization of meta-learners """
                loss = self.calc_loss(pred, query_y, phase, shift_ratio, stop_loss_ratio)
                if adapt_cfg.transform_y:
                    if not adapt_cfg.first_order:
                        support_y_adapted = self.model.mapper_y(support_x, support_y, inverse=False)
                    loss_y = F.mse_loss(support_y_adapted, support_y)
                    if adapt_cfg.first_order:
                        """ Please refer to Appendix C in https://arxiv.org/pdf/2306.09862.pdf """
                        with torch.no_grad():
                            query_output2, _ = self.get_output_adapted(query_x_adapted, model=None, transform=False)
                            query_pred2, shift_ratio2, stop_loss_ratio2 = self.calc_pred(query_output2, query_y, 'predict')
                            query_pred2 = self.model.mapper_y(query_x, query_pred2, inverse=True).detach()
                            # if mask_y is not None:
                            #     pred2 = pred2[mask_y[:meta_end]]
                            loss_old = self.calc_loss(query_pred2, query_y, phase, shift_ratio2, stop_loss_ratio2, False)
                        loss_y = (loss_old.item() - loss.item()) / adapt_cfg.sigma * loss_y + loss_y * adapt_cfg.reg
                    else:
                        loss_y = loss_y * adapt_cfg.reg
                    loss = loss + loss_y
                    # loss_y.backward()
                # if adapt_cfg.transform_x or adapt_cfg.transform_y:
                #     self.mapper_opt.step()
                # loss.backward()
                # self.inner.opt.step()
                loss_count = len(query_x)

        else:
            support_output, _ = self.get_output(support_x, support_z)
            query_output, _ = self.get_output(query_x, query_z)
            _, loss = self.calc_pred_loss(support_output, support_y, phase, save_to_step_dict=False)
            pred, _ = self.calc_pred_loss(query_output, query_y, 'predict', False)
            loss_count = len(support_x)

        pred_arr = pred.cpu().detach().numpy()

        self.step_dict[phase].pred.append(pred_arr)
        self.step_dict[phase].loss.append(loss.item())
        self.step_dict[phase].actual.append(query_y.cpu().detach().numpy())
        if phase == 'train' and self.cfg.episodic_backward:
            self.episodic_loss += loss * loss_count
            self.loss_count += loss_count
        
        return loss
    

    def consolidate(self):
        support_x, support_y, support_z = self.old_batch[0]
        query_x, query_y, query_z = self.old_batch[1]
        adapt_cfg = self.adapt_cfg
        with higher.innerloop_ctx(
            self.model, self.optimizer, 
            copy_initial_weights=False, 
            track_higher_grads=False,
            override={'lr': [self.offline_lr_dict.inner]}
        ) as (fmodel, diffopt):
            with torch.backends.cudnn.flags(enabled=adapt_cfg.first_order or not self.has_rnn):                
                support_output, _ = self.get_output_adapted(support_x, model=fmodel, transform=adapt_cfg.transform_x)
                support_loss, _ = self.calc_pred_loss(support_output, support_y, 'train', save_to_step_dict=False)
                diffopt.step(support_loss)
        query_output, query_x_adapted = self.get_output_adapted(query_x, model=fmodel, transform=adapt_cfg.transform_x)
        query_loss, _ = self.calc_pred_loss(query_output, query_y, 'train', save_to_step_dict=False)
        smoothing_weight = 1 - torch.exp(-adapt_cfg.okasa_lamda * query_loss.detach())
        return query_loss * smoothing_weight
    

    def get_output_adapted(self, batch_x: Tensor, batch_z: Tensor = None, model: nn.Module = None, transform: bool = False) -> Tensor:
        if transform:
            batch_x = self.model.mapper_x(batch_x)
        if model is None:
            output = self.get_output(batch_x, batch_z)
        else:
            output = model(batch_x)
        return output, batch_x


    def predict(self, x: torch.Tensor | np.ndarray) -> np.ndarray:
        cfg = self.cfg
        adapt_cfg = self.adapt_cfg
        if isinstance(x, np.ndarray):
            x = torch.from_numpy(x).to(cfg.device)
        if len(x.shape) == 4:
            x = rearrange(x, 'b t s c -> b t (s c)')
        output, x_adapted = self.get_output_adapted(x, model=None, transform=adapt_cfg.transform_x)
        pred ,_ ,_ = self.calc_pred(output, None, 'predict')
        if adapt_cfg.transform_y:
            pred = self.mapper_y(x_adapted, pred, inverse=True)
            pred_clamped = pred / (pred.abs().sum(dim=-1, keepdim=True) + 1e-9)
            pred = torch.where(pred.abs().sum(dim=-1, keepdim=True) < 1, pred, pred_clamped)        
        
        return pred.cpu().detach().numpy()


    def set_optimizers(self):
        cfg = self.cfg
        optimizer_enum = cfg.optimizer_enum

        lr_dict = self.offline_lr_dict
        self.lr = lr_dict.get('outer')
        self.lr_inner = lr_dict.get('inner')
        self.lr_data = lr_data = lr_dict.data
        self.lr_feature = lr_feature = lr_dict.get('feature', lr_data)
        self.lr_label = lr_label = lr_dict.get('label', lr_data)

        named_params = self.get_named_params()
        mapper_x_params = self.model.mapper_x.parameters()
        mapper_y_params = self.model.mapper_y.parameters()
        # mapper_params = mapper_x_params + mapper_y_params
        remainder_params = [p for name, p in named_params if 'mapper' not in name]
        param_dict_list = [
            dict(params=mapper_x_params, lr=lr_feature),
            dict(params=mapper_y_params, lr=lr_label),
            dict(params=remainder_params, lr=self.lr, weight_decay = cfg.weight_decay),
        ]
        # # self.inner_opt = Adam(param_dict_list)
        self.optimizer = optimizer_enum.value(param_dict_list)

        # self.optimizer = optimizer_enum.value(self.get_model_params())

      
        
        # self.optimizer.add_param_group(dict(params=self.model.mapper_y.parameters(), lr=lr_label))
        # self.optimizer.add_param_group(dict(params=self.model.mapper_x.parameters(), lr=lr_feature))

    # def configure_optimizers(self):
    #     return [self.optimizer]
    


    def override_online_lr(self):
        lr_dict = self.online_lr_dict
        if self.adapt_cfg.override_online_lr:
            self.lr_offline = self.lr 
            if self.adapt_cfg.double_adapt:
                lr_outer = lr_dict.get('outer')
                if lr_outer is not None:
                    self.lr = lr_outer

                lr_inner = lr_dict.get('inner')
                if lr_inner is not None:
                    self.optimizer.param_groups[2]['lr'] = lr_inner

                lr_data = lr_dict.data
                lr_feature = lr_dict.get('feature', lr_data)
                lr_label = lr_dict.get('label', lr_data)

                self.optimizer.param_groups[0]['lr'] = lr_feature
                self.optimizer.param_groups[1]['lr'] = lr_label

            else:
                lr_outer = lr_dict.get('outer')
                if lr_outer is not None:
                    self.lr = lr_outer
                    self.optimizer.param_groups[0]['lr'] = lr_outer


    # def on_save_checkpoint(self, checkpoint: dict):
    #     self.best_ckpt = deepcopy(self) # bug in saving ckpt


    def validation_step(self, batch, batch_idx):
        torch.set_grad_enabled(True)
        return super().validation_step(batch, batch_idx)
    
    def test_step(self, batch, batch_idx):
        torch.set_grad_enabled(True)
        return super().test_step(batch, batch_idx)
    

    def state_dict(self, destination=None, prefix='', keep_vars=False):
        # 调用父类的 state_dict
        state_dict = super().state_dict(destination=destination, prefix=prefix, keep_vars=keep_vars)

        # 删除不想保存的子模型的参数
        keys_to_remove = []
        for key in state_dict.keys():
            if 'old_' in key:
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del state_dict[key]

        return state_dict


    def on_validation_epoch_start(self):        
        self.ckpt = deepcopy(self.ckpt_dict())
        self.override_online_lr()
        super().on_validation_epoch_start()


    def on_validation_epoch_end(self):        
        self.load_ckpt_dict(self.ckpt)
        super().on_validation_epoch_end()


    def on_test_epoch_start(self):
        # self.ckpt = deepcopy(self.ckpt_dict())
        self.override_online_lr()
        super().on_test_epoch_start()


    # def on_test_epoch_end(self):
    #     self.load_ckpt_dict(self.ckpt)
    #     super().on_test_epoch_end()


    def on_test_model_eval(self):
        self.override_online_lr()
        super().on_test_model_eval()


    def on_predict_model_eval(self):
        self.override_online_lr()
        super().on_predict_model_eval()


    def ckpt_dict(self):
        # no optimizer when load from file ckpt
        return OrderedDict(
            model=self.model.state_dict(),
            optimizer=self.optimizer.state_dict(),
            # mapper_opt=self.mapper_opt.state_dict(),
        )


    def load_ckpt_dict(self, ckpt: OrderedDict):
        self.model.load_state_dict(ckpt['model'])
        self.optimizer.load_state_dict(ckpt['optimizer'])
        # self.mapper_opt.load_state_dict(ckpt['mapper_opt'])