import numpy as np
from sklearn.metrics import classification_report, confusion_matrix
import torch
from torch import Tensor, tensor
from torch.nn import functional as F
from core.cst import TaskType
from models.lightning_base import LightningBase
from core.predictor_config import PredictorConfig
from models.lightning_drt import LightningDRT



class LightningCLF(LightningDRT):

    def __init__(
        self,
        cfg: PredictorConfig,
    ):
        super().__init__(cfg)


    def calc_pred_loss(self, pred: Tensor, batch_y: Tensor, phase: str = 'train'):
        # if len(pred.shape) == 3:
        #     pred = pred.reshape(pred.shape[0], -1)
        # side = torch.argmax(pred, dim=1, keepdim=True)
        score = torch.sigmoid(pred).flatten()
        side_flatten = score * 2 - 1
        shift_ratio = torch.zeros_like(side_flatten)
        stop_loss_ratio = torch.ones_like(side_flatten)
        take_profit_ratio = torch.ones_like(side_flatten)
        # if len(batch_y.shape) != 2:
        #     batch_y = batch_y.reshape(batch_y.shape[0], -1)
        
        _ = self.calc_loss(side_flatten, batch_y, phase, shift_ratio, stop_loss_ratio, take_profit_ratio)
        
        label = (batch_y[:, -1, 0] > 0).float()
        loss = self.bce_loss_fn(score, label)
        return side_flatten, loss
    

    # def task_process(self, loss: np.ndarray, pred: np.ndarray, actual: np.ndarray, phase: str, epoch_str: str) -> None:
    #     win_rate = calc_win_rate(actual, pred, self.cfg.num_classes)
    #     report = classification_report(actual, pred) 
    #     print(f'{epoch_str} {phase} classification report:\n{report}\n{confusion_matrix(actual, pred)}\n\n{win_rate = :.5f}\n')
    #     metric = -win_rate
    #     if phase == 'val':
    #         # self.log('cum_acc_bias_mean', cum_acc_bias_mean)
    #         self.log('metric', metric)

        

def calc_win_rate(actual, pred, num_classes=3):
    short = 0
    long = num_classes - 1
    short_win = sum((pred == short) & (actual == short))
    long_win = sum((pred == long) & (actual == long))
    long_loss = sum((pred == long) & (actual == short))
    short_loss = sum((pred == short) & (actual == long))
    win_count = short_win + long_win
    trade_count = short_win + long_win + long_loss + short_loss
    win_rate = win_count / trade_count
    return win_rate