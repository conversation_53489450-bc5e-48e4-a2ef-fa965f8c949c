import torch
import torch.nn as nn
from core.predictor_config import PredictorConfig
from models.min_gru.min_gru import minGRU


class Model(nn.Module):
    def __init__(self, cfg: PredictorConfig):
        super(Model, self).__init__()
        self.cfg = cfg
        self.min_gru = minGRU(cfg.input_size, 16)
            
        self.backbone = nn.Sequential(
            self.min_gru,
            # nn.Tanh()
            nn.Mish()
        )
        self.fc = nn.Linear(cfg.seq_len * cfg.input_size, cfg.output_size)


    def forward(self, x, onehot=None):
        x = self.backbone(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)
        return x
        