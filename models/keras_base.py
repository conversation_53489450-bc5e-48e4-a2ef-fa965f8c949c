from itertools import chain
from einops import rearrange
import numpy as np
from keras import Model
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix, recall_score
import torch
from torch import Tensor, exp, long, tensor
from tqdm import tqdm
from core.cst import TaskType, TaskPhase
from core.backtest import backtest
from models.noise import GaussianNoise
from core.predictor_config import PredictorConfig
import pytorch_lightning as pl
import torch.nn as nn
import torch.nn.functional as F
from core.dot_dict import DotDict as dd
from utils.zigzag import max_drawdown


class KerasBase(Model):
    def __init__(
        self,
        cfg: PredictorConfig,
        is_meta: bool = False,
    ):
        super().__init__()
        self.cfg = cfg
        self.is_regression = cfg.is_regression
        self.task_enum =cfg.task_enum
        self.num_epochs = cfg.num_epochs
        self.batch_size = cfg.batch_size
        self.softmax = nn.Softmax(dim=1)
        self.clf_loss_fn = nn.CrossEntropyLoss(weight=cfg.loss_weights)
        self.bce_loss_fn = nn.BCELoss()
        self.reg_loss_fn = nn.MSELoss()
        self.batch_norm = nn.BatchNorm1d(cfg.pred_len)
        self.gaussian_noise = GaussianNoise(cfg.noise_std)
        self.tanh = nn.Tanh()
        
        if not cfg.is_regression:
            self.clf_layer = nn.Linear(cfg.pred_len, cfg.num_classes)
        self.optimizer_enum = cfg.optimizer_enum
        self.lr = cfg.learning_rate
        self.weight_decay = cfg.weight_decay
        # self.eps = cfg.eps
        # self.momentum = cfg.momentum
        self.is_meta = is_meta
        self.meta = cfg.get_meta_model()
        self.meta_thr = cfg.meta_thr
        self.best_thr = np.zeros(cfg.num_classes)

        self.task_phase = TaskPhase.TRAINING
        self.loss_dict = dd(
            drt=[],
            mle=[],
            mse=[],
            bce=[],            
            cum_mse=[],
            cum_bce=[],
            kl=[],
            dsm=[],
            all=[]
        )
        self.step_dict = dd()
        self.metric_dict = dd()
        for phase in ['train', 'val', 'test']:
            self.step_dict[phase] = dd(
                loss=[],
                pred=[],
                actual=[],
                meta_score=[],
                pnl=[],
                position=[],
            )
            self.metric_dict[phase] = dd(
                acc=[],
                cum_acc=[],
                cum_pnl=[],
                sharpe=[],
            )

        # LR decay here below        
        self.optimizer = None
        self.no_improvement_count = 0
        self.best_epoch_train_loss = 0
        self.cur_decay_index = 0
        self.LR_DECAY_CTABL = [0.005, 0.001, 0.0005, 0.0001, 0.00008, 0.00001]


    def train_meta(self, feature: Tensor | np.ndarray, label: Tensor | np.ndarray, reg: Tensor | np.ndarray = None):
        score = self.get_clf_score(feature, reg)
        if isinstance(feature, Tensor):
            feature = feature.cpu().detach().numpy()
        if isinstance(label, Tensor):
            label = label.cpu().detach().numpy()
        meta_x, _ = self.get_meta_x(score, feature)
        meta_label = abs(label - 1)
        self.meta.fit(meta_x, meta_label)


    def get_meta_x(self, score: np.ndarray, x: np.ndarray) -> np.ndarray:
        highest_recall_argmax = np.argmax(score - self.best_thr, axis=1)
        highest_recall_onehot = np.eye(self.cfg.num_classes)[highest_recall_argmax]
        meta_x = np.concatenate([x.reshape(x.shape[0], -1), highest_recall_onehot], axis=1)
        return meta_x, highest_recall_argmax
    

    def get_clf_score(self, feature: Tensor | np.ndarray, reg: Tensor | np.ndarray = None) -> np.ndarray:
        device = self.clf_layer.weight.device
        if reg is None:
            if isinstance(feature, np.ndarray):
                feature = tensor(feature, device=self.model.weight.device, dtype=torch.float32)
            reg = self.predict(feature)
        elif isinstance(reg, np.ndarray):
            reg = tensor(reg, dtype=torch.float32)
        reg = reg.to(device).reshape(reg.shape[0], -1)
        logit = self.clf_layer(reg)
        score = self.softmax(logit).cpu().detach().numpy()
        return score


    def make_meta_clf(self, feature: Tensor | np.ndarray, reg: Tensor | np.ndarray = None) -> np.ndarray:
        score = self.get_clf_score(feature, reg)
        if isinstance(feature, Tensor):
            feature = feature.cpu().detach().numpy()
        meta_x, highest_recall_clf = self.get_meta_x(score, feature)
        # meta_pred = self.meta.predict(meta_x)
        meta_score = self.meta.predict_proba(meta_x)
        meta_score[:, 1] -= self.meta_thr
        meta_pred = np.argmax(meta_score, axis=1)
        result = meta_pred * (highest_recall_clf - 1) + 1
        return result, meta_score
    

    def get_highest_recall_threshold(self, feature: Tensor | np.ndarray, label: Tensor | np.ndarray, reg: Tensor | np.ndarray = None):
        score = self.get_clf_score(feature, reg)

        rough_dict = dd()
        rough_rg = np.arange(0., 1., 0.05)
        
        def get_thr_arr(thr_list_str):
            thr_arr = np.array([float(x) for x in thr_list_str[1:-1].split(',')])
            return thr_arr - thr_arr.min()
        
        num_classes = self.cfg.num_classes
        func = max        
        # func = min
        if num_classes == 2:
            sign = -1
            labels = [0, 1]
            for thr0 in tqdm(rough_rg):
                for thr1 in rough_rg:
                    if thr0 == thr1 > 0:
                        continue
                    thr_list = [thr0, thr1]
                    thr = np.array(thr_list)
                    thr = thr - thr.min()
                    clf_argmax = np.argmax(score - thr, axis=1)
                    recall = recall_score(label, clf_argmax, labels=labels, average='weighted')
                    rough_dict[str(thr_list)] = recall
            rough_thr_list_str = func(rough_dict, key=lambda k: 10000 * rough_dict[k] + sign * get_thr_arr(k).sum())
            rough_recall = rough_dict[rough_thr_list_str]
            rough_thr = get_thr_arr(rough_thr_list_str)
            if not self.cfg.use_presice_threshold:
                best_recall = rough_recall
                self.best_thr = rough_thr
                print(f'{self.best_thr = }\n{best_recall = :.4f}\n{labels = }')
                return self.best_thr
            else:
                print(f'{rough_thr = }\n{rough_recall = :.4f}\n{labels = }')

            precise_dict = dd()
            precise_rg_list = []
            for v in rough_thr:
                precise_rg_list.append(np.arange(v - .05, v + .05, 0.01))
            
            for thr0 in tqdm(precise_rg_list[0]):
                for thr1 in precise_rg_list[1]:
                    if thr0 == thr1 > 0:
                        continue
                    thr_list = [thr0, thr1]
                    thr = np.array(thr_list)
                    thr = thr - thr.min()
                    clf_argmax = np.argmax(score - thr, axis=1)
                    recall = recall_score(label, clf_argmax, labels=labels, average='weighted')
                    precise_dict[str(thr_list)] = recall
                        
            best_thr_list_str = func(precise_dict, key=lambda k: 10000 * precise_dict[k] + sign * get_thr_arr(k).sum())
            best_recall = precise_dict[best_thr_list_str]
            best_thr = get_thr_arr(best_thr_list_str)

        elif num_classes == 3:
            if func == max:
                slc = slice(None, None, 2)            
                sign = -1
            else:
                slc = slice(1, 2, 1)            
                sign = 1
            labels = [0, 1, 2][slc]
            for thr0 in tqdm(rough_rg):
                for thr1 in rough_rg:
                    for thr2 in rough_rg:
                        if thr0 == thr1 == thr2 > 0:
                            continue
                        thr_list = [thr0, thr1, thr2]
                        thr = np.array(thr_list)
                        thr = thr - thr.min()
                        clf_argmax = np.argmax(score - thr, axis=1)
                        recall = recall_score(label, clf_argmax, labels=labels, average='weighted')
                        rough_dict[str(thr_list)] = recall
            rough_thr_list_str = func(rough_dict, key=lambda k: 10000 * rough_dict[k] + sign * get_thr_arr(k)[slc].sum())
            rough_recall = rough_dict[rough_thr_list_str]
            rough_thr = get_thr_arr(rough_thr_list_str)
            if not self.cfg.use_presice_threshold:            
                best_recall = rough_recall
                self.best_thr = rough_thr
                print(f'{self.best_thr = }\n{best_recall = :.4f}\n{labels = }')
                return self.best_thr
            else:
                print(f'{rough_thr = }\n{rough_recall = :.4f}\n{labels = }')

            precise_dict = dd()
            precise_rg_list = []
            for v in rough_thr:
                precise_rg_list.append(np.arange(v - .05, v + .05, 0.01))
            
            for thr0 in tqdm(precise_rg_list[0]):
                for thr1 in precise_rg_list[1]:
                    for thr2 in precise_rg_list[2]:
                        if thr0 == thr1 == thr2 > 0:
                            continue                    
                        thr_list = [thr0, thr1, thr2]
                        thr = np.array(thr_list)
                        thr = thr - thr.min()
                        clf_argmax = np.argmax(score - thr, axis=1)
                        recall = recall_score(label, clf_argmax, labels=labels, average='weighted')
                        precise_dict[str(thr_list)] = recall
                        
            best_thr_list_str = func(precise_dict, key=lambda k: 10000 * precise_dict[k] + sign * get_thr_arr(k)[slc].sum())
            best_recall = precise_dict[best_thr_list_str]
            best_thr = get_thr_arr(best_thr_list_str)

        self.best_thr = best_thr
        print(f'{self.best_thr = }\n{best_recall = :.4f}\n{labels = }')
        return self.best_thr


    def mad_loss(self, y_pred: Tensor, y_true: Tensor) -> Tensor:
        return torch.mean(torch.sign(-y_true * y_pred) * torch.abs(y_true))


    def direct_loss(self, predicted_position: Tensor, asset_return: Tensor, position: Tensor = 0, phase: str = 'train', directional_balance: bool = None) -> Tensor:
        if isinstance(position, int) and position == 0:
            position = torch.zeros([1, 1], device=predicted_position.device)
        elif len(position.shape) == 1:
            position = position.reshape(-1, 1)
        # if self.cfg.noise_std != 0:            
        #     asset_return = self.gaussian_noise(asset_return)
        if self.cfg.position_enum == PositionType.Long:
            predicted_position = predicted_position / 2 + 0.5
            predicted_position = torch.clamp(predicted_position, min=0, max=1)
        pnl = predicted_position * asset_return
        fee = torch.zeros_like(pnl, device=predicted_position.device)
        if not (self.cfg.shuffling.train
                #  and phase == 'train'
                 ):
            last_position = torch.roll(predicted_position, shifts=1, dims=0)
            last_position[0] = position[0]
            position_change = predicted_position - last_position
            # position_change = predicted_position.diff()
            fee = self.cfg.fee_ratio * torch.abs(position_change)
            # pnl -= self.cfg.fee_ratio * torch.abs(predicted_position - last_position)
        if self.cfg.optimize_sharpe:
            sharpe = (pnl.mean() / pnl.std())
            loss = -sharpe
        else:
            loss = -(pnl - fee).mean() * 100
        if directional_balance is None:
            directional_balance = self.cfg.directional_balance
        if directional_balance:
            loss = loss * (1 - predicted_position.mean().abs())
        return loss, pnl, fee


    def spread_loss(self, order_action: Tensor, asset_return_arr: Tensor, init_position: Tensor = 0) -> Tensor:
        long_ratio = order_action[0] / 2 + 0.5
        long_shift = order_action[1] / 2 - 0.5
        short_ratio = order_action[2] / 2 - 0.5
        short_shift = order_action[3] / 2 + 0.5
        cum_return_arr = asset_return_arr.cumsum(dim=1)        
        cum_return = cum_return_arr[:, -1]

        long_filled_arr = cum_return_arr.lt(np.ones_like(cum_return_arr) * long_shift) | long_shift == 0
        long_filled = long_filled_arr.any(dim=1)
        long_first_idx = long_filled_arr.argmax(dim=1)        
        long_filled_arr[:, long_first_idx:] = long_filled
        long_position_arr = long_filled_arr * long_ratio
        
        short_filled_arr = cum_return_arr.gt(np.ones_like(cum_return_arr) * short_shift) | short_shift == 0
        short_filled = short_filled_arr.any(dim=1)
        short_first_idx = short_filled_arr.argmax(dim=1)
        short_filled_arr[:, short_first_idx:] = short_filled
        short_position_arr = short_filled_arr * short_ratio
        
        position_arr = init_position + long_position_arr + short_position_arr
        last_position_arr = torch.roll(position_arr, shifts=1, dims=1)
        last_position_arr[:, 0] = init_position
        position_change_arr = position_arr - last_position_arr
        pnl_arr = position_arr * asset_return_arr
        fee = (self.cfg.fee_ratio) * torch.abs(position_change_arr)
        
        both = long_filled & short_filled
        # dual = (long_ratio + short_ratio == 0)
        # longer = ~dual & (long_ratio > short_ratio.abs())
        # shorter = ~dual & (short_ratio.abs() > long_ratio)        
        spread_ratio, _ = torch.min(long_ratio, short_ratio.abs()) * both.float()
        spread = (short_shift - long_shift)
        spread_pnl = spread_ratio * spread
        # remained_ratio = init_position + long_ratio * long_filled.float() + short_ratio * short_filled.float()
        # latest_position = position_arr[:, -1]
        direction_pnl = pnl_arr.sum(dim=1) - spread_pnl

        loss = -(spread_pnl + torch.clamp(direction_pnl, min=-torch.inf, max=0) - fee).mean()
        net_pnl_arr = pnl_arr - fee
        return loss, net_pnl_arr, position_arr, position_change_arr
    
        neither = ~(long_filled | short_filled)
        both = long_filled & short_filled
        only_long = long_filled & ~short_filled
        only_short = short_filled & ~long_filled
        long_pnl = long_filled.float() * long_ratio * (cum_return - long_shift)
        short_pnl = short_filled.float() * short_ratio * (cum_return - short_shift)
        position_pnl = init_position * cum_return

        dual = (long_ratio + short_ratio == 0)
        longer = ~dual & (long_ratio > short_ratio.abs())
        shorter = ~dual & (short_ratio.abs() > long_ratio)
        
        spread_ratio, _ = torch.min(long_ratio, short_ratio.abs()) * both.float()
        spread = (short_shift - long_shift)
        spread_pnl = spread_ratio * spread
        latest_position = init_position + long_ratio + short_ratio
        direction_pnl = latest_position * (cum_return - (longer.float() * long_shift + shorter.float() * short_shift))

        fee_ratio = self.cfg.fee_ratio
        fee = (long_filled.float() * long_ratio + short_filled.float() * short_ratio.abs()) * fee_ratio
        pnl = position_pnl + long_pnl + short_pnl - fee
        loss = spread_pnl + torch.clamp(direction_pnl, min=-torch.inf, max=0) - fee
        return -loss.mean(), loss


    def forward(self, *args, **kwargs):
        return self.model(*args, **kwargs)


    def training_step(self, batch: tuple[Tensor, Tensor]):
        return self.common_step(batch, 'train')


    def common_step(self, batch: tuple[Tensor, Tensor], phase: str = 'train'):
        batch_x, batch_y = batch
        if len(batch_x.shape) == 4:
            batch_x = rearrange(batch_x, 'b t s c -> (b s) t c')
            batch_y = rearrange(batch_y, 'b t s c -> (b s) t c')
        if self.cfg.noise_std != 0:            
            batch_x = self.gaussian_noise(batch_x)
        out = self(batch_x)

        if self.is_regression:
            loss = self.get_reg_loss(batch_y, out, phase)
            self.step_dict[phase].pred.append(out.cpu().detach().numpy())
        else:
            if len(out.shape) == 3:
                out = out.reshape(out.shape[0], -1)
            out = self.clf_layer(out)
            # out = F.softmax(out, dim=-1)
            loss = self.clf_loss_fn(out, batch_y.long())
            self.step_dict[phase].pred.append(out.cpu().detach().numpy().argmax(axis=-1))

        self.step_dict[phase].loss.append(loss.item())        
        self.step_dict[phase].actual.append(batch_y.cpu().detach().numpy())
        return loss


    def get_reg_loss(self, batch_y, out, phase: str = 'train'):
        if not self.cfg.pred_multi_step:
            out = out[..., -1]
        cfg = self.cfg
        out = out.reshape(out.shape[0], -1)
        if out.shape != batch_y.shape:
            batch_y = batch_y.reshape(out.shape)
        drt_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        mad_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        mse_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        bce_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        cum_mad_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        cum_mse_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        cum_bce_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        # print(f'{batch_y.shape = }') # (B, T, 1)
        if self.task_enum == TaskType.DirectTrading:
            predicted_position = self.tanh(out)
            drt_loss, pnl, fee = self.direct_loss(predicted_position, batch_y, self.position)
            if not cfg.shuffling.train:
                self.position = predicted_position[-1].detach()
            self.step_dict[phase].pnl.append(pnl.detach().cpu().numpy())
            self.step_dict[phase].position.append(predicted_position.detach().cpu().numpy())


        if cfg.use_mad_loss:
            mad_loss = self.mad_loss(out, batch_y)
        if cfg.use_mse_loss:
            mse_loss = self.reg_loss_fn(out, batch_y)
        if cfg.use_bce_loss:
            bce_loss = self.bce_loss_fn(out.flatten(), batch_y.gt(0).flatten().float())
        if cfg.pred_multi_step:
            output_cum = torch.cumsum(out, dim=1)  # [:, :,1:]
            y_cum = torch.cumsum(batch_y, dim=1)  # [:, :,1:]
            if cfg.use_cum_mad_loss:
                cum_mad_loss = self.mad_loss(output_cum, y_cum)
            if cfg.use_cum_mse_loss:
                cum_mse_loss = self.reg_loss_fn(output_cum, y_cum)
            if cfg.use_cum_bce_loss:
                cum_label = y_cum.gt(0).flatten().float()
                cum_bce_loss = self.bce_loss_fn(output_cum.flatten(), cum_label)

        loss = drt_loss + mad_loss + mse_loss + bce_loss + cum_mad_loss + cum_mse_loss + cum_bce_loss
        self.loss_dict.drt.append(drt_loss.item())
        self.loss_dict.mse.append(mse_loss.item())
        self.loss_dict.bce.append(bce_loss.item())
        self.loss_dict.cum_mse.append(cum_mse_loss.item())
        self.loss_dict.cum_bce.append(cum_bce_loss.item())
        self.loss_dict.all.append(loss.item())
        return loss
    
    def validation_step(self, batch, batch_idx):
        # self.__validation_and_testing(batch, 'val')
        return self.common_step(batch, 'val')


    def test_step(self, batch, batch_idx):
        # self.__validation_and_testing(batch, 'test')
        return self.common_step(batch, 'test')


    def __validation_and_testing(self, batch: tuple[Tensor, Tensor], phase: str = 'val'):
        batch_x, batch_y = batch
        if self.cfg.model_name == 'mlp':
            batch_x = batch_x.reshape(batch_x.shape[0], -1)     
        out = self.predict(batch_x)
        # if self.cfg.direct_trading_model_name in ['gru', 'lstm']:
        #     out = out[0][:, -1]
        if self.is_regression:
            self.val_test_reg(out, batch_y, phase)
        else:
            if len(out.shape) == 3:
                out = out.reshape(out.shape[0], -1)
            out = self.clf_layer(out)
            # out = F.softmax(out, dim=-1)
            mle_loss = self.clf_loss_fn(out, batch_y.long())
            self.step_dict[phase].loss.append(mle_loss.item())
            self.step_dict[phase].pred.append(out.cpu().detach().numpy().argmax(axis=-1))
            self.step_dict[phase].actual.append(batch_y.cpu().detach().numpy())
            

    def val_test_reg(self, out, batch_y, phase):
        cfg = self.cfg
        if not self.cfg.pred_multi_step:
            out = out[..., -1]
        out = out.reshape(out.shape[0], -1)
        if out.shape != batch_y.shape:
            batch_y = batch_y.reshape(out.shape)
        
        loss_count = 0
        if self.task_enum == TaskType.DirectTrading:
            predicted_position = self.tanh(out)
            # if cfg.position_enum == PositionType.Long:
            #     predicted_position = torch.clamp(predicted_position, min=0)
            if cfg.sign_as_position:
                predicted_position = torch.sign(predicted_position)
            loss, pnl, fee = self.direct_loss(predicted_position, batch_y, self.position)
            self.position = predicted_position[-1]
            loss_count += 1
            self.step_dict[phase].pnl.append(pnl.detach().cpu().numpy())
            self.step_dict[phase].position.append(predicted_position.detach().cpu().numpy())
        else:
            loss = tensor(0., device=cfg.device, dtype=torch.float32)

        if cfg.val_test_with_extra_loss:
            if cfg.use_mad_loss:
                mad_loss = self.mad_loss(out, batch_y)
                loss += mad_loss
                loss_count += 1            
            if cfg.use_mse_loss:
                mse_loss = self.reg_loss_fn(out, batch_y)
                loss += mse_loss
                loss_count += 1
            if cfg.use_bce_loss:
                bce_loss = self.bce_loss_fn(out.flatten(), batch_y.gt(0).flatten().float())
                loss += bce_loss
                loss_count += 1
            if cfg.pred_multi_step:
                output_cum = torch.cumsum(out, dim=1)  # [:, 1:]
                batch_y_cum = torch.cumsum(batch_y, dim=1)  # [:, 1:]
                if cfg.use_cum_mad_loss:
                    cum_mad_loss = self.mad_loss(output_cum, batch_y_cum)
                    loss += cum_mad_loss
                    loss_count += 1
                if cfg.use_cum_mse_loss:
                    cum_mse_loss = self.reg_loss_fn(output_cum, batch_y_cum)
                    loss += cum_mse_loss
                    loss_count += 1
                if cfg.use_cum_bce_loss:
                    cum_bce_label = batch_y_cum.gt(0).flatten().float()
                    cum_bce_loss = self.bce_loss_fn(output_cum.flatten(), cum_bce_label)
                    loss += cum_bce_loss
                    loss_count += 1
        if loss_count == 0:                    
            loss = self.reg_loss_fn(out, batch_y)
        self.step_dict[phase].loss.append(loss.item())
        self.step_dict[phase].pred.append(out.cpu().detach().numpy())
        self.step_dict[phase].actual.append(batch_y.cpu().detach().numpy())

    def predict_step(self, batch, batch_idx, dataloader_idx=0):
        return self.predict(batch)

    
    def predict(self, batch) -> Tensor:
        if isinstance(batch, tuple | list):
            batch = batch[0]
        if isinstance(batch, np.ndarray):
            batch = torch.from_numpy(batch).to(self.cfg.device)
        return self.model(batch)


    def configure_optimizers(self):
        if not self.is_regression:
            params = chain(self.model.parameters(), self.clf_layer.parameters())
        else:
            params = self.model.parameters()
        self.optimizer = self.optimizer_enum.value(
            params=params,
            lr=self.lr,
            # eps=self.eps,
            weight_decay=self.weight_decay,
            # momentum=self.momentum
            )
        return [self.optimizer]
    
    def __update_all_lr(self):
        start_lr = self.cfg.learning_rate
        self.lr = start_lr * (self.cfg.lr_decay ** self.current_epoch)
        # SETTING the new LR
        for g in self.optimizer.param_groups:
            g['lr'] = self.lr
        print(f'Updating learning rate to {self.lr:.8f}')


    def on_validation_epoch_end(self) -> None:
        self.__on_phase_end('val', True)
        # self.__update_all_lr()

    def on_train_epoch_end(self) -> None:
        self.__on_phase_end('train', True)
    # def on_test_epoch_end(self) -> None:
    #     self.__on_validation_and_testing_end('test', True)
        

    def on_test_end(self) -> None:
        self.__on_phase_end('test')


    def __on_phase_end(self, phase: str, is_epoch_end: bool = False) -> None:
        loss = np.mean(self.step_dict[phase].loss)
        epoch_str = f'Epoch {self.current_epoch}' 
        if not is_epoch_end:
            epoch_str = 'Final ' + epoch_str
        print(f'\n{epoch_str} {phase} net loss: {loss:.4f}')
        pred = np.concatenate(self.step_dict[phase].pred)
        shape = pred.shape
        actual = np.concatenate(self.step_dict[phase].actual).reshape(shape)
        if self.is_regression:
            if self.task_enum == TaskType.DirectTrading:
                pnl_arr = np.concatenate(self.step_dict[phase].pnl, axis=None)
                pnl_mean = pnl_arr.mean()
                pnl_std = pnl_arr.std()
                position_arr = np.concatenate(self.step_dict[phase].position, axis=None)
                # abs_position_sum = abs(position_arr).mean()
                position_max = position_arr.max()
                position_min = position_arr.min()
                position_mean = position_arr.mean()     
                sharpe = (pnl_arr.mean() / pnl_arr.std())
                print(f'{epoch_str} {phase} {sharpe = :.4f}, \t{pnl_mean = :.4f},\t{pnl_std = :.4f}\n{position_min = :.4f},\t{position_max = :.4f},\t{position_mean = :.4f}\n')
                # if not self.cfg.shuffling.train or phase in [
                if phase in [
                    'val', 
                    'test',
                    ]:
                    backtest(position_arr, actual, self.cfg.fee_ratio, f'{phase}_{self.cfg.symbol}_epc{self.current_epoch}_drt', n_codes=self.cfg.n_codes)
                metric = -sharpe
            else:
                pred = pred.reshape(-1, shape[-1])
                acc_arr = ((pred * actual) > 0).mean(axis=0)
                acc_dict = {i + 1: round(acc, 4) for i, acc in enumerate(acc_arr)}
                self.metric_dict[phase].acc.append(acc_dict)
                acc_bias_mean = abs(acc_arr - 0.5).mean()
                print(f'{epoch_str} {phase} {acc_dict = }\n{acc_bias_mean = :.4f}')
                cum_acc_arr = ((pred.cumsum(axis=1) * actual.cumsum(axis=1)) > 0).mean(axis=0)
                cum_acc_dict = {i + 1: round(cum_acc, 4) for i, cum_acc in enumerate(cum_acc_arr)}
                self.metric_dict[phase].cum_acc.append(cum_acc_dict)
                cum_acc_bias_mean = abs(cum_acc_arr - 0.5).mean()
                print(f'{epoch_str} {phase} {cum_acc_dict = }\n{cum_acc_bias_mean = :.4f}')
                metric = loss - acc_bias_mean + cum_acc_bias_mean
                print(f'{epoch_str} {phase} loss: {loss:.4f}\n')
        else:
            win_rate = calc_win_rate(actual, pred, self.cfg.num_classes)

            report = classification_report(actual, pred) 
            print(f'{epoch_str} {phase} classification report:\n{report}\n{confusion_matrix(actual, pred)}\n\n{win_rate = :.4f}\n')            
            metric = -win_rate
        if phase == 'val':
            if self.task_enum == TaskType.DirectTrading:
                self.log('pnl_mean', pnl_mean)
                self.log('sharpe', sharpe)
            # self.log('cum_acc_bias_mean', cum_acc_bias_mean)
            self.log('metric', metric)
        for k in list(self.step_dict[phase].keys()):
            self.step_dict[phase][k].clear()
        
        

def calc_win_rate(actual, pred, num_classes=3):
    short = 0
    long = num_classes - 1
    short_win = sum((pred == short) & (actual == short))
    long_win = sum((pred == long) & (actual == long))
    long_loss = sum((pred == long) & (actual == short))
    short_loss = sum((pred == short) & (actual == long))
    win_count = short_win + long_win
    trade_count = short_win + long_win + long_loss + short_loss
    win_rate = win_count / trade_count
    return win_rate

def get_meta_win_rate_dict(actual: np.ndarray, pred: np.ndarray, meta_score: np.ndarray, num_bins: int = 20, num_classes: int = 3):
    # meta_score 属于 [0, 1] 计算meta score的win_rate分布
    short = 0
    long = num_classes - 1
    bin_size = 1 / num_bins    
    win_list = [0 for _ in range(num_bins)]
    trade_list = [0 for _ in range(num_bins)]
    meta_score = meta_score[:, 1].flatten()
    for si, score in enumerate(meta_score):
        bin_idx = int(score // bin_size)
        win = (pred[si] == long) and (actual[si] == long) or (pred[si] == short) and (actual[si] == short)
        loss = (pred[si] == long) and (actual[si] == short) or (pred[si] == short) and (actual[si] == long)
        trade = (score > 0.5)
        if win and trade:
            win_list[bin_idx] += 1
        if (win or loss) and trade:
            trade_list[bin_idx] += 1
    
    win_arr = np.array(win_list)
    trade_arr = np.array(trade_list)
    trade_rate_arr = trade_arr / sum(trade_arr)
    win_rate_arr = np.where(trade_arr == 0, 0, (win_arr / trade_arr))
    bins_str = [f'{i * bin_size:.3f}-{(i + 1) * bin_size:.3f}' for i in range(num_bins)]
    trade_win_rate = list(zip((round(r, 4) for r in trade_rate_arr), (round(r, 4) for r in win_rate_arr)))
    win_rate_dict = dict(zip(bins_str, trade_win_rate))
    win_rate_dict = {k: v for k, v in win_rate_dict.items() if v[0] + v[1] > 0}
    return win_rate_dict
            
        