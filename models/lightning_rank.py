import numpy as np
from sklearn.metrics import classification_report, confusion_matrix
import torch
from torch import Tensor
from core.predictor_config import PredictorConfig
from models.lightning_drt import LightningDRT



class LightningRANK(LightningDRT):

    def __init__(
        self,
        cfg: PredictorConfig,
    ):
        super().__init__(cfg)


    def calc_pred_loss(self, pred: Tensor, batch_y: Tensor, phase: str = 'train'):        
        asset_return_seq = batch_y[..., 0]
        high_to_close_seq = batch_y[..., 1]
        low_to_close_seq = batch_y[..., 2]
        pred, shift_ratio, stop_loss_ratio, take_profit_ratio = self.calc_pred(pred)
        score_position = pred.flatten()
        label = asset_return_seq[:, -1]
        loss = self.get_ic_loss(score_position, label)
        self.calc_loss(score_position, asset_return_seq, high_to_close_seq, low_to_close_seq, phase, shift_ratio, stop_loss_ratio, take_profit_ratio)

        return score_position, loss
    
    def get_ic_loss(self, pred: Tensor, label: Tensor):
        # pearson correlation loss
        loss = -torch.dot(
                (pred - pred.mean()) / np.sqrt(pred.shape[0]) / pred.std(),
                (label - label.mean()) / np.sqrt(label.shape[0]) / label.std(),
            )
        return loss