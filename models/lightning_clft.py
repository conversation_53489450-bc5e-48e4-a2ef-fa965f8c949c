import numpy as np
from sklearn.metrics import classification_report, confusion_matrix
import torch
from torch import Tensor
from core.predictor_config import PredictorConfig
from models.lightning_drt import LightningDRT



class LightningCLFT(LightningDRT):

    def __init__(
        self,
        cfg: PredictorConfig,
    ):
        super().__init__(cfg)


    def calc_pred_loss(self, pred: Tensor, batch_y: Tensor, phase: str = 'train'):
        cfg = self.cfg        
        label_cfg = cfg.label
        # if len(pred.shape) == 3:
        #     pred = pred.reshape(pred.shape[0], -1)
        # side = torch.argmax(pred, dim=1, keepdim=True)
        # if len(batch_y.shape) != 2:
        #     batch_y = batch_y.reshape(batch_y.shape[0], -1)
        asset_return_seq = batch_y[..., 0]
        pred, shift_ratio, stop_loss_ratio, take_profit_ratio = self.calc_pred(pred)
        shape = pred.shape
        side = torch.argmax(pred, dim=1) * (2 ** (shape[1] == 2)) - 1
        max_position = torch.softmax(pred, dim=1).max(dim=1)[0]
        if shape[1] == 2 and label_cfg.softmax_advantage_as_position:
            max_position = 2 * max_position - 1
        score_position = side * max_position
        # max_position = 2 * torch.softmax(pred, dim=1).max(dim=1)[0] - 1
        # score_position = side * torch.clamp_min(max_position, 0)        
        if label_cfg.clip_quantile_classification:
            # side = torch.argmax(pred, dim=1) - 1
            # max_position = torch.softmax(pred, dim=1).max(dim=1)[0]
            # score_position = side * max_position
            # shift_ratio = torch.zeros_like(score_position)
            # stop_loss_ratio = torch.ones_like(score_position)
            # take_profit_ratio = torch.ones_like(score_position)

            label = asset_return_seq[:, -1]
            label = torch.ones_like(label, dtype=torch.long)
            pos_cond = ((label > label_cfg.pos_min) & (label <= label_cfg.pos_max))
            neg_cond = ((label >= label_cfg.neg_min) & (label < label_cfg.neg_max))
            label[pos_cond] = 2
            label[neg_cond] = 0

            loss = self.clf_loss_fn(pred, label)
            self.calc_loss(score_position, batch_y, phase, shift_ratio, stop_loss_ratio, take_profit_ratio)
            self.step_dict[phase].label.append(label.detach().cpu().numpy())

        else:

            loss = self.calc_loss(score_position, batch_y, phase, shift_ratio, stop_loss_ratio, take_profit_ratio)

        return score_position, loss