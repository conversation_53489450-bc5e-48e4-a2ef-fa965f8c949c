from models.lightning_base import LightningBase
from models.dva.embedding import DataEmbedding
from models.dva.model import denoise_net, diffusion_generate
from core.predictor_config import PredictorConfig
import torch
from torch import tensor



class LightningDVA(LightningBase):

    def __init__(
        self,
        cfg: PredictorConfig,
    ):
        super().__init__(cfg)

        self.gen_net = diffusion_generate(cfg).to(cfg.device)
        self.model = denoise_net(cfg).to(cfg.device)
        self.diff_step = cfg.diff_steps

        self.embedding = DataEmbedding(cfg.input_size, cfg.embedding_size, cfg.dropout_rate).to(cfg.device)


    def training_step(self, batch: tensor, batch_idx):
        batch_x, batch_y, x_mark, y_mark = batch
        pred_len = self.cfg.pred_len
        cfg = self.cfg
        device = cfg.device
        t = torch.randint(0, self.diff_step, (self.batch_size,)).long().to(device)
        batch_x = batch_x.to(device)
        x_mark = x_mark.float().to(device)
        batch_y = batch_y[...,-cfg.label_dim:].to(self.device)
        output, y_noisy, dsm_loss = self.model(batch_x, x_mark, batch_y, t)
        
        output_sample = output.sample()[:, :, :pred_len]

        recon = output.log_prob(y_noisy)
        kl_loss = - torch.mean(torch.sum(recon, dim=[1, 2, 3]))
        y_noisy = y_noisy[:, :, :pred_len]
        mse_loss = self.reg_loss_fn(output_sample, y_noisy)        

        bce_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        cum_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        cum_mse_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        cum_bce_loss = tensor(0., device=cfg.device, dtype=torch.float32)
        # print(f'{batch_y.shape = }') # (B, T, 1)
        if cfg.use_bce_loss:                     
            bce_sample = output.sample()[:, :, :pred_len] if cfg.sample_for_each_loss else output_sample
            bce_loss += self.bce_loss_fn(bce_sample.flatten(), batch_y[:, :pred_len].gt(0).flatten().float())
            
        if cfg.pred_multi_step:
            cum_sample = output.sample()[:, :, :pred_len] if cfg.sample_for_each_loss else output_sample
            output_cum = torch.cumsum(cum_sample, dim=2)#[:, :,1:]
            y_noisy_cum = torch.cumsum(y_noisy, dim=2)#[:, :,1:]
            cum_mse_loss = self.reg_loss_fn(output_cum, y_noisy_cum)
            cum_loss += cum_mse_loss
            if cfg.use_bce_loss:
                cum_bce_sample = output.sample()[:, :, :pred_len] if cfg.sample_for_each_loss else output_sample
                cum_label = y_noisy_cum.gt(0).flatten().float()
                cum_bce_loss = self.bce_loss_fn(cum_bce_sample.flatten(), cum_label)
                cum_loss += cum_bce_loss        
        loss = mse_loss + cfg.zeta * kl_loss + cfg.eta * dsm_loss + bce_loss + cum_loss

        self.loss_dict.mse.append(mse_loss.item())
        self.loss_dict.bce.append(bce_loss.item())
        self.loss_dict.cum_mse.append(cum_mse_loss.item())        
        self.loss_dict.cum_bce.append(cum_bce_loss.item())
        self.loss_dict.kl.append(kl_loss.item())
        self.loss_dict.dsm.append(dsm_loss.item())
        self.loss_dict.all.append(loss.item())

        return loss
    

    def predict(self, batch):
        batch_x, batch_y, x_mark, y_mark = batch
        with torch.set_grad_enabled(True):
            pred_len = self.cfg.pred_len
            noisy_out, out = self.model.predict(batch_x, x_mark)      
            out = out.reshape(out.shape[0], -1)[:, :pred_len]
        return out
    

    # def __validation_and_testing(self, batch, phase: str = 'val'):
    #     batch_y = batch[1]
    #     cfg = self.cfg
    #     out = self.predict(batch)
    #     if out.shape != batch_y.shape:            
    #         batch_y = batch_y.reshape(out.shape[0], -1)
    #     loss = self.reg_loss_fn(out, batch_y)

    #     if cfg.val_test_with_extra_loss:
    #         if cfg.use_bce_loss: 
    #             bce_loss = self.bce_loss_fn(out.flatten(), batch_y.gt(0).flatten().float())
    #             loss += bce_loss
    #         if cfg.use_cum_loss:
    #             output_cum = torch.cumsum(out, dim=1)#[:, 1:]
    #             batch_y_cum = torch.cumsum(batch_y, dim=1)#[:, 1:]
    #             cum_mse_loss = self.reg_loss_fn(output_cum, batch_y_cum)  
    #             cum_loss = cum_mse_loss                 
    #             if cfg.use_bce_loss: 
    #                 # cum_input = torch.gt(output_cum, 0).flatten().float()
    #                 cum_bce_label = batch_y_cum.gt(0).flatten().float()
    #                 cum_bce_loss = self.bce_loss_fn(output_cum.flatten(), cum_bce_label)
    #                 cum_loss += cum_bce_loss
    #             loss += cum_loss
        
    #     self.step_dict[phase].loss.append(loss.item())
    #     self.step_dict[phase].pred.append(out.cpu().detach().numpy())
    #     self.step_dict[phase].actual.append(batch_y.cpu().detach().numpy())