# -*-Encoding: utf-8 -*-
import numpy as np
import torch
from functools import partial
from inspect import isfunction
import torch.nn as nn
import torch.nn.functional as F
from .resnet import Res12_Quadratic


def get_beta_schedule(beta_schedule, beta_start, beta_end, num_diffusion_timesteps):
    if beta_schedule == 'quad':
      betas = np.linspace(beta_start ** 0.5, beta_end ** 0.5, num_diffusion_timesteps, dtype=np.float64) ** 2
    elif beta_schedule == 'linear':
      betas = np.linspace(beta_start, beta_end, num_diffusion_timesteps, dtype=np.float64)
    elif beta_schedule == 'const':
      betas = beta_end * np.ones(num_diffusion_timesteps, dtype=np.float64)
    elif beta_schedule == 'jsd':  # 1/T, 1/(T-1), 1/(T-2), ..., 1
      betas = 1. / np.linspace(num_diffusion_timesteps, 1, num_diffusion_timesteps, dtype=np.float64)
    else:
      raise NotImplementedError(beta_schedule)
    assert betas.shape == (num_diffusion_timesteps,)
    return betas


def default(val, d):
    if val is not None:
        return val
    return d() if isfunction(d) else d


def extract(a, t, x_shape):
    #print(a.shape, t.shape)
    b, *_ = t.shape
    out = a.gather(-1, t)
    #print(out.shape)
    return out.reshape(b, *((1,) * (len(x_shape) - 1)))


def noise_like(shape, device, repeat=False):
    repeat_noise = lambda: torch.randn((1, *shape[1:]), device=device).repeat(
        shape[0], *((1,) * (len(shape) - 1))
    )
    noise = lambda: torch.randn(shape, device=device)
    return repeat_noise() if repeat else noise()


class GaussianDiffusion(nn.Module):
    def __init__(
        self,
        bvae,
        input_size,
        beta_start=0,
        beta_end=0.1,
        diff_steps=100,
        betas=None,
        scale = 0.1,
        beta_schedule="linear",
    ):
        super().__init__()
        self.generative = bvae
        self.scale = scale
        self.beta_start = beta_start
        self.beta_end = beta_end
        betas = get_beta_schedule(beta_schedule, beta_start, beta_end, diff_steps)
        alphas = 1.0 - betas
        alphas_cumprod = np.cumprod(alphas, axis=0)

        alphas_label = 1.0 - betas*scale
        alphas_label_cumprod = np.cumprod(alphas_label, axis=0)
        self.alphas_label = alphas_label
        self.alphas_label_cumprod = alphas_label_cumprod

        (timesteps,) = betas.shape
        self.num_timesteps = int(timesteps)

        to_torch = partial(torch.tensor, dtype=torch.float32)
        self.register_buffer("betas", to_torch(betas))
        self.register_buffer("alphas_cumprod", to_torch(alphas_cumprod))

        self.register_buffer("sqrt_alphas_cumprod", to_torch(np.sqrt(alphas_cumprod)))
        self.register_buffer("sqrt_alphas_label_cumprod", to_torch(np.sqrt(alphas_label_cumprod)))
        self.register_buffer(
            "sqrt_one_minus_alphas_cumprod", to_torch(np.sqrt(1.0 - alphas_cumprod))
        )
        self.register_buffer(
            "sqrt_one_minus_alphas_label_cumprod", to_torch(np.sqrt(1.0 - alphas_label_cumprod))
        )

    def q_sample(self, x_start, t, noise=None):
        noise = default(noise, lambda: torch.randn_like(x_start))
        return (
            extract(self.sqrt_alphas_cumprod, t, x_start.shape) * x_start
            + extract(self.sqrt_one_minus_alphas_cumprod, t, x_start.shape) * noise
        )

    def q_sample_label(self, y_label, t, noise=None):
        noise = default(noise, lambda: torch.randn_like(y_label))

        return (
            extract(self.sqrt_alphas_label_cumprod, t, y_label.shape) * y_label
            + extract(self.sqrt_one_minus_alphas_label_cumprod, t, y_label.shape) * noise
        )

    def p_losses(self, x_start, y_label, t,  noise=None, noise1=None):
        B, T, _ = x_start.shape
        B1, T1, _ = y_label.shape
        x_start = x_start.reshape(B, 1, T, -1)
        y_label = y_label.reshape(B1, 1, T1, -1)

        noise = default(noise, lambda: torch.randn_like(x_start))
        noise1 = default(noise1, lambda: torch.randn_like(y_label))

        x_noisy = self.q_sample(x_start=x_start, t=t, noise=noise.to(x_start.device))

        y_noisy = self.q_sample_label(y_label=y_label, t=t, noise=noise1.to(y_label.device))
        x_noisy = x_noisy.reshape(B,1, T,-1)

        y_noisy = y_noisy.reshape(B1,1, T1,-1)

        logits = self.generative(x_noisy)

        output = self.generative.decoder_output(logits)
        return output, y_noisy

    def log_prob(self, x_input, y_label, time):
        output, y_noisy = self.p_losses(
            x_input, y_label, time,
        )
        return output, y_noisy
