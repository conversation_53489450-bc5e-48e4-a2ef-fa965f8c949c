
import torch.nn as nn
import torch
from models.bct.bin_nn import BiN
from models.bct.tabl.bl_layer import BL_layer
from models.bct.tabl.tabl_layer import TABL_layer
from core.predictor_config import PredictorConfig


class BiN_BTABL(nn.Module):
    def __init__(self, hidden_dim, in_channel, seq_len, hidden_len, pred_channel, pred_len):
        super().__init__()

        self.BiN = BiN(hidden_dim, in_channel, seq_len, hidden_len)
        self.BL = BL_layer(hidden_dim, in_channel, seq_len, hidden_len)
        self.TABL = TABL_layer(pred_channel, hidden_dim, hidden_len, pred_len)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x):
        # first of all we pass the input to the BiN layer, then we use the B(TABL) architecture
        x = torch.permute(x, (0, 2, 1))

        x = self.BiN(x)

        self.max_norm_(self.BL.W1.data)
        self.max_norm_(self.BL.W2.data)
        x = self.BL(x)
        x = self.dropout(x)

        self.max_norm_(self.TABL.W1.data)
        self.max_norm_(self.TABL.W.data)
        self.max_norm_(self.TABL.W2.data)
        x = self.TABL(x)
        x = torch.squeeze(x)

        return x

    def max_norm_(self, w):
        with torch.no_grad():
            if (torch.linalg.matrix_norm(w) > 10.0):
                norm = torch.linalg.matrix_norm(w)
                desired = torch.clamp(norm, min=0.0, max=10.0)
                w *= (desired / (1e-8 + norm))


class BiN_CTABL(nn.Module):
    def __init__(self, hidden_dim, in_channel, seq_len, hidden_len, extra_dim, extra_len, pred_channel, pred_len, is_reg=True):
        super().__init__()
        self.is_reg = is_reg
        self.BiN = BiN(hidden_dim, in_channel, seq_len, hidden_len)
        self.BL = BL_layer(hidden_dim, in_channel, seq_len, hidden_len)
        self.BL2 = BL_layer(extra_dim, hidden_dim, hidden_len, extra_len)
        self.TABL = TABL_layer(pred_channel, extra_dim, extra_len, pred_len)
        self.dropout = nn.Dropout(0.1)

    def forward(self, x, return_mid_layer=False, return_last_layer=False):
        # first of all we pass the input to the BiN layer, then we use the C(TABL) architecture

        x = torch.permute(x, (0, 2, 1))

        x = self.BiN(x)

        self.max_norm_(self.BL.W1.data)
        self.max_norm_(self.BL.W2.data)
        x = self.BL(x)
        x = self.dropout(x)

        self.max_norm_(self.BL2.W1.data)
        self.max_norm_(self.BL2.W2.data)
        x = self.BL2(x)
        if return_mid_layer:
            return x
        x = self.dropout(x)

        self.max_norm_(self.TABL.W1.data)
        self.max_norm_(self.TABL.W.data)
        self.max_norm_(self.TABL.W2.data)
        if return_last_layer:
            return self.TABL(x, return_last_layer)
        x = self.TABL(x)
        if self.is_reg:
            return x # B RegC T
        x = torch.squeeze(x, -1)
        x = torch.softmax(x, 1)
        return x # B ClfC

    def max_norm_(self, w):
        with torch.no_grad():
            if (torch.linalg.matrix_norm(w) > 10.0):
                norm = torch.linalg.matrix_norm(w)
                desired = torch.clamp(norm, min=0.0, max=10.0)
                w *= (desired / (1e-8 + norm))


class Model(nn.Module):
    def __init__(self, cfg: PredictorConfig | None):
        super().__init__()
        self.cfg = cfg
        hidden_size = cfg.hidden_size * 8
        self.bct = BiN_CTABL(
            hidden_dim=hidden_size, 
            in_channel=cfg.input_size, 
            seq_len=cfg.seq_len,
            hidden_len=hidden_size,
            extra_dim=hidden_size,
            extra_len=cfg.embedding_size,
            pred_channel=cfg.label_dim,
            pred_len=cfg.output_size,
            is_reg=cfg.is_regression
        )
    

    
    def forward(self, x, onehot=None, return_mid_layer=False, return_last_layer=False):
        return self.bct(x, return_mid_layer, return_last_layer)