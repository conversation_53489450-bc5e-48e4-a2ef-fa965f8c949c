import torch
import torch.nn as nn
import torch.nn.functional as F

class VariationalMLP(nn.Module):
    def __init__(self, input_size, output_size, hidden_size=128, num_layers=0, prior_variance=1.0):
        super(VariationalMLP, self).__init__()
        
        # MLP的隐藏层维度
        self.hidden_size = hidden_size
        self.mu_list = nn.ModuleList()
        self.logvar_list = nn.ModuleList()
        self.act_list = nn.ModuleList()
        self.num_layers = num_layers
        # prior_variance 用于控制变分分布的先验方差
        self.prior_variance = prior_variance

        if num_layers == 0:
            self.mu_list.append(nn.Linear(input_size, output_size))
            self.logvar_list.append(nn.Linear(input_size, output_size))
            self.act_list.append(nn.Identity())
            return
        
        for i in range(num_layers):
            if i == 0:
                self.mu_list.append(nn.Linear(input_size, hidden_size))
                self.logvar_list.append(nn.Linear(input_size, hidden_size))
            else:
                self.mu_list.append(nn.Linear(hidden_size, hidden_size))
                self.logvar_list.append(nn.Linear(hidden_size, hidden_size))
            self.act_list.append(nn.Tanh())
        
        # 输出层
        self.mu_list.append(nn.Linear(hidden_size, output_size))
        self.logvar_list.append(nn.Linear(hidden_size, output_size))
        self.act_list.append(nn.Identity())


    def forward(self, x: torch.Tensor):
        for mu_layer, logvar_layer, act_layer in zip(self.mu_list, self.logvar_list, self.act_list):
            mu = mu_layer(x)
            logvar = logvar_layer(x)
            eps = torch.randn_like(mu)
            if self.training:                            
                x = act_layer(mu + torch.exp(0.5 * logvar) * eps)
            else:
                x = act_layer(mu)
        loss = -0.5 * (1 + logvar - mu.pow(2) - logvar.exp()).mean()
        return x, loss
    

if __name__ == '__main__':
    input_size = 20
    output_size = 1
    hidden_size = 10
    num_layers = 2
    model = VariationalMLP(input_size, output_size, hidden_size, num_layers)
    print(model)
    x = torch.randn(10, input_size)
    y, loss = model(x)
    print(y.shape)

