import torch
import torch.nn as nn

from models.trn.transnext import TransNeXt
from core.predictor_config import PredictorConfig



class Vision(nn.Module):
    def __init__(self, cfg: PredictorConfig):
        super().__init__()
        self.cfg = cfg
        trn_kwargs = cfg.trn_kwargs
        device = cfg.device
        self.model = TransNeXt(**trn_kwargs).to(device)
        channel, height, width = cfg.get_image_shape()[-3:]
        sample = torch.randn(1, channel, height, width, device=device)
        output = self(sample)
        self.output_size = output.shape[1]

    def forward(self, x):
        x = self.model(x)
        return x


        






