import numpy as np
from torch import Tensor, nn
import torch
from core.predictor_config import PredictorConfig
from layers.multi_embedding import MultiEmbedding
from layers.multi_branch_conv import MultiBranchConv


class Model(nn.Module):
    backbone: nn.Module
    embedding: nn.Module
    conv: nn.Module
    fc: nn.Module
    def __init__(self, cfg: PredictorConfig):
        super().__init__()
        self.cfg = cfg
        fc_input_size = 0
        if cfg.use_backbone:
            self.backbone = cfg.get_backbone(cfg.backbone_name)
            fc_input_size += self.backbone.output_size * self.backbone.seq_len
        else:
            self.backbone = None
        self.num_categories = len(cfg.category_column_idx_list)
        if self.num_categories > 0:
            self.embedding = MultiEmbedding(cfg.category_size_dict)
            embedding_size = self.embedding.output_size
            fc_input_size += embedding_size
        else:
            self.embedding = None
        if cfg.feature_cfg.image:
            channel_or_branch, height, width = cfg.get_image_shape()[-3:] #[::-1]
            if cfg.image_cfg.multi_branch:
                channel = 1
                n_branches = channel_or_branch
            else:
                channel = channel_or_branch
                n_branches = 1
            # self.conv = cfg.get_vision(cfg.vision_name)
            self.conv = MultiBranchConv((height, width), in_channels=channel, out_channels=cfg.num_conv_channels, n_layers=cfg.num_conv_layers, n_branches=n_branches, step=cfg.image_cfg.step, dropout_rate=cfg.dropout_rate.conv, n_mixed_channel_layers=cfg.image_cfg.n_mixed_channel_layers)
            fc_input_size += self.conv.output_size
        else:
            self.image_shape = self.conv = None
        self.act = nn.Tanh()
        self.fc = nn.Linear(fc_input_size, cfg.output_size)
        self.dropout = nn.Dropout(cfg.dropout_rate.fc)


    def forward(self, seq: Tensor = None, category: Tensor = None, image: Tensor = None):
        result = []
        if self.backbone is not None and seq is not None:
            result.append(self.backbone(seq))
        if self.embedding is not None and category is not None:
            result.append(self.embedding(category))
        if self.conv is not None and image is not None:
            result.append(self.conv(image))
        result = torch.cat(result, dim=1)
        # result = self.act(result)
        result = self.fc(result)
        result = self.dropout(result)
        return result
            
