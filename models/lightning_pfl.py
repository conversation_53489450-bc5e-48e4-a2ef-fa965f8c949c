from collections import deque
import os
from shutil import copyfile
from einops import rearrange
import numpy as np
import pandas as pd
import polars as pl
import torch
from torch import Tensor
from torch.nn import functional as F
from core.backtest import backtest
from core.cst import PositionType, TaskType
from models.lightning_base import LightningBase
from core.predictor_config import PredictorConfig
from core.dot_dict import DotDict as dd



class LightningPFL(LightningBase):

    def __init__(
        self,
        cfg: PredictorConfig,
    ):
        super().__init__(cfg)


    def set_before_optimizers(self, cfg: PredictorConfig = None):
        super().set_before_optimizers(cfg)
        self.set_account()
        self.best_code_sign = np.ones(self.cfg.n_codes)
        self.best_pnl_mean = float('-inf')
        self.mask = None
        

    def set_account(self):
        cfg = self.cfg
        self.balance = self.cash = 1
        self.position = torch.zeros(cfg.n_codes, device=cfg.device, dtype=torch.float32)
        # self.code_sign = torch.ones(cfg.n_codes, device=cfg.device, dtype=torch.float32)
        
        self.return_arr = torch.zeros(1, cfg.n_codes, device=cfg.device, dtype=torch.float32)


    def on_train_epoch_start(self):
        self.set_account()


    def on_validation_epoch_start(self):
        self.set_account()

    
    def on_test_epoch_start(self):
        self.set_account()


    def calc_pred_loss(self, output: Tensor, batch_y: Tensor, phase: str = 'train', save_to_step_dict=True):
        pred, shift_ratio, stop_loss_ratio = self.calc_pred(output, batch_y, phase)

        if phase == 'predict':
            return None, pred.detach().cpu().numpy()

        loss = self.calc_loss(pred, batch_y, phase, shift_ratio, stop_loss_ratio, save_to_step_dict)

        return pred, loss
    

    def calc_pred(self, pred, batch_y=None, phase='train'):
        cfg = self.cfg
        n_codes = cfg.n_codes
        margin_coef = cfg.margin_scale
        if len(pred.shape) != 2:
            pred = pred.reshape(pred.shape[0], -1)
        if cfg.limit_margin_per_code:
            margin_ratio_arr = torch.clamp_min(F.sigmoid(pred[:, :n_codes]), margin_coef / n_codes)
            pred = pred[:, n_codes:]
        else:
            margin_ratio_arr = torch.ones(pred.shape[0], n_codes, device=cfg.device, dtype=torch.float32)

        if (shift_scale := cfg.limit_shift_scale) != 0:
            shift_ratio = F.sigmoid(pred[:, :n_codes]) * shift_scale
            pred = pred[:, n_codes:]
        else:
            shift_ratio = torch.zeros([pred.shape[0], n_codes], device=cfg.device, dtype=torch.float32)

        if cfg.stop_loss.in_use:
            stop_scale = cfg.stop_loss.scale
            if cfg.stop_loss.stop_per_code:
                if cfg.stop_loss.learnable:                    
                    stop_loss_ratio = F.sigmoid(pred[:, :n_codes]) * stop_scale
                    pred = pred[:, n_codes:]
                else:
                    stop_loss_ratio = torch.ones([pred.shape[0], n_codes], device=cfg.device, dtype=torch.float32) * cfg.stop_loss.min_ratio
            else:
                if cfg.stop_loss.learnable:
                    stop_loss_ratio = F.sigmoid(pred[:, 0]) * stop_scale
                    pred = pred[:, 1:]
                else:
                    stop_loss_ratio = torch.ones(pred.shape[0], device=cfg.device, dtype=torch.float32) * cfg.stop_loss.min_ratio
        else:
            stop_loss_ratio = torch.ones(pred.shape[0], device=cfg.device, dtype=torch.float32)
        stop_loss_ratio += cfg.stop_loss.min_ratio

        if cfg.skip_step:
            skipping_mask = (pred[:, :n_codes] > 0).float()
            pred = pred[:, n_codes:]
        else:
            skipping_mask = torch.ones(pred.shape[0], n_codes, device=cfg.device, dtype=torch.float32)

        

        if self.mask is None:
            self.mask = torch.from_numpy(cfg.custom_mask).to(cfg.device)
        pred = pred * self.mask.unsqueeze(0).repeat(pred.shape[0], 1)
        margin_ratio_arr = margin_ratio_arr * self.mask.unsqueeze(0).repeat(margin_ratio_arr.shape[0], 1)

        if cfg.use_softmax:            
            if cfg.position_enum == PositionType.Long:
                # pred = F.sigmoid(pred)
                pred = margin_ratio_arr * F.softmax(pred, dim=1)
            elif cfg.position_enum == PositionType.Short:
                # pred = -F.sigmoid(pred)
                pred = margin_ratio_arr * -F.softmax(pred, dim=1)
            elif cfg.position_enum == PositionType.Both:
                # pred = F.tanh(pred)
                sign = torch.sign(pred)
                pred = margin_ratio_arr * F.softmax(abs(pred), dim=1) * sign
            elif cfg.position_enum == PositionType.Hedge:
                ...

        else:
            if cfg.position_enum == PositionType.Long:
                pred = F.sigmoid(pred)
            elif cfg.position_enum == PositionType.Short:
                pred = -F.sigmoid(pred)
            elif cfg.position_enum == PositionType.Both:
                pred = F.tanh(pred)
            elif cfg.position_enum == PositionType.Hedge:
                ...
            if cfg.limit_margin_per_code:
                pred = margin_ratio_arr * pred / margin_ratio_arr.sum(axis=1, keepdim=True)
            else:
                pred = margin_ratio_arr * pred / pred.abs().sum(axis=1, keepdim=True)
        
        pred = pred * skipping_mask

        if cfg.adapt_distr_shift.get(phase):
            print(f'applying adapt_distr_shift in phase {phase}')
            alpha = cfg.adapt_distr_shift.ema_alpha
            inverse_coef = cfg.adapt_distr_shift.inverse_coef
            # ema_return = compute_ema_vectorized(batch_y.reshape(-1, n_codes), alpha)[-1 -pred.shape[0]: -1].sum(dim=1, keepdim=True).repeat(1, pred.shape[1])
            #.sum(dim=0, keepdim=True).repeat(pred.shape[0], 1)
            batch_return = pred.detach() * batch_y.reshape(-1, n_codes)
            self.return_arr = torch.cat([self.return_arr, batch_return], dim=0)
            ema_return = compute_ema_vectorized(self.return_arr, alpha)
            
            # ema_return = compute_moving_average(self.return_arr, 10)

            ema_return = ema_return[-1 - pred.shape[0]: -1].sum(dim=1, keepdim=True).repeat(1, pred.shape[1])

            pred = torch.where(ema_return > 0, pred, inverse_coef * pred)
            
        return pred, shift_ratio, stop_loss_ratio


    def calc_loss(self, pred, batch_y, phase, shift_ratio, stop_loss_ratio, save_to_step_dict=True):
        cfg = self.cfg
        if torch.isnan(pred).any():
            print(f"pred is nan in {phase}\n{pred = }")
        asset_return_arr = batch_y[..., 0] # [B, T, S]
        high_to_close_arr = batch_y[..., 1]
        low_to_close_arr = batch_y[..., 2]
        pfl_loss, pnl, fee, filled_position, filled_final_filled_return, position_after_stop, is_stop_loss, stop_loss_ratio_per_code, is_all_stop_loss, all_stop_loss_ratio, asset_return = self.portfolio_loss(pred, asset_return_arr, high_to_close_arr, low_to_close_arr, shift_ratio, stop_loss_ratio, self.position)
        loss = pfl_loss

        if not cfg.shuffling.train:
            self.position = position_after_stop[-1:].detach()
        
        if save_to_step_dict:
            self.step_dict[phase].pnl.append(pnl.detach().cpu().numpy())
            self.step_dict[phase].filled_position.append(filled_position.detach().cpu().numpy())
            self.step_dict[phase].asset_return.append(asset_return.detach().cpu().numpy())

            self.step_dict[phase].position_after_stop.append(position_after_stop.detach().cpu().numpy())
            self.step_dict[phase].final_filled_return.append(filled_final_filled_return.detach().cpu().numpy())
            # self.step_dict[phase].is_market.append(is_market.detach().cpu().numpy())
            # self.step_dict[phase].is_filled.append(is_filled.detach().cpu().numpy())
            self.step_dict[phase].is_stop_loss.append(is_stop_loss.detach().cpu().numpy())
            self.step_dict[phase].stop_loss_ratio_per_code.append(stop_loss_ratio_per_code.detach().cpu().numpy())
            self.step_dict[phase].is_all_stop_loss.append(is_all_stop_loss.detach().cpu().numpy())
            self.step_dict[phase].all_stop_loss_ratio.append(all_stop_loss_ratio.detach().cpu().numpy())

        return loss


    def process_epoch(self, loss: np.ndarray, pred: np.ndarray, actual: np.ndarray, phase: str, epoch_str: str) -> None:
        cfg = self.cfg
        pnl_mean, pnl_std, sign = self.calc_mean_std_sign(phase)
        asset_return_arr = np.concatenate(self.step_dict[phase].asset_return, axis=0).T.flatten()
        filled_position_arr = (np.concatenate(self.step_dict[phase].filled_position, axis=0) * sign).T.flatten()
        position_after_stop_arr = (np.concatenate(self.step_dict[phase].position_after_stop, axis=0) * sign).T.flatten()
        final_filled_return_arr = np.concatenate(self.step_dict[phase].final_filled_return, axis=0).T.flatten()
        # is_market_arr = np.concatenate(self.step_dict[phase].is_market, axis=0).T.flatten()
        # is_filled_arr = np.concatenate(self.step_dict[phase].is_filled, axis=0).T.flatten()
        is_stop_loss_arr = np.concatenate(self.step_dict[phase].is_stop_loss, axis=0).T.flatten()
        stop_loss_ratio_per_code_arr = np.concatenate(self.step_dict[phase].stop_loss_ratio_per_code, axis=0).T.flatten()
        is_all_stop_loss_arr = np.concatenate(self.step_dict[phase].is_all_stop_loss, axis=0).T.flatten()
        all_stop_loss_ratio_arr = np.concatenate(self.step_dict[phase].all_stop_loss_ratio, axis=0).T.flatten()
        # abs_position_sum = abs(position_arr).mean()
        position_max = position_after_stop_arr.max()
        position_min = position_after_stop_arr.min()
        position_mean = position_after_stop_arr.mean()
        sharpe = (pnl_mean / pnl_std) if pnl_std != 0 else 0
        print(f'{epoch_str} {phase} {sharpe = :.5f},\t{pnl_mean = :.5f},\t{pnl_std = :.5f}\n{position_max = :.5f},\t{position_min = :.5f},\t{position_mean = :.5f}\n')
        # if not cfg.shuffling.train or phase in [
        if phase in [
            'val', 
            'test',
            ]:
            interval = cfg.interval_cfg.base
            save_folder = os.path.join(self.ckpt_folder, cfg.datetime_str, f'epoch_{self.current_epoch}')
            if not os.path.exists(save_folder):
                os.makedirs(save_folder)
            if phase == 'val':
                save_path = os.path.join(self.ckpt_folder, cfg.datetime_str, cfg.script_name.split('/')[-1])
                if not os.path.exists(save_path):
                    # copy portfolio.py into save_path
                    copyfile(cfg.script_name, save_path)
            date_dict = self.date_dict
            date_str = f'{date_dict.val[0]}v{date_dict.val[1]}'
            save_str = f'{date_str}_{phase}_crypto{(n_codes := cfg.n_codes)}_{interval}min_epc{self.current_epoch}_{cfg.task_enum.value}'
            mean_pnl_before_fee, fee_mean, end_pnl_before_fee, fee_sum = backtest(filled_position_arr, asset_return_arr, position_after_stop_arr, final_filled_return_arr, is_stop_loss_arr, stop_loss_ratio_per_code_arr, is_all_stop_loss_arr, all_stop_loss_ratio_arr, cfg.fee_ratio, save_folder, save_str, n_codes=n_codes, code_list=
            cfg.code_list)
            
        else:
            mean_pnl_before_fee = fee_mean = end_pnl_before_fee = fee_sum = 0
        # metric = -loss
        metric = end_pnl_before_fee
        if phase == 'val' or cfg.for_deployment and phase == 'train' and not cfg.execute_phase.val:
            if self.task_enum in [TaskType.Portfolio, TaskType.PortfolioDoubleAdapt]:
                self.log('pnl_mean', pnl_mean)
                self.log('sharpe', sharpe)
                self.log('mean_pnl_before_fee', mean_pnl_before_fee)
                self.log('mean_pnl_after_fee', mean_pnl_before_fee - fee_mean)
            # self.log('cum_acc_bias_mean', cum_acc_bias_mean)
            self.log('metric', metric)


    def calc_mean_std_sign(self, phase):
        cfg = self.cfg
        pnl_arr = np.concatenate(self.step_dict[phase].pnl, axis=0)
        if cfg.use_code_sign:
            code_pnl_mean = pnl_arr.mean(axis=0)
            code_sign = np.sign(code_pnl_mean)
            signed_pnl_arr = pnl_arr * code_sign.reshape(1, -1).repeat(pnl_arr.shape[0], 0)
            pnl_mean = signed_pnl_arr.sum(axis=1).mean().item()
            pnl_std = signed_pnl_arr.sum(axis=1).std().item()
            if (pnl_mean > self.best_pnl_mean) and (phase == 'val'):
                self.best_pnl_mean = pnl_mean
                self.best_code_sign = code_sign
            sign = self.best_code_sign.reshape(1, -1).repeat(pnl_arr.shape[0], 0)
        else:
            sign = 1
            pnl_mean = pnl_arr.mean(axis=0).mean().item()
            pnl_std = pnl_arr.mean(axis=0).std().item()
        return pnl_mean,pnl_std,sign


    def preidct(self, x: torch.Tensor | np.ndarray) -> tuple:
        cfg = self.cfg
        if isinstance(x, np.ndarray):
            x = torch.from_numpy(x, device=cfg.device, dtype=torch.float32)
        pred = self.model(x)

        # if (shift_scale := cfg.order_shift_scale) != 0:
        #     shift_ratio = F.sigmoid(pred[:, -1]) * shift_scale
        # else:
        #     shift_ratio = torch.zeros(pred.shape[0], device=cfg.device, dtype=torch.float32)
        if cfg.stop_loss.in_use:
            if cfg.stop_loss.learnable:
                stop_loss_ratio = F.sigmoid(pred[:, 1]) * cfg.stop_loss.scale
                pred = pred[:, 0]
            else:                
                stop_loss_ratio = cfg.stop_loss.min_ratio * torch.ones_like(pred)
        else:            
            stop_loss_ratio = torch.ones_like(pred.shape[0])
        if cfg.position_enum == PositionType.Long:
            pred = F.sigmoid(pred)
        else:
            pred = F.tanh(pred)
        return pred, stop_loss_ratio


    def portfolio_loss(self, advised_position: Tensor, asset_return_arr: Tensor, high_to_close_arr: Tensor, low_to_close_arr: Tensor, shift_ratio: Tensor, stop_loss_ratio: Tensor, init_position: Tensor = 0, phase: str = 'train', directional_balance: bool = None) -> tuple:
        cfg = self.cfg        
        if isinstance(init_position, int) and init_position == 0:
            init_position = torch.zeros(cfg.n_codes, device=advised_position.device, dtype=torch.float32)
        elif len(init_position.shape) != 2:
            init_position = init_position.reshape(1, -1)
        batch_size, pred_len, n_codes = asset_return_arr.shape
        if len(stop_loss_ratio.shape) == 1:
            all_stop_ratio = stop_loss_ratio
            stop_ratio_per_code = torch.ones(batch_size, n_codes, device=advised_position.device, dtype=torch.float32)
        else:
            all_stop_ratio = torch.ones(batch_size, device=advised_position.device, dtype=torch.float32)
            stop_ratio_per_code = stop_loss_ratio

        fee_ratio_dict = self.cfg.fee_ratio
        cum_return_arr = asset_return_arr.cumsum(dim=1)
        cum_high_arr = cum_return_arr + high_to_close_arr
        cum_low_arr = cum_return_arr + low_to_close_arr
        
        asset_return = cum_return_arr[:, -1]
        
        is_market = shift_ratio == 0
        side = torch.sign(advised_position)
        signed_shift_ratio = - side * shift_ratio
        max_advangtage = torch.where(side > 0, cum_high_arr.max(dim=1)[0], cum_low_arr.min(dim=1)[0])
        max_return = max_advangtage + asset_return

        
        shift_arr = signed_shift_ratio.unsqueeze(1).repeat(1, pred_len, 1)
        long_filled_arr = (0 >= shift_arr) & (shift_arr > cum_low_arr)
        short_filled_arr = (0 <= shift_arr) & (shift_arr < cum_high_arr)
        is_filled_arr = long_filled_arr | short_filled_arr
        is_filled = is_filled_arr.any(dim=1) | is_market
        filled_idx = torch.where(is_filled, is_filled_arr.float().argmax(dim=1), pred_len)



        filled_position = is_filled.float() * advised_position
        asset_filled_return_arr = is_filled.float().unsqueeze(1).repeat(1, pred_len, 1) * cum_return_arr + (torch.sign(filled_position) * shift_ratio).unsqueeze(1).repeat(1, pred_len, 1)

        # asset_filled_return = asset_filled_return_arr[:, -1]
        asset_filled_return = get_asset_filled_return(shift_ratio, side, asset_return, cum_high_arr, cum_low_arr)
        # advantage = is_filled * (asset_filled_return - asset_return).abs()

        # filled_pnl_arr = filled_position.unsqueeze(1).repeat(1, pred_len, 1) * asset_filled_return_arr
        signed_stop_range_per_code = side * stop_ratio_per_code
        if cfg.stop_loss.with_position:            
            signed_stop_range_per_code[is_filled] = (filled_position * stop_ratio_per_code)[[is_filled]]
        else:
            signed_stop_range_per_code[is_filled] = (torch.sign(is_filled) * stop_ratio_per_code)[is_filled]
        stop_label_return_arr = asset_filled_return_arr - signed_stop_range_per_code.unsqueeze(1).repeat(1, pred_len, 1)
        is_stop_loss_arr = (cum_high_arr > stop_label_return_arr) & (stop_label_return_arr > 0) | (cum_low_arr < stop_label_return_arr) & (stop_label_return_arr < 0)
        # if stop_per_code:
        #     is_stop_loss_arr = (high_arr < stop_label_return_arr) | (low_arr > stop_label_return_arr)

        #     # is_stop_loss = is_stop_loss_arr.any(dim=1)
        #     # stop_loss_idx = torch.where(is_stop_loss, is_stop_loss_arr.float().argmax(dim=1), pred_len + 1)
        #     # filled_first = filled_idx <= stop_loss_idx
        #     # is_stop_loss[~filled_first] = False
        #     # return_idx = torch.where(is_stop_loss, is_stop_loss_arr.float().argmax(dim=1), -1).unsqueeze(1)
        # else:
        #     is_stop_loss_arr = filled_pnl_arr.sum(dim=-1) < -stop_loss_ratio.unsqueeze(1).repeat(1, pred_len)
        #     is_stop_loss_arr = is_stop_loss_arr.unsqueeze(-1).repeat(1, 1, cfg.n_codes)

        is_stop_loss = is_stop_loss_arr.any(dim=1)
        stop_loss_idx = torch.where(is_stop_loss, is_stop_loss_arr.float().argmax(dim=1), pred_len + 1)
        filled_first = filled_idx <= stop_loss_idx
        is_stop_loss[~filled_first] = False
        return_idx = torch.where(is_stop_loss, is_stop_loss_arr.float().argmax(dim=1), pred_len - 1).unsqueeze(1)


        # filled_final_filled_return = torch.gather(stop_label_return_arr, 1, return_idx).squeeze(1)
        # filled_final_filled_return = torch.where(is_stop_loss, filled_final_filled_return, asset_filled_return)

        filled_final_filled_return = asset_filled_return
        # filled_final_filled_return = get_final_filled_return(stop_range_per_code, side, advantage, asset_return, cum_high_arr, cum_low_arr)

        position_after_stop = torch.where(is_stop_loss, torch.zeros_like(filled_position), filled_position)                  
        fee_after_stop = torch.where(is_stop_loss, fee_ratio_dict.stop * abs(filled_position), torch.zeros_like(filled_position))
        # asset_return[is_filled] = asset_filled_return[is_filled]


        pnl = filled_position * filled_final_filled_return - fee_after_stop

        fee = torch.zeros_like(pnl, device=advised_position.device)
        pnl_sum = pnl.sum(dim=-1)
        is_all_stop_loss = pnl_sum < -all_stop_ratio
        pnl_sum[is_all_stop_loss] = -all_stop_ratio[is_all_stop_loss] * (1 + fee_ratio_dict.stop)

        if not (cfg.shuffling.train
                 and phase == 'train'
                 ):
  
            prev_position = torch.cat([init_position, position_after_stop])[:-1]
            position_change = filled_position - prev_position
            fee = fee_ratio_dict.limit * abs(position_change)
            fee[is_market] = fee_ratio_dict.market * abs(position_change[is_market])
        net_pnl = pnl_sum - fee.sum(dim=-1)
        if cfg.optimize_sharpe:
            sharpe = (net_pnl.mean() / net_pnl.std())
            loss = -sharpe
        elif cfg.optimize_profit_ratio:
            profit_ratio = torch.clamp_min(net_pnl, 0).sum() / net_pnl.abs().sum()
            loss = -profit_ratio * net_pnl.mean()
        elif cfg.optimize_to_oracle:
            # oracle = (max_return * advised_position).abs()
            oracle = max_return.abs()
            loss = (oracle.sum(dim=1) - net_pnl).mean()
        else:
            pnl_decay = cfg.pnl_decay
            if pnl_decay.in_use:
                thr = cfg.pnl_decay.threshold
                pos_pnl = torch.clamp_min(net_pnl, 0)
                pos_exceed_pnl = torch.clamp_max(pos_pnl - thr , 0)
                pos_clamp_pnl = torch.clamp_min(pos_pnl, thr)
                neg_pnl = torch.clamp_max(net_pnl, 0)
                neg_exceed_pnl = torch.clamp_min(neg_pnl + thr, 0)
                neg_clamp_pnl = torch.clamp_max(neg_pnl, -thr)
                net_pnl = pos_clamp_pnl + neg_clamp_pnl + torch.log(1 + pos_exceed_pnl) + torch.log(1 + neg_exceed_pnl)
            loss = -(
                torch.clamp_max(net_pnl, 0) * (1 + (punish_ratio := (cfg.position_punishment.in_use * abs(position_after_stop).sum(axis=-1) ** cfg.position_punishment.exponent))) + 
                torch.clamp_min(net_pnl, 0) * (1 - punish_ratio / 2)
            ).mean()

        if directional_balance is None:
            directional_balance = cfg.directional_balance
        if directional_balance:
            loss = loss * (1 - 100 * advised_position.mean().abs())
        result = (loss, pnl, fee, filled_position, filled_final_filled_return, position_after_stop, is_stop_loss, is_stop_loss * stop_ratio_per_code, is_all_stop_loss, is_all_stop_loss * all_stop_ratio, asset_return)
        for i, arr in enumerate(result):
            if torch.isnan(arr).any():
                print(f'{torch.isnan(arr).int().sum() = }\n{i}th {arr = }')
        return result




def compute_ema_vectorized(tensor, alpha):
    m, n = tensor.shape
    
    # 创建权重矩阵
    alpha_powers = torch.pow(alpha, torch.arange(m, dtype=tensor.dtype, device=tensor.device))
    weights = torch.tril(torch.ones((m, m), dtype=tensor.dtype, device=tensor.device)) * alpha_powers.view(-1, 1)
    
    # 创建归一化因子
    norm_factors = torch.pow(1 - alpha, torch.arange(m, dtype=tensor.dtype, device=tensor.device))
    
    # 计算加权和
    weighted_sum = torch.matmul(weights, tensor)
    
    # 计算EMA
    ema = weighted_sum / norm_factors.view(-1, 1)
    
    return ema

def compute_moving_average(tensor, window_size):
    m, n = tensor.shape
    
    # # 展开张量以便在第一个维度上应用卷积
    # tensor_reshaped = tensor.transpose(0, 1).unsqueeze(0)
    
    # # 创建卷积核
    # kernel = torch.ones(m, n, window_size, dtype=tensor.dtype, device=tensor.device) / window_size
    
    # # 使用卷积计算移动平均
    # moving_avg = F.conv1d(tensor_reshaped, kernel, padding=window_size-1)
    
    # # 恢复张量的原始形状
    # moving_avg = moving_avg.squeeze(0).transpose(0, 1)
    
    # # 对多余的填充部分进行修正
    # for i in range(1, window_size):
    #     moving_avg[i-1, :] *= (i / window_size)

    # 创建一个全零张量来存储移动平均值
    moving_avg = torch.zeros_like(tensor)
    
    # 计算移动平均值
    for i in range(m):
        start_index = max(0, i - window_size + 1)
        window = tensor[start_index:i + 1]
        moving_avg[i] = window.mean(dim=0)
    
    return moving_avg


def get_asset_filled_return(shift_ratio, side, asset_return, cum_high_arr, cum_low_arr):
    """\operatorname{sign}\left(x\right)\left(\left(\left|x\ \right|+\ \operatorname{sign}\left(x\right)b\right)\left(1\ \ -\frac{1}{1\ \ +\ \exp\left(-100000\left(\left|x\ \right|-\ c\ +\ 0.0001\right)\right)}\right)\right)\ """
    max_advangtage = torch.where(side < 0, cum_high_arr.max(dim=1)[0], -cum_low_arr.min(dim=1)[0])
    asset_filled_return = (shift_ratio + side * asset_return) * (1 - 1 / (1 + torch.exp(-1e-6 * (shift_ratio - max_advangtage + 1e-5))))
    return asset_filled_return * side


def get_final_filled_return(stop_range_per_code, side, advantage, asset_return, cum_high_arr, cum_low_arr):
    max_drawdown = torch.zeros_like(asset_return, device=asset_return.device)
    max_drawdown[side < 0] = cum_high_arr.max(dim=1)[0][side < 0]
    max_drawdown[side > 0] = -cum_low_arr.min(dim=1)[0][side > 0]
    abs_stop_loss_return = (stop_range_per_code - asset_return) / (1 + torch.exp(-1e-6 * (stop_range_per_code - max_drawdown))) - stop_range_per_code
    abs_stop_loss_return = abs_stop_loss_return * side.abs()
    return advantage + abs_stop_loss_return