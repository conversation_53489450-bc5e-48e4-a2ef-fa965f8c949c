import torch
import torch.nn as nn
from core.predictor_config import PredictorConfig
from layers.RWKV import Block, RWKV_Init
from models.dft.DFT import DFT


class Model(nn.Module):
    def __init__(self, cfg: PredictorConfig):
        super(Model, self).__init__()
        self.cfg = cfg
        n_head = cfg.n_head        
        embedding_size = cfg.embedding_size
        self.rwkv_block = Block(layer_id=0, n_embd=embedding_size,
                                n_attn=embedding_size, n_head=n_head, ctx_len=300,
                                n_ffn=embedding_size, hidden_sz=embedding_size)
        RWKV_Init(self.rwkv_block, vocab_size=embedding_size, n_embd=embedding_size, rwkv_emb_scale=1.0)
            
        self.backbone = nn.Sequential(
            nn.Linear(cfg.input_size, embedding_size), 
            self.rwkv_block,
            # nn.Tanh()
            nn.Mish()
        )
        self.fc = nn.Linear(cfg.seq_len *embedding_size, cfg.output_size)


    def forward(self, x, onehot=None):
        x = self.backbone(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)
        return x
        