from keras import Sequential, Input
from keras.api.layers import Dense, GRU, BatchNormalization
from keras.api.metrics import MeanAbsoluteError, binary_accuracy
from keras.api.optimizers import SGD, RMSprop, AdamW
from models.keras_base import KerasBase
from core.predictor_config import PredictorConfig
import keras.api.backend as K
from keras.api.ops import mean


class KerasGRU(KerasBase):

    def __init__(
        self,
        cfg: PredictorConfig,
    ):
        super().__init__(cfg)        
        self.model = Sequential()
        for i in range(cfg.num_rnn_layers):
            self.model.add(GRU(cfg.hidden_size * 4, return_sequences=True, activation='tanh',
                                recurrent_activation='hard_sigmoid', # good
                                dropout=cfg.dropout_rate, recurrent_dropout=cfg.dropout_rate))
        self.model.add(GRU(cfg.hidden_size, return_sequences=False, activation='tanh',
                            recurrent_activation='hard_sigmoid', # good
                            dropout=cfg.dropout_rate, recurrent_dropout=cfg.dropout_rate))
        self.model.add(BatchNormalization(axis=-1, beta_initializer='ones')) # good            
        self.model.add(Dense(1, activation='tanh'))
        opt = AdamW(learning_rate=cfg.learning_rate)
        self.loss_fn = lambda y_true, y_pred: -100. * mean(y_true * y_pred)
        self.model.compile(loss=self.loss_fn,
                           optimizer=opt,
                           metrics=['accuracy'])
        # self.model.build(input_shape=(None, self.model.input_shape[1], self.model.input_shape[2]))
        self.model.summary()
