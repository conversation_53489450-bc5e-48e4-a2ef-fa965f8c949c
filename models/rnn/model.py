
import torch
import torch.nn as nn
from core.cst import FC_Type
from layers.fast_kan import FastKAN
from layers.multi_head_self_attention import MultiheadSelfAttention
from models.lightning_pflda import FeatureAdapter, LabelAdapter
from models.onehot_fc import OneHotFC
from models.tcn.model import TC<PERSON>ayer
from core.predictor_config import PredictorConfig


class Model(nn.Module):
    def __init__(self, cfg: PredictorConfig, input_size: int = None):
        super(Model, self).__init__()
        self.cfg = cfg
        seq_len = self.seq_len = cfg.seq_len
        scale = cfg.n_codes #if 'pfl' in cfg.task_enum.value else 1
        hidden_size = self.hidden_size = int(cfg.hidden_size * scale)
        embedding_dim = self.embedding_dim = int(cfg.embedding_size * scale)
        num_layers = self.num_layers = cfg.num_rnn_layers
        if input_size is None:
            input_size = cfg.input_size
        output_size = cfg.output_size        
        bidirectional = cfg.bidirectional

        self.output_last = cfg.rnn_output_last
        self.dropout = nn.Dropout(cfg.dropout_rate.fc)
        if cfg.tcn_before_rnn:
            self.tcn = TCNLayer(cfg)
            input_size = self.tcn.output_size
        else:
            self.tcn = nn.Identity()
        if cfg.use_projection:
            self.projection = nn.Linear(input_size, embedding_dim)
            input_size = embedding_dim
            self.tanh0 = nn.Tanh()
        else:
            self.projection = nn.Identity()
            self.tanh0 = nn.Identity()
        if cfg.mhsa_before_rnn:
            self.mhsa0 = MultiheadSelfAttention(input_size, input_size, input_size, cfg.num_heads)
        else:
            self.mhsa0 = nn.Identity()
        RnnClass = cfg.rnn_class_dict[cfg.rnn_name]
        self.rnn = RnnClass(input_size, hidden_size, num_layers, batch_first=True, dropout=cfg.dropout_rate.backbone, bidirectional=bidirectional)
        if cfg.mhsa_after_rnn:
            self.mhsa1 = MultiheadSelfAttention(hidden_size, hidden_size, hidden_size, cfg.num_heads)
        else:
            self.mhsa1 = nn.Identity()

        channel_dim = hidden_size * (2 ** bidirectional)
        if cfg.channel_att_after_rnn:            
            self.att_fc1 = nn.Linear(channel_dim, channel_dim)
            self.tanh1 = nn.Tanh()
            self.att_fc2 = nn.Linear(channel_dim, channel_dim)
        else:
            self.att_fc1 = nn.Identity()
            self.tanh1 = nn.Identity()
            self.att_fc2 = nn.Identity()

        self.fc_input_size = channel_dim * (seq_len * cfg.channel_att_after_rnn + 1)

        # if cfg.concat_hidden:
        #     self.fc_input_size += hidden_size * (2 ** bidirectional) * num_layers
        
        self.mish = nn.Mish()        
        # self.tanh2 = nn.Tanh()
        self.batch_norm = nn.BatchNorm1d(self.fc_input_size) if cfg.use_batch_norm else nn.Identity()
        self.fc_with_onehot = cfg.feature_cfg.non_seq

        if self.cfg.is_channel_independent or not self.cfg.backbone_with_fc:
            self.fc = nn.Identity()
        else:
            self.fc_input_size += cfg.concat_prediction * cfg.n_codes
            if self.fc_with_onehot:
                onehot_dim = cfg.feature_cfg.onehot_dim
                self.fc_input_size += onehot_dim
                self.fc = OneHotFC(self.fc_input_size, onehot_dim, output_size)     
            elif cfg.fc_enum == FC_Type.KAN:
                self.fc = FastKAN([self.fc_input_size, output_size])
            else:
                self.fc = nn.Linear(self.fc_input_size, output_size)

        factor_num = cfg.n_seq_features * cfg.seq_len
        num_heads = cfg.meta_adapt.num_heads
        temperature = cfg.meta_adapt.temperature
        self.mapper_x = FeatureAdapter(cfg, factor_num, num_heads, temperature)
        self.mapper_y = LabelAdapter(cfg, factor_num, num_heads, temperature)


    def forward(self, x: torch.Tensor, onehot: torch.Tensor = None, prediction: torch.Tensor = None): 
        x = self.tcn(x)
        x = self.projection(x)
        x = self.tanh0(x)
        x = self.mhsa0(x)
        
        out, hidden = self.rnn(x)
        out = self.mhsa1(out) # [batch_size, seq_len, hidden_size]
        last_out = out[:, -1, :]
        # if self.output_last:
        #     out = out[:, -1, :]
        # else:
        #     out = out.reshape(out.shape[0], -1)
        
        # out = self.dropout(out[:, -1, :])#.reshape(x.shape[0], -1)
        if self.cfg.channel_att_after_rnn:
            att = self.att_fc1(out)
            att = self.tanh1(out)
            att = self.att_fc2(att)
            att = att.softmax(dim=1)
            out = (out * att)
            out = torch.cat([out.flatten(1), last_out], dim=1)
        else:
            out = last_out        
            out = self.mish(out)

        # out = self.tanh1(out)
        out = self.batch_norm(out)

        if self.cfg.is_channel_independent or not self.cfg.backbone_with_fc:
            return out

        if prediction is not None:
            out = torch.cat([out, prediction], dim=1)
        if self.fc_with_onehot:
            out = self.fc(out, onehot)
        else:
            out = self.fc(out)
        return out