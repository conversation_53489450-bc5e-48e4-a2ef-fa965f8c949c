from copy import deepcopy
from math import e
import os
from shutil import copyfile
from matplotlib import category, pyplot as plt
import numpy as np
import pandas as pd
import polars as pl
from sklearn.metrics import classification_report, confusion_matrix
import torch
from torch import Tensor, corrcoef
from torch.nn import functional as F
from utils.analyze_results import get_fee, get_mdd_arr, get_top_k_in_one_row, get_top_pnl, get_turnover
from core.backtest import backtest
from core.cst import Objective, PositionType, LabelType, TaskType
from models.lightning_drt import LightningDRT
from core.predictor_config import PredictorConfig
from core.dot_dict import DotDict as dd
from utils.report_clf_ic import report_clf_ic



class LightningMKM(LightningDRT):

    # def __init__(
    #     self,
    #     cfg: PredictorConfig,
    # ):
    #     super().__init__(cfg)
    #     self.position = 0.


    # def on_train_epoch_start(self):        
    #     self.position = 0.


    # def on_validation_epoch_start(self):        
    #     self.position = 0.

    
    # def on_test_epoch_start(self):        
    #     self.position = 0.


    def calc_pred_loss(self, pred: Tensor, batch_x: Tensor, batch_y: Tensor, phase: str = 'train', save_to_step_dict: bool = True):

        pred = self.calc_pred(pred)
        # if len(batch_y.shape) != 2:
        #     batch_y = batch_y.reshape(batch_y.shape[0], -1)

        loss = self.calc_loss(pred, batch_y, phase, save_to_step_dict)
        return None, pred, loss


    def calc_pred(self, pred: Tensor) -> Tensor:
        cfg = self.cfg
        shape = pred.shape
        # batch_size = shape[0]
        if len(shape) != 2:
            pred = pred.reshape(shape[0], -1)
        shift_scale = cfg.limit_shift_scale
        pred = F.tanh(pred)
        if cfg.fair_price_shift_spread_for_market_making:
            position = pred[:, 0]
            long_position = position / 2 + 0.5
            short_position = -long_position
            fair_price_shift = (pred[:, 1]) * shift_scale
            spread = (pred[:, 2] / 2 + 0.5) * shift_scale
            long_shift = (fair_price_shift - spread).clamp(None, 0)
            short_shift = (fair_price_shift + spread).clamp(0, None)
        else:
            long_position = pred[:, 0] / 2 + 0.5
            long_shift = (pred[:, 1] / 2 - 0.5) * shift_scale
            short_position = pred[:, 2] / 2 - 0.5
            short_shift = (pred[:, 3] / 2 + 0.5) * shift_scale
            fair_price_shift = (long_shift + short_shift) / 2
            spread = short_shift - long_shift


        pred = torch.stack([long_position, long_shift, short_position, short_shift, fair_price_shift, spread], dim=1)
        
        return pred


    def calc_loss(self, pred, batch_y, phase, save_to_step_dict=True):
        cfg = self.cfg
        result = self.spread_loss(pred, batch_y, phase)
        loss, pnl, fee, spread_pnl, spread_position, spread_fee, spread, final_position, final_filled_return, abs_position, asset_return, high_return, low_return = result
        if not cfg.shuffling.train:
            self.position = final_position[-1:].detach()
        if save_to_step_dict:
            self.step_dict[phase].pnl.append(pnl.detach().cpu().numpy())
            self.step_dict[phase].fee.append(fee.detach().cpu().numpy())
            self.step_dict[phase].asset_return.append(asset_return.detach().cpu().numpy())
            self.step_dict[phase].high_return.append(high_return.detach().cpu().numpy())
            self.step_dict[phase].low_return.append(low_return.detach().cpu().numpy()) 
            self.step_dict[phase].abs_position.append(abs_position.detach().cpu().numpy())
            self.step_dict[phase].final_position.append(final_position.detach().cpu().numpy())
            self.step_dict[phase].final_filled_return.append(final_filled_return.detach().cpu().numpy())
            self.step_dict[phase].spread_pnl.append(spread_pnl.detach().cpu().numpy())        
            self.step_dict[phase].spread_position.append(spread_position.detach().cpu().numpy())
            self.step_dict[phase].spread_fee.append(spread_fee.detach().cpu().numpy())
            self.step_dict[phase].spread.append(spread.detach().cpu().numpy())

        return loss
    

    def process_epoch(self, loss: np.ndarray, pred: np.ndarray, actual: np.ndarray, phase: str, epoch_str: str) -> None:
        cfg = self.cfg
        n_codes = cfg.n_codes
        if 'top' in cfg.monitor and phase in ['val', 'test']:
            epoch_dict = dd()
            for col in ['pnl', 'fee', 'asset_return', 'high_return', 'low_return', 'abs_position', 'final_position', 'final_filled_return', 'spread_pnl', 'spread_position', 'spread_fee', 'spread', 'label']:
                col_list = self.step_dict[phase].get(col)
                if col_list is not None and len(col_list) > 0:
                    epoch_dict[col] = np.concatenate(col_list, axis=None).flatten()
            if 'high_return' not in epoch_dict:
                epoch_dict['high_return'] = epoch_dict['asset_return']
            if 'low_return' not in epoch_dict:
                epoch_dict['low_return'] = epoch_dict['asset_return']
            
            epoch_dict['position'] = epoch_dict.final_position#('final_position', epoch_dict.filled_position)
            if 'final_filled_return' not in epoch_dict:
                epoch_dict['final_filled_return'] = epoch_dict['asset_return']
            epoch_df = pd.DataFrame(epoch_dict)
            
            
            epoch_df.index = cfg.valid_index_dict[phase]
            group_by_str = cfg.group_by_str            
            # if cfg.load_all_codes:
            #     epoch_df.index = cfg.valid_index_dict[phase]
            #     group_by_str = cfg.group_by_str
            # else:
            #     seq_step_count = len(epoch_df) // n_codes
            #     epoch_df['code'] = np.array(cfg.code_list).reshape(1, -1).repeat(seq_step_count)
            #     epoch_df['code_idx'] = np.arange(len(epoch_df), dtype=np.int32) % n_codes
            #     epoch_df['group_idx'] = np.arange(len(epoch_df), dtype=np.int32) // n_codes
            #     group_by_str = 'group_idx'
            
            # print(f'{epoch_df.tail(10) = }')
            # ic = np.corrcoef(epoch_df.position.values, epoch_df.asset_return.values)
            
            self.backtest_top_k(epoch_df, phase, group_by_str)
            return
        
        pnl_arr = np.concatenate(self.step_dict[phase].pnl, axis=None)
        if cfg.load_all_codes:
            ...
            # pnl_df = pd.DataFrame(self.step_dict[phase].pnl, index=cfg.valid_index_dict[phase])
            # pnl_arr = pnl_df.groupby(level='group_idx').mean().values
        else:
            # pnl_arr = pnl_arr.reshape(n_codes, -1).mean(axis=0)
            ...
        pnl_mean = pnl_arr.mean()
        pnl_std = pnl_arr.std()
        # asset_return_arr = np.concatenate(self.step_dict[phase].asset_return, axis=None)
        # high_return_arr = np.concatenate(self.step_dict[phase].high_return, axis=None)
        # low_return_arr = np.concatenate(self.step_dict[phase].low_return, axis=None)
        # filled_position_arr = np.concatenate(self.step_dict[phase].filled_position, axis=None)
        final_position_arr = np.concatenate(self.step_dict[phase].final_position, axis=None)
        # final_filled_return_arr = np.concatenate(self.step_dict[phase].final_filled_return, axis=None)
        # is_market_arr = np.concatenate(self.step_dict[phase].is_market, axis=None)
        # is_filled_arr = np.concatenate(self.step_dict[phase].is_filled, axis=None)
        # is_stop_loss_arr = np.concatenate(self.step_dict[phase].is_stop_loss, axis=None)
        # stop_loss_ratio_arr = np.concatenate(self.step_dict[phase].stop_loss_ratio, axis=None)
        # if len(label_list := self.step_dict[phase].label) > 0:
        #     label = np.concatenate(label_list, axis=None)
        # else:
        #     label = None
        # abs_position_sum = abs(position_arr).mean()
        position_max = final_position_arr.max()
        position_min = final_position_arr.min()
        position_mean = final_position_arr.mean()     
        sharpe = (pnl_mean / pnl_std)
        print(f'{epoch_str} {phase} {sharpe = :.5f},\t{pnl_mean = :.5f},\t{pnl_std = :.5f}\n{position_min = :.5f},\t{position_max = :.5f},\t{position_mean = :.5f}\n')
        if phase == 'val':
            metric = pnl_mean
            if self.cfg.task_enum == TaskType.Rank:
                metric = sharpe
            self.log('pnl_mean', pnl_mean)
            self.log('sharpe', sharpe)
            self.log('metric', metric)


    def predict(self, x: torch.Tensor | np.ndarray) -> tuple:
        cfg = self.cfg
        if isinstance(x, np.ndarray):
            x = torch.from_numpy(x)
        pred = self.model(x)
        return self.calc_pred(pred)


    def spread_loss(self, order_action: Tensor, batch_y: Tensor, phase: str = 'train') -> Tensor:
        cfg = self.cfg            
        long_position = order_action[:, 0: 1] # N, 1
        long_shift = order_action[:, 1: 2]
        short_position = order_action[:, 2: 3]
        short_shift = order_action[:, 3: 4]
        fair_price_shift = order_action[:, 4] # N,
        spread = order_action[:, 5] # N,
        asset_return_seq = batch_y[..., 0] # N, T
        high_to_close_seq = batch_y[..., 1]
        low_to_close_seq = batch_y[..., 2]              
        
        if cfg.label_enum == LabelType.RawNormalized:
            cum_return_arr = asset_return_seq
            cum_high_arr = high_to_close_seq
            cum_low_arr = low_to_close_seq
        elif cfg.label_enum == LabelType.LogReturn:
            cum_return_arr = asset_return_seq.cumsum(dim=1)
            cum_high_arr = cum_return_arr + high_to_close_seq
            cum_low_arr = cum_return_arr + low_to_close_seq        
        elif cfg.label_enum == LabelType.PercentChange:
            cum_return_arr = (asset_return_seq + 1).cumprod(dim=1) - 1
            cum_high_arr = (cum_return_arr + 1) * (1 + high_to_close_seq) - 1
            cum_low_arr = (cum_return_arr + 1) * (1 + low_to_close_seq) - 1
        else: # simple return
            cum_high_arr = cum_low_arr = cum_return_arr = (asset_return_seq + 1).cumprod(dim=1) - 1

        batch_size, pred_len = cum_return_arr.shape

        asset_return = cum_return_arr[:, -1]
        high_return = cum_high_arr[:, -1]
        low_return = cum_low_arr[:, -1]        
        
        idx_arr = torch.arange(pred_len, device=asset_return.device).unsqueeze(0).repeat(batch_size, 1) # N, T
        long_shift_arr = long_shift.repeat(1, pred_len) # N, T
        long_filled_arr = cum_low_arr.lt(long_shift_arr) # N, T
        long_is_market = long_shift_arr == 0
        long_filled = long_filled_arr.any(dim=1, keepdim=True) | long_is_market # N, 1
        long_first_idx = long_filled_arr.float().argmax(dim=1, keepdim=True) # N, 1

        long_filled_arr[long_first_idx >= idx_arr] = long_filled[long_first_idx >= idx_arr]
        long_position_arr = long_filled_arr * long_position.repeat(1, pred_len) # N, T

        
        short_shift_arr = short_shift.repeat(1, pred_len) # N, T
        short_filled_arr = cum_high_arr.gt(short_shift_arr) # N, T
        short_is_market = short_shift_arr == 0
        short_filled = short_filled_arr.any(dim=1, keepdim=True) | short_is_market # N, 1
        short_first_idx = short_filled_arr.float().argmax(dim=1, keepdim=True) # N, 1
        short_filled_arr[short_first_idx >= idx_arr] = short_filled[short_first_idx >= idx_arr]
        short_position_arr = short_filled_arr * short_position.repeat(1, pred_len) # N, T
        
        final_position_arr = long_position_arr + short_position_arr # N, T

        # init_position = torch.zeros([batch_size, 1], device=asset_return.device)
        # prev_position_arr = torch.cat([init_position, final_position_arr], dim=1)[:, :-1]
        # position_change_arr = final_position_arr - prev_position_arr # N, T

        fee_ratio = cfg.fee_ratio.limit
        # directional_fee = cfg.pnl_loss_with_fee_scale * fee_ratio * torch.abs(position_change_arr).sum(dim=1)
        init_position = torch.zeros(batch_size, device=asset_return.device)
        final_position = final_position_arr[:, -1]
        directional_fee = cfg.pnl_loss_with_fee_scale * fee_ratio * torch.abs(final_position - init_position)

        long_filled_return = long_filled.flatten().float() * (asset_return - long_shift.flatten())
        short_filled_return = short_filled.flatten().float() * (asset_return - short_shift.flatten())
        final_filled_return = long_filled_return + short_filled_return
        # final_filled_return = (final_position != 0).float() * asset_return # N,
        directional_pnl = final_position * final_filled_return - directional_fee # N,
        # is_filled = long_filled | short_filled
        # asset_filled_return_arr = 
        both = long_filled & short_filled # N, 1
        filled_position = long_filled.float() * long_position + short_filled.float() * short_position.abs() # N, 1
        abs_position = torch.max(long_position, short_position.abs())
        # dual = (long_position + short_position == 0)
        # longer = ~dual & (long_position > short_position.abs())
        # shorter = ~dual & (short_position.abs() > long_position)        
        spread_position = (torch.min(long_position, short_position.abs()) * both.float()).flatten() # N,
        # spread = (short_shift - long_shift).flatten() # N,
        
        spread_fee = (fee_ratio * 2 * spread_position)
        spread_pnl = spread_position * spread - spread_fee
        fee = directional_fee + spread_fee
        pnl = directional_pnl + spread_pnl + fee
        # remained_ratio = init_position + long_position * long_filled.float() + short_position * short_filled.float()
        # latest_position = position_arr[:, -1]
        loss = -spread_pnl.mean()
        if cfg.with_fair_price_shift_spread_loss:
            max_high_return = cum_high_arr.max(dim=1)[0]
            min_low_return = cum_low_arr.min(dim=1)[0]
            shift_loss = F.mse_loss(fair_price_shift, (max_high_return + min_low_return) / 2)
            spread_loss = F.mse_loss(spread, max_high_return - min_low_return)
            loss += shift_loss + spread_loss
        if cfg.with_directional_profit:
            loss += -directional_pnl.mean()
        # else:
        #     loss += -torch.clamp(directional_pnl, min=-torch.inf, max=0).mean()
        
        
        return loss, pnl, fee, spread_pnl, spread_position, spread_fee, spread, final_position, final_filled_return, abs_position, asset_return, high_return, low_return