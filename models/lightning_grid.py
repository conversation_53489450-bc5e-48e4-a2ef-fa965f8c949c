import torch
from torch import Tensor
from models.lightning_mkm import LightningMKM
from core.predictor_config import PredictorConfig
from core.dot_dict import DotDict as dd


class LightningGRID(LightningMKM):

    def __init__(
        self,
        cfg: PredictorConfig,
    ):
        super().__init__(cfg)


    def calc_pred_loss(self, pred: Tensor, batch_x: Tensor, batch_y: Tensor, phase: str = 'train', save_to_step_dict: bool = True):
        self.calc_direction_std(batch_x)

        logits, score_position, grid_count, grid_size, grid_position_ratio = self.calc_pred(pred)

        loss = self.calc_loss(logits, score_position, batch_y, phase, grid_count, grid_size, grid_position_ratio, save_to_step_dict)
        return logits, score_position, loss
    

    def calc_pred(self, pred: Tensor) -> Tensor:
        cfg = self.cfg
        grid_cfg = cfg.grid_cfg
        shape = pred.shape
        if len(shape) != 2:
            pred = pred.reshape(pred.shape[0], -1)
        if grid_cfg.output_count:
            logits_num = grid_cfg.max_count - grid_cfg.min_count
            grid_count_logits = pred[:, :logits_num]
            grid_count = torch.argmax(grid_count_logits, dim=-1) + grid_cfg.min_count
            pred = pred[:, logits_num:]
        else:
            grid_count = grid_cfg.fix_count * torch.ones(shape[0], device=pred.device)
        grid_size = self.rolling_std * grid_cfg.scale
        grid_position_ratio = torch.tanh(pred[:, 0]) / 2 + 0.5
        logits = pred[:, 1:]
        score_position = self.get_score_position(logits)
        result = (
            logits,
            score_position,
            grid_count,
            grid_size,
            grid_position_ratio,
        )
        for i, arr in enumerate(result):
            if torch.isnan(arr).any():
                print(f'{i}th array has {torch.isnan(arr).int().sum()} nan values\n{arr = }')        
        return result


    def calc_loss(self, logits, score_position, batch_y, phase, grid_count, grid_size, grid_position_ratio, save_to_step_dict=True):
        cfg = self.cfg
        result = self.grid_loss(logits, score_position, batch_y, grid_count, grid_size, grid_position_ratio, phase, self.position)
        loss, pnl, fee, grid_pnl, grid_position, final_position, asset_return, high_return, low_return = result
        if not cfg.shuffling.train:
            self.position = final_position[-1:].detach()
        if save_to_step_dict:
            self.step_dict[phase].pnl.append(pnl.detach().cpu().numpy())
            self.step_dict[phase].fee.append(fee.detach().cpu().numpy())
            self.step_dict[phase].asset_return.append(asset_return.detach().cpu().numpy())
            self.step_dict[phase].high_return.append(high_return.detach().cpu().numpy())
            self.step_dict[phase].low_return.append(low_return.detach().cpu().numpy())
            self.step_dict[phase].abs_position.append(score_position.abs().detach().cpu().numpy())
            self.step_dict[phase].spread_pnl.append(grid_pnl.detach().cpu().numpy())
            # self.step_dict[phase].spread_position.append(grid_position.detach().cpu().numpy())
            self.step_dict[phase].final_position.append(final_position.detach().cpu().numpy())

        return loss
    

    def grid_loss(self, logits: Tensor, advised_position: Tensor, batch_y: Tensor, grid_count: Tensor, grid_size: Tensor, grid_position_ratio: Tensor, phase: str = 'train', init_position: Tensor = 0.) -> Tensor:
        cfg = self.cfg
        
        if len(logits.shape) == 2:
            if logits.shape[1] == 1:
                logits = logits.squeeze(1)
        if isinstance(init_position, float):
            init_position = torch.tensor(init_position, device=advised_position.device).reshape(1)
        fee_ratio_dict = cfg.fee_ratio
        net_grid_size = grid_size
        grid_size = grid_size + fee_ratio_dict.limit * 2

        position_per_grid = advised_position.abs() * grid_position_ratio / grid_count
        non_grid_position = advised_position * (1 - grid_position_ratio)
        zero_batch = torch.zeros_like(non_grid_position)
        norm_close_arr, norm_high_arr, norm_low_arr = self.get_norm_arr(batch_y)
        norm_open_arr = torch.cat([zero_batch.unsqueeze(1), norm_close_arr[:, :-1]], dim=1) # [N, T]
        hlc = torch.stack([norm_high_arr, norm_low_arr, norm_close_arr], dim=-1) # [N, T, 3]
        lhc = torch.stack([norm_low_arr, norm_high_arr, norm_close_arr], dim=-1)
        up_mask = (norm_close_arr > norm_open_arr).unsqueeze(-1).expand_as(hlc)

        # batch_size, pred_len = norm_close_arr.shape
        
        price_path = torch.where(up_mask, hlc, lhc).flatten(1) # [N, T*3]
        price_location = (price_path / grid_size.unsqueeze(1)).clamp(-grid_count.unsqueeze(1), grid_count.unsqueeze(1)) # [N, T*3]

        grid_filled = torch.zeros_like(price_location)
        int_location = torch.zeros_like(zero_batch)

        for i in range(price_location.shape[1]):
            location_diff = price_location[:, i] - int_location
            diff_count = location_diff.abs().floor() * location_diff.sign()
            grid_filled[:, i] = -diff_count
            int_location = int_location + diff_count

        signed_end_count = -int_location # [N]
        grid_filled_count = grid_filled.abs().sum(dim=1) # [N]
        grid_filled_position = (grid_filled_count - signed_end_count.abs()) * position_per_grid

        end_price = norm_close_arr[:, -1]
        grid_end_position = position_per_grid * signed_end_count
        grid_avg_price = (grid_size * int_location / 2)
        grid_end_pnl = (end_price - grid_avg_price) * grid_end_position
        grid_filled_pnl = grid_filled_position * grid_size / 2
        grid_pnl = grid_end_pnl + grid_filled_pnl
        grid_fee = (2 * grid_end_position.abs() + grid_filled_position) * fee_ratio_dict.limit


        final_position = non_grid_position
        non_grid_fee = non_grid_position * fee_ratio_dict.limit * 2
        non_grid_pnl = end_price * non_grid_position

        pnl = grid_pnl + non_grid_pnl
        fee = grid_fee + non_grid_fee
        net_pnl = pnl - fee * cfg.pnl_loss_with_fee_scale
        loss = -net_pnl.mean()
        result = (
            loss, 
            pnl,
            fee,
            grid_pnl,
            grid_end_position,
            final_position,
            end_price,
            norm_high_arr[:, -1],
            norm_low_arr[:, -1],
        )
        for i, arr in enumerate(result):
            if torch.isnan(arr).any():
                print(f'{i}th array has {torch.isnan(arr).int().sum()} nan values\n{arr = }')
        return result

