import torch
import torch.nn as nn
import torch.nn.functional as F
from core.predictor_config import PredictorConfig


class Model(nn.Module):
    def __init__(self, cfg: PredictorConfig):
        super().__init__()
        self.cfg = cfg
        self.batch_size = cfg.batch_size
        self.output_size = cfg.output_size
        self.n_features = cfg.n_all_features * cfg.seq_len
        self.n_selected_features = cfg.n_selected_features
        self.depth = cfg.tree_depth
        self.n_estimators = cfg.n_estimators
        self.dropout_rate = cfg.dropout_rate
        # 树的结构参数
        self.internal_node_num = 2 ** self.depth - 1
        self.leaf_node_num = 2 ** self.depth

        # 数据采样和统计
        self.configure_features_and_data()

        # 路径标识符和内部节点索引生成
        self.generate_path_information()

        # 初始化模型参数
        self.init_model_parameters()


    def configure_features_and_data(self):
        # 动态调整 n_selected_features
        if self.n_selected_features == 1:
            self.n_selected_features = self.n_features        
        elif self.n_selected_features > 1:
            self.n_selected_features = min(self.n_selected_features, self.n_features)
        else:
            self.n_selected_features = int(self.n_features * self.n_selected_features)
            self.n_selected_features = min(self.n_selected_features, 50)
            self.n_selected_features = max(self.n_selected_features, 10)
            self.n_selected_features = min(self.n_selected_features, self.n_features)
        
        # 特征采样（每棵树使用的特征子集）
        self.estimator_feature_indices = [
            torch.tensor(
                torch.randperm(self.n_features)[: self.n_selected_features]
            )
            for _ in range(self.n_estimators)
        ]        

    def generate_path_information(self):
        """生成路径标识符和内部节点索引，用于描述树的分裂逻辑。"""
        path_identifier_list = []
        internal_node_index_list = []

        for leaf_index in range(self.leaf_node_num):
            for current_depth in range(1, self.depth + 1):
                # 标识路径方向（左分支=0, 右分支=1）
                path_identifier = ((leaf_index // (2 ** (self.depth - current_depth))) % 2)
                path_identifier_list.append(path_identifier)

                # 标识路径中的内部节点索引
                internal_node_index = (2 ** (current_depth - 1) - 1) + (
                    leaf_index // (2 ** (self.depth - current_depth + 1))
                )
                internal_node_index_list.append(internal_node_index)

        path_identifier_list = (
            torch.tensor(path_identifier_list)
            .view(self.leaf_node_num, self.depth)
            .float()
        )
        internal_node_index_list = (
            torch.tensor(internal_node_index_list)
            .view(self.leaf_node_num, self.depth)
            .long()
        )

        for name, tensor in zip(["path_identifier_list", "internal_node_index_list"], [path_identifier_list, internal_node_index_list]):
            self.register_buffer(name, tensor)


    def init_model_parameters(self):
        """初始化模型中的所有可训练参数。"""
        # 叶节点存储的类别或回归值
        leaf_classes_array_shape = (self.n_estimators, self.leaf_node_num, self.output_size)
        self.leaf_classes_array = nn.Parameter(
            torch.randn(*leaf_classes_array_shape), requires_grad=True
        )

        # 每个内部节点的分裂阈值
        self.split_values = nn.Parameter(
            torch.randn(self.n_estimators, self.internal_node_num, self.n_selected_features),
            requires_grad=True
        )

        # 每个内部节点的分裂特征索引
        self.split_index_array = nn.Parameter(
            torch.randn(self.n_estimators, self.internal_node_num, self.n_selected_features),
            requires_grad=True,
        )

        # 集成权重
        self.estimator_weights = nn.Parameter(
            torch.randn(self.n_estimators, self.leaf_node_num), requires_grad=True
        )


    def apply_dropout_leaf(self, index_array: torch.Tensor) -> torch.Tensor:
        """
        对输入张量 `index_array` 应用 dropout，模拟叶节点的稀疏性。

        Args:
            index_array (torch.Tensor): 输入张量，形状为 (batch_size, num_leaves)。
            training (bool): 当前模式是否为训练模式。

        Returns:
            torch.Tensor: 经过 dropout 和归一化处理的张量。
        """
        if self.training and self.dropout_rate > 0.0:
            # 应用 dropout（按指定 rate 丢弃元素）并重新缩放
            index_array = F.dropout(index_array, p=self.dropout_rate, training=True) * (1 - self.dropout_rate)
            # 对每个样本的叶节点概率进行归一化
            index_array = index_array / index_array.sum(dim=1, keepdim=True)
        return index_array
    

    def forward(self, inputs: torch.Tensor, onehot=None):
        # inputs = inputs.to(torch.float32)
        # 选择特征子集
        inputs = inputs.flatten(1)
        X_estimator = torch.stack(
                [inputs[:, idx] for idx in self.estimator_feature_indices], dim=1
            )


        # 路径分裂规则计算
        split_index_array = entmax15(self.split_index_array)  # 稀疏化分裂概率
        split_index_array = split_index_array - split_index_array.detach() + F.one_hot(
            split_index_array.argmax(dim=-1), num_classes=split_index_array.size(-1)
        )

        # 计算节点输出
        s1_sum = torch.einsum("ein,ein->ei", self.split_values, split_index_array)
        s2_sum = torch.einsum("ben,ein->bei", X_estimator, split_index_array)
        node_result = (torch.tanh(s1_sum - s2_sum) + 1) / 2

        # 离散化节点决策（ST操作）
        node_result_corrected = node_result - node_result.detach() + node_result.round()

        # 路径概率计算
        node_result_extended = node_result_corrected[:, :, self.internal_node_index_list]
        p = torch.prod(
            ((1 - self.path_identifier_list) * node_result_extended
            + self.path_identifier_list * (1 - node_result_extended)),
            dim=-1,
        )

        # 计算叶节点加权
        estimator_weights_leaf = torch.einsum("el,bel->be", self.estimator_weights, p)
        estimator_weights_leaf_softmax = F.softmax(estimator_weights_leaf, dim=-1)
        estimator_weights_leaf_softmax = self.apply_dropout_leaf(estimator_weights_leaf_softmax)

        # 模型输出
        logits = torch.einsum("elc,bel->bec", self.leaf_classes_array, p)
    
        result = torch.einsum("be,bec->bec", estimator_weights_leaf_softmax, logits).sum(dim=1)
            
        return result


def entmax15(inputs, dim=-1):
    inputs = inputs / 2
    inputs = inputs - inputs.max(dim=dim, keepdim=True).values
    tau_star, support_size = entmax_threshold_and_support(inputs, dim=dim)
    outputs = torch.clamp(inputs - tau_star, min=0) ** 2
    return outputs


def entmax_threshold_and_support(inputs, dim=-1):
    sorted_inputs, _ = torch.sort(inputs, dim=dim, descending=True)
    # k = inputs.size(dim)
    # sorted_inputs_, _ = top_k_over_axis(inputs, k)
    # print((sorted_inputs_ == sorted_inputs).all())
    # rho = torch.arange(1, sorted_inputs.size(dim) + 1, device=inputs.device).view(
    #     *([-1 if i == dim else 1 for i in range(inputs.dim())])
    # )
    rho = make_ix_like(inputs, axis=dim)
    mean = torch.cumsum(sorted_inputs, dim=dim) / rho
    mean_sq = torch.cumsum(sorted_inputs ** 2, dim=dim) / rho
    delta = (1 - rho * (mean_sq - mean ** 2)) / rho
    support_size = (delta > 0).sum(dim=dim, keepdim=True)
    tau_star = mean.gather(dim, support_size - 1)
    return tau_star, support_size


def top_k_over_axis(inputs, k, axis=-1, largest=True, sorted=True):
    """
    在指定轴上获取输入张量的 top-k 值及其索引。

    Args:
        inputs (torch.Tensor): 输入张量。
        k (int): 需要选择的最大值的个数。
        axis (int): 指定的轴。
        largest (bool): 是否选择最大的 k 个值。默认 True。
        sorted (bool): 是否返回排序的结果。默认 True。

    Returns:
        values (torch.Tensor): top-k 值。
        indices (torch.Tensor): top-k 值在原张量中的索引。
    """
    if axis == -1:
        # 如果是最后一个轴，直接调用 torch.topk
        return torch.topk(inputs, k, dim=axis, largest=largest, sorted=sorted)

    # 构造 permute 的顺序，将指定轴移动到最后
    perm_order = list(range(inputs.ndim))
    perm_order.append(perm_order.pop(axis))  # 将指定轴移到最后
    inv_order = [perm_order.index(i) for i in range(len(perm_order))]  # 恢复顺序

    # 转换输入张量的维度
    input_perm = inputs.permute(*perm_order)

    # 在最后一个维度上执行 top-k 操作
    values, indices_perm = torch.topk(input_perm, k, dim=-1, largest=largest, sorted=sorted)

    # 恢复维度顺序
    values = values.permute(*inv_order)
    indices = indices_perm.permute(*inv_order)

    return values, indices


def make_ix_like(inputs, axis=-1):
    """
    生成沿指定轴的索引序列并扩展到与输入张量形状匹配。

    Args:
        inputs (torch.Tensor): 输入张量。
        axis (int): 指定的轴。

    Returns:
        torch.Tensor: 索引张量，其形状与输入张量相兼容。
    """
    assert inputs.ndim is not None, "Input tensor must have a defined number of dimensions."
    size = inputs.size(axis)
    rho = torch.arange(1, size + 1, dtype=inputs.dtype, device=inputs.device)

    # 构造目标形状
    view = [1] * inputs.ndim
    view[axis] = -1

    return rho.view(*view)