from itertools import chain
# import cvxpy as cp
# from cvxpylayers.torch import CvxpyLayer
from typing import Any, Callable, Iterator, Optional, Tuple, Union
from einops import rearrange
import numpy as np
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix, recall_score
import torch
from torch import Tensor, exp, long, tensor
from tqdm import tqdm
from adapter.proceed import Proceed
from core.cst import AllocationType, FC_Type, TaskType, TaskPhase
from core.backtest import backtest
from layers.decomposition import SeriesDecomp
from layers.fast_kan import FastKAN
from layers.noise import GaussianNoise
from core.predictor_config import PredictorConfig
import pytorch_lightning as pl
from pytorch_lightning.core.optimizer import LightningOptimizer
from torch.optim.optimizer import Optimizer
import torch.nn as nn
import torch.nn.functional as F
from core.dot_dict import DotDict as dd
from core.lr_scheduler import ChainedScheduler


class LightningBase(pl.LightningModule):

    def __init__(
        self,
        cfg: PredictorConfig = None,
        is_meta: bool = False,
    ):
        super().__init__()
        self.is_meta = is_meta
        self.set_before_optimizers(cfg)
        self.set_optimizers()
        self.set_after_optimizers()
        if not self.is_resume:
            self.save_hyperparameters('cfg')

    def set_after_optimizers(self):
        ...

    def set_before_optimizers(self, cfg: PredictorConfig = None):
        n_codes = cfg.n_codes
        if cfg is None:
            self.cfg = self.hparams.cfg
            self.is_resume = True
            self.date_dict = cfg.raw_date_dict
        else:
            self.cfg = cfg
            self.is_resume = False
            self.date_dict = cfg.task_date_dict
        self.ckpt_folder = cfg.get_ckpt_folder()
        
                
        if cfg.decompsition.in_use:
            self.decompsition = SeriesDecomp(cfg.decompsition.kernel_size)
        if cfg.is_channel_independent:
            self.model = nn.ModuleList([cfg.get_model(cfg.model_name) for _ in range(cfg.n_seq_features)])
            fc_input_size = ((self.model[0].__dict__.get("fc_input_size") or cfg.fc_input_size) * cfg.n_seq_features)
        else:
            self.model = cfg.get_model(cfg.model_name)
            fc_input_size = self.model.__dict__.get("fc_input_size") or cfg.fc_input_size
            if cfg.adapter_cfg.in_use:
                self.model = Proceed(self.model, cfg)
        if cfg.backbone_with_fc:
            self.fc = None
        else:
            fc_input_size += cfg.concat_prediction * n_codes
            if cfg.fc_enum == FC_Type.KAN:
                self.fc = FastKAN([fc_input_size, cfg.output_size])
            else:
                self.fc = nn.Linear(fc_input_size, cfg.output_size)
                
        self.prev_batch = None

        self.allocator_optimized_count = 0
        if cfg.allocation_enum != AllocationType.Equal:
            weights_pos = cp.Variable(n_codes, nonneg=True)
            weights_neg = cp.Variable(n_codes, nonneg=True)
            # cov_mtx = cp.Parameter((n_codes, n_codes))
            predicted_return_pos = cp.Parameter(n_codes)
            predicted_return_neg = cp.Parameter(n_codes)
            covmat_sqrt_pos = cp.Parameter((n_codes, n_codes))
            covmat_sqrt_neg = cp.Parameter((n_codes, n_codes))
            constraints = [
                cp.sum(weights_pos) + cp.sum(weights_neg) == 1,
                weights_pos <= 1,
                weights_neg <= 1,
                # weights_pos - weights_neg <= .5,
                # weights_pos - weights_neg >= -.5,
                # cp.sum(weights_pos - weights_neg) <= 1,
                # cp.sum(weights_pos - weights_neg) >= -1,
                # cp.norm1(weights) == 1,
                # cp.abs(weights) <= .5,
                ]
            pnl = predicted_return_pos @ weights_pos - predicted_return_neg @ weights_neg
            entropy = cp.sum(cp.entr(weights_pos)) + cp.sum(cp.entr(weights_neg))
            # total_var = cp.quad_form(weights, covmat_sqrt, assume_PSD=True)
            total_var = cp.sum_squares(covmat_sqrt_pos @ weights_pos) + cp.sum_squares(covmat_sqrt_neg @ weights_neg)
            if cfg.allocation_enum == AllocationType.Sharpe:
                objective = cp.Maximize(pnl - total_var * 0.1)
                parameters = [predicted_return_pos, predicted_return_neg, covmat_sqrt_pos, covmat_sqrt_neg]
            elif cfg.allocation_enum == AllocationType.MinVar:
                objective = cp.Minimize(total_var)
                parameters = [covmat_sqrt_pos, covmat_sqrt_neg]
            else:
                objective = cp.Maximize(pnl + entropy)
                parameters = [predicted_return_pos, predicted_return_neg]
            problem = cp.Problem(objective, constraints)
            # parameters = [predicted_return, covmat_sqrt]
            # assert problem.is_dpp()
            self.allocator = CvxpyLayer(problem, parameters=parameters, variables=[weights_pos, weights_neg])
        else:
            self.allocator = None
        
        self.task_enum = cfg.task_enum
        self.is_regression = cfg.is_regression
        self.num_epochs = cfg.num_epochs
        self.batch_size = cfg.batch_size
        # self.automatic_optimization = not cfg.episodic_backward
        self.episodic_loss = tensor(0., device=self.cfg.device, dtype=torch.float32)
        self.num_trained_batch = 0
        self.softmax = nn.Softmax(dim=1)
        self.clf_loss_fn = nn.CrossEntropyLoss(weight=cfg.loss_weights)
        self.bce_loss_fn = nn.BCELoss()
        self.reg_loss_fn = nn.MSELoss()
        self.gaussian_noise = GaussianNoise(cfg.noise_std)
        
        
        
        # self.eps = cfg.eps
        # self.momentum = cfg.momentum
        self.meta = cfg.get_meta_model()
        self.meta_thr = cfg.meta_thr
        self.best_thr = np.zeros(cfg.num_classes)
        self.loss_count = 0

        self.task_phase = TaskPhase.TRAINING
        self.loss_dict = dd(
            mkm=[],
            pfl=[],
            drt=[],
            cta=[],
            mle=[],
            mad=[],
            mse=[],
            bce=[],
            cum_mad=[],            
            cum_mse=[],
            cum_bce=[],
            kl=[],
            dsm=[],
            all=[]
        )
        self.step_dict = dd()
        self.metric_dict = dd()
        for phase in ['train', 'val', 'test']:
            self.step_dict[phase] = dd(
                loss=[],
                pred=[],
                label=[],
                actual=[],
                meta_score=[],
                pnl=[],
                fee=[],
                end_position=[],
                abs_position=[],          
                filled_position=[],
                asset_return=[],
                high_return=[],
                low_return=[],
                final_position=[],
                stop_loss_range=[],
                profit_position=[],
                profit_range=[],                
                final_filled_return=[],
                is_market=[],
                is_filled=[],
                is_stop_loss=[],
                is_take_profit=[],
                stop_loss_ratio=[],
                take_profit_ratio=[],
                stop_loss_ratio_per_code=[],
                is_all_stop_loss=[],
                all_stop_loss_ratio=[],
                spread=[],
                spread_pnl=[],
                spread_fee=[],
                spread_position=[],
            )
            self.metric_dict[phase] = dd(
                acc=[],
                cum_acc=[],
                cum_pnl=[],
                sharpe=[],
            )


    def train_meta(self, feature: Tensor | np.ndarray, label: Tensor | np.ndarray, reg: Tensor | np.ndarray = None):
        score = self.get_clf_score(feature, reg)
        if isinstance(feature, Tensor):
            feature = feature.cpu().detach().numpy()
        if isinstance(label, Tensor):
            label = label.cpu().detach().numpy()
        meta_x, _ = self.get_meta_x(score, feature)
        meta_label = abs(label - 1)
        self.meta.fit(meta_x, meta_label)


    def get_meta_x(self, score: np.ndarray, x: np.ndarray) -> np.ndarray:
        highest_recall_argmax = np.argmax(score - self.best_thr, axis=1)
        highest_recall_onehot = np.eye(self.cfg.num_classes)[highest_recall_argmax]
        meta_x = np.concatenate([x.reshape(x.shape[0], -1), highest_recall_onehot], axis=1)
        return meta_x, highest_recall_argmax
    

    def get_clf_score(self, feature: Tensor | np.ndarray, reg: Tensor | np.ndarray = None) -> np.ndarray:
        device = self.clf_layer.weight.device
        if reg is None:
            if isinstance(feature, np.ndarray):
                feature = tensor(feature, device=self.model.weight.device, dtype=torch.float32)
            reg = self.predict(feature)
        elif isinstance(reg, np.ndarray):
            reg = tensor(reg, dtype=torch.float32)
        reg = reg.to(device).reshape(reg.shape[0], -1)
        logit = self.clf_layer(reg)
        score = self.softmax(logit).cpu().detach().numpy()
        return score


    def make_meta_clf(self, feature: Tensor | np.ndarray, reg: Tensor | np.ndarray = None) -> np.ndarray:
        score = self.get_clf_score(feature, reg)
        if isinstance(feature, Tensor):
            feature = feature.cpu().detach().numpy()
        meta_x, highest_recall_clf = self.get_meta_x(score, feature)
        # meta_pred = self.meta.predict(meta_x)
        meta_score = self.meta.predict_proba(meta_x)
        meta_score[:, 1] -= self.meta_thr
        meta_pred = np.argmax(meta_score, axis=1)
        result = meta_pred * (highest_recall_clf - 1) + 1
        return result, meta_score
    

    def get_highest_recall_threshold(self, feature: Tensor | np.ndarray, label: Tensor | np.ndarray, reg: Tensor | np.ndarray = None):
        score = self.get_clf_score(feature, reg)

        rough_dict = dd()
        rough_rg = np.arange(0., 1., 0.05)
        
        def get_thr_arr(thr_list_str):
            thr_arr = np.array([float(x) for x in thr_list_str[1:-1].split(',')])
            return thr_arr - thr_arr.min()
        
        num_classes = self.cfg.num_classes
        func = max        
        # func = min
        if num_classes == 2:
            sign = -1
            labels = [0, 1]
            for thr0 in tqdm(rough_rg):
                for thr1 in rough_rg:
                    if thr0 == thr1 > 0:
                        continue
                    thr_list = [thr0, thr1]
                    thr = np.array(thr_list)
                    thr = thr - thr.min()
                    clf_argmax = np.argmax(score - thr, axis=1)
                    recall = recall_score(label, clf_argmax, labels=labels, average='weighted')
                    rough_dict[str(thr_list)] = recall
            rough_thr_list_str = func(rough_dict, key=lambda k: 10000 * rough_dict[k] + sign * get_thr_arr(k).sum())
            rough_recall = rough_dict[rough_thr_list_str]
            rough_thr = get_thr_arr(rough_thr_list_str)
            if not self.cfg.use_presice_threshold:
                best_recall = rough_recall
                self.best_thr = rough_thr
                print(f'{self.best_thr = }\n{best_recall = :.5f}\n{labels = }')
                return self.best_thr
            else:
                print(f'{rough_thr = }\n{rough_recall = :.5f}\n{labels = }')

            precise_dict = dd()
            precise_rg_list = []
            for v in rough_thr:
                precise_rg_list.append(np.arange(v - .05, v + .05, 0.01))
            
            for thr0 in tqdm(precise_rg_list[0]):
                for thr1 in precise_rg_list[1]:
                    if thr0 == thr1 > 0:
                        continue
                    thr_list = [thr0, thr1]
                    thr = np.array(thr_list)
                    thr = thr - thr.min()
                    clf_argmax = np.argmax(score - thr, axis=1)
                    recall = recall_score(label, clf_argmax, labels=labels, average='weighted')
                    precise_dict[str(thr_list)] = recall
                        
            best_thr_list_str = func(precise_dict, key=lambda k: 10000 * precise_dict[k] + sign * get_thr_arr(k).sum())
            best_recall = precise_dict[best_thr_list_str]
            best_thr = get_thr_arr(best_thr_list_str)

        elif num_classes == 3:
            if func == max:
                slc = slice(None, None, 2)            
                sign = -1
            else:
                slc = slice(1, 2, 1)            
                sign = 1
            labels = [0, 1, 2][slc]
            for thr0 in tqdm(rough_rg):
                for thr1 in rough_rg:
                    for thr2 in rough_rg:
                        if thr0 == thr1 == thr2 > 0:
                            continue
                        thr_list = [thr0, thr1, thr2]
                        thr = np.array(thr_list)
                        thr = thr - thr.min()
                        clf_argmax = np.argmax(score - thr, axis=1)
                        recall = recall_score(label, clf_argmax, labels=labels, average='weighted')
                        rough_dict[str(thr_list)] = recall
            rough_thr_list_str = func(rough_dict, key=lambda k: 10000 * rough_dict[k] + sign * get_thr_arr(k)[slc].sum())
            rough_recall = rough_dict[rough_thr_list_str]
            rough_thr = get_thr_arr(rough_thr_list_str)
            if not self.cfg.use_presice_threshold:            
                best_recall = rough_recall
                self.best_thr = rough_thr
                print(f'{self.best_thr = }\n{best_recall = :.5f}\n{labels = }')
                return self.best_thr
            else:
                print(f'{rough_thr = }\n{rough_recall = :.5f}\n{labels = }')

            precise_dict = dd()
            precise_rg_list = []
            for v in rough_thr:
                precise_rg_list.append(np.arange(v - .05, v + .05, 0.01))
            
            for thr0 in tqdm(precise_rg_list[0]):
                for thr1 in precise_rg_list[1]:
                    for thr2 in precise_rg_list[2]:
                        if thr0 == thr1 == thr2 > 0:
                            continue                    
                        thr_list = [thr0, thr1, thr2]
                        thr = np.array(thr_list)
                        thr = thr - thr.min()
                        clf_argmax = np.argmax(score - thr, axis=1)
                        recall = recall_score(label, clf_argmax, labels=labels, average='weighted')
                        precise_dict[str(thr_list)] = recall
                        
            best_thr_list_str = func(precise_dict, key=lambda k: 10000 * precise_dict[k] + sign * get_thr_arr(k)[slc].sum())
            best_recall = precise_dict[best_thr_list_str]
            best_thr = get_thr_arr(best_thr_list_str)

        self.best_thr = best_thr
        print(f'{self.best_thr = }\n{best_recall = :.5f}\n{labels = }')
        return self.best_thr


    def mad_loss(self, y_pred: Tensor, y_true: Tensor) -> Tensor:
        return torch.mean(torch.sign(-y_true * y_pred) * torch.abs(y_true))


    def forward(self, x):
        input_dim = self.cfg.input_size
        n_model_feature = self.cfg.n_seq_features
        if self.cfg.decompsition.in_use:
            seasonal_init, trend_init = self.decompsition(x)
            if self.cfg.is_channel_independent:
                if not self.cfg.decompsition.trend_only:
                    seasonal_output = []
                trend_output = []
                for i in range(n_model_feature):
                    if not self.cfg.decompsition.trend_only:
                        seasonal = self.model[i](
                            seasonal_init[..., i * input_dim: (i+1) * input_dim])
                        seasonal_output.append(seasonal)
                    trend = self.model[i](
                        trend_init[..., i * input_dim: (i+1) * input_dim])
                    trend_output.append(trend)
                output = torch.cat(trend_output, dim=-1)
                if not self.cfg.decompsition.trend_only:
                    output += torch.cat(seasonal_output, dim=-1)
                output = self.fc(output)

            else:
                if self.cfg.decompsition.concat:
                    init = torch.cat([seasonal_init, trend_init], dim=-1)
                    if self.cfg.decompsition.with_original:
                        init = torch.cat([x, init], dim=-1)
                    return self.model(init)
                if not self.cfg.decompsition.trend_only:
                    seasonal_output = self.model(seasonal_init)
                trend_output = self.model(trend_init)

                output = trend_output

                if not self.cfg.decompsition.trend_only:
                    output += seasonal_output

        else:
            if self.cfg.is_channel_independent:
                output = []
                for i in range(n_model_feature):
                    out = self.model[i](x[..., i * input_dim: (i+1) * input_dim])
                    output.append(out)
                output = self.fc(torch.cat(output, dim=-1))
            else:
                output = self.model(x)

        return output
    

    # def optimizer_zero_grad(self, epoch: int, batch_idx: int, optimizer: Optimizer) -> None:
    #     if not self.cfg.episodic_backward:
    #         super().optimizer_zero_grad(epoch, batch_idx, optimizer)

    # def backward(self, loss: Tensor, *args: Any, **kwargs: Any) -> None:
    #     if not self.cfg.episodic_backward:
    #         super().backward(loss, *args, **kwargs)        
            

    def training_step(self, batch: tuple[Tensor, Tensor], batch_idx):
        self.num_trained_batch += 1
        return self.common_step(batch, 'train')


    def common_step(self, batch: tuple[Tensor, Tensor, Tensor, Tensor], phase: str = 'train') -> Tensor:
        cfg = self.cfg
        batch_x, batch_y, batch_z, non_seq, image = self.unpack(batch)
        # if torch.isnan(batch_x).any():
        #     print(f"batch_x is nan in {phase}\n{batch_x = }")
        
        # if torch.isnan(batch_y).any():
        #     print(f"batch_y is nan in {phase}\n{batch_y = }")

        output = self.get_output(batch_x, batch_z, non_seq, image)
        output, reg = self.unpack_output(output)

        logits, pred, loss = self.calc_pred_loss(output, batch_x, batch_y, phase)

        loss = loss + reg

        if phase == 'predict':
            return pred
        
        if cfg.adapter_cfg.in_use:
            # y_to_cat = batch_y[..., 0: 1].repeat(1, 1, batch_x.shape[-1])
            # self.model.recent_batch = torch.cat([batch_x, y_to_cat], 1)
            self.model.recent_batch = batch_x
            # prev_batch = dd(
            #     x=batch_x,
            #     y=batch_y,
            #     z=batch_z,
            #     non_seq=non_seq,
            # )

            # if self.prev_batch is not None:
            #     prev_x = self.prev_batch.x
            #     prev_y = self.prev_batch.y
            #     prev_z = self.prev_batch.z
            #     # prev_non_seq = self.prev_batch.non_seq
            #     prev_output = self.get_output(prev_x, prev_z, self.prev_batch.non_seq)
            #     prev_output, reg = self.unpack_output(prev_output)
            #     loss = self.calc_pred_loss(prev_output, prev_x, prev_y, phase, save_to_step_dict=False)[-1] + reg
            # else:
            #     loss = 0
            # self.prev_batch = prev_batch
    

        loss_count = len(batch_x)
        self.step_dict[phase].loss.append(loss.item())
        self.step_dict[phase].actual.append(batch_y[..., 0].cpu().detach().numpy())
        if self.task_enum not in [TaskType.MarketMakingPortfolio]:
            pred_arr = pred.cpu().detach().numpy()
            # if self.task_enum == TaskType.Classification:
            #     pred_arr = pred_arr.argmax(axis=-1)
            self.step_dict[phase].pred.append(pred_arr)
        if phase == 'train' and cfg.episodic_backward:
            # self.loss_dict.all.append(loss)
            self.episodic_loss += loss * loss_count
            self.loss_count += loss_count
        return loss

    def unpack_output(self, output):
        if isinstance(output, tuple):
            output, reg = output
        else:
            reg = 0
        return output,reg
    
    
    def get_allocation(self, logits: Tensor, score: Tensor, history_return: Tensor) -> Tensor:
        cfg = self.cfg
        # if cfg.score_as_predicted_return:
        #     predicted_return = score
        # else:
        #     predicted_return = score * asset_return
        predicted_return_pos = logits[:, 0]
        predicted_return_neg = logits[:, 1]
        covmat_sqrt_pos = history_return.cov().sqrt()
        covmat_sqrt_neg = (-history_return).cov().sqrt()
        if cfg.allocation_enum == AllocationType.Equal:
            return score
        elif cfg.allocation_enum == AllocationType.Sharpe:
            result = self.allocator(predicted_return_pos, predicted_return_neg, covmat_sqrt_pos, covmat_sqrt_neg)
        elif cfg.allocation_enum == AllocationType.MinVar:
            result = self.allocator(covmat_sqrt_pos, covmat_sqrt_neg)
        elif cfg.allocation_enum == AllocationType.MaxReturn:
            result = self.allocator(predicted_return_pos, predicted_return_neg)
        else:
            raise ValueError(f'Invalid allocation type: {cfg.allocation_enum}')
        if len(result) == 2 and isinstance(result, tuple):
            result = result[0] - result[1]
            result = result / result.abs().sum(dim=0, keepdim=True)
        return result


    def get_output(self, batch_x: Tensor, batch_z: Tensor, non_seq: Tensor = None, image: Tensor = None) -> Tensor:
        cfg = self.cfg
        if cfg.feature_cfg.adapt:
            batch_x = self.model.mapper_x(batch_x)
        if len(cfg.non_seq_column_idx_list) == 0:
            non_seq = None
        if not cfg.backbone_with_fc:
            output = self(batch_x)
            if cfg.concat_prediction:
                output = torch.cat([output, batch_z.flatten(1)], dim=-1)
            if non_seq is not None:
                output = torch.cat([output, non_seq], dim=-1)
            output = self.fc(output)
        else:
            if cfg.concat_prediction:
                output = self.model(batch_x, non_seq, batch_z.flatten(1))
            elif cfg.feature_cfg.image:
                output = self.model(batch_x, non_seq, image)
            else:
                output = self.model(batch_x, non_seq)

        return output


    def unpack(self, batch: tuple[Tensor]) -> tuple[Tensor, Tensor, Tensor, Tensor]:
        for i in range(len(batch)):
            if isinstance(batch[i], np.ndarray) and batch[i] is not None:
                batch[i] = torch.from_numpy(batch[i]).to(self.cfg.device)
        batch_x, batch_y, batch_z, non_seq, image = batch

        if len(batch_x.shape) == 4:
            batch_x = rearrange(batch_x, 'b t s c -> b t (s c)')

        # if len(batch_y.shape) != 3 and self.task_enum != TaskType.MarketMaking:
        #     batch_y = batch_y.reshape(batch_y.shape[0], -1, self.cfg.n_codes)
        
        if self.cfg.noise_std != 0:
            batch_x = self.gaussian_noise(batch_x)

        return batch_x, batch_y, batch_z, non_seq, image

    def calc_pred(self, output: Tensor) -> Tensor:
        raise NotImplementedError()
    
    def calc_pred_loss(self, pred: Tensor, batch_x: Tensor, batch_y: Tensor, phase: str = 'train'):
        raise NotImplementedError()

    

    def validation_step(self, batch, batch_idx):
        return self.common_step(batch, 'val')


    def test_step(self, batch, batch_idx):
        return self.common_step(batch, 'test')


    def predict_step(self, batch, batch_idx=0, dataloader_idx=0):
        return self.common_step(batch, 'predict')


    def predict(self, batch_x, batch_z=None) -> Tensor:
        batch = [batch_x, None, batch_z]
        return self.predict_step(batch).flatten()


    def set_optimizers(self):
        cfg = self.cfg
        self.lr = cfg.learning_rate
        weight_decay = cfg.weight_decay
        optimizer_enum = cfg.optimizer_enum
        model_params = self.get_params()
        self.optimizer = optimizer_enum.value(
            params=model_params,
            lr=self.lr,
            # eps=self.eps,
            weight_decay=weight_decay,
            # momentum=self.momentum
        )


    def freeze_parameters(self):
        cfg = self.cfg
        if self.allocator is not None:
            for param in self.model.para_net_list.parameters():
                param.requires_grad = False
            # self.lr = cfg.learning_rate
            # weight_decay = cfg.weight_decay
            # optimizer_enum = cfg.optimizer_enum
            # allocator_params = self.allocator.parameters()
            # for param in allocator_params:
            #     print(f'{param = }')
            # self.optimizer = optimizer_enum.value(
            #     params=allocator_params,
            #     lr=self.lr,
            #     # eps=self.eps,
            #     weight_decay=weight_decay,
            #     # momentum=self.momentum
            # )
        if cfg.fine_tune:
            for param in self.model.conv.parameters():
                param.requires_grad = False
            self.lr = cfg.lr.fine_tune
            weight_decay = cfg.weight_decay
            self.optimizer = cfg.optimizer_enum.value(
                params=self.model.fc.parameters(),
                lr=self.lr,
                # eps=self.eps,
                weight_decay=weight_decay,
                # momentum=self.momentum
            )        


    def configure_optimizers(self):
        cfg = self.cfg
        reduce_cfg = cfg.reduce_lr_on_plateau
        if cfg.cosine_lr_scheduler:
            scheduler = ChainedScheduler(self.optimizer, T_0=15, T_mul=1,
                                             eta_min=2e-5, last_epoch=-1, max_lr=self.lr,
                                             warmup_steps=10, gamma=1.,
                                             coef=1., step_size=3, cosine_period=4)
            return {
                'optimizer': self.optimizer,
                'lr_scheduler': {
                   'scheduler': scheduler,
                   'monitor': cfg.monitor
                }
            }
        elif reduce_cfg.in_use and cfg.resume_from_ckpt:
            return {
                'optimizer': self.optimizer,
                'lr_scheduler': {
                   'scheduler': torch.optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, factor=reduce_cfg.factor, patience=reduce_cfg.patience, verbose=True),
                   'monitor': cfg.monitor
                }
            }
        else:
            return self.optimizer

    
    def get_named_params(self) -> Iterator[Tuple[str, nn.Parameter]]:
        if self.fc is not None:
            named_params = chain(self.model.named_parameters(), self.fc.named_parameters())
        else:
            named_params = self.model.named_parameters()
        
        return named_params
    
    def get_params(self) -> Iterator[nn.Parameter]:
        if self.fc is not None:
            params = chain(self.model.parameters(), self.fc.parameters())
        else:
            params = self.model.parameters()
        total_params = sum(p.numel() for p in self.model.parameters())
        # 检查conv部分参数量
        print(f'# {total_params = }')
        if hasattr(self.model, 'conv') and self.model.conv is not None:
            conv_params = sum(p.numel() for p in self.model.conv.parameters())
            print(f'# {conv_params = }')
        return params
    
    def __update_all_lr(self):
        start_lr = self.cfg.learning_rate
        self.lr = start_lr * (self.cfg.lr_decay ** self.current_epoch)
        # SETTING the new LR
        for g in self.optimizer.param_groups:
            g['lr'] = self.lr
        print(f'Updating learning rate to {self.lr:.8f}')

    # def on_train_epoch_start(self) -> None:
    #     self.cfg.selected_phase = 'train'
    #     super().on_train_epoch_start()

    # def on_validation_epoch_start(self) -> None:
    #     self.cfg.selected_phase = 'val'
    #     super().on_validation_epoch_start()

    def on_validation_model_eval(self) -> None:        
        if self.cfg.episodic_backward:
            self.episodic_loss /= self.loss_count
            self.backward(self.episodic_loss)
            self.optimizer.step()
            self.optimizer.zero_grad()
            self.loss_count = 0
            self.episodic_loss = torch.tensor(0.0, device=self.cfg.device)
        super().on_validation_model_eval()


    def on_train_epoch_end(self) -> None:
        # if self.cfg.episodic_backward:
        #     self.optimizer.step()
        #     self.optimizer.zero_grad()
        self.on_phase_end('train', True)
        super().on_train_epoch_end()
    # def on_test_epoch_end(self) -> None:
    #     self.__on_validation_and_testing_end('test', True)
        
    def on_validation_epoch_end(self) -> None:
        self.on_phase_end('val', True)
        super().on_validation_epoch_end()
        # self.__update_all_lr()

    def on_test_end(self) -> None:
        self.on_phase_end('test')
        super().on_test_end()


    def on_phase_end(self, phase: str, is_epoch_end: bool = False) -> None:
        loss = np.mean(self.step_dict[phase].loss)
        epoch_str = f'Epoch {self.current_epoch}' 
        if not is_epoch_end:
            epoch_str = 'Final ' + epoch_str
        print(f'\n{epoch_str} {phase} net loss: {loss:.5f}')
        pred = np.concatenate(self.step_dict[phase].pred)
        shape = pred.shape
        actual = np.concatenate(self.step_dict[phase].actual).reshape(shape[0], -1)
        self.process_epoch(loss, pred, actual, phase, epoch_str)
        for k in list(self.step_dict[phase].keys()):
            self.step_dict[phase][k] = []
        # if phase == 'val' and is_epoch_end:
        #     self.trainer.fit_loop.epoch_progress.current.completed += 1

    def process_epoch(self, loss: np.ndarray, pred: np.ndarray, actual: np.ndarray, phase: str, epoch_str: str) -> None:
        raise NotImplementedError()
    

    # def backtest_top_k(self, epoch_df, phase: str):
        ...


def get_meta_win_rate_dict(actual: np.ndarray, pred: np.ndarray, meta_score: np.ndarray, num_bins: int = 20, num_classes: int = 3):
    # meta_score 属于 [0, 1] 计算meta score的win_rate分布
    short = 0
    long = num_classes - 1
    bin_size = 1 / num_bins    
    win_list = [0 for _ in range(num_bins)]
    trade_list = [0 for _ in range(num_bins)]
    meta_score = meta_score[:, 1].flatten()
    for si, score in enumerate(meta_score):
        bin_idx = int(score // bin_size)
        win = (pred[si] == long) and (actual[si] == long) or (pred[si] == short) and (actual[si] == short)
        loss = (pred[si] == long) and (actual[si] == short) or (pred[si] == short) and (actual[si] == long)
        trade = (score > 0.5)
        if win and trade:
            win_list[bin_idx] += 1
        if (win or loss) and trade:
            trade_list[bin_idx] += 1
    
    win_arr = np.array(win_list)
    trade_arr = np.array(trade_list)
    trade_rate_arr = trade_arr / sum(trade_arr)
    win_rate_arr = np.where(trade_arr == 0, 0, (win_arr / trade_arr))
    bins_str = [f'{i * bin_size:.3f}-{(i + 1) * bin_size:.3f}' for i in range(num_bins)]
    trade_win_rate = list(zip((round(r, 4) for r in trade_rate_arr), (round(r, 4) for r in win_rate_arr)))
    win_rate_dict = dict(zip(bins_str, trade_win_rate))
    win_rate_dict = {k: v for k, v in win_rate_dict.items() if v[0] + v[1] > 0}
    return win_rate_dict
            
        