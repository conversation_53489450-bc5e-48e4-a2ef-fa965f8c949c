import pandas as pd
import numpy as np
import polars as pl
import matplotlib.pyplot as plt
import yfinance as yf
from datetime import datetime, timedelta
from two_way_market_maker_limit import TwoWayMarketMakerLimit

def fetch_data(symbol, start_date, end_date, interval='1h'):
    """
    Fetch historical data from Yahoo Finance
    
    Parameters:
    -----------
    symbol : str
        Ticker symbol
    start_date : str
        Start date in YYYY-MM-DD format
    end_date : str
        End date in YYYY-MM-DD format
    interval : str
        Data interval (1m, 2m, 5m, 15m, 30m, 60m, 90m, 1h, 1d, 5d, 1wk, 1mo, 3mo)
        
    Returns:
    --------
    pd.DataFrame
        Historical price data
    """
    data = yf.download(symbol, start=start_date, end=end_date, interval=interval)
    data.reset_index(inplace=True)
    data.columns = [col.lower() for col in data.columns]
    return data

def generate_synthetic_data(days=30, freq='1h', volatility=0.01, trend=0.0001):
    """
    Generate synthetic price data for testing
    
    Parameters:
    -----------
    days : int
        Number of days to generate
    freq : str
        Data frequency (e.g., '1h', '15min')
    volatility : float
        Price volatility
    trend : float
        Price trend factor
        
    Returns:
    --------
    pd.DataFrame
        Synthetic price data
    """
    # Calculate number of periods
    if freq.endswith('h'):
        hours = int(freq[:-1])
        periods = int(24 / hours * days)
    elif freq.endswith('min'):
        minutes = int(freq[:-3])
        periods = int(24 * 60 / minutes * days)
    else:
        periods = days
    
    # Generate datetime index
    end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    start_date = end_date - timedelta(days=days)
    dates = pd.date_range(start=start_date, end=end_date, periods=periods)
    
    # Generate price data
    np.random.seed(42)  # For reproducibility
    returns = np.random.normal(trend, volatility, periods)
    prices = 100 * (1 + np.cumsum(returns))
    
    # Create DataFrame
    data = pd.DataFrame({
        'datetime': dates,
        'open': prices * (1 + np.random.normal(0, volatility/2, periods)),
        'high': prices * (1 + np.abs(np.random.normal(0, volatility, periods))),
        'low': prices * (1 - np.abs(np.random.normal(0, volatility, periods))),
        'close': prices,
        'volume': np.random.randint(1000, 100000, periods)
    })
    
    # Ensure high is the highest and low is the lowest
    data['high'] = data[['open', 'close', 'high']].max(axis=1)
    data['low'] = data[['open', 'close', 'low']].min(axis=1)
    
    return data

def run_example_with_real_data():
    """Run example with real data from Yahoo Finance"""
    # Fetch data
    print("Fetching data from Yahoo Finance...")
    data = fetch_data(
        symbol="BTC-USD",
        start_date=(datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d'),
        end_date=datetime.now().strftime('%Y-%m-%d'),
        interval='1h'
    )
    
    # Initialize strategy
    strategy = TwoWayMarketMakerLimit(
        trade_direction="Both",
        use_atr=True,
        atr_period=14,
        atr_multiplier=1.0,
        fixed_percent=0.01,
        cooldown_period=6,
        initial_position_pct=0.01,
        scale_in_coef=1.5,
        max_scale_in=5,
        take_profit_pct=0.02,
        inventory_decay=0.95,
        commission_pct=0.001
    )
    
    # Run backtest
    print("Running backtest...")
    results = strategy.run_backtest(data)
    
    # Analyze performance
    metrics = strategy.analyze_performance(results)
    strategy.print_performance_summary(metrics)
    
    # Plot results
    strategy.plot_results(results)

def run_example_with_synthetic_data():
    """Run example with synthetic data"""
    # Generate synthetic data
    print("Generating synthetic data...")
    data = generate_synthetic_data(days=30, freq='1h', volatility=0.005, trend=0.0001)
    
    # Initialize strategy
    strategy = TwoWayMarketMakerLimit(
        trade_direction="Both",
        use_atr=False,
        fixed_percent=0.005,
        cooldown_period=3,
        initial_position_pct=0.02,
        scale_in_coef=1.2,
        max_scale_in=3,
        take_profit_pct=0.01,
        inventory_decay=0.98,
        commission_pct=0.0002
    )
    
    # Run backtest
    print("Running backtest...")
    results = strategy.run_backtest(data)
    
    # Analyze performance
    metrics = strategy.analyze_performance(results)
    strategy.print_performance_summary(metrics)
    
    # Plot results
    strategy.plot_results(results)

def run_example_with_multiple_symbols():
    """Run example with multiple symbols"""
    # Fetch data for multiple symbols
    print("Fetching data for multiple symbols...")
    symbols = ["BTC-USD", "ETH-USD"]
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    end_date = datetime.now().strftime('%Y-%m-%d')
    
    all_results = {}
    all_metrics = {}
    
    for symbol in symbols:
        print(f"Processing {symbol}...")
        data = fetch_data(symbol, start_date, end_date, interval='1h')
        
        # Add symbol column
        data['symbol'] = symbol
        
        # Initialize strategy
        strategy = TwoWayMarketMakerLimit(
            trade_direction="Both",
            use_atr=True,
            atr_period=14,
            atr_multiplier=0.8,
            fixed_percent=0.01,
            cooldown_period=4,
            initial_position_pct=0.01,
            scale_in_coef=1.3,
            max_scale_in=4,
            take_profit_pct=0.015,
            inventory_decay=0.97,
            commission_pct=0.001
        )
        
        # Run backtest
        results = strategy.run_backtest(data)
        all_results[symbol] = results
        
        # Analyze performance
        metrics = strategy.analyze_performance(results)
        all_metrics[symbol] = metrics
        
        # Print performance summary
        print(f"\nPerformance Summary for {symbol}:")
        strategy.print_performance_summary(metrics)
        
        # Plot results
        strategy.plot_results(results)
    
    # Compare performance across symbols
    print("\nPerformance Comparison:")
    comparison = pd.DataFrame({
        symbol: {
            'Total Return': metrics['total_return'],
            'Annual Return': metrics['annual_return'],
            'Sharpe Ratio': metrics['sharpe_ratio'],
            'Max Drawdown': metrics['max_drawdown'],
            'Total Trades': metrics['total_trades'],
            'Win Rate': metrics['win_rate']
        } for symbol, metrics in all_metrics.items()
    })
    
    print(comparison)

if __name__ == "__main__":
    print("Two-Way Market Maker Strategy Example")
    print("====================================")
    
    # Choose which example to run
    example = input("Choose example to run (1: Real Data, 2: Synthetic Data, 3: Multiple Symbols): ")
    
    if example == "1":
        run_example_with_real_data()
    elif example == "2":
        run_example_with_synthetic_data()
    elif example == "3":
        run_example_with_multiple_symbols()
    else:
        print("Invalid choice. Running with synthetic data by default.")
        run_example_with_synthetic_data()
