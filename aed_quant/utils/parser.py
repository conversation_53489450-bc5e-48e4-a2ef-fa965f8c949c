from typing import Union

import numpy as np
from pandas import DataFrame, to_datetime
from aed_quant.utils.tools import get_cur_timestamp_ms, ts_to_datetime_str


def parse_history_kline(platform: str, raw_data: Union[list, dict], symbol: str, code_idx: int, exclude_timestamp_ms: int = None) -> tuple[DataFrame, int]:
	if 'binance' in platform:
		# data = np.array(raw_data, dtype=np.float32)
		columns = [
			"open_timestamp", "open", "high", "low", "close", "volume", "timestamp", "quote",
			"count", "buy_volume", "buy_quote", "code"  # "ignore"
			]
		df = DataFrame(raw_data, columns=columns)
		n = len(df)
		now_ms = get_cur_timestamp_ms()
		# now_ms = df.open_timestamp[n - 1]
		now_str = ts_to_datetime_str(now_ms / 1000)
		if exclude_timestamp_ms is None:
			exclude_timestamp_ms = this_open_ms = df.open_timestamp[n - 1]
		else:
			this_open_ms = exclude_timestamp_ms
		this_open_str = ts_to_datetime_str(this_open_ms / 1000)
		interval_ms = df.open_timestamp[n - 2] - df.open_timestamp[n - 3]
		next_open_ms = this_open_ms + interval_ms
		next_kline_to_come_in_sec = (next_open_ms - now_ms) / 1000
		print(f"{symbol}, {now_str = }, {this_open_str = }, {next_kline_to_come_in_sec = }")
		# 将df截取为timestamp_ms之前的k线数据
		df = df[df.open_timestamp < exclude_timestamp_ms][-(n - 1):]
		df['open_time'] = to_datetime(df.open_timestamp * 1e6)
		df['code'] = symbol
		df['code_idx'] = code_idx
		df.drop(columns=['timestamp', 'open_timestamp', 'count'], inplace=True)

		return df, exclude_timestamp_ms


def process_kline(df: DataFrame) -> DataFrame:
	# columns = ['open', 'high', 'low', 'close', 'volume', 'quote']
	# if 'buy_volume' in df.columns:
	# 	columns += ['buy_volume', 'buy_quote']
	# df = df[columns].astype(np.float32)
	df.reset_index(inplace=True, drop=True)
	df['avg_price'] = np.where(df['volume'] != 0, df['quote'] / df['volume'], df.close)
	if 'buy_volume' in df.columns:
		df['sell_volume'] = df['volume'] - df['buy_volume']
		df['sell_quote'] = df['quote'] - df['buy_quote']
	return df


def parse_stream_kline(raw_data: Union[list, dict], code_idx: int) -> DataFrame:
	if 'binance' in raw_data.get('platform'):
		df = DataFrame(raw_data, index=[0])
		df['open_time'] = to_datetime(df.open_timestamp * 1e6)
		df.rename(columns={'symbol': 'code'}, inplace=True)
		df.drop(columns=['platform', 'timestamp', 'kline_type', 'is_closed'], inplace=True)
		df['code_idx'] = code_idx
		return df