# -*- coding:utf-8 -*-

"""
some constants

Author: CyberQuant
Date:   2023/06/01
Email:  <EMAIL>
"""


# Exchange Names
BINANCE = "binance"  # Binance https://www.binance.com
BINANCE_FUTURE = "binance_future"  # https://www.binance-cn.com/cn/futures/BTCUSDT
BITFINEX = "bitfinex"
BITMEX = "bitmex"  # BitMEX https://www.bitmex.com/
BITSTAMP = "bitstamp"
COINBASE_PRO = "coinbase_pro"  # Coinbase Pro https://pro.coinbase.com/
COINSUPER = "coinsuper"  # Coinsuper https://www.coinsuper.com/
COINSUPER_PRE = "coinsuper_pre"  # Coinsuper Premium https://premium.coinsuper.com
DERIBIT = "deribit"  # Deribit https://www.deribit.com/
DIGIFINEX = "digifinex"  # Digifinex https://docs.digifinex.vip/
FCOIN = "fcoin"  # Fcoin https://www.fcoin.com/
GATE = "gate"  # Gate.io https://gateio.news/
GEMINI = "gemini"  # Gemini https://gemini.com/
HUOBI = "huobi"  # Huobi https://www.hbg.com/zh-cn/
HUOBI_FUTURE = "huobi_future"  # Huobi Future https://www.hbdm.com/en-us/contract/exchange/
KUCOIN = "kucoin"  # Kucoin https://www.kucoin.com/
KRAKEN = "kraken"  # Kraken https://www.kraken.com
MXC = "mxc"
OKEX = "okex"  # OKEx SPOT https://www.okex.me/spot/trade
OKEX_MARGIN = "okex_margin"  # OKEx MARGIN https://www.okex.me/spot/marginTrade
OKEX_FUTURE = "okex_future"  # OKEx FUTURE https://www.okex.me/future/trade
OKEX_SWAP = "okex_swap"  # OKEx SWAP https://www.okex.me/future/swap


# Market Types
MARKET_TYPE_TRADE = "trade"
MARKET_TYPE_ORDERBOOK = "orderbook"
MARKET_TYPE_KLINE = "kline"
MARKET_TYPE_KLINE_1M = "kline_1m"
MARKET_TYPE_KLINE_3M = "kline_3m"
MARKET_TYPE_KLINE_5M = "kline_5m"
MARKET_TYPE_KLINE_15M = "kline_15m"
MARKET_TYPE_KLINE_30M = "kline_30m"
MARKET_TYPE_KLINE_1H = "kline_1h"
MARKET_TYPE_KLINE_2H = "kline_2h"
MARKET_TYPE_KLINE_4H = "kline_4h"
