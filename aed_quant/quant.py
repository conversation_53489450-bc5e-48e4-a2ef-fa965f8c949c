# -*- coding:utf-8 -*-

"""
Asynchronous event driven quantitative trading framework.

Author: CyberQuant
Date:   2023/06/01
Email:  <EMAIL>
"""
import sys
import os
import signal
import asyncio
from typing import Union

from aed_quant.utils import logger
from aed_quant.config import config


class Quant:
    """ Asynchronous event driven quantitative trading framework.
    """

    def __init__(self):
        self.loop = None
        self.event_center = None
        self.config_module = None

    def initialize(self, config_module: Union[str, dict] = None):
        """ Initialize.

        Args:
            config_module: config file path, normally it"s a json file.
        """
        if self.config_module is None:
            self.config_module = config_module
        self._get_event_loop()
        self._load_settings(self.config_module)
        self._init_logger()
        self._init_event_center()
        self._do_heartbeat()

    def start(self):
        """Start the event loop."""
        def keyboard_interrupt(s, f):
            print("KeyboardInterrupt (ID: {}) has been caught. Cleaning up...".format(s))
            self.loop.stop()
        signal.signal(signal.SIGINT, keyboard_interrupt)

        logger.info("start io loop ...", caller=self)
        self.loop.run_forever()

    def stop(self):
        """Stop the event loop."""
        logger.info("stopping io loop...", caller=self)
        # 取消所有待处理的任务
        # for task in asyncio.all_tasks(self.loop):
            # task.cancel()
        # 停止事件循环
        self.loop.stop()
        # 运行一次以处理取消的任务
        # pending = asyncio.all_tasks(self.loop)
        # self.loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
        # # 现在可以安全地关闭事件循环
        # self.loop.close()
        # logger.info("io loop stopped and closed.", caller=self)

    def start_with_restart(self, hours: float = 24.0, start_cost_in_sec: float = 5.5):
        """启动服务并设置定期重启
        
        Args:
            hours: 重启间隔小时数，默认24小时
        """

        def restart_program():
            """重启当前程序"""
            python = sys.executable
            os.execl(python, python, *sys.argv)

        async def _schedule_restart():
            await asyncio.sleep(hours * 3600 - start_cost_in_sec)  # 转换小时到秒
            logger.info(f"执行计划重启 (间隔: {hours}小时)", caller=self)
            try:
                # 重启程序
                restart_program()
            except Exception as e:
                logger.error(f"重启过程出错: {str(e)}", caller=self)
                # 如果重启失败，等待1分钟后重试
                await asyncio.sleep(60)
                self.loop.create_task(_schedule_restart())

        # 启动定时重启任务
        self.loop.create_task(_schedule_restart())
        logger.info(f"服务已启动，将每{hours}小时自动重启一次", caller=self)
        
        # 启动服务
        self.start()

    def _get_event_loop(self):
        """ Get a main io loop. """
        if not self.loop:
            self.loop = asyncio.get_event_loop()
        return self.loop

    def _load_settings(self, config_module):
        """ Load config settings.

        Args:
            config_module: config file path, normally it"s a json file.
        """
        config.loads(config_module)

    def _init_logger(self):
        """Initialize logger."""
        console = config.log.get("console", True)
        level = config.log.get("level", "DEBUG")
        path = config.log.get("path", "/tmp/logs/Quant")
        name = config.log.get("name", "quant.log")
        clear = config.log.get("clear", False)
        backup_count = config.log.get("backup_count", 0)
        if console:
            logger.initLogger(level)
        else:
            logger.initLogger(level, path, name, clear, backup_count)

    def _init_event_center(self):
        """Initialize event center."""
        if config.rabbitmq:
            from aed_quant.event import EventCenter
            self.event_center = EventCenter()
            self.loop.run_until_complete(self.event_center.connect())

    def _do_heartbeat(self):
        """Start server heartbeat."""
        from aed_quant.heartbeat import heartbeat
        self.loop.call_later(0.5, heartbeat.ticker)


quant = Quant()
