import os
from lightning import LightningModule
from pytorch_lightning.trainer import Trainer
from core.data_module import KlineDataModule
from core.predictor_config import PredictorConfig
import torch
from core.cst import PositionType, TaskType, Optimizers
torch.set_float32_matmul_precision('medium') 


pred_cfg = PredictorConfig()

# pred_cfg.concat_prediction = True
# pred_cfg.concat_hidden = True
# pred_cfg.proba_as_prediction = False
# pred_cfg.feature_category.factor = True
# pred_cfg.feature_category.extra = False
# pred_cfg.backbone_with_fc = False # very different results

# pred_cfg.force_train = False
# pred_cfg.selected_indices = [0, 1, 8, 13, 16, 17, 18]
pred_cfg.set_seed()
pred_cfg.n_codes = 20
# pred_cfg.code_sort_by_quote = True
pred_cfg.monitor = 'mean_pnl_before_fee'
# pred_cfg.monitor = 'mean_pnl_after_fee'
# pred_cfg.position_enum == PositionType.Long = True
pred_cfg.use_softmax = False
# pred_cfg.clip_return.train = True
pred_cfg.limit_margin_per_code = True
# pred_cfg.adapt_distr_shift.val = True
# pred_cfg.adapt_distr_shift.test = True
# pred_cfg.directional_balance = True
# pred_cfg.use_scaler = False
# pred_cfg.scale_per_code = True

# pred_cfg.task_enum = TaskType.Portfolio
pred_cfg.task_enum = TaskType.PortfolioDoubleAdapt
# pred_cfg.optimizer_enum = Optimizers.RMSPROP
pred_cfg.optimizer_enum = Optimizers.ADAM
pred_cfg.sample_pair = True
# pred_cfg.adapt.transform_x = False
pred_cfg.meta_adapt.transform_y = False
# pred_cfg.adapt.num_heads = 8 # bad
# pred_cfg.adapt.inner_copy_initial_weights = True # bad

# pred_cfg.position_enum = PositionType.Short

# pred_cfg.shuffling.train = False
# pred_cfg.shuffling.codewise = True
# pred_cfg.order_shift_scale = 0.001
# pred_cfg.fee_ratio.stop = 0.0004
pred_cfg.stop_loss.learnable = True
# pred_cfg.stop_loss.in_use = True
pred_cfg.stop_loss.scale = 0.01
# pred_cfg.position_punishment.in_use = True
# pred_cfg.stop_loss.min_ratio = 0.02
pred_cfg.dropout_rate = 0.
# pred_cfg.weight_decay = 0.0001 # 0.0001 bad for tsmx
# pred_cfg.noise_std = 0.01 # 0.05 bad
# pred_cfg.use_batch_norm = True
# pred_cfg.use_projection = True
# pred_cfg.embedding_dim = 16

pred_cfg.seq_len = 36
pred_cfg.pred_len = 1
step_size = pred_cfg.outer_step.val = pred_cfg.outer_step.test = pred_cfg.pred_len
if not pred_cfg.shuffling.train:
    pred_cfg.outer_step.train = step_size
pred_cfg.patience = 30
pred_cfg.num_epochs = 30
pred_cfg.batch_size = 42
pred_cfg.learning_rate = 20e-5
pred_cfg.hidden_size = 8 # 1 for episodic_backward, 2 for not
# pred_cfg.num_epochs = 100
# pred_cfg.hidden_size = 8
# pred_cfg.episodic_backward = True
# pred_cfg.batch_size = 64
# pred_cfg.learning_rate = 1000e-5
# pred_cfg.optimize_sharpe = True
# pred_cfg.fee_ratio = 0
pred_cfg.tcn_with_rnn = True
pred_cfg.num_rnn_layers = 2
# pred_cfg.num_heads = 1
# pred_cfg.mhsa_before_rnn = True
# pred_cfg.mhsa_after_rnn = True
# pred_cfg.bidirectional = True
# pred_cfg.rnn_output_last = False
# pred_cfg.kan_as_fc = True

pred_cfg.num_block_convs = 3
pred_cfg.num_tcn_blocks = 2
pred_cfg.num_tcn_stride = 1
# pred_cfg.zigzag_labelling.in_use = True
# pred_cfg.num_classes = 2
# pred_cfg.sign_as_position = True
# pred_cfg.num_epochs = 1
# pred_cfg.position_enum == PositionType.Long = True
# pred_cfg.optimizer_enum = cst.Optimizers.SGD
# pred_cfg.learning_rate *= .1#1e-4
# pred_cfg.use_mse_loss = True
# pred_cfg.use_cum_mse_loss = True
# pred_cfg.use_bce_loss = True
# pred_cfg.use_cum_bce_loss = True
# pred_cfg.use_mad_loss = True
# pred_cfg.pred_len_as_batch_size.train = True
# pred_cfg.model_name = 'tsmx'
# pred_cfg.model_name = 'mlp'
pred_cfg.model_name = 'rnn'
# pred_cfg.rnn_name = 'slstm'
# pred_cfg.rnn_name = 'lstm' # shuffle code, no adapt, bsz64 2022.01.01 t 2024.01.01 v 2024.08.01 pnl1.66297 mdd 0.13640
pred_cfg.rnn_name = 'gru' # no shuffle code, adapt, bsz42 2021.01.01 t 2024.01.01 v 2024.08.01 pnl1.74404 mdd 0.20368
# pred_cfg.rnn_name = 'rnn'
# pred_cfg.model_name = 'itrm'
# pred_cfg.model_name = 'tcn'
# pred_cfg.fee_ratio = 0.001
pred_cfg.interval_cfg.base = 240
pred_cfg.symbol = 'BTCUSDT'
# pred_cfg.learning_rate *= .100
# pred_cfg.pred_multi_step = False
# pred_cfg.use_ta = True
# pred_cfg.use_original = True
# pred_cfg.decompsition.in_use = True
# pred_cfg.decompsition.concat = True
# pred_cfg.decompsition.with_original = True
# pred_cfg.decompsition.trend_only = True
# pred_cfg.is_channel_independent = True

pred_cfg.augment_data.train = True
# pred_cfg.augment_data.val = True
pred_cfg.augment_data.rev = True
# pred_cfg.train_start_date = '2021.01.01'
# pred_cfg.train_end_date = '2022.01.01'    
# pred_cfg.val_end_date = '2022.03.01'
# pred_cfg.test_end_date = '2022.05.01'
# pred_cfg.train_end_date = '2022.03.01'    
# pred_cfg.val_end_date = '2022.05.01'
# pred_cfg.test_end_date = '2022.07.01'
# pred_cfg.train_end_date = '2022.05.01'    
# pred_cfg.val_end_date = '2022.07.01'
# pred_cfg.test_end_date = '2022.09.01'    
# pred_cfg.train_end_date = '2022.07.01'    
# pred_cfg.val_end_date = '2022.09.01'
# pred_cfg.test_end_date = '2022.11.01'
# pred_cfg.train_end_date = '2022.09.01'    
# pred_cfg.val_end_date = '2022.11.01'
# pred_cfg.test_end_date = '2023.01.01'      

# pred_cfg.train_end_date = '2022.11.01'    
# pred_cfg.val_end_date = '2023.02.01'
# pred_cfg.test_end_date = '2023.05.01'   

# pred_cfg.train_end_date = '2023.01.01'    
# pred_cfg.val_end_date = '2023.02.01'
# pred_cfg.test_end_date = '2023.03.01'   
# pred_cfg.train_end_date = '2023.02.01'    
# pred_cfg.val_end_date = '2023.03.01'
# pred_cfg.test_end_date = '2023.04.01'       
# pred_cfg.train_end_date = '2023.03.01'    
# pred_cfg.val_end_date = '2023.04.01'
# pred_cfg.test_end_date = '2023.05.01'           
# pred_cfg.train_end_date = '2023.04.01'    
# pred_cfg.val_end_date = '2023.05.01'
# pred_cfg.test_end_date = '2023.06.01'           
# pred_cfg.train_end_date = '2023.01.01'    
# pred_cfg.val_end_date = '2023.03.01'
# pred_cfg.test_end_date = '2023.05.01'      
# pred_cfg.train_start_date = '2022.11.01'  
# pred_cfg.train_end_date = '2023.03.01'    
# pred_cfg.val_end_date = '2023.05.01'
# pred_cfg.test_end_date = '2023.07.01'  
# pred_cfg.train_start_date = '2023.01.01'  
# pred_cfg.train_end_date = '2023.05.01'    
# pred_cfg.val_end_date = '2023.07.01'
# pred_cfg.test_end_date = '2023.09.01'      
# pred_cfg.train_start_date = '2023.03.01'  
# pred_cfg.train_end_date = '2023.07.01'    
# pred_cfg.val_end_date = '2023.09.01'
# pred_cfg.test_end_date = '2023.11.01'         
# pred_cfg.train_end_date = '2023.09.01'    
# pred_cfg.val_end_date = '2023.11.01'
# pred_cfg.test_end_date = '2024.01.01'  

# pred_cfg.train_end_date = '2023.11.01'
# pred_cfg.val_end_date = '2023.12.01'
# pred_cfg.test_end_date = '2024.01.01'

# pred_cfg.train_end_date = '2023.12.01'
# pred_cfg.val_end_date = '2024.01.01'
# pred_cfg.test_end_date = '2024.02.01'

pred_cfg.start_date.multi = '2023.01.01'
# pred_cfg.train_end_date = '2024.01.01'    # hard
# pred_cfg.val_end_date = '2024.02.01'
# pred_cfg.test_end_date = '2024.03.01'
# pred_cfg.train_end_date = '2022.03.01'
# pred_cfg.val_end_date = '2022.07.01'    
# pred_cfg.test_end_date = '2022.11.01'

# pred_cfg.train_end_date = '2022.07.01'
# pred_cfg.val_end_date = '2022.11.01'    
# pred_cfg.test_end_date = '2023.03.01'

# pred_cfg.train_end_date = '2022.11.01'
# pred_cfg.val_end_date = '2023.03.01'    
# pred_cfg.test_end_date = '2023.07.01'
# pred_cfg.train_end_date = '2023.03.01'
# pred_cfg.val_end_date = '2023.07.01'    
# pred_cfg.test_end_date = '2023.11.01'
# pred_cfg.train_end_date = '2023.07.01'
# pred_cfg.val_end_date = '2023.11.01'    
# pred_cfg.test_end_date = '2024.03.01'
# pred_cfg.train_end_date = '2022.07.01'
# pred_cfg.val_end_date = '2023.01.01'    
# pred_cfg.test_end_date = '2023.07.01'
# pred_cfg.train_end_date = '2023.11.01'
# pred_cfg.val_end_date = '2024.03.01'
# pred_cfg.test_end_date = '2024.07.01'
# pred_cfg.train_end_date = '2024.01.01'
# pred_cfg.val_end_date = '2024.03.01'
# pred_cfg.test_end_date = '2024.07.01'

pred_cfg.train_end_date = '2024.01.01'
# pred_cfg.val_start_date = '2024.03.01'
pred_cfg.val_end_date = '2024.08.01'
# pred_cfg.test_start_date = '2024.05.01'
# pred_cfg.test_end_date = '2024.08.01'

# pred_cfg.train_end_date = '2024.03.01'
# pred_cfg.val_start_date = '2024.05.01'
# pred_cfg.val_end_date = '2024.05.01'
# pred_cfg.test_start_date = '2024.05.01'
# pred_cfg.test_end_date = '2024.07.01'
# pred_cfg.train_end_date = '2024.07.01'
# pred_cfg.val_end_date = '2024.07.01'
# pred_cfg.test_end_date = '2024.07.01'
# pred_cfg.fracdiff = .2
# pred_cfg.cum_feature_num = 7
# pred_cfg.execute_phase.val = False
pred_cfg.execute_phase.test = False


if __name__ == '__main__':
    torch.autograd.set_detect_anomaly(True)
    # pred_cfg.adapt.is_osaka = True
    # pred_cfg.adapt.transform_x = False
    # pred_cfg.pnl_decay.in_use = True
    # pred_cfg.pnl_decay.threshold = 0.00002
    # pred_cfg.feature_category.factor = False
    LightningClass = LightningModule
    task_str = pred_cfg.task_enum.value
    # model_name = pred_cfg.model_name
    class_name = f'Lightning{task_str.upper()}'
    exec(f'from models.lightning_{task_str} import {class_name} as LightningClass')
    
    # pred_cfg.resume_from_ckpt = True # buggy
    trainer = pred_cfg.get_trainer()
    ckpt_folder = pred_cfg.get_ckpt_folder()
    data_module_ready = False    
    if (path_not_exist := not os.path.exists(ckpt_folder)) or pred_cfg.force_train:
        data_module = KlineDataModule(pred_cfg)
        pred_cfg.save_scaler(ckpt_folder)
        data_module_ready = True
        if path_not_exist:
            os.makedirs(ckpt_folder, exist_ok=True)
        lm = LightningClass(pred_cfg).to(pred_cfg.device)
        print(lm.model)
        # lm.date_dict = data_module.date_dict
        resume_path = None
        if pred_cfg.resume_from_ckpt:
            ckpt_file_name = pred_cfg.get_last_ckpt_file_name()
            # ckpt_file_name = 'last-v8.ckpt'
            print(f'{ckpt_file_name = }')
            resume_path = os.path.join(ckpt_folder, ckpt_file_name)
        trainer.fit(lm, datamodule=data_module, ckpt_path=resume_path)
        

        
    ckpt_file_name = pred_cfg.get_last_ckpt_file_name()
    print(f'{ckpt_file_name = }')
    ckpt_path = os.path.join(ckpt_folder, ckpt_file_name)
    # lm.task_phase = cst.TaskPhase.VALIDATION_MODEL
    # trainer.test(lm, dataloaders=data_module.val_dataloader(), ckpt_path="best")
    # lm.task_phase = cst.TaskPhase.TESTING
    # trainer.test(lm, dataloaders=data_module.test_dataloader(), ckpt_path="best")
    lmc = LightningClass.load_from_checkpoint(ckpt_path)
    pred_cfg: PredictorConfig = lmc.hparams.cfg
    if not data_module_ready:
        pred_cfg.load_scaler(ckpt_folder)
        # pred_cfg.sample_pair_by_step_on_eval = True
        # pred_cfg.adapt_distr_shift.inverse_coef = -0.1
        # pred_cfg.adapt_distr_shift.val = True
        # pred_cfg.adapt_distr_shift.test = True
        # lmc.cfg = pred_cfg
        data_module = KlineDataModule(pred_cfg)
    # lmc.date_dict = data_module.date_dict
    # lmc = LightningClass.load_from_checkpoint(ckpt_path, cfg=pred_cfg)
    trainer.validate(lmc, dataloaders=data_module.val_dataloader())
    if pred_cfg.execute_phase.test:
        trainer.test(lmc, dataloaders=data_module.test_dataloader())