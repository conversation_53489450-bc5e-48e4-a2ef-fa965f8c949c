import pandas as pd
import numpy as np
import polars as pl
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from typing import Tuple, Dict, List, Optional, Union
import talib
from datetime import datetime
import seaborn as sns
from matplotlib.ticker import FuncFormatter

class TwoWayMarketMakerLimit:
    """
    Python implementation of Two-Way Market Maker Limit strategy
    using vectorized operations with Polars/Pandas
    """

    def __init__(self,
                 trade_direction: str = "Both",
                 use_atr: bool = False,
                 atr_period: int = 1,
                 atr_multiplier: float = 1.0,
                 fixed_percent: float = 0.01,
                 cooldown_period: int = 1,
                 initial_position_pct: float = 0.01,
                 scale_in_coef: float = 1.0,
                 max_scale_in: int = 100,
                 take_profit_pct: float = 0.01,
                 inventory_decay: float = 0.99,
                 commission_pct: float = 0.0002):
        """
        Initialize the Two-Way Market Maker Limit strategy

        Parameters:
        -----------
        trade_direction : str
            Trading direction, one of "Long Only", "Short Only", "Both"
        use_atr : bool
            Whether to use ATR for entry/scale-in calculations
        atr_period : int
            ATR period for volatility calculation
        atr_multiplier : float
            Multiplier for ATR
        fixed_percent : float
            Fixed percentage for entry/scale-in if not using ATR
        cooldown_period : int
            Cooldown period in bars after entry
        initial_position_pct : float
            Initial position size as percentage of equity
        scale_in_coef : float
            Coefficient for scale-in order sizing
        max_scale_in : int
            Maximum number of scale-in orders
        take_profit_pct : float
            Take profit percentage
        inventory_decay : float
            Inventory decay factor for take profit adjustment
        commission_pct : float
            Commission percentage per trade
        """
        # Store strategy parameters
        self.trade_direction = trade_direction
        self.use_atr = use_atr
        self.atr_period = atr_period
        self.atr_multiplier = atr_multiplier
        self.fixed_percent = fixed_percent
        self.cooldown_period = cooldown_period
        self.initial_position_pct = initial_position_pct
        self.scale_in_coef = scale_in_coef
        self.max_scale_in = max_scale_in
        self.take_profit_pct = take_profit_pct
        self.inventory_decay = inventory_decay
        self.commission_pct = commission_pct

        # Trading flags
        self.allow_long = trade_direction in ["Long Only", "Both"]
        self.allow_short = trade_direction in ["Short Only", "Both"]

    def get_entry_level(self, price: float, side: int, atr_value: float = None) -> float:
        """
        Calculate entry level based on price and side

        Parameters:
        -----------
        price : float
            Base price for calculation
        side : int
            Trading side (1 for long, -1 for short)
        atr_value : float, optional
            ATR value if using ATR-based entries

        Returns:
        --------
        float
            Entry price level
        """
        if self.use_atr and atr_value is not None:
            distance = atr_value * self.atr_multiplier
        else:
            distance = price * self.fixed_percent

        return price - side * distance

    def get_scale_in_ratio(self, count: int) -> float:
        """
        Calculate position size ratio for scale-in orders

        Parameters:
        -----------
        count : int
            Current scale-in count

        Returns:
        --------
        float
            Scale-in ratio
        """
        return np.power(self.scale_in_coef, count)

    def get_tp_level(self, avg_price: float, count: int, side: int) -> float:
        """
        Calculate take profit level

        Parameters:
        -----------
        avg_price : float
            Average position price
        count : int
            Current scale-in count
        side : int
            Trading side (1 for long, -1 for short)

        Returns:
        --------
        float
            Take profit price level
        """
        return avg_price * (1 + side * np.power(self.inventory_decay, count) * self.take_profit_pct)

    def calculate_avg_price(self, current_avg: float, current_size: float,
                           new_price: float, new_size: float) -> float:
        """
        Calculate average position price after adding a new position

        Parameters:
        -----------
        current_avg : float
            Current average price
        current_size : float
            Current position size
        new_price : float
            New entry price
        new_size : float
            New position size

        Returns:
        --------
        float
            New average price
        """
        total_size = current_size + new_size
        if total_size == 0:
            return 0
        return (current_avg * current_size + new_price * new_size) / total_size

    def run_backtest(self, data: Union[pd.DataFrame, pl.DataFrame]) -> Union[pd.DataFrame, pl.DataFrame]:
        """
        Run backtest on the provided data

        Parameters:
        -----------
        data : Union[pd.DataFrame, pl.DataFrame]
            Price data with columns: datetime, open, high, low, close, volume

        Returns:
        --------
        Union[pd.DataFrame, pl.DataFrame]
            Backtest results with additional columns for strategy signals and performance
        """
        # Convert to Polars if input is Pandas DataFrame
        if isinstance(data, pd.DataFrame):
            df = pl.from_pandas(data)
        else:
            df = data.clone()

        # Calculate ATR if needed
        if self.use_atr:
            # Convert to pandas for talib ATR calculation
            pdf = df.select(['high', 'low', 'close']).to_pandas()
            atr = talib.ATR(pdf['high'].values, pdf['low'].values,
                            pdf['close'].values, timeperiod=self.atr_period)
            # Add ATR back to polars dataframe
            df = df.with_columns(pl.Series(name="atr", values=atr))
        else:
            # Add placeholder ATR column
            df = df.with_columns(pl.lit(None).alias("atr"))

        # Initialize strategy state columns
        df = df.with_columns(
            # Base price and levels
            pl.lit(None).alias("base_price"),
            pl.lit(None).alias("long_base_price"),
            pl.lit(None).alias("short_base_price"),
            pl.lit(None).alias("long_avg_price"),
            pl.lit(None).alias("short_avg_price"),
            pl.lit(None).alias("long_tp_level"),
            pl.lit(None).alias("short_tp_level"),
            pl.lit(None).alias("long_entry_level"),
            pl.lit(None).alias("short_entry_level"),

            # Position tracking
            pl.lit(0).alias("long_scale_in_count"),
            pl.lit(0).alias("short_scale_in_count"),
            pl.lit(0.0).alias("long_position_ratio"),
            pl.lit(0.0).alias("short_position_ratio"),
            pl.lit(0.0).alias("max_long_ratio"),
            pl.lit(0.0).alias("max_short_ratio"),
            pl.lit(-9999).alias("last_long_entry_bar"),
            pl.lit(-9999).alias("last_short_entry_bar"),

            # Current position tracking
            pl.lit(0.0).alias("prev_long_pos"),
            pl.lit(0.0).alias("prev_short_pos"),
            pl.lit(0.0).alias("curr_long_pos"),
            pl.lit(0.0).alias("curr_short_pos"),

            # Order tracking
            pl.lit(0).alias("completed_orders_count"),
            pl.lit(0).alias("prev_completed_orders_count"),

            # Position state flags
            pl.lit(False).alias("long_pos_increased"),
            pl.lit(False).alias("short_pos_increased"),
            pl.lit(False).alias("position_closed"),
            pl.lit(False).alias("long_in_cooldown"),
            pl.lit(False).alias("short_in_cooldown"),

            # Order signals
            pl.lit(False).alias("long_entry_signal"),
            pl.lit(False).alias("short_entry_signal"),
            pl.lit(False).alias("long_tp_signal"),
            pl.lit(False).alias("short_tp_signal"),

            # Performance tracking
            pl.lit(0.0).alias("equity"),
            pl.lit(0.0).alias("returns")
        )

        # Process each bar sequentially to simulate the strategy
        results = []

        # Initial state
        state = {
            "base_price": None,
            "long_base_price": None,
            "short_base_price": None,
            "long_avg_price": None,
            "short_avg_price": None,
            "long_tp_level": None,
            "short_tp_level": None,
            "long_entry_level": None,
            "short_entry_level": None,
            "long_scale_in_count": 0,
            "short_scale_in_count": 0,
            "long_position_ratio": 0.0,
            "short_position_ratio": 0.0,
            "max_long_ratio": 0.0,
            "max_short_ratio": 0.0,
            "last_long_entry_bar": -9999,
            "last_short_entry_bar": -9999,
            "prev_long_pos": 0.0,
            "prev_short_pos": 0.0,
            "curr_long_pos": 0.0,
            "curr_short_pos": 0.0,
            "completed_orders_count": 0,
            "prev_completed_orders_count": 0,
            "equity": 10000.0,  # Starting equity
            "pending_long_orders": [],
            "pending_short_orders": [],
            "pending_long_tp_orders": [],
            "pending_short_tp_orders": []
        }

        # Process each bar
        for i, row in enumerate(df.iter_rows(named=True)):
            # Create a copy of the current state to modify
            new_state = state.copy()

            # Update bar index
            bar_index = i

            # Extract current bar data
            open_price = row['open']
            high_price = row['high']
            low_price = row['low']
            close_price = row['close']
            atr_value = row['atr'] if self.use_atr else None

            # Update cooldown status
            new_state["long_in_cooldown"] = (bar_index - new_state["last_long_entry_bar"]) < self.cooldown_period
            new_state["short_in_cooldown"] = (bar_index - new_state["last_short_entry_bar"]) < self.cooldown_period

            # Update order tracking
            new_state["prev_completed_orders_count"] = new_state["completed_orders_count"]

            # Process pending orders (limit orders)
            # This simulates the execution of limit orders during the bar

            # Process long entry orders
            long_entries_executed = []
            for order in new_state["pending_long_orders"]:
                if low_price <= order["price"] <= high_price and self.allow_long:
                    # Order executed
                    execution_price = order["price"]
                    order_size = order["size"]

                    # Update position
                    if new_state["curr_long_pos"] == 0:
                        new_state["long_avg_price"] = execution_price
                        new_state["long_position_ratio"] = self.get_scale_in_ratio(0)
                        # Cancel all short orders on first long entry
                        new_state["pending_short_orders"] = []
                    else:
                        new_state["long_avg_price"] = self.calculate_avg_price(
                            new_state["long_avg_price"], new_state["curr_long_pos"],
                            execution_price, order_size
                        )
                        new_state["long_position_ratio"] += self.get_scale_in_ratio(
                            new_state["long_scale_in_count"]
                        )

                    new_state["curr_long_pos"] += order_size
                    new_state["long_scale_in_count"] += 1
                    new_state["max_long_ratio"] = max(new_state["max_long_ratio"],
                                                   new_state["long_position_ratio"])
                    new_state["last_long_entry_bar"] = bar_index
                    new_state["long_pos_increased"] = True
                    new_state["completed_orders_count"] += 1

                    # Add to executed orders list
                    long_entries_executed.append(order)

            # Remove executed orders from pending list
            for order in long_entries_executed:
                new_state["pending_long_orders"].remove(order)

            # Process short entry orders
            short_entries_executed = []
            for order in new_state["pending_short_orders"]:
                if low_price <= order["price"] <= high_price and self.allow_short:
                    # Order executed
                    execution_price = order["price"]
                    order_size = order["size"]

                    # Update position
                    if new_state["curr_short_pos"] == 0:
                        new_state["short_avg_price"] = execution_price
                        new_state["short_position_ratio"] = self.get_scale_in_ratio(0)
                        # Cancel all long orders on first short entry
                        new_state["pending_long_orders"] = []
                    else:
                        new_state["short_avg_price"] = self.calculate_avg_price(
                            new_state["short_avg_price"], new_state["curr_short_pos"],
                            execution_price, order_size
                        )
                        new_state["short_position_ratio"] += self.get_scale_in_ratio(
                            new_state["short_scale_in_count"]
                        )

                    new_state["curr_short_pos"] += order_size
                    new_state["short_scale_in_count"] += 1
                    new_state["max_short_ratio"] = max(new_state["max_short_ratio"],
                                                    new_state["short_position_ratio"])
                    new_state["last_short_entry_bar"] = bar_index
                    new_state["short_pos_increased"] = True
                    new_state["completed_orders_count"] += 1

                    # Add to executed orders list
                    short_entries_executed.append(order)

            # Remove executed orders from pending list
            for order in short_entries_executed:
                new_state["pending_short_orders"].remove(order)

            # Process long take profit orders
            long_tp_executed = []
            for order in new_state["pending_long_tp_orders"]:
                if low_price <= order["price"] <= high_price:
                    # Order executed
                    execution_price = order["price"]
                    order_size = order["size"]

                    # Update position
                    new_state["curr_long_pos"] -= order_size
                    new_state["completed_orders_count"] += 1
                    new_state["long_tp_signal"] = True

                    # Calculate profit
                    profit = (execution_price - new_state["long_avg_price"]) * order_size
                    profit -= (execution_price + new_state["long_avg_price"]) * order_size * self.commission_pct
                    new_state["equity"] += profit

                    # Add to executed orders list
                    long_tp_executed.append(order)

            # Remove executed orders from pending list
            for order in long_tp_executed:
                new_state["pending_long_tp_orders"].remove(order)

            # Process short take profit orders
            short_tp_executed = []
            for order in new_state["pending_short_tp_orders"]:
                if low_price <= order["price"] <= high_price:
                    # Order executed
                    execution_price = order["price"]
                    order_size = order["size"]

                    # Update position
                    new_state["curr_short_pos"] -= order_size
                    new_state["completed_orders_count"] += 1
                    new_state["short_tp_signal"] = True

                    # Calculate profit
                    profit = (new_state["short_avg_price"] - execution_price) * order_size
                    profit -= (execution_price + new_state["short_avg_price"]) * order_size * self.commission_pct
                    new_state["equity"] += profit

                    # Add to executed orders list
                    short_tp_executed.append(order)

            # Remove executed orders from pending list
            for order in short_tp_executed:
                new_state["pending_short_tp_orders"].remove(order)

            # Check if position is closed
            new_state["position_closed"] = (new_state["curr_long_pos"] == 0 and
                                         new_state["curr_short_pos"] == 0 and
                                         new_state["completed_orders_count"] > new_state["prev_completed_orders_count"])

            # Initialize or reset on first bar or when position is closed
            if bar_index == 0 or new_state["position_closed"]:
                # Update base price and levels
                new_state["base_price"] = close_price
                new_state["long_base_price"] = close_price
                new_state["short_base_price"] = close_price
                new_state["long_tp_level"] = close_price * (1 + self.take_profit_pct)
                new_state["short_tp_level"] = close_price * (1 - self.take_profit_pct)

                # Calculate entry levels
                new_state["long_entry_level"] = self.get_entry_level(new_state["base_price"], 1,
                                                                  atr_value if self.use_atr else None)
                new_state["short_entry_level"] = self.get_entry_level(new_state["base_price"], -1,
                                                                   atr_value if self.use_atr else None)

                # Reset position counters
                new_state["long_scale_in_count"] = 0
                new_state["short_scale_in_count"] = 0
                new_state["long_position_ratio"] = 0.0
                new_state["short_position_ratio"] = 0.0

                # Clear pending orders
                new_state["pending_long_orders"] = []
                new_state["pending_short_orders"] = []
                new_state["pending_long_tp_orders"] = []
                new_state["pending_short_tp_orders"] = []

                # Place new entry orders
                order_size = self.initial_position_pct * new_state["equity"]

                if self.allow_long:
                    new_state["pending_long_orders"].append({
                        "price": new_state["long_entry_level"],
                        "size": order_size,
                        "comment": "L_1"
                    })

                if self.allow_short:
                    new_state["pending_short_orders"].append({
                        "price": new_state["short_entry_level"],
                        "size": order_size,
                        "comment": "S_1"
                    })

            # Handle long position increase
            if new_state["long_pos_increased"] and self.allow_long:
                # Update take profit level
                new_state["long_tp_level"] = self.get_tp_level(
                    new_state["long_avg_price"],
                    new_state["long_scale_in_count"],
                    1
                )

                # Place take profit order
                new_state["pending_long_tp_orders"] = [{
                    "price": new_state["long_tp_level"],
                    "size": new_state["curr_long_pos"],
                    "comment": f"TP_L_{new_state['long_scale_in_count']}"
                }]

                # Update base prices
                new_state["short_base_price"] = max(new_state["long_tp_level"], new_state["base_price"])
                new_state["short_entry_level"] = self.get_entry_level(
                    new_state["short_base_price"], -1, atr_value if self.use_atr else None
                )
                new_state["long_entry_level"] = self.get_entry_level(
                    close_price, 1, atr_value if self.use_atr else None
                )

                # Place next scale-in order if not at max and not in cooldown
                if (new_state["long_scale_in_count"] < self.max_scale_in and
                    not new_state["long_in_cooldown"]):
                    order_ratio = self.get_scale_in_ratio(new_state["long_scale_in_count"])
                    order_size = order_ratio * self.initial_position_pct * new_state["equity"]

                    new_state["pending_long_orders"].append({
                        "price": new_state["long_entry_level"],
                        "size": order_size,
                        "comment": f"L_{new_state['long_scale_in_count'] + 1}"
                    })

            # Handle short position increase
            if new_state["short_pos_increased"] and self.allow_short:
                # Update take profit level
                new_state["short_tp_level"] = self.get_tp_level(
                    new_state["short_avg_price"],
                    new_state["short_scale_in_count"],
                    -1
                )

                # Place take profit order
                new_state["pending_short_tp_orders"] = [{
                    "price": new_state["short_tp_level"],
                    "size": new_state["curr_short_pos"],
                    "comment": f"TP_S_{new_state['short_scale_in_count']}"
                }]

                # Update base prices
                new_state["long_base_price"] = min(new_state["short_tp_level"], new_state["base_price"])
                new_state["long_entry_level"] = self.get_entry_level(
                    new_state["long_base_price"], 1, atr_value if self.use_atr else None
                )
                new_state["short_entry_level"] = self.get_entry_level(
                    close_price, -1, atr_value if self.use_atr else None
                )

                # Place next scale-in order if not at max and not in cooldown
                if (new_state["short_scale_in_count"] < self.max_scale_in and
                    not new_state["short_in_cooldown"]):
                    order_ratio = self.get_scale_in_ratio(new_state["short_scale_in_count"])
                    order_size = order_ratio * self.initial_position_pct * new_state["equity"]

                    new_state["pending_short_orders"].append({
                        "price": new_state["short_entry_level"],
                        "size": order_size,
                        "comment": f"S_{new_state['short_scale_in_count'] + 1}"
                    })

            # Update previous position sizes for next bar
            new_state["prev_long_pos"] = new_state["curr_long_pos"]
            new_state["prev_short_pos"] = new_state["curr_short_pos"]

            # Calculate returns for this bar
            if i > 0:
                new_state["returns"] = (new_state["equity"] / state["equity"]) - 1
            else:
                new_state["returns"] = 0.0

            # Store the updated state for the next iteration
            state = new_state

            # Add current state to results
            results.append({
                "datetime": row["datetime"] if "datetime" in row else i,
                "open": open_price,
                "high": high_price,
                "low": low_price,
                "close": close_price,
                "base_price": state["base_price"],
                "long_base_price": state["long_base_price"],
                "short_base_price": state["short_base_price"],
                "long_avg_price": state["long_avg_price"],
                "short_avg_price": state["short_avg_price"],
                "long_tp_level": state["long_tp_level"],
                "short_tp_level": state["short_tp_level"],
                "long_entry_level": state["long_entry_level"],
                "short_entry_level": state["short_entry_level"],
                "long_scale_in_count": state["long_scale_in_count"],
                "short_scale_in_count": state["short_scale_in_count"],
                "long_position_ratio": state["long_position_ratio"],
                "short_position_ratio": state["short_position_ratio"],
                "max_long_ratio": state["max_long_ratio"],
                "max_short_ratio": state["max_short_ratio"],
                "curr_long_pos": state["curr_long_pos"],
                "curr_short_pos": state["curr_short_pos"],
                "long_pos_increased": state["long_pos_increased"],
                "short_pos_increased": state["short_pos_increased"],
                "position_closed": state["position_closed"],
                "long_in_cooldown": state["long_in_cooldown"],
                "short_in_cooldown": state["short_in_cooldown"],
                "equity": state["equity"],
                "returns": state["returns"]
            })

        # Convert results to Polars DataFrame
        results_df = pl.DataFrame(results)

        # Calculate cumulative returns
        if len(results_df) > 0:
            results_df = results_df.with_columns(
                pl.col("returns").cum_prod().add(1).alias("cum_returns")
            )

        return results_df

    def plot_results(self, results: Union[pd.DataFrame, pl.DataFrame], figsize: Tuple[int, int] = (15, 10)):
        """
        Plot strategy results

        Parameters:
        -----------
        results : Union[pd.DataFrame, pl.DataFrame]
            Backtest results from run_backtest method
        figsize : Tuple[int, int]
            Figure size for the plot
        """
        # Convert to pandas if input is Polars DataFrame
        if isinstance(results, pl.DataFrame):
            df = results.to_pandas()
        else:
            df = results.copy()

        # Create figure and subplots
        fig, axs = plt.subplots(3, 1, figsize=figsize, gridspec_kw={'height_ratios': [3, 1, 1]}, sharex=True)

        # Plot price and levels
        ax1 = axs[0]
        ax1.plot(df['datetime'], df['close'], label='Close Price', color='black', alpha=0.7)

        # Plot base price and levels
        ax1.plot(df['datetime'], df['base_price'], label='Base Price', color='orange', linestyle='-')

        # Plot long levels if allowed
        if self.allow_long:
            ax1.plot(df['datetime'], df['long_entry_level'], label='Long Entry Level', color='lime', linestyle='--')
            ax1.plot(df['datetime'], df['long_tp_level'], label='Long Take Profit', color='aqua', linestyle=':')

        # Plot short levels if allowed
        if self.allow_short:
            ax1.plot(df['datetime'], df['short_entry_level'], label='Short Entry Level', color='red', linestyle='--')
            ax1.plot(df['datetime'], df['short_tp_level'], label='Short Take Profit', color='fuchsia', linestyle=':')

        # Add position markers
        long_entries = df[df['long_pos_increased']]
        short_entries = df[df['short_pos_increased']]
        long_exits = df[df['long_tp_signal']]
        short_exits = df[df['short_tp_signal']]

        if len(long_entries) > 0:
            ax1.scatter(long_entries['datetime'], long_entries['close'], marker='^', color='green', s=100, label='Long Entry')
        if len(short_entries) > 0:
            ax1.scatter(short_entries['datetime'], short_entries['close'], marker='v', color='red', s=100, label='Short Entry')
        if len(long_exits) > 0:
            ax1.scatter(long_exits['datetime'], long_exits['close'], marker='o', color='cyan', s=100, label='Long Exit')
        if len(short_exits) > 0:
            ax1.scatter(short_exits['datetime'], short_exits['close'], marker='o', color='magenta', s=100, label='Short Exit')

        ax1.set_title('Two-Way Market Maker Strategy')
        ax1.set_ylabel('Price')
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)

        # Plot position sizes
        ax2 = axs[1]
        ax2.fill_between(df['datetime'], df['curr_long_pos'], 0, label='Long Position', color='green', alpha=0.3)
        ax2.fill_between(df['datetime'], -df['curr_short_pos'], 0, label='Short Position', color='red', alpha=0.3)
        ax2.set_ylabel('Position Size')
        ax2.legend(loc='upper left')
        ax2.grid(True, alpha=0.3)

        # Plot equity curve
        ax3 = axs[2]
        ax3.plot(df['datetime'], df['equity'], label='Equity', color='blue')
        ax3.set_ylabel('Equity')
        ax3.set_xlabel('Date')
        ax3.legend(loc='upper left')
        ax3.grid(True, alpha=0.3)

        # Format x-axis dates if datetime objects
        if isinstance(df['datetime'].iloc[0], (datetime, pd.Timestamp)):
            ax3.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
            ax3.xaxis.set_major_locator(mdates.AutoDateLocator())

        plt.tight_layout()
        plt.show()

    def analyze_performance(self, results: Union[pd.DataFrame, pl.DataFrame]) -> Dict:
        """
        Analyze strategy performance

        Parameters:
        -----------
        results : Union[pd.DataFrame, pl.DataFrame]
            Backtest results from run_backtest method

        Returns:
        --------
        Dict
            Dictionary with performance metrics
        """
        # Convert to pandas if input is Polars DataFrame
        if isinstance(results, pl.DataFrame):
            df = results.to_pandas()
        else:
            df = results.copy()

        # Calculate daily returns if datetime is available
        if isinstance(df['datetime'].iloc[0], (datetime, pd.Timestamp)):
            df['date'] = df['datetime'].dt.date
            daily_returns = df.groupby('date')['returns'].sum()
        else:
            # Use arbitrary periods if datetime not available
            daily_returns = df['returns']

        # Calculate performance metrics
        total_return = df['cum_returns'].iloc[-1] - 1 if 'cum_returns' in df.columns else df['equity'].iloc[-1] / df['equity'].iloc[0] - 1
        annual_return = (1 + total_return) ** (252 / len(daily_returns)) - 1 if len(daily_returns) > 0 else 0
        daily_std = daily_returns.std()
        annual_volatility = daily_std * np.sqrt(252) if daily_std is not None else 0
        sharpe_ratio = annual_return / annual_volatility if annual_volatility != 0 else 0
        max_drawdown = self._calculate_max_drawdown(df['equity'])

        # Count trades
        long_entries = df['long_pos_increased'].sum()
        short_entries = df['short_pos_increased'].sum()
        total_trades = long_entries + short_entries

        # Calculate win rate (approximate based on equity increases)
        equity_changes = df['equity'].diff().dropna()
        winning_trades = (equity_changes > 0).sum()
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # Compile metrics
        metrics = {
            'total_return': total_return,
            'annual_return': annual_return,
            'annual_volatility': annual_volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'long_trades': long_entries,
            'short_trades': short_entries,
            'win_rate': win_rate
        }

        return metrics

    def print_performance_summary(self, metrics: Dict):
        """
        Print performance summary

        Parameters:
        -----------
        metrics : Dict
            Dictionary with performance metrics from analyze_performance method
        """
        print("===== PERFORMANCE SUMMARY =====")
        print(f"Total Return: {metrics['total_return']:.2%}")
        print(f"Annual Return: {metrics['annual_return']:.2%}")
        print(f"Annual Volatility: {metrics['annual_volatility']:.2%}")
        print(f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}")
        print(f"Maximum Drawdown: {metrics['max_drawdown']:.2%}")
        print(f"Total Trades: {metrics['total_trades']}")
        print(f"Long Trades: {metrics['long_trades']}")
        print(f"Short Trades: {metrics['short_trades']}")
        print(f"Win Rate: {metrics['win_rate']:.2%}")
        print("===============================")

    def _calculate_max_drawdown(self, equity_curve: pd.Series) -> float:
        """
        Calculate maximum drawdown

        Parameters:
        -----------
        equity_curve : pd.Series
            Equity curve series

        Returns:
        --------
        float
            Maximum drawdown as a decimal (not percentage)
        """
        # Calculate the running maximum
        running_max = equity_curve.cummax()
        # Calculate the drawdown
        drawdown = (equity_curve - running_max) / running_max
        # Get the maximum drawdown
        max_drawdown = drawdown.min()

        return max_drawdown
