import os
import joblib
from lightning import LightningModule
from pytorch_lightning.trainer import Trainer
from core.data_module import KlineDataModule
from core.predictor_config import PredictorConfig
from direct_trading import pred_cfg, fit_val_test
import torch
from core.cst import PositionType, TaskType, Optimizers
from core.dot_dict import DotDict as dd
torch.set_float32_matmul_precision('medium') 


pred_cfg.task_enum = TaskType.GridTrading
pred_cfg.script_name = __file__
pred_cfg.merge_history_data = False
pred_cfg.pnl_loss_with_fee_scale = 1
# pred_cfg.learning_rate = 1e-4
# pred_cfg.batch_size = 128
# pred_cfg.pred_len = 4
# pred_cfg.start_date.multi = '2024.01.01'
# pred_cfg.quote_end_date = '2024.08.01'
# pred_cfg.train_end_date = '2024.08.01'
# pred_cfg.val_start_date = '2024.08.01'
# pred_cfg.model_name = 'tcn'
# pred_cfg.model_name = 'rnn'
# pred_cfg.interval_cfg.base = 120
# pred_cfg.interval_cfg.base = 15
base_interval = pred_cfg.interval_cfg.base

pred_len = pred_cfg.pred_len = label_len = pred_cfg.interval_cfg.label // base_interval    
pred_cfg.cycle_mask_idx = (label_len - 1) # ! not set if using all indices

step_list = pred_cfg.step_list = [pred_len]
pred_cfg.inner_step = max(step_list) #! will cause zscore overlap if using all indices and inner_step > 1

label_step = label_len // pred_len

pred_cfg.label_step.train = label_step
pred_cfg.label_step.val = label_step


# pred_cfg.execute_phase.train = False
# pred_cfg.resume_from_ckpt = True
if pred_cfg.resume_from_ckpt: 
    if pred_cfg.interval_cfg.base == 60:
        pred_cfg.merge_history_data = True
        pred_cfg.history_file_name = '2025_0125_012706_s20210101_crypto30_60min'        
        if pred_cfg.train_end_date == '2024.08.01':
            # pred_cfg.task_folder = '2025_0118_194001_v20250101_train-fee_scale=2.4'
            pred_cfg.task_folder = '2025_0127_173728_v20250101_train'
            pred_cfg.ckpt_file_name = 'epoch=0.ckpt'	    
    if pred_cfg.interval_cfg.base == 240:
        if pred_cfg.train_end_date == '2024.08.01':
            # pred_cfg.task_folder = '2025_0118_194001_v20250101_train-fee_scale=2.4'
            pred_cfg.task_folder = '2025_0129_190138_v20250101_train'
            pred_cfg.ckpt_file_name = 'epoch=0.ckpt'        

if __name__ == '__main__':
    # pred_cfg.with_directional_profit = False
    
    # pred_cfg.episodic_backward = True
    # pred_cfg.learning_rate = 10e-3
    # pred_cfg.interval_cfg.base = 120
    # pred_cfg.limit_margin_per_code = False
    # pred_cfg.shuffling.codewise = True
    # pred_cfg.force_train = False
    # pred_cfg.adapt_distr_shift.val = True
    # pred_cfg.pnl_decay.in_use = True
    # pred_cfg.pnl_decay.threshold = 0.00002
    # pred_cfg.feature_cfg.factor = False
    # pred_cfg.merge_history_data = True
    # pred_cfg.history_file_name = '2024_0827_062913_s20210101'
    fit_val_test(pred_cfg)