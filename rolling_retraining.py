import os
from lightning import LightningModule
from core.cst import Optimizers, TaskType
from core.data_module import KlineDataModule
from core.predictor_config import PredictorConfig
# from portfolio import pred_cfg
from direct_trading import pred_cfg
# from classification_trading import pred_cfg


pred_cfg.script_name = __file__
pred_cfg.rolling_retraining.in_use = True
# pred_cfg.label_enum == LabelType.LogReturn = True
# pred_cfg.rolling_retraining.interval_in_day = 14
# pred_cfg.seq_len = 84
# pred_cfg.hidden_size = 12
pred_cfg.start_date.multi = '2021.01.01'
pred_cfg.train_end_date = '2024.01.01'
# pred_cfg.train_end_date = '2024.08.01'
# pred_cfg.interval_cfg.base = 240
pred_cfg.num_epochs = 80
pred_cfg.patience = 80
pred_cfg.load_all_codes = True
# pred_cfg.n_codes = 1
# pred_cfg.shuffling.codewise = True
# pred_cfg.rnn_name = 'gru'
# pred_cfg.rnn_name = 'lstm'
pred_cfg.model_name = 'tcn'   

# pred_cfg.adapt.in_use = True
if pred_cfg.meta_adapt.in_use:
    pred_cfg.task_enum = TaskType.PortfolioDoubleAdapt
    # pred_cfg.optimizer_enum = Optimizers.RMSPROP
    pred_cfg.optimizer_enum = Optimizers.ADAM
    pred_cfg.sample_pair = True
    # pred_cfg.adapt.transform_x = False
    pred_cfg.meta_adapt.transform_y = False
    pred_cfg.meta_adapt.offline_lr_dict.outer = pred_cfg.meta_adapt.offline_lr_dict.inner = pred_cfg.learning_rate = 1e-4
    pred_cfg.batch_size = 42
    


def rolling_retrain(cfg: PredictorConfig):
    history_kline_dict = cfg.get_history_kline_dict()
    local_vars = {}
    task_str = cfg.task_enum.value    
    class_name = f'Lightning{task_str.upper()}'
    exec(f'from models.lightning_{task_str} import {class_name}', globals(), local_vars)
    LightningClass: LightningModule = local_vars[class_name]
    
    ckpt_folder = pred_cfg.get_ckpt_folder()
    
    data_module = KlineDataModule(pred_cfg, history_kline_dict)

    if not os.path.exists(ckpt_folder):
        os.makedirs(ckpt_folder, exist_ok=True)

    for i, dataloader_dict in enumerate(data_module):
        print(f'====  starting {i}-th retraining  ====')
        # if i < 1:
        #     continue
        lm = LightningClass(pred_cfg).to(pred_cfg.device)  
        lm.date_dict = data_module.date_dict
        # trainer.fit(lm, train_dataloaders=dataloader_dict.train, val_dataloaders=dataloader_dict.val)
        trainer = pred_cfg.get_trainer()
        trainer.fit(lm, datamodule=data_module)


if __name__ == '__main__':
    # pred_cfg.force_train = False
    # pred_cfg.shuffling.codewise = True
    # pred_cfg.rnn_name = 'lstm'

    # pred_cfg.limit_margin_per_code = False
    # pred_cfg.interval_cfg.base = 120    
    # pred_cfg.adapt_distr_shift.val = True
    # pred_cfg.pnl_decay.in_use = True
    # pred_cfg.pnl_decay.threshold = 0.00002
    # pred_cfg.feature_category.factor = False
    # pred_cfg.train_end_date = '2023.08.01'
    # pred_cfg.merge_history_data = True
    # pred_cfg.history_prefix = '2024_0827_065229_s20220101' 
    # pred_cfg.history_prefix = '2024_0828_005253_s20210101'
    rolling_retrain(pred_cfg)