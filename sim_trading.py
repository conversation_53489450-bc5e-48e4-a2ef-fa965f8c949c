from aed_quant.quant import quant
from core.async_portfolio_manager import AsyncPortfolioManager
from online_trading import model, cfg


if __name__ == "__main__":
	# cfg.debug_ticker = True
	# cfg.use_scaler = False # ! not set to False when live trading !
	cfg.sim_base_interval = 3
	# cfg.interval_cfg.label = 100
	# cfg.base_notional = 120.
	# cfg.is_live_trading = True
	cfg.cycle_mask_idx = None
	AsyncPortfolioManager(cfg, model)
	quant.start()
