//@version=5
strategy("Two-Way Market Maker Limit", shorttitle="TWMM-Limit", overlay=true, pyramiding=100, default_qty_type=strategy.percent_of_equity, default_qty_value=1, commission_type=strategy.commission.percent, commission_value=0.02)

// Input parameters
trade_direction = input.string("Both", title="Trading Direction", options=["Long Only", "Short Only", "Both"], group="Strategy Settings")
next_entry_based_on_close = input.bool(false, "Next Entry Based On Close", group="Entry Parameters")
use_atr = input.bool(true, title="Use ATR for Entry/Scale-in (otherwise fixed %)", group="Entry Parameters")
atr_period = input.int(1, title="ATR Period", minval=1, group="Entry Parameters")
atr_multiplier = input.float(1.0, title="ATR Multiplier", minval=0.1, step=0.1, group="Entry Parameters")
fixed_percent = input.float(1.0, title="Fixed Percentage (%)", minval=0.1, step=0.1, group="Entry Parameters") / 100

cooldown_period = input.int(1, title="Cooldown Period (bars)", minval=1, group="Position Sizing")
initial_position_pct = input.float(1.0, title="Initial Position Size (%)", minval=0.1, step=0.1, group="Position Sizing") / 100
scale_in_coef = input.float(1.0, title="Scale-in Order Coefficient", minval=1.0, step=0.01, group="Position Sizing")
max_scale_in = input.int(100, title="Maximum Scale-in Orders", minval=1, maxval=100, group="Position Sizing")

take_profit_pct = input.float(2.0, title="Take Profit (%)", minval=0.1, step=0.1, group="Exit Parameters") / 100
inventory_decay = input.float(1, title="Inventory Decay", minval=0.1, step=0.01, group="Exit Parameters")

// Variables
var float base_price = na
var float long_base_price = na
var float short_base_price = na
var float long_avg_price = na
var float short_avg_price = na
var float long_tp_level = na
var float short_tp_level = na
var float long_entry_level = na
var float short_entry_level = na
var int long_scale_in_count = 0
var int short_scale_in_count = 0
var float long_position_ratio = 0.0
var float short_position_ratio = 0.0
var float max_long_ratio = 0
var float max_short_ratio = 0
// 冷却期变量
var int last_long_entry_bar = -9999  // 最后一次多头开仓的bar索引
var int last_short_entry_bar = -9999 // 最后一次空头开仓的bar索引

// 仓位跟踪
var float prev_long_pos = 0.0
var float prev_short_pos = 0.0
var float curr_long_pos = 0.0
var float curr_short_pos = 0.0

// 订单完成数量跟踪
var int completed_orders_count = 0
var int prev_completed_orders_count = 0

long_entry_str = "L_"
short_entry_str = "S_"
// atr = ta.tr(true)
atr = ta.atr(atr_period)
atr_distance = (na(atr) ? ta.tr(true): atr) * atr_multiplier

// Calculate entry levels - recalculated on each bar
get_entry_level(price, side, ratio) =>
    distance = use_atr ? atr_distance: price * fixed_percent
    price - side * distance * ratio

// Calculate position sizes for scale-in orders
get_scale_in_ratio(count) =>
    math.pow(scale_in_coef, count)

get_tp_level(avg_price, count, side) =>
    result = avg_price * (1 + side * math.pow(inventory_decay, count) * take_profit_pct)
    result
    // if side > 0
    //     math.min(base_price, result)
    // else
    //     math.max(base_price, result)

// Function to calculate average position price
calculate_avg_price(current_avg, current_size, new_price, new_size) =>
    total_size = current_size + new_size
    (current_avg * current_size + new_price * new_size) / total_size

// 检查是否在冷却期内
long_in_cooldown = (bar_index - last_long_entry_bar) < cooldown_period
short_in_cooldown = (bar_index - last_short_entry_bar) < cooldown_period

// 检查交易方向
allow_long = trade_direction == "Long Only" or trade_direction == "Both"
allow_short = trade_direction == "Short Only" or trade_direction == "Both"

// 更新订单完成数量
prev_completed_orders_count := completed_orders_count
completed_orders_count := strategy.closedtrades

// 更新当前仓位
curr_long_pos := strategy.position_size > 0 ? strategy.position_size : 0
curr_short_pos := strategy.position_size < 0 ? math.abs(strategy.position_size) : 0

// 检测仓位变化
long_pos_increased = curr_long_pos > prev_long_pos
short_pos_increased = curr_short_pos > prev_short_pos
// long_pos_decreased = curr_long_pos < prev_long_pos
// short_pos_decreased = curr_short_pos < prev_short_pos
// long_pos_closed = prev_long_pos > 0 and curr_long_pos == 0
// short_pos_closed = prev_short_pos > 0 and curr_short_pos == 0

// 检测同时成交多空限价单的情况（仓位为0且完成的订单数量增加）
position_closed = completed_orders_count > prev_completed_orders_count

// Initialize base price on first bar
if bar_index == 0 or position_closed
    // // 取消所有现有的限价单
    // strategy.cancel(long_entry_str)
    // strategy.cancel(short_entry_str)

    // 更新基准价格
    base_price := close
    long_base_price := close
    short_base_price := close
    long_tp_level := close * (1 + take_profit_pct)
    short_tp_level := close * (1 - take_profit_pct)
    long_entry_level := get_entry_level(base_price, 1, 1)
    short_entry_level := get_entry_level(base_price, -1, 1)

    // 重置多头和空头计数和仓位
    long_scale_in_count := 0
    short_scale_in_count := 0
    long_position_ratio := 0.0
    short_position_ratio := 0.0

    // 放置新的多头和空头限价单
    order_size = strategy.default_entry_qty(close)

    if allow_long
        strategy.order(long_entry_str, strategy.long, qty=order_size, limit=long_entry_level, comment=long_entry_str + "1")

    if allow_short
        strategy.order(short_entry_str, strategy.short, qty=order_size, limit=short_entry_level, comment=short_entry_str + "1")


// 处理多头仓位增加
if long_pos_increased and allow_long
    // 更新计数和平均价格
    long_scale_in_count += 1

    // 更新平均价格和仓位大小
    if na(long_avg_price) or prev_long_pos == 0
        long_avg_price := strategy.position_avg_price
        long_position_ratio := get_scale_in_ratio(0)

        // 首次建立多头仓位时，取消所有空头限价单
        strategy.cancel(short_entry_str)
    else
        long_avg_price := strategy.position_avg_price
        long_position_ratio += get_scale_in_ratio(long_scale_in_count - 1)

    max_long_ratio := math.max(max_long_ratio, long_position_ratio)

    // 更新止盈价格并放置止盈限价单
    long_tp_level := get_tp_level(long_avg_price, long_scale_in_count, 1)
    strategy.exit(long_entry_str, limit=long_tp_level, comment="TP" + long_entry_str + str.tostring(long_scale_in_count))

    // 更新基准价格和下一个进场价格
    short_base_price := math.max(long_tp_level, base_price)
    short_entry_level := get_entry_level(short_base_price, -1, 1)
    long_entry_base_price = next_entry_based_on_close ? close : long_entry_level
    order_ratio = get_scale_in_ratio(long_scale_in_count)
    long_entry_level := get_entry_level(long_entry_base_price, 1, order_ratio)

    // 如果未达到最大加仓次数，放置新的加仓限价单
    if long_scale_in_count < max_scale_in and not long_in_cooldown        
        order_size = order_ratio * strategy.default_entry_qty(close)
        strategy.order(long_entry_str, strategy.long, qty=order_size, limit=long_entry_level, comment=long_entry_str + str.tostring(long_scale_in_count + 1))

    // 更新最后一次多头开仓时间
    last_long_entry_bar := bar_index

// 处理空头仓位增加
if short_pos_increased and allow_short
    // 更新计数和平均价格
    short_scale_in_count += 1

    // 更新平均价格和仓位大小
    if na(short_avg_price) or prev_short_pos == 0
        short_avg_price := strategy.position_avg_price
        short_position_ratio := get_scale_in_ratio(0)

        // 首次建立空头仓位时，取消所有多头限价单
        strategy.cancel(long_entry_str)
    else
        short_avg_price := strategy.position_avg_price
        short_position_ratio += get_scale_in_ratio(short_scale_in_count - 1)

    max_short_ratio := math.max(max_short_ratio, short_position_ratio)

    // 更新止盈价格并放置止盈限价单
    short_tp_level := get_tp_level(short_avg_price, short_scale_in_count, -1)

    strategy.exit(short_entry_str, limit=short_tp_level, comment="TP" + short_entry_str + str.tostring(short_scale_in_count))

    // 更新基准价格和下一个进场价格
    long_base_price := math.min(short_tp_level, base_price)
    long_entry_level := get_entry_level(long_base_price, 1, 1)
    short_entry_base_price = next_entry_based_on_close ? close : short_entry_level
    order_ratio = get_scale_in_ratio(short_scale_in_count)
    short_entry_level := get_entry_level(short_entry_base_price, -1, order_ratio)

    // 如果未达到最大加仓次数，放置新的加仓限价单
    if short_scale_in_count < max_scale_in and not short_in_cooldown
        order_size = order_ratio * strategy.default_entry_qty(close)

        strategy.order(short_entry_str, strategy.short, qty=order_size, limit=short_entry_level, comment=short_entry_str + str.tostring(short_scale_in_count + 1))

    // 更新最后一次空头开仓时间
    last_short_entry_bar := bar_index

// 更新前一个仓位大小，用于下一个bar的比较
prev_long_pos := curr_long_pos
prev_short_pos := curr_short_pos

// Visualization using plotchar with line-like characters
plot(base_price, title="Base Price", color=color.orange, style = plot.style_stepline_diamond) // Solid line for base price
plot(strategy.position_avg_price, title="Position Average Price", color=color.gray, style = plot.style_stepline_diamond) // Solid line for base price
// Long entry and take profit levels with different characters
plot(allow_long ? long_entry_level : na, title="Long Entry Level", color=color.lime, style = plot.style_stepline_diamond) // Dashed line for entry
plot(allow_long ? long_tp_level : na, title="Long Take Profit", color=color.aqua, style = plot.style_stepline_diamond) // Dotted line for take profit

// Short entry and take profit levels with different characters
plot(allow_short ? short_entry_level : na, title="Short Entry Level", color=color.red, style = plot.style_stepline_diamond) // Dashed line for entry
plot(allow_short ? short_tp_level : na, title="Short Take Profit", color=color.fuchsia, style = plot.style_stepline_diamond) // Dotted line for take profit

// Display information
var table info_table = table.new(position.top_right, 8, 8, color.black, color.white, 1, color.gray, 1)
table.cell(info_table, 0, 0, "Trading Direction", text_color=color.white)
table.cell(info_table, 1, 0, trade_direction, text_color=color.white)
table.cell(info_table, 0, 1, "Base Price", text_color=color.orange)
table.cell(info_table, 1, 1, str.tostring(base_price, "#.#####"), text_color=color.orange)

// 根据交易方向显示相关信息
if allow_long
    table.cell(info_table, 0, 2, "Long Avg Price", text_color=color.green)
    table.cell(info_table, 1, 2, str.tostring(long_avg_price, "#.####"), text_color=color.green)
    table.cell(info_table, 0, 4, "Long Scale-in Count", text_color=color.green)
    table.cell(info_table, 1, 4, str.tostring(long_scale_in_count), text_color=color.green)
    table.cell(info_table, 0, 6, "Max Long Ratio", text_color=color.green)
    table.cell(info_table, 1, 6, str.tostring(max_long_ratio, "#.###"), text_color=color.green)

if allow_short
    table.cell(info_table, 0, 3, "Short Avg Price", text_color=color.red)
    table.cell(info_table, 1, 3, str.tostring(short_avg_price, "#.####"), text_color=color.red)
    table.cell(info_table, 0, 5, "Short Scale-in Count", text_color=color.red)
    table.cell(info_table, 1, 5, str.tostring(short_scale_in_count), text_color=color.red)
    table.cell(info_table, 0, 7, "Max Short Ratio", text_color=color.red)
    table.cell(info_table, 1, 7, str.tostring(max_short_ratio, "#.###"), text_color=color.red)

// 显示冷却状态
var label long_cooldown_label = na
var label short_cooldown_label = na

if long_in_cooldown and allow_long
    long_cooldown_label := label.new(bar_index, high, "L冷却中", color=color.new(color.green, 70), style=label.style_label_down, textcolor=color.white, size=size.small)
    label.delete(long_cooldown_label[1])

if short_in_cooldown and allow_short
    short_cooldown_label := label.new(bar_index, low, "S冷却中", color=color.new(color.red, 70), style=label.style_label_up, textcolor=color.white, size=size.small)
    label.delete(short_cooldown_label[1])
