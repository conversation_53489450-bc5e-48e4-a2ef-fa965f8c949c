import sys
from aed_quant.quant import quant
# from training_for_deployment import pred_cfg
from direct_trading import pred_cfg
from core.async_portfolio_manager import AsyncPortfolioManager
from core.predictor_config import PredictorConfig


online_cfg = pred_cfg.online_trading
# online_cfg.LOG.console = True
quant.initialize(online_cfg)
cfg: PredictorConfig
ckpt_folder = pred_cfg.get_ckpt_folder(make_new_task_folder=False)
model, cfg = pred_cfg.load_model_cfg_from_ckpt(ckpt_folder=ckpt_folder, ckpt_file_name=pred_cfg.ckpt_file_name)
# cfg.reset_datetime_str()
# cfg.online_excluded_codes += ['SOLUSDT']
cfg.base_notional = 470.
# cfg.use_scaler = False # ! not set to False when live trading !
cfg.limit_shift_ratio = 1e-4
cfg.price_queue = 1
cfg.leverage = 1.1
cfg.top_num = 20
cfg.max_lot_notional = cfg.base_notional * cfg.leverage / cfg.top_num
# cfg.batch_size = 2
# cfg.adapt.on_init = True
# cfg.raw_date_dict.train[1] = '2024.08.02'
# cfg.selected_indices = [
#         0, 1, 2, 3,
#         7, 8, 9, 10, 
#         12, 13, 14,
#         16, 17, 18, 
#         ]
# cfg.force_update_trading_pairs = True



if __name__ == "__main__":
	# cfg.debug_ticker = True
	cfg.is_live_trading = True
	cfg.use_limit_order = True
	print(f'{cfg.cycle_mask_idx = }')
	AsyncPortfolioManager(cfg, model)
	quant.start()

	