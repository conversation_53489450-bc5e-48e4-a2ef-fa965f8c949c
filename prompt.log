网格做市商在一波暴跌之后积攒了大量的库存，每笔买入的价格是p1, p2, ... , pn 如果一次性以pn-1的网格价格卖出，会造成巨大的亏损，而暴跌结束之后，往往会伴随长时间震荡，希望在pn附近不断的进行高抛低吸，一边积累利润，一边补偿库存产生的亏损，并慢慢减少库存，怎么建立这样的数学模型

考虑做多的情况，做市商在多个价格点（p1​,p2​,…,pn​，其中 p1​>p2​>…>pn​）买入资产，积累了大量的库存n * q, 总和记为Q。持仓均价为p_avg, 设定固定利润比例为r， 做市商如果在p_avg(1 + r)卖出Q，就可以一次性获得利润，并清除库存， 但是这需要价格反弹相当大的幅度，不容易达成，希望每次价格触达pn以上的某些价位时， 只卖出一部分库存q + d，然后，如果价格再次下跌，就买入库存q， 使得经过多次这样的高抛低吸，也能减少大量库存，并且实现盈利，怎么建立这样的数学模型

我希望用Mojo和Python语言做一个可以供AI调用的，加密货币合约市场分钟级的，高性能的事件驱动的策略回测和优化系统 作为MCP(Model Context Protocol)服务，用来帮助AI挖掘量化交易策略，而且同时可以供研究人员开发策略，其中Python负责数据的获取，保存到arcticdb或dolphindb，可视化k线以及指标，回测结果，绩效分析，Mojo通过numpy或其他方式调用python中读取的数据，负责核心策略编写和回测以及优化，并将结果返回给python，希望你能在此基础上参考nautilus_trader，hummingbot，quantaxis，freqtrade和banbot这些量化交易软件的设计思路，帮我设计一个构建这个模块化系统的蓝图，让其他AI可以按这个蓝图分模块实现这个系统

阅读mojo语言和magic项目管理器的文档，按照这个markdown蓝图的设计，分步实现这个MCPQuant项目，arcticdb数据库数据已经在/home/<USER>/Projects/Directrader-Local/arcticdb 准备好了，可以先从最核心的mojo通过调用python的arcticdb接口获取pd.Dataframe数据开始，实现策略回测核心部分

请你重新设计基于本地地址/home/<USER>/Projects/Directrader-Local/arcticdb数据库arcticdb的DataProvider，和其他所需Python模块
注意这个arcticdb的libraries为binance_futures， 其通过get_symbols获得的是['4h', '30m', '1m', '5m', '2h', '15m', '1h', '3m']这些时间周期，而在每个周期下储存了所有交易对，字段是code，另外所需的数据列为
["open", "high", "low", "close", "volume", "buy_volume", "quote", "buy_quote"]
