"""
动态均值图表示例
演示如何在Plotly图表中实现选中legend的动态均值计算和显示
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
from datetime import datetime, timedelta

def create_sample_data():
    """创建示例数据"""
    # 生成时间序列
    start_date = datetime(2024, 1, 1)
    dates = [start_date + timedelta(days=i) for i in range(100)]
    
    # 生成多个代码的累计收益数据
    np.random.seed(42)
    codes = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT']
    
    data = {}
    for code in codes:
        # 生成随机收益率
        returns = np.random.normal(0.001, 0.02, 100)  # 日收益率
        # 计算累计收益
        cumulative_returns = np.cumprod(1 + returns) - 1
        data[code] = cumulative_returns
    
    df = pd.DataFrame(data, index=dates)
    return df

def create_dynamic_mean_chart(df, title="Dynamic Mean Chart"):
    """
    创建支持动态均值计算的交互式图表
    
    Args:
        df: DataFrame，包含多个时间序列数据
        title: 图表标题
    
    Returns:
        HTML字符串
    """
    
    # 创建Plotly图表
    fig = go.Figure()
    
    # 计算夏普比率（示例）
    sharp = (df.mean() / df.std()).round(4)
    
    # 为每个列添加一条线
    for col in df.columns:
        fig.add_trace(go.Scatter(
            x=df.index,
            y=df[col],
            mode='lines',
            name=f'{col}_shp{sharp[col]:.4f}',
            opacity=0.7,
            visible=True,  # 默认可见
            customdata=[col] * len(df.index),  # 存储列名用于计算
        ))
    
    # 添加均值线（初始为空，将通过JavaScript动态更新）
    fig.add_trace(go.Scatter(
        x=df.index,
        y=[0] * len(df.index),  # 初始值
        mode='lines',
        name='Selected Mean',
        line=dict(color='red', width=3, dash='dash'),
        visible=False,  # 初始不可见
        showlegend=True
    ))
    
    # 更新布局
    fig.update_layout(
        title=dict(
            text=title,
            x=0.5,  # 水平居中
            xanchor='center'  # 确保标题以中心为锚点
        ),
        xaxis_title='Time',
        yaxis_title='Cumulative Returns',
        legend_title='Assets (Click to select/deselect)',
        template='plotly_white',
        hovermode='x unified',
        showlegend=True,
        height=600
    )
    
    # 添加网格和交互功能
    fig.update_xaxes(
        tickformat="%Y-%m-%d",  # 日期格式
        hoverformat="%Y-%m-%d",  # 悬停时显示的日期格式
        showspikes=True,  # 显示垂直延长线
        spikethickness=1,
        spikecolor="gray",
        spikesnap="cursor",
        spikemode="across",
        spikedash="solid",
        showgrid=True, 
        gridwidth=1, 
        gridcolor='LightGrey'
    )
    fig.update_yaxes(
        tickformat=".4f",  # 刻度格式：4位小数
        hoverformat=".4f",  # 悬停时显示的数值格式
        showspikes=True,  # 显示水平延长线
        spikethickness=1,
        spikecolor="gray",
        spikesnap="cursor",
        spikemode="across",
        spikedash="solid",
        showgrid=True, 
        gridwidth=1, 
        gridcolor='LightGrey'
    )
    
    # JavaScript代码实现动态均值计算
    mean_calculation_script = f'''
    <script>
        // 存储原始数据
        var originalData = {df.to_json(orient='index')};
        var columnNames = {list(df.columns)};
        var timeIndex = {[str(t) for t in df.index]};
        
        // 等待图表加载完成
        document.addEventListener('DOMContentLoaded', function() {{
            var gd = document.getElementsByClassName('plotly-graph-div')[0];
            if (!gd) return;
            
            // 监听图例点击事件
            gd.on('plotly_legendclick', function(data) {{
                setTimeout(function() {{
                    updateMeanLine();
                }}, 100); // 延迟执行，确保图例状态已更新
                return true; // 允许默认的图例点击行为
            }});
            
            // 监听图例双击事件
            gd.on('plotly_legenddoubleclick', function(data) {{
                setTimeout(function() {{
                    updateMeanLine();
                }}, 100);
                return true;
            }});
            
            function updateMeanLine() {{
                var visibleTraces = [];
                var meanTraceIndex = gd.data.length - 1; // 均值线是最后一个trace
                
                // 找出当前可见的数据线（排除均值线）
                for (var i = 0; i < gd.data.length - 1; i++) {{
                    if (gd.data[i].visible === true || gd.data[i].visible === undefined) {{
                        visibleTraces.push(i);
                    }}
                }}
                
                if (visibleTraces.length === 0) {{
                    // 如果没有可见的线，隐藏均值线
                    Plotly.restyle(gd, {{'visible': false}}, [meanTraceIndex]);
                    return;
                }}
                
                // 计算选中线条的均值
                var meanValues = [];
                var selectedCodes = [];
                
                for (var i = 0; i < timeIndex.length; i++) {{
                    var sum = 0;
                    var count = 0;
                    
                    for (var j = 0; j < visibleTraces.length; j++) {{
                        var traceIndex = visibleTraces[j];
                        var colName = columnNames[traceIndex];
                        selectedCodes.push(colName);
                        
                        if (gd.data[traceIndex].y && gd.data[traceIndex].y[i] !== undefined) {{
                            sum += gd.data[traceIndex].y[i];
                            count++;
                        }}
                    }}
                    
                    meanValues.push(count > 0 ? sum / count : 0);
                }}
                
                // 去重选中的代码名称
                selectedCodes = [...new Set(selectedCodes)];
                
                // 计算选中线条的平均夏普比率
                var totalSharp = 0;
                for (var k = 0; k < selectedCodes.length; k++) {{
                    var codeName = selectedCodes[k];
                    var sharpValue = parseFloat(gd.data[visibleTraces[k]].name.split('_shp')[1]);
                    if (!isNaN(sharpValue)) {{
                        totalSharp += sharpValue;
                    }}
                }}
                var avgSharp = selectedCodes.length > 0 ? (totalSharp / selectedCodes.length).toFixed(4) : '0.0000';
                
                // 更新均值线
                var meanName = 'Mean of ' + selectedCodes.length + ' selected (avg_shp' + avgSharp + ')';
                
                Plotly.restyle(gd, {{
                    'y': [meanValues],
                    'visible': true,
                    'name': meanName
                }}, [meanTraceIndex]);
                
                console.log('Updated mean line for:', selectedCodes);
            }}
            
            // 初始化时计算所有线的均值
            updateMeanLine();
        }});
    </script>
    '''
    
    # 获取基本的HTML内容
    basic_html = fig.to_html(
        include_plotlyjs='cdn',
        include_mathjax=False,
        full_html=False
    )
    
    # 创建完整的HTML文件
    full_html = f'''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{title}</title>
        <style>
            body {{
                margin: 0;
                padding: 20px;
                font-family: Arial, sans-serif;
                background-color: #f5f5f5;
            }}
            .container {{
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .plotly-graph-div {{
                width: 100% !important;
                height: 600px !important;
            }}
            .instructions {{
                margin-bottom: 20px;
                padding: 15px;
                background-color: #e3f2fd;
                border-left: 4px solid #2196f3;
                border-radius: 4px;
            }}
            .instructions h3 {{
                margin-top: 0;
                color: #1976d2;
            }}
            .instructions ul {{
                margin-bottom: 0;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="instructions">
                <h3>使用说明</h3>
                <ul>
                    <li><strong>单击图例</strong>：显示/隐藏对应的数据线</li>
                    <li><strong>双击图例</strong>：只显示该数据线，隐藏其他线</li>
                    <li><strong>红色虚线</strong>：当前选中（可见）数据线的均值</li>
                    <li><strong>均值线名称</strong>：显示选中线条数量和平均夏普比率</li>
                    <li><strong>悬停</strong>：查看具体数值和时间</li>
                </ul>
            </div>
            {basic_html}
            {mean_calculation_script}
        </div>
    </body>
    </html>
    '''
    
    return full_html

def main():
    """主函数"""
    # 创建示例数据
    df = create_sample_data()
    
    # 创建动态均值图表
    html_content = create_dynamic_mean_chart(df, "加密货币累计收益动态均值图表")
    
    # 保存为HTML文件
    output_file = "dynamic_mean_chart_example.html"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"图表已保存为: {output_file}")
    print("请在浏览器中打开该文件查看效果")
    print("\n功能说明:")
    print("1. 单击图例中的任意项目可以显示/隐藏对应的数据线")
    print("2. 双击图例项目可以只显示该线，隐藏其他所有线")
    print("3. 红色虚线会实时显示当前选中（可见）线条的均值")
    print("4. 均值线的名称会显示选中的线条数量和平均夏普比率")

if __name__ == "__main__":
    main()
