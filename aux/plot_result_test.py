import torch
from tianshou.data import Batch, to_numpy

from utils.config_template import TrainingConfig
from quant_env.kline_data_source import KlineDataSource
from quant_env.envs import TradingEnv
from train import get_policy


def get_action(policy, observation):
	batch = Batch(obs=[observation], info={})
	result = policy(batch)
	return to_numpy(result.act)

def plot_result(cfg: TrainingConfig):
	state_dict = torch.load(cfg.model_path, map_location=torch.device('cpu'))
	cfg.use_solo = True
	cfg.account.cash.is_seperated = False
	# cfg.augment.in_use = True
	# cfg.augment.index = 1
	cfg.episode_step_count.test = 600
	# task = cfg.tasks[cfg.task_index]
	# cfg.task.index = 0
	# cfg.n_codes = 1
	# cfg.test_num = 16
	# cfg.augment.in_front = True
	# cfg.feature.buy_sell = False
	# cfg.interval_cfg.base = '5m' 
	data_source = KlineDataSource(cfg, is_training=False)
	env_index = cfg.test_num - 1
	test_data_source = data_source.get_slice_by(env_index)
	env = TradingEnv(cfg, test_data_source, env_index)
	
	cfg.feature.space = env.observation_space
	cfg.action.space = env.action_space
	cfg.feature.shape = env.observation_space.shape or env.observation_space.n
	cfg.action.shape = env.action_space.shape or env.action_space.n
	cfg.action.dim = env.action_dim
	cfg.n_extra_features = env.n_extra_features
	cfg.n_major_features = env.n_major_features
	cfg.n_features = env.n_features
	
	policy = get_policy(cfg)		
	policy.load_state_dict(state_dict)
	policy.eval()
	obs, info = env.reset()
	for _ in env.step_index_range:
		action = get_action(policy, obs)
		obs = env.step(action)[0]
	env.plot_result()
	
	
if __name__ == '__main__':
	# config = get_config_dict('best_model/train.json')
	from best_model.config import cfg
	plot_result(cfg)