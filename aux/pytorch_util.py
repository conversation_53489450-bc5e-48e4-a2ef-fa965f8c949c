import torch
import numpy as np


def soft_update_from_to(source, target, tau):
	for label_param, param in zip(target.parameters(), source.parameters()):
		label_param.data.copy_(label_param.data * (1.0 - tau) + param.data * tau)


def copy_model_params_from_to(source, target):
	for label_param, param in zip(target.parameters(), source.parameters()):
		label_param.data.copy_(param.data)


def fanin_init(tensor):
	size = tensor.size()
	if len(size) == 2:
		fan_in = size[0]
	elif len(size) > 2:
		fan_in = np.prod(size[1:])
	else:
		raise Exception("Shape must be have dimension at least 2.")
	bound = 1. / np.sqrt(fan_in)
	return tensor.data.uniform_(-bound, bound)


def fanin_init_weights_like(tensor):
	size = tensor.size()
	if len(size) == 2:
		fan_in = size[0]
	elif len(size) > 2:
		fan_in = np.prod(size[1:])
	else:
		raise Exception("Shape must be have dimension at least 2.")
	bound = 1. / np.sqrt(fan_in)
	new_tensor = FloatTensor(tensor.size())
	new_tensor.uniform_(-bound, bound)
	return new_tensor


"""
GPU wrappers
"""

_use_gpu = False
device = None
_gpu_id = 0


def set_gpu_mode(mode, gpu_id=0):
	global _use_gpu
	global device
	global _gpu_id
	_gpu_id = gpu_id
	_use_gpu = mode
	device = torch.device("cuda:" + str(gpu_id) if _use_gpu else "cpu")
	torch.cuda.set_device(device)


def gpu_enabled():
	return _use_gpu


def set_device(gpu_id):
	torch.cuda.set_device(gpu_id)


# noinspection PyPep8Naming
def FloatTensor(*args, torch_device=None, **kwargs):
	if torch_device is None:
		torch_device = device
	return torch.FloatTensor(*args, **kwargs, device=torch_device)


def from_numpy(*args, **kwargs):
	return torch.from_numpy(*args, **kwargs).float().to(device)


def get_numpy(tensor):
	return tensor.to('cpu').detach().numpy()


def zeros(*sizes, torch_device=None, **kwargs):
	if torch_device is None:
		torch_device = device
	return torch.zeros(*sizes, **kwargs, device=torch_device)


def ones(*sizes, torch_device=None, **kwargs):
	if torch_device is None:
		torch_device = device
	return torch.ones(*sizes, **kwargs, device=torch_device)


def ones_like(*args, torch_device=None, **kwargs):
	if torch_device is None:
		torch_device = device
	return torch.ones_like(*args, **kwargs, device=torch_device)


def randn(*args, torch_device=None, **kwargs):
	if torch_device is None:
		torch_device = device
	return torch.randn(*args, **kwargs, device=torch_device)


def rand(*args, torch_device=None, **kwargs):
	if torch_device is None:
		torch_device = device
	return torch.rand(*args, **kwargs, device=torch_device)


def zeros_like(*args, torch_device=None, **kwargs):
	if torch_device is None:
		torch_device = device
	return torch.zeros_like(*args, **kwargs, device=torch_device)


def tensor(*args, torch_device=None, **kwargs):
	if torch_device is None:
		torch_device = device
	return torch.tensor(*args, **kwargs, device=torch_device)


def normal(*args, **kwargs):
	return torch.normal(*args, **kwargs).to(device)


def fast_clip_grad_norm(parameters, max_norm):
	r"""Clips gradient norm of an iterable of parameters.
	Only support norm_type = 2
	max_norm = 0, skip the total norm calculation and return 0
	https://pytorch.org/docs/stable/_modules/torch/nn/utils/clip_grad.html#clip_grad_norm_
	Returns:
		Total norm of the parameters (viewed as a single vector).
	"""
	max_norm = float(max_norm)
	if abs(max_norm) < 1e-6:  # max_norm = 0
		return 0
	else:
		if isinstance(parameters, torch.Tensor):
			parameters = [parameters]
		parameters = list(filter(lambda p: p.grad is not None, parameters))
		total_norm = torch.stack([(p.grad.detach().pow(2)).sum() for p in parameters]).sum().sqrt().item()
		clip_coef = max_norm / (total_norm + 1e-6)
		if clip_coef < 1:
			for p in parameters:
				p.grad.detach().mul_(clip_coef)
		return total_norm


# import torch
import torch.nn as nn
from torch.distributions.utils import _standard_normal
from torch.distributions import Normal
from typing import Iterable


# NN weight utils
def weight_init(m):
	if isinstance(m, nn.Linear):
		nn.init.orthogonal_(m.weight.data)
		if hasattr(m.bias, 'data'):
			m.bias.data.fill_(0.0)
	elif isinstance(m, nn.Conv2d) or isinstance(m, nn.ConvTranspose2d):
		gain = nn.init.calculate_gain('relu')
		nn.init.orthogonal_(m.weight.data, gain)
		if hasattr(m.bias, 'data'):
			m.bias.data.fill_(0.0)


def soft_update(target, source, tau):
	for label_param, param in zip(target.parameters(), source.parameters()):
		label_param.data.copy_(label_param.data * (1.0 - tau) + param.data * tau)


def hard_update(target, source):
	for label_param, param in zip(target.parameters(), source.parameters()):
		label_param.data.copy_(param.data)


# NN module utils

def get_parameters(modules: Iterable[nn.Module]):
	model_parameters = []
	for module in modules:
		model_parameters += list(module.parameters())
	return model_parameters


class FreezeParameters:
	def __init__(self, modules: Iterable[nn.Module]):
		self.modules = modules
		self.param_states = [p.requires_grad for p in get_parameters(self.modules)]
	
	def __enter__(self):
		for param in get_parameters(self.modules):
			param.requires_grad = False
	
	def __exit__(self, exc_type, exc_val, exc_tb):
		for i, param in enumerate(get_parameters(self.modules)):
			param.requires_grad = self.param_states[i]


# torch dist utils

class TruncatedNormal(Normal):
	def enumerate_support(self, expand=True):
		pass
	
	def __init__(self, loc, scale, low=-1.0, high=1.0, eps=1e-6):
		super().__init__(loc, scale, valate_args=False)
		self.low = low
		self.high = high
		self.eps = eps
	
	def _clamp(self, x):
		clamped_x = torch.clamp(x, self.low + self.eps, self.high - self.eps)
		x = x - x.detach() + clamped_x.detach()
		return x
	
	def sample(self, clip=None, sample_shape=torch.Size()):
		shape = self._extended_shape(sample_shape)
		eps = _standard_normal(shape, dtype=self.loc.dtype,	device=self.loc.device)
		eps *= self.scale
		if clip is not None:
			eps = torch.clamp(eps, -clip, clip)
		x = self.loc + eps
		return self._clamp(x)