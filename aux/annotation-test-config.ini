#todo:
(reversal trade mode), (multi-custom_step change shift), 
(split dataset and test), (live mode train and val),
(non-time-based bar), (continuous flip data) for expert trajectory!!

transfer learning
(batch norm), (L2 w = 1e-4), (dropout), 
information bottleneck beta = 0.05, 
early stop, voting, (action mask?)
(tcn), (cqn), (D2RL/DenseNet), (lr scheduler)
(more features),  * -1 if position is negative? 
(qrdqn, iqn, fqf?x, sacd, sac, dsac?x, ppo?x, ppod?x)
(Munchausen?), 

model based rl algo, (alm?) in sac
compress model,
(max drawdown), differential (sharp ratio!!), volatility stop loss, 
(plot!!), (tensorboard)
(cut loss!!), (shuffle folds), 
(random start), (shuffle fix/random length episode)
(save top k model), (paper/live trading), 
(sts += [non-split, pnl per fee])
(plot test results), send msg to dingding or telegram
record trade data, trade execution,
(use limit order, estimate slippage)

analyze asset return kurtosis and skewness
cross grid/quantile features compared with normalized features
auto feature engineering/genetic feature selection
State Model/SCINet/NLinear/DLinear/FlowFormer/recurrent/tcn encoder, 
mlp Regularization is all you Need,
layer norm, noise feature filter
offline, awac, cql, DT, imitation, from demonstration
reload replay buffer, goal, her, udrl,
sample trajectory of sequences with priority, trajectory gradient descent
fast MACD?, RSRS indicator, 
maybe delayed zigzag indicator/classification/feature/expert behavior
(roundtrip env, numpy)/(polars? portfolio env), insert neutral into ranks 
quantile loss is good but computation expansive 
huber loss reduce penalty to squared error, layer norm
predict n step ahead might compensate n +- m step prediction lag
add 'reverse ratio', 'pnl per notional' in sts
unify sts_dict and statistics

factors:
walk_path_efficiency = sum(price_changes[-recent_index:])/sum(abs(price_changes))
money_efficiency = sum(abs(price_changes)) / sum(quote)