# -*- coding:utf-8 -*-

from setuptools import setup


setup(
    name="aed_quant",
    version="0.1.1",
    packages=[
        "aed_quant",
        "aed_quant.utils",
        "aed_quant.platform",
    ],
    description="Asynchronous event driven quantitative trading framework.",
    # url="https://github.com/TheNextQuant/thenextquant",
    author="CyberQuant",
    author_email="<EMAIL>",
    license="MIT",
    keywords=[
        "aed_quant", "quant", "framework", "async", "asynchronous", "digiccy", "digital", "currency",
        "marketmaker", "binance", "okex", "huobi", "bitmex", "deribit", "kraken", "gemini", "kucoin", "digifinex"
    ],
    install_requires=[
        "aiohttp>=3.8.1",
        "aioamqp==0.14.0",
        "motor>=3.0.0"
    ],
)
