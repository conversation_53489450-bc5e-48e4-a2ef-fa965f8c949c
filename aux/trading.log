I [2024-07-18 03:54:26,513] [-] [EventCenter.connect] host: 127.0.0.1 port: 5672 
I [2024-07-18 03:54:26,521] Recv open ok
I [2024-07-18 03:54:26,523] [-] [EventCenter.connect] Rabbitmq initialize success! 
I [2024-07-18 03:54:26,869] [-] [Quant.start] start io loop ... 
I [2024-07-18 03:54:26,901] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.ADAUSDT ROUTING_KEY: binance_future.ADAUSDT 
I [2024-07-18 03:54:26,901] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.ADAUSDT ROUTING_KEY: binance_future.ADAUSDT 
I [2024-07-18 03:54:26,901] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.ATOMUSDT ROUTING_KEY: binance_future.ATOMUSDT 
I [2024-07-18 03:54:26,901] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.ATOMUSDT ROUTING_KEY: binance_future.ATOMUSDT 
I [2024-07-18 03:54:26,901] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.AVAXUSDT ROUTING_KEY: binance_future.AVAXUSDT 
I [2024-07-18 03:54:26,901] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.AVAXUSDT ROUTING_KEY: binance_future.AVAXUSDT 
I [2024-07-18 03:54:26,901] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.AXSUSDT ROUTING_KEY: binance_future.AXSUSDT 
I [2024-07-18 03:54:26,901] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.AXSUSDT ROUTING_KEY: binance_future.AXSUSDT 
I [2024-07-18 03:54:26,901] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.BCHUSDT ROUTING_KEY: binance_future.BCHUSDT 
I [2024-07-18 03:54:26,901] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.BCHUSDT ROUTING_KEY: binance_future.BCHUSDT 
I [2024-07-18 03:54:26,901] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.BNBUSDT ROUTING_KEY: binance_future.BNBUSDT 
I [2024-07-18 03:54:26,901] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.BNBUSDT ROUTING_KEY: binance_future.BNBUSDT 
I [2024-07-18 03:54:26,901] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.BTCUSDT ROUTING_KEY: binance_future.BTCUSDT 
I [2024-07-18 03:54:26,901] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.BTCUSDT ROUTING_KEY: binance_future.BTCUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.DOGEUSDT ROUTING_KEY: binance_future.DOGEUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.DOGEUSDT ROUTING_KEY: binance_future.DOGEUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.DOTUSDT ROUTING_KEY: binance_future.DOTUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.DOTUSDT ROUTING_KEY: binance_future.DOTUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.EOSUSDT ROUTING_KEY: binance_future.EOSUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.EOSUSDT ROUTING_KEY: binance_future.EOSUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.ETCUSDT ROUTING_KEY: binance_future.ETCUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.ETCUSDT ROUTING_KEY: binance_future.ETCUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.ETHUSDT ROUTING_KEY: binance_future.ETHUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.ETHUSDT ROUTING_KEY: binance_future.ETHUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.FILUSDT ROUTING_KEY: binance_future.FILUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.FILUSDT ROUTING_KEY: binance_future.FILUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.FTMUSDT ROUTING_KEY: binance_future.FTMUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.FTMUSDT ROUTING_KEY: binance_future.FTMUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.LINKUSDT ROUTING_KEY: binance_future.LINKUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.LINKUSDT ROUTING_KEY: binance_future.LINKUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.LTCUSDT ROUTING_KEY: binance_future.LTCUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.LTCUSDT ROUTING_KEY: binance_future.LTCUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.MATICUSDT ROUTING_KEY: binance_future.MATICUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.MATICUSDT ROUTING_KEY: binance_future.MATICUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.NEARUSDT ROUTING_KEY: binance_future.NEARUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.NEARUSDT ROUTING_KEY: binance_future.NEARUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.SOLUSDT ROUTING_KEY: binance_future.SOLUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.SOLUSDT ROUTING_KEY: binance_future.SOLUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_ORDERBOOK EXCHANGE: Orderbook QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.XRPUSDT ROUTING_KEY: binance_future.XRPUSDT 
I [2024-07-18 03:54:26,902] [-] [EventCenter.subscribe] NAME: EVENT_KLINE_4HOUR EXCHANGE: Kline.4hour QUEUE: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.XRPUSDT ROUTING_KEY: binance_future.XRPUSDT 
I [2024-07-18 03:54:26,965] [-] [Websocket._connect] url: wss://fstream.binance.com/ws/Joifjjbyf8QIMBNwksBqqNDGyteeFBlLPHqWJJRh20LW2k9kVBhxBodsJfTDiiz2 
I [2024-07-18 03:54:27,108] [-] [BinanceFutureTrade.connected_callback] Websocket connection authorized successfully. 
I [2024-07-18 03:54:27,509] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: ***********_af1c07dc449811efb80500, client_order_id: None, action: BUY, symbol: DOTUSDT, price: 6.246, quantity: 2.3, remain: 2.3, status: SUBMITTED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 03:54:27,732] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: ***********_af23cb66449811efb80500, client_order_id: None, action: BUY, symbol: FTMUSDT, price: 0.508100, quantity: 42, remain: 42.0, status: SUBMITTED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 03:54:28,003] [-] [AsyncPortfolioManager.on_event_init_success_callback] initialize success: True 
I [2024-07-18 03:54:28,095] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: BNBUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,096] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: ETCUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,098] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: DOGEUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,099] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: LTCUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,100] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: ATOMUSDT, quantity: 2.96, avg_price: 6.************, short_quantity: 0, short_avg_price: 0, long_quantity: 2.96, long_avg_price: 6.************, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,103] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: FILUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,104] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: SOLUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,105] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: XRPUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,106] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: DOTUSDT, quantity: -1.5, avg_price: 6.382, short_quantity: 1.5, short_avg_price: 6.382, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,107] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: ADAUSDT, quantity: 29.0, avg_price: 0.*************, short_quantity: 0, short_avg_price: 0, long_quantity: 29.0, long_avg_price: 0.*************, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,108] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: ETHUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,109] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: LINKUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,110] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: EOSUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,111] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: NEARUSDT, quantity: 9.0, avg_price: 6.************, short_quantity: 0, short_avg_price: 0, long_quantity: 9.0, long_avg_price: 6.************, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,112] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: BCHUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,112] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: AVAXUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,113] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: MATICUSDT, quantity: 94.0, avg_price: 0.54946, short_quantity: 0, short_avg_price: 0, long_quantity: 94.0, long_avg_price: 0.54946, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,114] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: BTCUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,115] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: FTMUSDT, quantity: 37.0, avg_price: 0.5186, short_quantity: 0, short_avg_price: 0, long_quantity: 37.0, long_avg_price: 0.5186, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:28,116] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: AXSUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 03:54:29,331] [-] [AsyncPortfolioManager.init_data_module] data module ready: True 
I [2024-07-18 03:54:31,535] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.ADAUSDT 
I [2024-07-18 03:54:31,539] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.ADAUSDT 
I [2024-07-18 03:54:31,543] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.ATOMUSDT 
I [2024-07-18 03:54:31,547] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.ATOMUSDT 
I [2024-07-18 03:54:31,552] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.AVAXUSDT 
I [2024-07-18 03:54:31,556] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.AVAXUSDT 
I [2024-07-18 03:54:31,559] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.AXSUSDT 
I [2024-07-18 03:54:31,563] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.AXSUSDT 
I [2024-07-18 03:54:31,567] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.BCHUSDT 
I [2024-07-18 03:54:31,570] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.BCHUSDT 
I [2024-07-18 03:54:31,574] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.BNBUSDT 
I [2024-07-18 03:54:31,578] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.BNBUSDT 
I [2024-07-18 03:54:31,582] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.BTCUSDT 
I [2024-07-18 03:54:31,586] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.BTCUSDT 
I [2024-07-18 03:54:31,589] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.DOGEUSDT 
I [2024-07-18 03:54:31,593] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.DOGEUSDT 
I [2024-07-18 03:54:31,596] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.DOTUSDT 
I [2024-07-18 03:54:31,600] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.DOTUSDT 
I [2024-07-18 03:54:31,603] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.EOSUSDT 
I [2024-07-18 03:54:31,607] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.EOSUSDT 
I [2024-07-18 03:54:31,611] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.ETCUSDT 
I [2024-07-18 03:54:31,614] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.ETCUSDT 
I [2024-07-18 03:54:31,618] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.ETHUSDT 
I [2024-07-18 03:54:31,621] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.ETHUSDT 
I [2024-07-18 03:54:31,625] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.FILUSDT 
I [2024-07-18 03:54:31,628] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.FILUSDT 
I [2024-07-18 03:54:31,632] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.FTMUSDT 
I [2024-07-18 03:54:31,635] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.FTMUSDT 
I [2024-07-18 03:54:31,638] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.LINKUSDT 
I [2024-07-18 03:54:31,642] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.LINKUSDT 
I [2024-07-18 03:54:31,645] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.LTCUSDT 
I [2024-07-18 03:54:31,649] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.LTCUSDT 
I [2024-07-18 03:54:31,652] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.MATICUSDT 
I [2024-07-18 03:54:31,656] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.MATICUSDT 
I [2024-07-18 03:54:31,659] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.NEARUSDT 
I [2024-07-18 03:54:31,663] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.NEARUSDT 
I [2024-07-18 03:54:31,666] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.SOLUSDT 
I [2024-07-18 03:54:31,670] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.SOLUSDT 
I [2024-07-18 03:54:31,674] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Orderbook.binance_future.XRPUSDT 
I [2024-07-18 03:54:31,678] [-] [EventCenter._initialize] queue: 6d4a9348-44b9-11ef-85d9-02001702659f.Kline.4hour.binance_future.XRPUSDT 
I [2024-07-18 04:00:01,342] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "ETHUSDT", "open": "3386.01", "high": "3440.00", "low": "3381.83", "close": "3429.99", "volume": "269858.152", "buy_volume": "140199.257", "quote": "921624792.94458", "buy_quote": "478826222.36659", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,377] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "BNBUSDT", "open": "568.800", "high": "576.850", "low": "568.680", "close": "574.050", "volume": "63461.24", "buy_volume": "32282.52", "quote": "36365757.41390", "buy_quote": "18497170.77820", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,380] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "ATOMUSDT", "open": "6.480", "high": "6.578", "low": "6.479", "close": "6.548", "volume": "865455.28", "buy_volume": "441394.62", "quote": "5659004.15341", "buy_quote": "2885719.48682", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,382] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "BTCUSDT", "open": "64055.80", "high": "65173.80", "low": "63871.20", "close": "64827.80", "volume": "35420.270", "buy_volume": "18598.061", "quote": "2285974048.90780", "buy_quote": "1200682989.24770", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,384] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "LINKUSDT", "open": "13.730", "high": "13.999", "low": "13.729", "close": "13.868", "volume": "1184824.50", "buy_volume": "600964.30", "quote": "16435548.43162", "buy_quote": "8336192.40040", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,386] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "AVAXUSDT", "open": "27.2250", "high": "27.7840", "low": "27.2170", "close": "27.7380", "volume": "952520", "buy_volume": "495200", "quote": "26247947.8170", "buy_quote": "13645087.3980", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,388] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "DOGEUSDT", "open": "0.121720", "high": "0.123900", "low": "0.121700", "close": "0.123300", "volume": "335955490", "buy_volume": "170204745", "quote": "41316009.400860", "buy_quote": "20936616.291660", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,390] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "BCHUSDT", "open": "375.01", "high": "384.00", "low": "374.85", "close": "381.08", "volume": "55003.088", "buy_volume": "27385.379", "quote": "20864145.35355", "buy_quote": "10387216.52290", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,393] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "MATICUSDT", "open": "0.53920", "high": "0.55050", "low": "0.53910", "close": "0.54960", "volume": "29854768", "buy_volume": "15885955", "quote": "16313301.85590", "buy_quote": "8675246.73490", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,395] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "SOLUSDT", "open": "155.4660", "high": "159.9120", "low": "155.4660", "close": "158.8850", "volume": "1910102", "buy_volume": "975321", "quote": "301915455.3450", "buy_quote": "154168008.5420", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,397] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "FTMUSDT", "open": "0.508500", "high": "0.519000", "low": "0.508500", "close": "0.514100", "volume": "17558317", "buy_volume": "8441022", "quote": "9030982.873900", "buy_quote": "4340440.800300", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,402] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "DOTUSDT", "open": "6.250", "high": "6.402", "low": "6.248", "close": "6.363", "volume": "2461789.7", "buy_volume": "1258105.9", "quote": "15600688.4908", "buy_quote": "7972017.8847", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,404] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "AXSUSDT", "open": "5.99400", "high": "6.34200", "low": "5.99400", "close": "6.31900", "volume": "1363313", "buy_volume": "728575", "quote": "8485553.78300", "buy_quote": "4531003.40300", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,409] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "ETCUSDT", "open": "22.950", "high": "23.351", "low": "22.948", "close": "23.275", "volume": "435033.42", "buy_volume": "223307.17", "quote": "10085642.91255", "buy_quote": "5176602.77085", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,420] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "ADAUSDT", "open": "0.43570", "high": "0.44330", "low": "0.43560", "close": "0.44110", "volume": "51043808", "buy_volume": "25825200", "quote": "22464766.66660", "buy_quote": "11363204.72980", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,422] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "FILUSDT", "open": "4.553", "high": "4.655", "low": "4.546", "close": "4.602", "volume": "5795631.8", "buy_volume": "2786812.8", "quote": "26686803.4724", "buy_quote": "12832072.9043", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,459] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "XRPUSDT", "open": "0.6256", "high": "0.6366", "low": "0.6006", "close": "0.6069", "volume": "482231856.6", "buy_volume": "228494305.2", "quote": "296979922.18793", "buy_quote": "140712449.22795", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:01,469] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "NEARUSDT", "open": "6.0680", "high": "6.2320", "low": "6.0680", "close": "6.1940", "volume": "3609063", "buy_volume": "1773769", "quote": "22232788.0060", "buy_quote": "10929057.5710", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:03,026] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "LTCUSDT", "open": "71.32", "high": "72.39", "low": "71.32", "close": "72.35", "volume": "179475.312", "buy_volume": "98001.579", "quote": "12923282.16268", "buy_quote": "7056954.87764", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:03,049] [-] [AsyncPortfolioManager.on_event_kline_update] kline: {"platform": "binance_future", "symbol": "EOSUSDT", "open": "0.600", "high": "0.611", "low": "0.600", "close": "0.609", "volume": "********.5", "buy_volume": "9579943.8", "quote": "********.0294", "buy_quote": "5810878.4807", "timestamp": *************, "kline_type": "kline_4h", "is_closed": true} 
I [2024-07-18 04:00:03,051] [-] [AsyncPortfolioManager.cancel_order] cancelling order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: ***********_af1c07dc449811efb80500, client_order_id: None, action: BUY, symbol: DOTUSDT, price: 6.246, quantity: 2.3, remain: 2.3, status: SUBMITTED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:00:03,052] [-] [AsyncPortfolioManager.cancel_order] cancelling order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: ***********_af23cb66449811efb80500, client_order_id: None, action: BUY, symbol: FTMUSDT, price: 0.508100, quantity: 42, remain: 42.0, status: SUBMITTED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:00:03,096] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: ***********_af1c07dc449811efb80500, client_order_id: None, action: BUY, symbol: DOTUSDT, price: 6.246, quantity: 2.3, remain: 2.3, status: CANCELED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:00:03,101] [-] [AsyncPortfolioManager.cancel_order] order cancelling result: *********** 
I [2024-07-18 04:00:03,200] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: ***********_af23cb66449811efb80500, client_order_id: None, action: BUY, symbol: FTMUSDT, price: 0.508100, quantity: 42, remain: 42.0, status: CANCELED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:00:03,201] [-] [AsyncPortfolioManager.cancel_order] order cancelling result: *********** 
I [2024-07-18 04:00:04,353] [-] [AsyncPortfolioManager.on_event_kline_update] label_ratio_arr = array([-0.********, -0.********, -0.********, -0.********, -0.        ,\n       -0.        , -0.        , -0.********, -0.********, -0.********,\n       -0.0057838 , -0.        , -0.0048864 , -0.********, -0.********,\n       -0.        , -0.********, -0.********, -0.********, -0.        ]) 
I [2024-07-18 04:00:04,354] [-] [AsyncPortfolioManager.on_event_kline_update] sending order: ADAUSDT LIMIT SELL 31.0 @ 0.4414 
I [2024-07-18 04:00:04,397] [-] [AsyncPortfolioManager.on_event_kline_update] order sending result:  42837695591_36ab073644ba11ef85d900 
I [2024-07-18 04:00:04,397] [-] [AsyncPortfolioManager.on_event_kline_update] sending order: ATOMUSDT LIMIT SELL 3.18 @ 6.552 
I [2024-07-18 04:00:04,398] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 42837695591_36ab073644ba11ef85d900, client_order_id: None, action: SELL, symbol: ADAUSDT, price: 0.44140, quantity: 31, remain: 31.0, status: SUBMITTED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:00:04,438] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 19751877313_36b166da44ba11ef85d900, client_order_id: None, action: SELL, symbol: ATOMUSDT, price: 6.552, quantity: 3.18, remain: 3.18, status: SUBMITTED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:00:04,438] [-] [AsyncPortfolioManager.on_event_kline_update] order sending result:  19751877313_36b166da44ba11ef85d900 
I [2024-07-18 04:00:04,438] [-] [AsyncPortfolioManager.on_event_kline_update] sending order: DOTUSDT LIMIT BUY 1.3 @ 6.359 
I [2024-07-18 04:00:04,479] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 23980708059_36b7b99a44ba11ef85d900, client_order_id: None, action: BUY, symbol: DOTUSDT, price: 6.359, quantity: 1.3, remain: 1.3, status: SUBMITTED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:00:04,480] [-] [AsyncPortfolioManager.on_event_kline_update] order sending result:  23980708059_36b7b99a44ba11ef85d900 
I [2024-07-18 04:00:04,480] [-] [AsyncPortfolioManager.on_event_kline_update] sending order: FTMUSDT LIMIT SELL 42.0 @ 0.5144 
I [2024-07-18 04:00:04,522] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 22161657949_36be1af644ba11ef85d900, client_order_id: None, action: SELL, symbol: FTMUSDT, price: 0.514400, quantity: 42, remain: 42.0, status: SUBMITTED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:00:04,523] [-] [AsyncPortfolioManager.on_event_kline_update] order sending result:  22161657949_36be1af644ba11ef85d900 
I [2024-07-18 04:00:04,523] [-] [AsyncPortfolioManager.on_event_kline_update] sending order: MATICUSDT LIMIT SELL 98.0 @ 0.5499 
I [2024-07-18 04:00:04,562] [-] [AsyncPortfolioManager.on_event_kline_update] order sending result:  34059396901_36c49c9644ba11ef85d900 
I [2024-07-18 04:00:04,562] [-] [AsyncPortfolioManager.on_event_kline_update] sending order: NEARUSDT LIMIT SELL 9.0 @ 6.197 
I [2024-07-18 04:00:04,563] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 34059396901_36c49c9644ba11ef85d900, client_order_id: None, action: SELL, symbol: MATICUSDT, price: 0.54990, quantity: 98, remain: 98.0, status: SUBMITTED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:00:04,602] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 25126231090_36caa48844ba11ef85d900, client_order_id: None, action: SELL, symbol: NEARUSDT, price: 6.1970, quantity: 9, remain: 9.0, status: SUBMITTED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:00:04,603] [-] [AsyncPortfolioManager.on_event_kline_update] order sending result:  25126231090_36caa48844ba11ef85d900 
I [2024-07-18 04:00:04,620] [-] [AsyncPortfolioManager.on_event_kline_update]            prev_position  label_position  prev_ratio  label_ratio  order_side  order_qty   order_price  avg_price       close\nsymbol                                                                                                                         \nADAUSDT            29.00            -2.00    0.065597     -0.004503        -1.0      31.00      0.441276   0.443345      0.4411\nATOMUSDT            2.96            -0.22    0.099685     -0.007353        -1.0       3.18      6.550619   6.600764      6.5480\nAVAXUSDT            0.00             0.00    0.000000      0.000000        -1.0       0.00     27.749095   0.000000     27.7380\nAXSUSDT             0.00             0.00    0.000000      0.000000        -1.0       0.00      6.321528   0.000000      6.3190\nBCHUSDT             0.00             0.00    0.000000      0.000000         0.0       0.00    381.080000   0.000000    381.0800\nBNBUSDT             0.00             0.00    0.000000      0.000000         0.0       0.00    574.050000   0.000000    574.0500\nBTCUSDT             0.00             0.00    0.000000      0.000000         0.0       0.00  64827.800000   0.000000  64827.8000\nDOGEUSDT            0.00             0.00    0.000000      0.000000        -1.0       0.00      0.123349   0.000000      0.1233\nDOTUSDT            -1.50            -0.20   -0.048842     -0.006490         1.0       1.30      6.360455   6.382000      6.3630\nEOSUSDT             0.00             0.00    0.000000      0.000000        -1.0       0.00      0.609244   0.000000      0.6090\nETCUSDT             0.00             0.00    0.000000      0.000000        -1.0       0.00     23.284310   0.000000     23.2750\nETHUSDT             0.00             0.00    0.000000      0.000000         0.0       0.00   3429.990000   0.000000   3429.9900\nFILUSDT             0.00             0.00    0.000000      0.000000        -1.0       0.00      4.603841   0.000000      4.6020\nFTMUSDT            37.00            -5.00    0.097899     -0.013120        -1.0      42.00      0.514306   0.518600      0.5141\nLINKUSDT            0.00             0.00    0.000000      0.000000        -1.0       0.00     13.873547   0.000000     13.8680\nLTCUSDT             0.00             0.00    0.000000      0.000000         0.0       0.00     72.350000   0.000000     72.3500\nMATICUSDT          94.00            -4.00    0.263517     -0.011221        -1.0      98.00      0.549820   0.549460      0.5496\nNEARUSDT            9.00             0.00    0.280319      0.000000        -1.0       9.00      6.196478   6.104727      6.1940\nSOLUSDT             0.00             0.00    0.000000      0.000000        -1.0       0.00    158.948554   0.000000    158.8850\nXRPUSDT             0.00             0.00    0.000000      0.000000         0.0       0.00      0.606900   0.000000      0.6069 
I [2024-07-18 04:00:06,467] [-] [AsyncPortfolioManager.on_event_account_update_callback] account update: {'balances': {'USDT': {'wallet_balance': '195.********', 'cross_wallet_balance': '195.********', 'balance_change': '0'}}, 'positions': {'ATOMUSDT': {'position_amount': '-0.22', 'entry_price': '6.552', 'cum_realized_pnl': '-0.********', 'unrealized_pnl': '0.********', 'margin_type': 'cross', 'isolated_wallet_margin': '0', 'position_side': 'BOTH', 'margin_asset': 'USDT'}}, 'reason': 'ORDER'} 
I [2024-07-18 04:00:06,469] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 19751877313_36b166da44ba11ef85d900, client_order_id: None, action: SELL, symbol: ATOMUSDT, price: 6.552, quantity: 3.18, remain: 0.0, status: FILLED, avg_price: 6.552, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:00:07,403] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: ATOMUSDT, quantity: -0.22, avg_price: 6.552, short_quantity: 0.22, short_avg_price: 6.552, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,404] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: FILUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,405] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: SOLUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,406] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: XRPUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,407] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: DOTUSDT, quantity: -1.5, avg_price: 6.382, short_quantity: 1.5, short_avg_price: 6.382, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,408] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: ADAUSDT, quantity: 29.0, avg_price: 0.*************, short_quantity: 0, short_avg_price: 0, long_quantity: 29.0, long_avg_price: 0.*************, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,409] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: ETHUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,410] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: LINKUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,412] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: EOSUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,413] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: NEARUSDT, quantity: 9.0, avg_price: 6.************, short_quantity: 0, short_avg_price: 0, long_quantity: 9.0, long_avg_price: 6.************, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,414] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: BCHUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,415] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: AVAXUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,416] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: MATICUSDT, quantity: 94.0, avg_price: 0.54946, short_quantity: 0, short_avg_price: 0, long_quantity: 94.0, long_avg_price: 0.54946, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,417] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: BTCUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,418] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: FTMUSDT, quantity: 37.0, avg_price: 0.5186, short_quantity: 0, short_avg_price: 0, long_quantity: 37.0, long_avg_price: 0.5186, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:07,419] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: AXSUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:08,222] [-] [AsyncPortfolioManager.on_event_account_update_callback] account update: {'balances': {'USDT': {'wallet_balance': '195.********', 'cross_wallet_balance': '195.********', 'balance_change': '0'}}, 'positions': {'FTMUSDT': {'position_amount': '-5', 'entry_price': '0.5144', 'cum_realized_pnl': '-0.********', 'unrealized_pnl': '0.********', 'margin_type': 'cross', 'isolated_wallet_margin': '0', 'position_side': 'BOTH', 'margin_asset': 'USDT'}}, 'reason': 'ORDER'} 
I [2024-07-18 04:00:08,223] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 22161657949_36be1af644ba11ef85d900, client_order_id: None, action: SELL, symbol: FTMUSDT, price: 0.514400, quantity: 42, remain: 0.0, status: FILLED, avg_price: 0.514400, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:00:08,403] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: FTMUSDT, quantity: -5.0, avg_price: 0.5144, short_quantity: 5.0, short_avg_price: 0.5144, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:00:08,404] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: AXSUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:05:30,723] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 25126231090_36caa48844ba11ef85d900, client_order_id: None, action: SELL, symbol: NEARUSDT, price: 6.1970, quantity: 9, remain: 9.0, status: CANCELED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:06:59,793] [-] [AsyncPortfolioManager.on_event_account_update_callback] account update: {'balances': {'USDT': {'wallet_balance': '195.********', 'cross_wallet_balance': '195.********', 'balance_change': '0'}}, 'positions': {'DOTUSDT': {'position_amount': '-0.2', 'entry_price': '6.382', 'cum_realized_pnl': '0.********', 'unrealized_pnl': '0.********', 'margin_type': 'cross', 'isolated_wallet_margin': '0', 'position_side': 'BOTH', 'margin_asset': 'USDT'}}, 'reason': 'ORDER'} 
I [2024-07-18 04:06:59,795] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 23980708059_36b7b99a44ba11ef85d900, client_order_id: None, action: BUY, symbol: DOTUSDT, price: 6.359, quantity: 1.3, remain: 0.0, status: FILLED, avg_price: 6.359, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:07:00,748] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: DOTUSDT, quantity: -0.2, avg_price: 6.382, short_quantity: 0.2, short_avg_price: 6.382, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:00,749] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: ADAUSDT, quantity: 29.0, avg_price: 0.*************, short_quantity: 0, short_avg_price: 0, long_quantity: 29.0, long_avg_price: 0.*************, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:00,751] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: ETHUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:00,752] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: LINKUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:00,753] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: EOSUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:00,754] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: NEARUSDT, quantity: 9.0, avg_price: 6.************, short_quantity: 0, short_avg_price: 0, long_quantity: 9.0, long_avg_price: 6.************, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:00,755] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: BCHUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:00,756] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: AVAXUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:00,757] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: MATICUSDT, quantity: 94.0, avg_price: 0.54946, short_quantity: 0, short_avg_price: 0, long_quantity: 94.0, long_avg_price: 0.54946, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:00,758] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: BTCUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:00,759] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: FTMUSDT, quantity: -5.0, avg_price: 0.5144, short_quantity: 5.0, short_avg_price: 0.5144, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:00,760] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: AXSUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:29,759] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 25126282841_android_wvPujvVxMVgehv8PCuuP, client_order_id: None, action: SELL, symbol: NEARUSDT, price: 6.1710, quantity: 9, remain: 9.0, status: SUBMITTED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:07:49,912] [-] [AsyncPortfolioManager.on_event_account_update_callback] account update: {'balances': {'USDT': {'wallet_balance': '196.********', 'cross_wallet_balance': '196.********', 'balance_change': '0'}}, 'positions': {'NEARUSDT': {'position_amount': '0', 'entry_price': '0', 'cum_realized_pnl': '-0.********', 'unrealized_pnl': '0', 'margin_type': 'cross', 'isolated_wallet_margin': '0', 'position_side': 'BOTH', 'margin_asset': 'USDT'}}, 'reason': 'ORDER'} 
I [2024-07-18 04:07:49,914] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 25126282841_android_wvPujvVxMVgehv8PCuuP, client_order_id: None, action: SELL, symbol: NEARUSDT, price: 6.1710, quantity: 9, remain: 0.0, status: FILLED, avg_price: 6.1710, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:07:50,787] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: NEARUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:50,789] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: BCHUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:50,790] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: AVAXUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:50,791] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: MATICUSDT, quantity: 94.0, avg_price: 0.54946, short_quantity: 0, short_avg_price: 0, long_quantity: 94.0, long_avg_price: 0.54946, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:50,792] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: BTCUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:50,793] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: FTMUSDT, quantity: -5.0, avg_price: 0.5144, short_quantity: 5.0, short_avg_price: 0.5144, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:07:50,794] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: AXSUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:09:10,079] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 34059396901_36c49c9644ba11ef85d900, client_order_id: None, action: SELL, symbol: MATICUSDT, price: 0.54990, quantity: 98, remain: 98.0, status: SUBMITTED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:09:13,929] [-] [AsyncPortfolioManager.on_event_account_update_callback] account update: {'balances': {'USDT': {'wallet_balance': '195.********', 'cross_wallet_balance': '195.********', 'balance_change': '0'}}, 'positions': {'MATICUSDT': {'position_amount': '-4', 'entry_price': '0.5485', 'cum_realized_pnl': '-0.********', 'unrealized_pnl': '0.********', 'margin_type': 'cross', 'isolated_wallet_margin': '0', 'position_side': 'BOTH', 'margin_asset': 'USDT'}}, 'reason': 'ORDER'} 
I [2024-07-18 04:09:13,931] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 34059396901_36c49c9644ba11ef85d900, client_order_id: None, action: SELL, symbol: MATICUSDT, price: 0.54990, quantity: 98, remain: 0.0, status: FILLED, avg_price: 0.54850, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:09:14,854] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: MATICUSDT, quantity: -4.0, avg_price: 0.5485, short_quantity: 4.0, short_avg_price: 0.5485, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:09:14,856] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: BTCUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:09:14,857] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: FTMUSDT, quantity: -5.0, avg_price: 0.5144, short_quantity: 5.0, short_avg_price: 0.5144, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:09:14,858] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: AXSUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:09:58,954] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 42837695591_36ab073644ba11ef85d900, client_order_id: None, action: SELL, symbol: ADAUSDT, price: 0.44140, quantity: 31, remain: 31.0, status: SUBMITTED, avg_price: 0, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:10:05,630] [-] [AsyncPortfolioManager.on_event_account_update_callback] account update: {'balances': {'USDT': {'wallet_balance': '195.********', 'cross_wallet_balance': '195.********', 'balance_change': '0'}}, 'positions': {'ADAUSDT': {'position_amount': '-2', 'entry_price': '0.4409', 'cum_realized_pnl': '-0.********', 'unrealized_pnl': '-0.********', 'margin_type': 'cross', 'isolated_wallet_margin': '0', 'position_side': 'BOTH', 'margin_asset': 'USDT'}}, 'reason': 'ORDER'} 
I [2024-07-18 04:10:05,632] [-] [AsyncPortfolioManager.on_event_order_update_callback] order: [platform: binance_future, account: <EMAIL>, strategy: portfolio, order_id_with_client: 42837695591_36ab073644ba11ef85d900, client_order_id: None, action: SELL, symbol: ADAUSDT, price: 0.44140, quantity: 31, remain: 0.0, status: FILLED, avg_price: 0.44090, is_limit: LIMIT, trade_type: 0, ctime: *************, utime: *************] 
I [2024-07-18 04:10:05,896] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: ADAUSDT, quantity: -2.0, avg_price: 0.4409, short_quantity: 2.0, short_avg_price: 0.4409, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:10:05,898] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: ETHUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:10:05,899] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: LINKUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:10:05,900] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: EOSUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:10:05,901] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: NEARUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:10:05,902] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: BCHUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:10:05,903] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: AVAXUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:10:05,904] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: MATICUSDT, quantity: -4.0, avg_price: 0.5485, short_quantity: 4.0, short_avg_price: 0.5485, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:10:05,905] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: BTCUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:10:05,906] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: FTMUSDT, quantity: -5.0, avg_price: 0.5144, short_quantity: 5.0, short_avg_price: 0.5144, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
I [2024-07-18 04:10:05,907] [-] [AsyncPortfolioManager.on_event_position_update_callback] position: [platform: binance_future, account: <EMAIL>, strategy: portfolio, symbol: AXSUSDT, quantity: 0.0, avg_price: 0.0, short_quantity: 0, short_avg_price: 0, long_quantity: 0, long_avg_price: 0, liquid_price: 0, utime: *************] 
