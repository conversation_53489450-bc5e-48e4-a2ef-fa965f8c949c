from dataclasses import dataclass
from typing import Iterable, List, Union

from gym import Space
from sklearn.preprocessing import RobustScaler
import torch
from customized.modules.tsmixer import TSMixer

from core.predictor_config import PredictorConfig
from core.dot_dict import DotDict as dd
from core.symbols import sd
from numpy import where
import numpy as np
import polars as pl


COMMISSION = dd(
	# limit=-2.5e-5,
	limit=12e-5,
	# limit=2e-5,
	market=30e-5,
	# limit=20e-5,
	# market=40e-5,	
	punish_ratio=0,
	func=lambda x: COMMISSION['limit'] if x == 1 else COMMISSION['market'],
	func_np=lambda x: where(x == 1, COMMISSION['limit'], COMMISSION['market'])
)

@dataclass
class MLPConfig:
	klass = ... # MLP
	name = 'MLP'
	kwargs = dd(
	)

@dataclass
class LSTMConfig:
	klass = ... # LSTM
	name = 'LSTM'
	kwargs = dd(
	)

@dataclass
class GRUConfig:
	klass = ... # GRU
	name = 'GRU'
	kwargs = dd(
	)

@dataclass
class TCNConfig:
	klass = ... # TCN
	name = 'TCN'
	kwargs = dd(
	)

@dataclass
class TSMixerConfig:
	klass = TSMixer
	name = 'TSMixer'
	kwargs = dd(
		seq_len=60,
		in_channel=5,
		pred_len=1,
		n_mixer=1,
		# dropout=0.6,
		dropout=0.,
		out_hidden=128,
		n_paralell=8,
		# use_projection=True,
		# with_inverse=True
	)


class ActionConfig:
	softmax_scale = 1.5
	space = Space
	shape = ()
	dim = 0
	type_str = dd(
		options=[None, "dcp", "d", "c", "ddp", "qp"],
		index=0,
	)
	mapping_to = dd(
		options=["change", "target"],
		index=1
	)
	clip = dd(
		value=0.5,
		in_use=False
	)
	scale = dd(
		value=0.9,
		in_use=False
	)
	limit_offset_scale = 0.01
	notional_scale = 1
	is_parameterized = True
	market_target = True
	dict_name = 'market_limit_dict'
	parameter_dict = dd(
		order_skip=dd(
			dim=1, 
			func_by_dim={
				1:(lambda s: (s > 0).astype(int)), 
				2:(lambda s: np.argmax(s.reshape(-1, 2), axis=1))}),	

		is_limit=dd(
			dim=1, 
			func_by_dim={
				1:(lambda il: (il <= 0).astype(int)), 
				2:(lambda il: np.argmax(il.reshape(-1, 2), axis=1))}),

		# market_or_limit_index=dd(
		# 	dim=1, 
		# 	func_by_dim={
		# 		1:(lambda market_or_limit_arr: (market_or_limit_arr > 0).astype(int)), 
		# 		2:(lambda market_or_limit_arr: np.argmax(market_or_limit_arr.reshape(-1, 2), axis=1))}),		   			

		exposure=dd(
			dim=2,
			func_by_dim={
				1:(lambda market_or_limit_idx, exposure_arr: exposure_arr), 
				2:(lambda market_or_limit_idx, exposure_arr: exposure_arr.reshape(-1, 2)[np.arange(len(market_or_limit_idx)), market_or_limit_idx.astype(int)])}
			),			

		limit_offset=dd(dim=1),

		# cash_skip=dd(
		# 	dim=1, 
		# 	func_by_dim={
		# 		1:(lambda c: (c > 0).astype(int)), 
		# 		2:(lambda c: np.argmax(c.reshape(-1, 2), axis=1))}),	

		cash_ratio=dd(dim=1),
	)

	long_short_dict = dd(
		# long_skip=dd(
		# 	dim=1, 
		# 	func_by_dim={
		# 		1:(lambda s: (s > 0).astype(int)), 
		# 		2:(lambda s: np.argmax(s.reshape(-1, 2), axis=1))}),

		long_notional_ratio=dd(
			dim=1,
			func_by_dim={
				1:(lambda market_or_limit_idx, exposure_arr: exposure_arr), 
				2:(lambda market_or_limit_idx, exposure_arr: exposure_arr.reshape(-1, 2)[np.arange(len(market_or_limit_idx)), market_or_limit_idx.astype(int)])}
			),				

		# long_is_limit=dd(
		# 	dim=1, 
		# 	func_by_dim={
		# 		1:(lambda il: (il <= 0).astype(int)), 
		# 		2:(lambda il: np.argmax(il.reshape(-1, 2), axis=1))}),

		long_offset=dd(dim=1),

		# short_skip=dd(
		# 	dim=1, 
		# 	func_by_dim={
		# 		1:(lambda s: (s > 0).astype(int)), 
		# 		2:(lambda s: np.argmax(s.reshape(-1, 2), axis=1))}),

		short_notional_ratio=dd(
			dim=1,
			func_by_dim={
				1:(lambda market_or_limit_idx, exposure_arr: exposure_arr), 
				2:(lambda market_or_limit_idx, exposure_arr: exposure_arr.reshape(-1, 2)[np.arange(len(market_or_limit_idx)), market_or_limit_idx.astype(int)])}
			),		

		# short_is_limit=dd(
		# 	dim=1, 
		# 	func_by_dim={
		# 		1:(lambda il: (il <= 0).astype(int)), 
		# 		2:(lambda il: np.argmax(il.reshape(-1, 2), axis=1))}),

		short_offset=dd(dim=1),		
	)

	market_limit_dict = dd(
		market_skip=dd(
			dim=1, 
			func_by_dim={
				1:(lambda s: (s > 0).astype(int)), 
				2:(lambda s: np.argmax(s.reshape(-1, 2), axis=1))}),


		market_exposure_ratio=dd(
			dim=1,
			func_by_dim={
				1:(lambda market_or_limit_idx, exposure_arr: exposure_arr), 
				2:(lambda market_or_limit_idx, exposure_arr: exposure_arr.reshape(-1, 2)[np.arange(len(market_or_limit_idx)), market_or_limit_idx.astype(int)])}
			),	

		# long_skip=dd(
		# 	dim=1, 
		# 	func_by_dim={
		# 		1:(lambda s: (s > 0).astype(int)), 
		# 		2:(lambda s: np.argmax(s.reshape(-1, 2), axis=1))}),

		long_notional_ratio=dd(
			dim=1,
			func_by_dim={
				1:(lambda market_or_limit_idx, exposure_arr: exposure_arr), 
				2:(lambda market_or_limit_idx, exposure_arr: exposure_arr.reshape(-1, 2)[np.arange(len(market_or_limit_idx)), market_or_limit_idx.astype(int)])}
			),

		long_offset=dd(dim=1),

		# short_skip=dd(
		# 	dim=1, 
		# 	func_by_dim={
		# 		1:(lambda s: (s > 0).astype(int)), 
		# 		2:(lambda s: np.argmax(s.reshape(-1, 2), axis=1))}),

		short_notional_ratio=dd(
			dim=1,
			func_by_dim={
				1:(lambda market_or_limit_idx, exposure_arr: exposure_arr), 
				2:(lambda market_or_limit_idx, exposure_arr: exposure_arr.reshape(-1, 2)[np.arange(len(market_or_limit_idx)), market_or_limit_idx.astype(int)])}
			),

		short_offset=dd(dim=1),	
	)
	offset_spread_dict = dd(
		market_skip=dd(
			dim=1, 
			func_by_dim={
				1:(lambda s: (s > 0).astype(int)), 
				2:(lambda s: np.argmax(s.reshape(-1, 2), axis=1))}),


		market_exposure_ratio=dd(
			dim=1,
			func_by_dim={
				1:(lambda market_or_limit_idx, exposure_arr: exposure_arr), 
				2:(lambda market_or_limit_idx, exposure_arr: exposure_arr.reshape(-1, 2)[np.arange(len(market_or_limit_idx)), market_or_limit_idx.astype(int)])}
			),	

		# limit_skip=dd(
		# 	dim=1, 
		# 	func_by_dim={
		# 		1:(lambda s: (s > 0).astype(int)), 
		# 		2:(lambda s: np.argmax(s.reshape(-1, 2), axis=1))}),

		limit_notional_ratio=dd(
			dim=1,
			func_by_dim={
				1:(lambda market_or_limit_idx, exposure_arr: exposure_arr), 
				2:(lambda market_or_limit_idx, exposure_arr: exposure_arr.reshape(-1, 2)[np.arange(len(market_or_limit_idx)), market_or_limit_idx.astype(int)])}
			),

		limit_offset=dd(dim=1),

		limit_spread=dd(dim=1),

	)	
	is_bidirectional = False
	discrete_dim = 3
	n_categories = 1
	continuous_dim = 1
	parameter_dim_list = [1]
	use_skip = False
	use_softmax = False
	market_limit_share_parameter = False
	market_limit_share_category = False
	with_mask = False


def parse_parameter(parameter_dict: dict, n_codes=1, cash_is_seperated=True):
	categories = list(parameter_dict.items())
	dims = [(k, v.dim * n_codes ** (1 - ('cash' in k and not cash_is_seperated))) for k, v in categories if v.dim > 0]
	dim_dict = dd(dims)
	dim_sum = sum(dim_dict.values())
	slices = zip([k for k, _ in dims], index_to_slice(np.cumsum([v for _, v in dims])))
	slice_dict = dd(slices)
	func_dict = dd({k: v['func_by_dim'][v.dim] for k, v in categories if 'func_by_dim' in v})
	print(f'action {dim_sum = }')
	return dim_dict, slice_dict, func_dict
	

def split_action_array(slice_dict: dict, action_array: np.ndarray) -> dict:
	return {k: action_array[slc] for k, slc in slice_dict.items()}


@dataclass
class RewardConfig:
	type_str = dd(
		# can be: bts, pdb, rlz_pdb, rtn, rlz_rtn, sr, rlg_sr, rlz_sr,
		# stn, rlg_stn, rlz_stn, dsr, and args like -inv,
		# -range, -shift, -min_exp_zoom10, -bias-0.01zoom5
		options=["rtn", "rlz_rtn", "pdb", "rlz_pdb", "bts"],
		index=0
	)
	suffix = dd(
		options=["", "-inv", "-range0.2", "-shift2",
		         "-min_exp_zoom10", "-bias-0.01zoom5"],
		index=0,
	)
	punish_ratio = 1
	threshold = 1e5
	n_multi_step = 1


@dataclass
class RoundTripConfig:
	in_use = False
	length = 16
	order_type = dd(
		options=['market', 'limit', 'mix', 'with_offset', 'mix_with_offset'],
		entry_index=0,
		exit_index=1
	)
	ref_price = dd(
		options=['close', 'vwp'],
		index=1,
	)


@dataclass
class QuantileConfig:
	type_str = dd(
		options=["qrd", "iqn", "fqf"],
		index=1
	)
	in_use = False
	dense = False
	iqn_sample_sizes = dd(
		eval=32,
		train=8,
		target=8,
		in_use=True,
	)
	risk_type = dd(
		options=["VaR", "MV"],
		index=0,
	)
	layer_size = 512


@dataclass
class CriticConfig:
	quantile = QuantileConfig()
	twins_in_one = False
	updating = dd(
		interval=3,
		simultaneously=True,
	)
	q_advantage = dd(
		weight=0.03,
		in_use=False
	)
	minimum_q = dd(
		weight=0.9,
		in_use=True
	)
	reg = dd(
		weight=0.005,
		in_use=False
	)
	use_min_max_q = False
	use_value_function = False
	use_value_advantage = False
	use_dueling = False
	n_dueling_intervals = 20
	use_advantage = False
	exploration_weight = 0.05


@dataclass
class ActorConfig:
	update_freq = 1
	use_target = False
	use_tanh = False
	double = dd(
		in_use=False,
		cross_update=False,
		use_min_entropy_action=False,
	)
	reg = dd(
		weight=0.5,
		in_use=False
	)
	elementwise_compare = False
	entropy_penalty = dd(
		weight=0.5,
		in_use=False
	)


@dataclass
class PrioritizedConfig:
	type_str = dd(
		options=["td", "ti", "tdi"],
		index=0,
	)
	alpha = 0.5
	beta = dd(
		initial=0.9,
		final=1,
		final_step=10000,
		final_done=False,
		annealing=True,
	)
	in_use = True


@dataclass
class ReplayBufferConfig:
	size = 10_000_000
	ignore_obs_next = False
	save_only_last_obs = False
	stack = dd(
		value=1,
		in_use=False,
		options=['obs', 'act', 'rew', 'obs_next', 'done'],
		indice=[0, 1, 2, 3, 4]
	)
	prioritized = PrioritizedConfig()
	hindsight = dd(
		horizon=50,
		future_k=8,
		in_use=False,
	)


class StackableState:
	def __init__(self, stack_num: int = 1, base_length: int = 1, *args, **kwargs) -> None:
		self.stack_num = stack_num
		self.base_length = base_length
	
	@property
	def is_stacked(self) -> bool:
		return self.stack_num > 1


@dataclass
class FeatureConfig:
	space = Space
	shape = ()
	scaler = RobustScaler()
	normalize = dd(
		in_env=False,
		in_source=False
	)
	fit_done = False
	use_z_score = False
	use_rolling_z_score = False
	use_ln = True
	expand_features_in_data_source = True
	look_back = 1
	channel_as_last_dim = True
	window = dd(
		options=(
			[0, 1, 2, 3, 4, 6, 8, 12, 16, 24, 32, 48, 64,
			 96, 128, 160, 192, 224, 256, 288, 320, 352, 384],
			[1, 2, 3, 4, 6, 8, 12, 16, 24, 32, 48, 64],
			[i for i in range(64)],
		),
		index=1,
		slice_index=0,
		is_ewm=True,
	)
	columns = ['open_time', 'open', 'high', 'low', 'close', 'volume', 'quote', 'buy_volume', 'buy_quote', 'avg_price', 'sell_volume', 'sell_quote']
	period_type = dd(
		options=['i', 'ms', 's', 'm', 'h', 'd'],
		index=0,
	)
	exponential_smoothing = dd(
		options=[0.1, 0.5, 0.9],
		index=1
	)
	label = False
	price = False
	avg_price = False
	vwp = False
	momentum = True
	volatility = False
	skewness = False
	kurtosis = False
	kalman_filter = False
	kalman_smooth = False
	ranging = False
	bound = False
	volume = True
	buy_volume = True
	sell_volume = True
	quote = False
	buy_sell = False
	exposure_ratio_changed = False
	
	position_remainder = False
	position_duration = False
	pnl_diff_per_balance = False
	balance_ratio_return = False
	source = dd(
		in_use = True,
		stack_num = 1
	)
	encoder = dd(
		in_use = False,
		stack_num = 1
	)	
	account = dd(
		in_use = False,
		stack_num = 1
	)
	action = dd(
		in_use = False,
		stack_num = 1
	)
	action_duration = False
	cum_mean = False
	collect_offline = False
	future = dd(
		step_ahead=16,
		# step_ahead_index=7,
		in_use=False,
		inverse_percentage=0.,
		noise_bias_ratio=1.,
		noise_base=0.,
		is_autoregressive=False,
		is_cum=True,
		use_trend=False,
		use_momentum=False,
		use_single=False,
	)
	zigzag = dd(
		log_thresholds=[1e-3, 2e-3, 3e-3],
		one_hot=False,
		in_use=False,
	)
	def get_n_categories(self):
		return 


@dataclass
class LabelConfig:
	windows = [2 * i for i in range(1, 21)]
	window_back = 4
	in_use = False

	@property
	def window_mean(self):
		return int(np.mean(self.windows))

@dataclass
class AccountConfig:
	realized_cash_ratio = False  # in most of the cases False
	cash = dd(
		in_ratio=True,
		use_skip=False,
		is_seperated=True,
		average_seperated_on_update=False  # should consider changing realized_pnl_arr
	)
	use_max_lot_count = False
	reset_threshold = 0.3
	exposure_ratio = dd(
		options=["fixed", "dynamic", "mix"],
		index=0,
		mix_threshold=1.5,
		fixed_multiple=0.1,
		realized_numerator=False,
		realized_denominator=False,
		limit=dd(
			value=0.8,
			in_use=False,
		),
		decline_base=0.8
	)
	long_only = False


@dataclass
class OrderModeConfig:
	market = False
	limit = False
	with_offset = False


@dataclass
class OfflineConfig:
	cql_weight = 1.
	lagrange = dd(
		in_use=True,
		threshold=10.
	)
	td3bc_alpha = 2.5


@dataclass
class TrainingConfig:
	viz_tracer = False
	watch_performance = False
	check_nan = False
	platform = 'binance'
	long_only = False
	use_solo = True
	exclude_codes = ['BTCUSDT', 'DOGEUSDT']
	n_codes_dict = {
		'1m': 42,
		'5m': 57,
		'15m': 57,
	}
	# n_codes = 57 # exclude exclude_codes, 59 indeed
	n_codes = 1
	group_by_col = 'open_time'
	dolphin = dd(
		linux_conf_path="/etc/resolv.conf",
		db_name="dfs://monthly",
		tb_name="ohlc_",
		start_date=dd(
			single="2020.01.01",
			multi="2021.01.01"
		),
		conn_info=dd(
			host="localhost",
			port=8848,
			userid="admin",
			password="123456"
		)
	)
	mp_context_mode = dd(
		options=["spawn", "forkserver", "fork"],
		index=0,
	)
	# data
	source_type = dd(
		options=["dolphin", "mongo", "csv"],
		index=0,
	)
	# data_source_class_name = 'KlineDataSource'
	data_source_class_name = 'KlineDataSet'
	dataset_class_name = 'KlineDataSet'
	symbol_dict = dd(sd)
	code_series = None
	file_name = "config.py"
	csv_path = "C:/Users/<USER>/Downloads/BinanceData/"
	order_flow_dir = "/root/crypto/"
	symbol = "ETHUSDT"
	order_flow_date = dd(
		train=('2023-10-01', '2023-10-12'),
		test=('2023-10-13', '2023-10-16'),
	)
	kline_date = dd(
		train=('2020-10-01', '2022-07-01'),
		val=('2023-07-01', '2023-07-01'),
		test=('2023-07-02', '2024-02-29'),
	)	
	n_days = 16
	pqt_name_list = ['event', 'lob', 'price', 'pred']
	downsampling = dd(
		unit='ms',
		base=100,
		factor=10
	)
	interval_type = dd(
		options=["kline", "aggTrades"],
		index=0,
		kline="5m",
		aggTrades=1000000
	)
	latency = 200
	max_no_trade_count = 100
	history_kline_limit = 1000
	years = [2020, 2021, 2022, 2023]
	last_month = 12
	dataframe_type = dd(
		options=["polars", "pandas"],
		index=0,
	)
	augment = dd(
		options=[None, 'twin', 'spin', 'w', 'sine', 'cosine'],
		index=1,
		in_use=False,
		# plot=False,
		in_front=False
	)
	
	make_dataset = False
	
	predictor = PredictorConfig()

	label = LabelConfig()
	
	# feature
	feature = FeatureConfig()
	
	# trading
	account = AccountConfig()
	is_trading_online = False
	is_live = False
	is_training = True
	overlap_split = False
	use_split_test = False
	use_in_sample_val_set = True
	order_horizon = 1
	n_test_divisions = 2
	side_multiplier = 2
	max_position_on_realized_balance = True
	max_random_step_ahead = 10
	random_position_on_reset = True
	random_position_ratio = 0.5
	non_random_prob_on_reset = 0.1
	
	init_cash = 10000.0
	lot_size = dd(
		count=1,
		value=0.001,
		round=3,
		ratio=0.05
	)
	tick_size = dd(
		shift_count=0,
		value=0.01,
		round=2,
	)
	slippage = dd(
		count=2,
		in_use=True
	)
	commission = COMMISSION
	order_mode = dd(
		options=["market", "limit", "mix", "with_offset",
		         "mix_with_offset", "market_in_offset", "both", "all"],
		index=4
	)
	order = OrderModeConfig()
	
	order_min_notional = dd(
		value=5,
		in_use=True
	)
	unrealized_pnl_include_COMMISSION = False
	consider_fee_for_target = False
	stop_loss = dd(
		base_on=dd(
			types=["drawdown", "volatility"],
			index=0
		),
		threshold=-1,
		return_fee=False
	)
	
	# env
	action = ActionConfig()
	reward = RewardConfig()
	metric = dd(
		options=["train", "val", "test"],
		index=1,
		train="total_pnl_ratio_per_step",
		val="total_pnl_ratio_per_step",
		test="test_pnl_ratio_per_step"
	)
	task = dd(
		options=[
			"TradingEnv-v0", 
			"PortfolioEnv-v0", 
			"RoundTripEnv-v0", 
			"MarketMakingEnv-v0", 
			"CartPole-v1", 
			"Pendulum-v1", 
			"Ant-v3", 
			"Humanoid-v2",
			"FetchReach-v3", 
			"FetchPush-v2"
			],
		index=-0
	)
	# goal_conditioned = False
	stack_obs = dd(
		value=1,
		in_use=False,
		transpose=False
	)
	
	# train
	device = 'cuda' if torch.cuda.is_available() else 'cpu'
	compile = dd(
		options=["default", "reduce-overhead", "max-autotune"],
		index=0,
		in_use=True)
	training_num = 12
	test_num = 24
	batch_size = 4096
	n_step = 4
	n_gradient_step_to_start_test = 100000
	# @property
	# def n_gradient_step_to_start_test(self):
	#     return 200000 // (1 + (self.feature.future.in_use or self.is_offline))
	
	epoch = 10000
	repeat_per_collect = 10  # for on-policy
	step_per_epoch_base = 40_000
	update_per_step = 0.1  # for off-policy
	
	@property
	def step_per_collect(self):
		return dd(
			onpolicy=self.training_num * 1000,
			offpolicy=10
		)
	
	@property
	def step_per_epoch(self):
		return dd(
			onpolicy=self.training_num * 10000,
			offpolicy=int(self.step_per_epoch_base *
			              self.step_per_collect.offpolicy * self.update_per_step),
			offline=10000
		)
	
	logdir = "log"
	render = 0.
	env_share_memory = True
	# use_subprocess_env=True
	
	n_episodes = 1000000,
	episode_step_count = dd(
		train=2000,
		test=45000,
		online=999999,
	)
	# episode_step_count_multiplier = dd(
	#     train=1,
	#     test=1,
	# )
	
	# model
	model_path = "best_model/state_dict.pth"
	resume_path = "resume_model/state_dict.pth"
	resume_model = False
	running_mean_std = False
	algo_name = "dsc"
	on_policy_names = ["ppo", "ppod"]
	off_policy_names = ['alm', 'td3', 'sac', 'sacd', 'dsc',
	                    'ssc', 'redq', 'drqn', 'qrdqn', 'iqn', 'fqf']
	offline_names = ['cql', 'td3bc']
	offline_folder = 'dataset/offline/'
	net = dd(
		options=[MLPConfig, LSTMConfig, GRUConfig, TCNConfig, TSMixerConfig],
		index=0,
	)
	dense_mode = dd(
		options=["", "obs_out", "in_out"],
		index=0
	)
	use_mish = True
	network_normalizer = dd(
		batch=False,
		layer=False,
		avg_l1=False
	)
	weight_decay = 0
	dropout_ratio = 0
	loss_fn = dd(
		options=["mse", "huber", "quantile"],
		index=1,
	)
	optimizer = dd(
		options=["RAdam", "AdamW", "Adam", "Adan"],
		index=-1,
		lookahead=dd(
			value=5,
			in_use=False,
		)
	)
	lr = dd(
		initial=1e-4,
		base=1e-5,
		actor=1e-5,
		critic=1e-5,
		alpha=5e-5,
		cql_alpha=10e-5,
		world=1e-5,
		reward=1e-5,
		fraction=2.5e-9,
		schedule=True,
	)
	seed = dd(
		value=1626,
		in_use=True
	)
	epsilon = dd(
		train=0.1,
		test=0.05,
	)
	gamma = dd(
		options=[0.995, 0.99, 0.98, 0.95, 0.9],
		index=0,
		# learnable=False,
	)
	label_update_freq = 320
	tau = 0.005
	
	# algo
	munchausen = dd(
		alpha=0.01,
		tau=0.03,
		low=-1,
		in_use=False,
	)
	# ppo
	gae_lambda = 0.95
	max_grad_norm = 0.5
	vf_coef = 0.25
	ppo_ent_coef = 0.01
	ppo_rew_norm = True
	action_scaling = True
	bound_action_method = 'clip'
	lr_decay = True
	eps_clip = 0.2
	dual_clip = None
	value_clip = 0
	norm_adv = 0
	recompute_adv = 1
	# alm
	use_twin_critic = True
	# qrdqn
	use_dueling = True
	num_quantiles = 32
	# iqn
	sample_size = 32
	online_sample_size = 8
	label_sample_size = 8
	num_cosines = 64
	# fqf
	num_fractions = 32
	fqf_ent_coef = 10.0
	fraction_lr = 2.5e-9
	# ddpg & td3
	exploration_noise = 0.1
	policy_noise = 0.2
	noise_clip = 0.5
	rew_norm = False
	# sac
	conditioned_sigma = False
	temperature = dd(
		value=1,
		in_use=True,
		auto=True,
		cql=1.
		# for_critic=False,
	)
	critic = CriticConfig()
	actor = ActorConfig()
	preprocess = dd(
		share=False,
		with_aux_task=False,
		use_lff=False,
		in_use=True,
		sizes=[512, 512, 512, 512],
	)
	latent = dd(
		in_use=True,
		sizes=[512, 512, 512, 512],
	)
	hidden = dd(
		in_use=False,
		sizes=[512, 512, 512, 512],
		use_last=False
	)
	dueling = dd(
		in_use=False,
		sizes=[512],
	)
	replay_buffer = ReplayBufferConfig()
	
	exploration = dd(
		use_env_noise=False,
		use_before_training=True,
		end_step_num=100000,
		weight=0.05
	)
	offline = OfflineConfig()
	
	def update(self, **kwargs):
		self.__dict__.update(kwargs)
	
	def get_step_per_epoch(self):
		return int(self.step_per_epoch_base * self.step_per_collect.offpolicy * self.update_per_step)
	
	def set_future_feature_off(self):
		self.feature.future.in_use = False
		self.feature.zigzag.in_use = False

	def get_net_config(self):
		tmp = self.net.get_index_item()
		tmp.n_major_features = self.n_major_features
		tmp.n_extra_features = self.n_extra_features
		tmp.n_features = self.n_features
		tmp.n_major_categories = self.n_major_categories
		tmp.n_temporal_columns = self.n_temporal_columns
		tmp.pred_len = self.preprocess.sizes[-1]
		tmp.n_codes = self.n_codes
		tmp.kwargs.cfg = tmp
		return tmp
	
	@property
	def is_solo(self):
		return self.n_codes == 1 and self.use_solo
	
	@property
	def is_portfolio(self):
		return self.task.index == 1
	
	@property
	def is_offline(self):
		return self.algo_name in self.offline_names


def get_instance_by_config(cfg: Union[TSMixerConfig, dd]):
	return cfg.klass(**cfg.kwargs)


def index_to_slice(indices: Iterable[int]) -> List[slice]:
	start = 0
	slices = []
	for idx in indices:
		slices.append(slice(start, idx))
		start = idx
	return slices


if __name__ == '__main__':
	cfg = ActionConfig()
	print(parse_parameter(cfg.parameter_dict))
	# cfg = OmegaConf.structured(cfg)
	print(cfg)
	# print(parse_parameter(cfg.parameter_dict))