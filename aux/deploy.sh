sudo -i
# 添加root密码登录
vi /root/.ssh/authorized_keys
# 删除ssh-rsa前所有字符
vi /etc/ssh/sshd_config
# 添加以下字段
PermitRootLogin yes
PasswordAuthentication yes
# 保存退出后设置root登录密码
passwd
NewPassword:
ConfirmNewPassword:
# 完成后重启, 并可以在连接中使用root密码登录
reboot

# 升级软件包
apt-get update
apt install python3-pip

# 安装torch-cpu等依赖包
pip3 install torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cpu --no-cache-dir
# aioamqp==0.14.0 if Python<3.10
pip install aioamqp aiohttp motor einops pandas matplotlib dolphindb scikit-learn torchinfo importlib-metadata polars viztracer pyarrow -U --no-cache-dir



# 下载依赖项目
#cd /home/<USER>/
#git clone https://github.com/pilotao/TheNextQuant.git
#cd TheNextQuant  # 进入项目目录
#python3 setup.py install # 安装依赖项目thenextquant
#cd .. #

# 安装docker
curl -fsSL https://get.docker.com | bash -s docker


# 重启后
#sudo -i
service docker restart

# docker中安装rabbitmq
docker run -d --restart always --name rabbitmq -p 5672:5672 -p 15672:15672 rabbitmq:3-management

# 查看docker中运行的实例
docker ps -a

# 重启服务器
reboot

# 创建一个window获取并分发行情
screen -S market
cd /root/Directrader/
#cd /home/<USER>/Directrader/
# 每2周/14日重启一次行情获取程序
python3 market_stream.py
ctrl-a + d

# 创建一个window执行策略
screen -S trade
cd /root/Directrader/
#cd /home/<USER>/Directrader/
python3 online_trading.py
ctrl-a + d

# 杀死/关闭一个window
screen -X -S market quit

# 查看所有window
screen -ls

# 回到某window
screen -r market

# 部署DolphinDB
docker run -itd --name dolphindb \
  --hostname ddb210 \
  -p 8850:8848 \
  # 本机文件夹:(映射到)镜像内的文件夹, 使得镜像能够访问本机文件夹
  -v /root:/root \
  dolphindb/dolphindb:v2.00.10 \
  sh