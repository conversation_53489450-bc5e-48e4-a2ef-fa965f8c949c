from direct_trading import pred_cfg
from core.fit_val_test import fit_val_test
from utils.filter_mask import filter_quantile


# pred_cfg.n_codes = 20
pred_cfg.symbol = 'ETHUSDT'

pred_cfg.interval_cfg.base = 5
pred_cfg.interval_cfg.label = 30
base_interval = pred_cfg.interval_cfg.base
pred_cfg.pred_len = label_len = pred_cfg.interval_cfg.label // base_interval
label_step = label_len // pred_cfg.pred_len
pred_cfg.label_step.train = label_step
pred_cfg.label_step.val = label_step

# pred_cfg.filter_cfg.col_name = 'roc_div_volume_div_std'
# pred_cfg.filter_cfg.col_name = 'roc_div_volume'
pred_cfg.feature_cfg.filter = True
pred_cfg.filter_mask_name = None
pred_cfg.filter_cfg.quantile = .99
pred_cfg.execute_phase.train = True
pred_cfg.resume_from_ckpt = False
pred_cfg.merge_history_data = False

if __name__ == '__main__':
    pred_cfg.filter_mask_fn = filter_quantile
    fit_val_test(pred_cfg)