#%%
from core.data_module import KlineDataModule
from direct_trading import pred_cfg

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import polars as pl

pred_cfg.interval_cfg.base = 3
pred_cfg.code_sort_by_quote = True
pred_cfg.start_date.multi = '2024.10.01'
pred_cfg.train_end_date = '2025.04.01'
pred_cfg.val_end_date = '2025.05.01'
pred_cfg.is_eda = True
dm = KlineDataModule(pred_cfg)

#%%
tr_df = dm.dataset_dict.train.data.reset_index()
vl_df = dm.dataset_dict.val.data.reset_index()
tr_df = pl.from_pandas(tr_df)
vl_df = pl.from_pandas(vl_df)

# 添加close_roc列（收盘价变化率）
tr_df = tr_df.with_columns(
    close_roc=pl.col('close').pct_change().over('code')
)
vl_df = vl_df.with_columns(
    close_roc=pl.col('close').pct_change().over('code')
)

#%%
# 计算每个code的quote_sum
code_quote_sum = tr_df.group_by('code').agg(
    pl.col('quote').sum().alias('quote_sum')
)

# 计算每个code的close_roc皮尔逊相关性矩阵
def compute_pearson_correlation_matrix(df: pd.DataFrame):
    # 将数据透视为宽格式，每个code一列
    pivot_df = df.pivot(
        index='open_time',
        on='code',
        values='close_roc'
    )

    # 计算相关性矩阵
    corr_matrix = pivot_df.corr()

    # 将相关性矩阵转换为长格式
    corr_long = []
    for code1 in corr_matrix.columns:
        for code2 in corr_matrix.columns:
            if code1 != code2:  # 排除自相关
                corr_long.append({
                    'code1': code1,
                    'code2': code2,
                    'correlation': corr_matrix.loc[code1, code2]
                })

    return pl.from_pandas(pd.DataFrame(corr_long))

def compute_pearson_correlation_matrix_polars_optimized(df: pl.DataFrame):
    # 将数据透视为宽格式，每个code一列
    pivot_df = df.pivot(
        index='open_time',
        on='code',
        values='close_roc'
    )

    # 获取所有代码列名
    code_columns = [col for col in pivot_df.columns if col != 'open_time']

    # 创建所有可能的代码对组合
    code_pairs = [(code1, code2) for code1 in code_columns for code2 in code_columns if code1 != code2]

    # 创建结果列表
    corr_list = []

    # 计算每对代码的相关性
    for code1, code2 in code_pairs:
        correlation = pivot_df.select(
            pl.corr(code1, code2)
        ).item()

        corr_list.append({
            'code1': code1,
            'code2': code2,
            'correlation': correlation
        })

    # 创建相关性DataFrame
    return pl.DataFrame(corr_list)

# 计算相关性矩阵
# corr_df = compute_pearson_correlation_matrix(tr_df.to_pandas())
corr_df = compute_pearson_correlation_matrix_polars_optimized(tr_df)
# 计算每个code的平均相关性
avg_corr_by_code = corr_df.group_by('code1').agg(
    pl.col('correlation').mean().alias('avg_correlation')
).rename({'code1': 'code'})

# 合并quote_sum和平均相关性
result_df = code_quote_sum.join(avg_corr_by_code, on='code')

# 按照quote_sum和相关性排序
sorted_by_quote = result_df.sort('quote_sum', descending=True)
sorted_by_corr = result_df.sort('avg_correlation', descending=True)
sorted_by_both = result_df.with_columns(
    (pl.col('quote_sum').rank(descending=True) + pl.col('avg_correlation').rank(descending=True)).alias('combined_rank')
).sort('combined_rank')

print("按交易量排序的前10个代码:")
print(sorted_by_quote.head(10))

print("\n按相关性排序的前10个代码:")
print(sorted_by_corr.head(10))

print("\n按交易量和相关性综合排序的前10个代码:")
print(sorted_by_both.head(10))

# 找出两两相关性和quote_sum综合较高的code配对
# 1. 为每个code对添加quote_sum信息
# 创建一个映射表，包含code和quote_sum
code_quote_map = code_quote_sum.select(['code', 'quote_sum'])

# 为每个配对添加quote_sum信息
pair_df = (
    corr_df
    .join(code_quote_map, left_on='code1', right_on='code')
    .rename({'quote_sum': 'code1_quote_sum'})
    .join(code_quote_map, left_on='code2', right_on='code')
    .rename({'quote_sum': 'code2_quote_sum'})
)

# 计算配对的综合指标
pair_df = pair_df.with_columns(
    (pl.col('code1_quote_sum') + pl.col('code2_quote_sum')).alias('pair_quote_sum'),
    pl.min_horizontal('code1_quote_sum', 'code2_quote_sum').alias('pair_min_quote_sum'),
).with_columns(
    (pl.col('correlation') * (pl.col('pair_min_quote_sum'))).alias('weighted_correlation')
)

# 按照综合指标排序
sorted_pairs = pair_df.sort('weighted_correlation', descending=True)

# 实现排他性选择逻辑
def select_exclusive_pairs(pairs_df, top_n=20):
    """
    选择排他性的交易对，已选择的代码不会在后续配对中再次出现

    Args:
        pairs_df: 包含所有配对的DataFrame
        top_n: 要选择的配对数量

    Returns:
        排他性选择后的配对DataFrame
    """
    selected_pairs = []
    used_codes = set()

    # 遍历所有配对
    for row in pairs_df.iter_rows(named=True):
        code1, code2 = row['code1'], row['code2']

        # 如果两个代码都未被使用，则选择该配对
        if code1 not in used_codes and code2 not in used_codes:
            selected_pairs.append(row)
            used_codes.add(code1)
            used_codes.add(code2)

            # 如果已选择足够数量的配对，则停止
            if len(selected_pairs) >= top_n:
                break

    # 转换为DataFrame
    return pl.DataFrame(selected_pairs)

# 选择排他性的交易对
exclusive_pairs = select_exclusive_pairs(sorted_pairs, top_n=20)

print("\n按相关性和交易量综合排序的前20个代码配对(排他性选择):")
print(exclusive_pairs.select(['code1', 'code2', 'correlation', 'pair_min_quote_sum', 'weighted_correlation']))

plt.figure(figsize=(10, 6))
plt.scatter(result_df['quote_sum'], result_df['avg_correlation'], alpha=0.7)

# 标记一些特殊点
top_by_quote = sorted_by_quote.head(5)
top_by_corr = sorted_by_corr.head(5)
for df, color in [(top_by_quote, 'red'), (top_by_corr, 'green')]:
    plt.scatter(df['quote_sum'], df['avg_correlation'], color=color, s=100)
    for i, row in enumerate(df.iter_rows(named=True)):
        plt.annotate(row['code'],
                    (row['quote_sum'], row['avg_correlation']),
                    xytext=(5, 5),
                    textcoords='offset points')

plt.xlabel('Quote Sum')
plt.ylabel('Average Correlation')
plt.title('Quote Sum vs Average Correlation by Code')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig('quote_sum_vs_correlation.png')
print("图表已保存为 'quote_sum_vs_correlation.png'")

# 可视化代码配对的相关性和最小交易量
plt.figure(figsize=(12, 8))

# 使用排他性选择后的配对
top_pairs = exclusive_pairs

# 创建散点图，横轴为pair_min_quote_sum，纵轴为correlation，大小表示pair_quote_sum
scatter = plt.scatter(
    top_pairs['pair_min_quote_sum'],
    top_pairs['correlation'],
    s=top_pairs['pair_min_quote_sum'] / 1e11,  # 缩放大小以便可视化
    c=top_pairs['weighted_correlation'],
    cmap='viridis',
    alpha=0.7
)

# 添加颜色条
cbar = plt.colorbar(scatter)
cbar.set_label('Weighted Correlation')

# 为每个点添加标签
for i, row in enumerate(top_pairs.iter_rows(named=True)):
    plt.annotate(
        f"{row['code1']}-{row['code2']}",
        (row['pair_min_quote_sum'], row['correlation']),
        xytext=(5, 5),
        textcoords='offset points',
        fontsize=8
    )

plt.xlabel('Pair Min Quote Sum')
plt.ylabel('Correlation')
plt.title('Exclusive Code Pairs: Correlation vs Min Quote Sum')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig('exclusive_pairs_corr_vs_min_quote.png')
print("排他性代码配对图表已保存为 'exclusive_pairs_corr_vs_min_quote.png'")

# 可视化配对网络图
try:
    import networkx as nx

    # 创建一个无向图
    G = nx.Graph()

    # 使用排他性选择后的配对
    top_pairs_for_network = exclusive_pairs

    # 添加节点
    all_codes = set()
    for row in top_pairs_for_network.iter_rows(named=True):
        all_codes.add(row['code1'])
        all_codes.add(row['code2'])

    for code in all_codes:
        # 获取该代码的quote_sum
        quote_sum = code_quote_sum.filter(pl.col('code') == code)['quote_sum'].item()
        G.add_node(code, quote_sum=quote_sum)

    # 添加边
    for row in top_pairs_for_network.iter_rows(named=True):
        G.add_edge(
            row['code1'],
            row['code2'],
            weight=row['correlation'],
            weighted_corr=row['weighted_correlation']
        )

    # 绘制网络图
    plt.figure(figsize=(12, 10))

    # 使用spring布局
    pos = nx.spring_layout(G, seed=42)

    # 节点大小基于quote_sum
    node_sizes = [G.nodes[code]['quote_sum'] / 1e11 for code in G.nodes]

    # 边宽度基于相关性
    edge_weights = [G[u][v]['weight'] * 3 for u, v in G.edges]

    # 绘制节点
    nx.draw_networkx_nodes(G, pos, node_size=node_sizes, alpha=0.7, node_color='skyblue')

    # 绘制边
    nx.draw_networkx_edges(G, pos, width=edge_weights, alpha=0.5, edge_color='gray')

    # 绘制标签
    nx.draw_networkx_labels(G, pos, font_size=10)

    plt.title('Network of Exclusive Code Pairs')
    plt.axis('off')
    plt.tight_layout()
    plt.savefig('exclusive_code_pair_network.png')
    print("排他性代码配对网络图已保存为 'exclusive_code_pair_network.png'")
except ImportError:
    print("未安装networkx库，跳过网络图绘制")
# %%
