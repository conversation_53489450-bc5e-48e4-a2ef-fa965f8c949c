import os
from pprint import pprint
from lightning import LightningModule
import numpy as np
from pytorch_lightning.trainer import Trainer
from sklearn.metrics import classification_report, confusion_matrix
import torch
from core import cst
from core.backtest import backtest
from core import dataset
from core.data_module import KlineDataModule
from autogluon.tabular import TabularPredictor
from models.lightning_base import get_meta_win_rate_dict, calc_win_rate
from core.predictor_config import PredictorConfig
from aux.config_template import TrainingConfig
from core.dot_dict import DotDict as dd
import warnings
warnings.filterwarnings("ignore")


if __name__ == '__main__':
    torch.set_float32_matmul_precision('medium')
    cfg = TrainingConfig()
    # pre_train = False
    pre_train = True
    # pred_all = False
    pred_all = True    
    pred_cfg = cfg.predictor
    pred_cfg.set_seed()
    pred_cfg.is_regression = False
    num_classes = pred_cfg.num_classes = 2
    zigzag_labelling = pred_cfg.zigzag_labelling.up_pct = 0.001
    zigzag_labelling = pred_cfg.zigzag_labelling.in_use = True
    # zigzag_labelling = pred_cfg.zigzag_labelling.in_use = False
    pred_cfg.use_triple_barrier = False
    # pred_cfg.train_start_date = '2021.01.01'
    # pred_cfg.train_end_date = '2022.01.01'
    # pred_cfg.val_end_date = '2022.09.01'
    # pred_cfg.test_end_date = '2023.01.01'
    # pred_cfg.train_end_date = '2022.07.01'
    # pred_cfg.val_end_date = '2023.01.01'
    # pred_cfg.test_end_date = '2023.07.01'
    pred_cfg.price_change_threshold = 0.004
    # pred_cfg.model_name = 'tsmx'
    kline = pred_cfg.interval_cfg.base = 60
    pred_cfg.symbol = 'ETHUSDT'
    # pred_cfg.learning_rate *= 50 # when using bct model
    pred_cfg.batch_size = 512
    pred_cfg.use_presice_threshold = True
    # pred_cfg.augment_data = True
    concat_train_val = pred_cfg.concat_train_val = True
    # pred_cfg.meta_thr = 0.98
    # pred_cfg.pred_multi_step = False
    pred_cfg.seq_len = 1
    pred_cfg.pred_len = 1
    # pred_cfg.fracdiff = .999
    # pred_cfg.cum_feature_num = 7
    data_module = KlineDataModule(cfg)
    model_name = 'auto'
    train_str = 'train_val' if concat_train_val else 'train'
    excluded_model_types = ['NN_TORCH', 'RF', 'XT', 'CAT']
    pred_dict = dd()

    # test_returns[0] = 0
    if zigzag_labelling and pred_all:
        test_returns = data_module.dataset_dict.test.label.flatten()
        test_returns = np.concatenate([test_returns[1:], np.array([0])])
        print(f'{test_returns.mean() = }')        
        label_str_list = [
            # 'is_top', 
            'is_hrz_top', 
            # 'is_long', 
            # 'is_trend',
            ]
        for label_str in label_str_list:
            label_train = data_module.dataset_dict[train_str].zigzag_label_dict[label_str]
            label_test = data_module.dataset_dict.test.zigzag_label_dict[label_str]
            train_set = data_module.dataset_dict[train_str].feature_df
            test_set = data_module.dataset_dict.test.feature_df
            train_set[label_str] = label_train
            test_set[label_str] = label_test

            predictor = TabularPredictor(label=label_str).fit(train_set, excluded_model_types=excluded_model_types)
            predictions = predictor.predict(test_set)

            report = classification_report(label_test, predictions, digits=3)
            cm = confusion_matrix(test_set[label_str], predictions)
            print(f'{label_str} classification report:\n{report}\n{cm}')
            train_set.drop(columns=label_str, inplace=True)
            test_set.drop(columns=label_str, inplace=True)
            pred_positions = 2 * predictions.values - 1
            if label_str in ['is_top', 'is_hrz_top']:
                pred_positions *= 1
            save_str = f'{label_str}_{kline[0]}{kline[1]}'
            backtest(pred_positions, test_returns, pred_cfg.fee_ratio, save_str)
            pred_dict[label_str] = predictions.values
        sum_pred = np.sum(pred_dict['is_top'] + pred_dict['is_hrz_top'], axis=0) / 2
        save_str = 'sum_top_hrz_top'
        backtest(pred_positions, test_returns, pred_cfg.fee_ratio, save_str)
    else:
        label_str = f'clf{num_classes}'
        if zigzag_labelling:
            label_str += '_zl'
        train_set = data_module.dataset_dict[train_str].make_entire_dataset(output_df=True, label_str=label_str)
        test_set = data_module.dataset_dict.test.make_entire_dataset(output_df=True, label_str=label_str)
        predictor = TabularPredictor(label=label_str).fit(train_set, excluded_model_types=excluded_model_types)
        predictions = predictor.predict(test_set)
        label_test = test_set[label_str].values
        report = classification_report(label_test, predictions, digits=3)
        cm = confusion_matrix(test_set[label_str], predictions)
        print(f'{label_str} classification report:\n{report}\n{cm}')
        pred_positions = 2 ** (num_classes == 2) * predictions.values - 1
        save_str = f'{label_str}_{kline[0]}{kline[1]}'
        test_returns = data_module.dataset_dict.test.make_label_set().flatten()
        backtest(pred_positions, test_returns, pred_cfg.fee_ratio, save_str)

    # ckpt_folder = pred_cfg.get_ckpt_folder()
    # if not os.path.exists(ckpt_folder) or pre_train:
    #     lm = LightningClass(pred_cfg).to(pred_cfg.device)
    #     trainer.fit(lm, data_module)
    
    # ckpt_file_name = pred_cfg.get_last_ckpt_file_name()
    # # ckpt_file_name = 'last-v8.ckpt'
    # print(f'{ckpt_file_name = }')

    # ckpt_path = f'{ckpt_folder}/{ckpt_file_name}'
    # if not os.path.exists(ckpt_folder):
    #     os.makedirs(os.path.dirname(ckpt_folder), exist_ok=True)

    # lmc = LightningClass.load_from_checkpoint(ckpt_path, cfg=pred_cfg)
    
    # trainer.test(lmc, dataloaders=data_module.test_dataloader())

    # # meta classifier
    # pred_dict = dd()
    # for phase in ['train', 'val', 'test']:
    #     pred_list = trainer.predict(lmc, dataloaders=data_module.dataloader_dict[phase])
    #     pred = torch.cat(pred_list)
    #     pred_dict[phase] = pred

    # X_train, Y_train = data_module.dataset_dict.train.make_entire_dataset()
    # X_val, Y_val = data_module.dataset_dict.val.make_entire_dataset()
    # X_test, Y_test = data_module.dataset_dict.test.make_entire_dataset()
    
    
    
    # print(f'Training meta classifier on threshold {lmc.best_thr}')
    # lmc.train_meta(X_train, Y_train, pred_dict.train)

    # for phase, data_set, label_set in zip(['train', 'val', 'test'], [X_train, X_val, X_test], [Y_train, Y_val, Y_test]):
    #     pred_set = pred_dict[phase]
    #     meta_pred, meta_score = lmc.make_meta_clf(data_set, pred_set)
    #     meta_report = classification_report(label_set, meta_pred)
    #     cm = confusion_matrix(label_set, meta_pred)
    #     win_rate = calc_win_rate(label_set, meta_pred, pred_cfg.num_classes)
    #     meta_win_rate_dict = get_meta_win_rate_dict(label_set, meta_pred, meta_score, num_classes=pred_cfg.num_classes)
    #     print(f'Meta {phase} classifier report: \n{meta_report}')
    #     print(f'{cm}\n\n{win_rate = :4f}\n')
    #     pprint(meta_win_rate_dict)


    # lmc.get_highest_recall_threshold(X_val, Y_val, pred_dict.val)
    # print(f'Training meta classifier on threshold {lmc.best_thr}')
    # # reset meta classifier
    # lmc.meta = pred_cfg.get_meta_model()
    # lmc.train_meta(X_train, Y_train, pred_dict.train)
    
    # for phase, data_set, label_set in zip(['train', 'val', 'test'], [X_train, X_val, X_test], [Y_train, Y_val, Y_test]):
    #     pred_set = pred_dict[phase]
    #     meta_pred, meta_score = lmc.make_meta_clf(data_set, pred_set)
    #     meta_report = classification_report(label_set, meta_pred)
    #     cm = confusion_matrix(label_set, meta_pred)
    #     win_rate = calc_win_rate(label_set, meta_pred, pred_cfg.num_classes)
    #     meta_win_rate_dict = get_meta_win_rate_dict(label_set, meta_pred, meta_score, num_classes=pred_cfg.num_classes)
    #     print(f'Meta {phase} classifier report: \n{meta_report}')
    #     print(f'{cm}\n\n{win_rate = :4f}\n')
    #     pprint(meta_win_rate_dict)
