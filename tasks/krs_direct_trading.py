import os
os.environ["KERAS_BACKEND"] = "tensorflow"
# os.environ["KERAS_BACKEND"] = "torch"
import keras
from lightning import LightningModule
from pytorch_lightning.trainer import Trainer
import torch
from keras import Model
from core.data_module import KlineDataModule
from models.dva.lightning_dva import LightningDVA
from core.predictor_config import PredictorConfig
from core.backtest import backtest
from core.cst import TaskType
from aux.config_template import TrainingConfig


if __name__ == '__main__':
    torch.set_float32_matmul_precision('medium')    
    # keras.config.enable_unsafe_deserialization()
    cfg = TrainingConfig()
    pred_cfg = cfg.predictor
    pred_cfg.front_end = 'Keras'
    # pred_cfg.force_train = False
    pred_cfg.set_seed()    
    n_codes = pred_cfg.n_codes = 30
    # pred_cfg.position_enum == PositionType.Long = True
    # pred_cfg.directional_balance = True
    # pred_cfg.use_scaler = False
    pred_cfg.task_enum = TaskType.DirectTrading
    # pred_cfg.shuffling.train = False
    pred_cfg.dropout_rate = 0.2
    # pred_cfg.weight_decay = 0.0001 # 0.0001 bad for tsmx
    pred_cfg.noise_std = 0.01 # 0.05 bad
    pred_cfg.use_batch_norm = True
    pred_cfg.batch_size = 2048
    pred_cfg.embedding_size = 16
    pred_cfg.hidden_size = 16
    pred_cfg.seq_len = 30
    pred_cfg.pred_len = 1
    pred_cfg.patience = 10
    pred_cfg.num_epochs = 20
    pred_cfg.monitor = 'val_loss'
    pred_cfg.learning_rate = 6e-4
    # pred_cfg.optimize_sharpe = True
    # pred_cfg.fee_ratio = 0
    pred_cfg.num_rnn_layers = 1
    # pred_cfg.zigzag_labelling.in_use = True
    # pred_cfg.num_classes = 2
    # pred_cfg.sign_as_position = True
    # pred_cfg.num_epochs = 1
    # pred_cfg.position_enum == PositionType.Long = True
    # pred_cfg.optimizer_enum = cst.Optimizers.SGD
    # pred_cfg.learning_rate *= .1#1e-4
    # pred_cfg.use_mse_loss = True
    # pred_cfg.use_cum_mse_loss = True
    # pred_cfg.use_bce_loss = True
    # pred_cfg.use_cum_bce_loss = True
    # pred_cfg.use_mad_loss = True
    # pred_cfg.pred_len_as_batch_size.train = True
    pred_cfg.model_name = 'drt'
    # pred_cfg.direct_trading_model_name= 'tsmx'
    # pred_cfg.direct_trading_model_name= 'mlp'
    # pred_cfg.direct_trading_model_name= 'lstm'
    # pred_cfg.direct_trading_model_name= 'gru'
    # pred_cfg.fee_ratio = 0.001
    pred_cfg.interval_cfg.base = 360
    pred_cfg.symbol = 'BTCUSDT'
    # pred_cfg.learning_rate *= .100
    # pred_cfg.pred_multi_step = False
    pred_cfg.augment_data.train = True
    pred_cfg.augment_data.val = True
    pred_cfg.augment_data.rev = True
    # pred_cfg.train_start_date = '2021.01.01'
    # pred_cfg.train_end_date = '2022.01.01'    
    # pred_cfg.val_end_date = '2022.03.01'
    # pred_cfg.test_end_date = '2022.05.01'
    # pred_cfg.train_end_date = '2022.03.01'    
    # pred_cfg.val_end_date = '2022.05.01'
    # pred_cfg.test_end_date = '2022.07.01'
    # pred_cfg.train_end_date = '2022.05.01'    
    # pred_cfg.val_end_date = '2022.07.01'
    # pred_cfg.test_end_date = '2022.09.01'    
    # pred_cfg.train_end_date = '2022.07.01'    
    # pred_cfg.val_end_date = '2022.09.01'
    # pred_cfg.test_end_date = '2022.11.01'
    # pred_cfg.train_end_date = '2022.09.01'    
    # pred_cfg.val_end_date = '2022.11.01'
    # pred_cfg.test_end_date = '2023.01.01'      
    pred_cfg.train_end_date = '2022.12.01'    
    pred_cfg.val_end_date = '2023.01.01'
    pred_cfg.test_end_date = '2023.02.01'   
    # pred_cfg.train_end_date = '2023.01.01'    
    # pred_cfg.val_end_date = '2023.02.01'
    # pred_cfg.test_end_date = '2023.03.01'   
    # pred_cfg.train_end_date = '2023.01.01'    
    # pred_cfg.val_end_date = '2023.03.01'
    # pred_cfg.test_end_date = '2023.05.01'      
    # pred_cfg.train_start_date = '2022.11.01'  
    # pred_cfg.train_end_date = '2023.03.01'    
    # pred_cfg.val_end_date = '2023.05.01'
    # pred_cfg.test_end_date = '2023.07.01'  
    # pred_cfg.train_start_date = '2023.01.01'  
    # pred_cfg.train_end_date = '2023.05.01'    
    # pred_cfg.val_end_date = '2023.07.01'
    # pred_cfg.test_end_date = '2023.09.01'      
    # pred_cfg.train_start_date = '2023.03.01'  
    # pred_cfg.train_end_date = '2023.07.01'    
    # pred_cfg.val_end_date = '2023.09.01'
    # pred_cfg.test_end_date = '2023.11.01'         
    # pred_cfg.train_end_date = '2023.09.01'    
    # pred_cfg.val_end_date = '2023.11.01'
    # pred_cfg.test_end_date = '2024.01.01'  
    # pred_cfg.train_end_date = '2023.11.01'    
    # pred_cfg.val_end_date = '2024.01.01'
    # pred_cfg.test_end_date = '2024.03.01'                             
    # pred_cfg.train_end_date = '2022.07.01'
    # pred_cfg.val_end_date = '2023.01.01'    
    # pred_cfg.test_end_date = '2023.07.01'
    # pred_cfg.fracdiff = .2
    # pred_cfg.cum_feature_num = 7
    data_module = KlineDataModule(cfg)
    FrontEndClass = LightningModule if pred_cfg.front_end == 'Lightning' else Model
    model_name = pred_cfg.model_name
    
    class_name = f'{pred_cfg.front_end}{model_name.upper()}'
    exec(f'from models.{model_name}.{pred_cfg.front_end.lower()}_{model_name} import {class_name} as FrontEndClass')
    # print(lm.model)
    
    trainer = pred_cfg.get_trainer()
    ckpt_folder = pred_cfg.get_ckpt_folder()
    if (path_not_exist := not os.path.exists(ckpt_folder)) or pred_cfg.force_train:
        if path_not_exist:
            os.makedirs(ckpt_folder, exist_ok=True)
        fem = FrontEndClass(pred_cfg)
        fem.model.fit(
            data_module.dataloader_dict.train,
            validation_data=data_module.dataloader_dict.val,
            batch_size=pred_cfg.batch_size,
            epochs=pred_cfg.num_epochs,
            callbacks=pred_cfg.get_keras_callbacks()
            )
        phase = 'val'
        phase_pred = fem.model.predict(data_module.dataloader_dict[phase])
        save_str = f'{phase}_{n_codes}_{model_name}'
        label = data_module.dataset_dict[phase].make_label_set()
        backtest(phase_pred, label, pred_cfg.fee_ratio, save_str, n_codes=n_codes)
        phase = 'test'
        phase_pred = fem.model.predict(data_module.dataloader_dict[phase])
        save_str = f'{phase}_{n_codes}_{model_name}'
        label = data_module.dataset_dict[phase].make_label_set()
        backtest(phase_pred, label, pred_cfg.fee_ratio, save_str, n_codes=n_codes)
        # fem.model.save_model(ckpt_folder)
        # trainer.fit(fem, data_module)
    ckpt_file_name = pred_cfg.get_last_ckpt_file_name(is_keras=True)
    
    print(f'{ckpt_file_name = }')
    ckpt_path = f'{ckpt_folder}/{ckpt_file_name}'

    
    # lm.task_phase = cst.TaskPhase.VALIDATION_MODEL
    # trainer.test(lm, dataloaders=data_module.val_dataloader(), ckpt_path="best")
    # lm.task_phase = cst.TaskPhase.TESTING
    # trainer.test(lm, dataloaders=data_module.test_dataloader(), ckpt_path="best")
    femc = keras.models.load_model(ckpt_path)

    phase = 'test'
    phase_pred = fem.model.predict(data_module.dataloader_dict[phase])
    save_str = f'{phase}_{n_codes}_{model_name}'
    label = data_module.dataset_dict[phase].make_label_set()
    backtest(phase_pred, label, pred_cfg.fee_ratio, save_str, n_codes=n_codes)