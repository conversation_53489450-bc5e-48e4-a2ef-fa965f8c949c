from operator import imod
import os
from lightning import LightningModule
from matplotlib import axis
import numpy as np
from pytorch_lightning.trainer import Trainer
from sklearn.ensemble import RandomForestClassifier
import torch
from core import cst
from core.data_module import KlineDataModule
from models.dva.lightning_dva import LightningDVA
from core.predictor_config import PredictorConfig
from aux.config_template import TrainingConfig
from core.dot_dict import DotDict as dd
# 导入评估指标
from sklearn.metrics import classification_report
# 导入 SVM 模型
from sklearn.svm import SVC
# 导入 KNN 模型
from sklearn.neighbors import KNeighborsClassifier
# 导入LGBM模型
import lightgbm as lgb
# 导入XGBoost模型

if __name__ == '__main__':
    torch.set_float32_matmul_precision('medium')
    cfg = TrainingConfig()
    pred_cfg = cfg.predictor
    pred_cfg.set_seed()
    pred_cfg.seq_len = 720
    pred_cfg.pred_len = 120
    # pred_cfg.train_end_date = '2023-06-01'
    # pred_cfg.model_name = 'tsmx'
    pred_cfg.interval_cfg.base = 1
    pred_cfg.symbol = 'BTCUSDT'
    pred_cfg.overlapping_on_slicing = False
    pred_cfg.linear_law.in_use = True
    # pred_cfg.learning_rate *= .100
    # pred_cfg.cum_feature_num = 7
    test_set_name = 'test'
    data_module = KlineDataModule(cfg)
    models = [
        SVC(), 
        KNeighborsClassifier(n_neighbors=3), 
        # RandomForestClassifier(n_estimators=100, max_depth=5, random_state=0),
        lgb.LGBMClassifier(n_estimators=100, learning_rate=0.01, num_leaves=4, max_depth=2, random_state=0),
        ]
    feature_dict = dd()
    label_dict = dd()
    label_dict = dd()
    pred_dict = dd()
    for phase in ['train', 'val', test_set_name]:
        feature = data_module.dataset_dict[phase].linear_law_features
        label = data_module.dataset_dict[phase].make_label_set()
        label_sum = label.reshape(label.shape[0], -1).sum(axis=1)
        label = (label_sum > 0).astype(int).flatten()
        if phase == 'train':
            # 获取label各类别的support index
            index_dict = dd()
            for c in range(2):
                index_dict[c] = (label == c).nonzero()[0]

            # 获得最小支持类别
            min_support_class = min(index_dict.keys(), key=lambda x: index_dict[x].shape[0])
            # 将其他类别的样本裁切为最小支持类别的样本数
            for c in index_dict.keys():
                if c != min_support_class:
                    index_dict[c] = index_dict[c][-index_dict[min_support_class].shape[0]:]
                print(f'{phase} set label {c} support: {index_dict[c].shape[0]}')
            selected_index = np.concatenate([*index_dict.values()])
            feature_dict[phase] = feature[selected_index]
            label_dict[phase] = label[selected_index]
            label_dict[phase] = label[selected_index]            
        else:
            feature_dict[phase] = feature
            label_dict[phase] = label
            label_dict[phase] = label

    test_feature = feature_dict[test_set_name]
    test_label = label_dict[test_set_name]
    
    for model in models:
        model_name = model.__class__.__name__
        print(f'{model_name} start training...')
        model.fit(
            feature_dict.train, label_dict.train,
            # eval_set=(feature_dict.val, label_dict.val)
            )
        print(f'{model_name} training finished.')
        print(f'{model_name} start predicting...')
        
        train_pred = model.predict(feature_dict.train)
        print('train report:\n',classification_report(label_dict.train, train_pred))
        val_pred = model.predict(feature_dict.val)
        print('val report:\n',classification_report(label_dict.val, val_pred))
        test_pred = pred_dict[model_name] = model.predict(test_feature)
        # 报告评估结果
        print('test report:\n',classification_report(test_label, test_pred))
        print(f'{model_name} predicting finished.')

        
