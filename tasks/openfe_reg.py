import sys
print(sys.path)
from core.data_module import KlineDataModule
import pandas as pd
from openfe import OpenFE, tree_to_formula, transform, TwoStageFeatureSelector
from sklearn.model_selection import train_test_split
import lightgbm as lgb
from sklearn.metrics import classification_report, confusion_matrix, f1_score, mean_squared_error, precision_recall_fscore_support
from direct_trading import pred_cfg


def get_score(train_x, test_x, train_y, test_y):
    # train_x, test_x, train_y, test_y = train_test_split(train_x, train_y, test_size=0.2, random_state=1)
    params = {'n_estimators': 1000, 'n_jobs': n_jobs, 'seed': 1}
    gbm = lgb.LGBMRegressor(**params)
    gbm.fit(train_x, train_y, eval_set=[(test_x, test_y)], callbacks=[lgb.early_stopping(50, verbose=False)])
    pred_reg = pd.DataFrame(gbm.predict(test_x), index=test_x.index)
    score = mean_squared_error(test_y, pred_reg)
    gbmc = lgb.LGBMClassifier(**params)
    gbmc.fit(train_x, (train_y > 0).astype(int), eval_set=[(test_x, (test_y > 0).astype(int))], callbacks=[lgb.early_stopping(50, verbose=False)])
    pred_clf = gbmc.predict(test_x)
    report = classification_report(test_label := (test_y > 0).astype(int), pred_clf , digits=3)
    avg_p, avg_r, avg_f1, _ = precision_recall_fscore_support(test_label, pred_clf)
    cm = confusion_matrix(test_label, pred_clf)
    print(f'classification report:\n{report}\nconfusion matrix:\n{cm}')
    return score, avg_f1.mean()


if __name__ == '__main__':
    max_selected_features = 200
    feature_step = 5
    n_jobs = 8
    pred_cfg.n_codes = 20
    pred_cfg.seq_len = 1
    pred_cfg.feature_cfg.factor = True
    # pred_cfg.concat_train_val = True
    data_module = KlineDataModule(pred_cfg)
    dsd = data_module.dataset_dict
    train_df, train_y = dsd.train.make_entire_dataset(True)
    val_df, val_y = dsd.val.make_entire_dataset(True)
    train_x = train_df.drop(columns=['label'])
    val_x = val_df.drop(columns=['label'])
    train_y = train_y[..., 0].reshape(-1, 1)
    val_y = val_y[..., 0].reshape(-1, 1)
    # get baseline score
    score, f1 = get_score(train_x, val_x, train_y, val_y)
    print(f"The MSE before feature generation is {score}\nF1 score is {f1}")
    # We use the two-stage pruning algorithm of OpenFE to perform Feature Selection

    # fs = TwoStageFeatureSelector(n_jobs=n_jobs)
    # features = fs.fit(data=train_x, label=train_y)

    # # OpenFE gives the ranking of the base features:
    # print(features)
    # # Select the top 20 features
    # new_features = features[:20]
    # score = get_score(train_x[new_features], val_x[new_features], train_y, val_y)
    # print("The MSE after feature selection is", score)

    ofe = OpenFE()
    # ofe.fit(data=train_x, label=train_y, task='regression', n_jobs=n_jobs, verbose=False)    
    ofe.fit(data=train_x, label=(train_y > 0).astype(int), task='classification', n_jobs=n_jobs, verbose=False)
    
    # OpenFE gives the base features:
    new_features = ofe.new_features_list[:max_selected_features]
    print(f"The top {max_selected_features} generated features are")
    feature_name_list = []
    for i, feature in enumerate(new_features):
        feature_name = f'autoFE_f_{i}'
        feature_name_list.append(feature_name)
        print(f'{feature_name}: {tree_to_formula(feature)}')
    train_x, val_x = transform(train_x, val_x, new_features, n_jobs=n_jobs)
    best_fi_ft_num = 0
    best_fi = 0
    for n in range(feature_step, max_selected_features + 1, feature_step):
        selected_features = feature_name_list[:n]
        print(f"\nUsing top {n} generated features")
        score, f1 = get_score(train_x.loc[:, selected_features], val_x.loc[:, selected_features], train_y, val_y)
        if f1 > best_fi:
            best_fi = f1
            best_fi_ft_num = n
        print(f"The MSE before feature generation is {score}\nF1 score is {f1}")

    print(f"The best number of features is {best_fi_ft_num}, with F1 score {best_fi}")
    selected_features = feature_name_list[:best_fi_ft_num]
    score, f1 = get_score(train_x.loc[:, selected_features], val_x.loc[:, selected_features], train_y, val_y) 