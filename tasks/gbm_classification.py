import os
from lightning import LightningModule
import numpy as np
from pytorch_lightning.trainer import Trainer
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
from sklearn.metrics import classification_report, confusion_matrix
import torch
from xgboost import XGBClassifier
from core.cst import TaskType
from core.backtest import backtest
from core.data_module import KlineDataModule
from autogluon.tabular import TabularPredictor
from lightgbm import LGBMClassifier
from models.lightning_base import get_meta_win_rate_dict
from core.predictor_config import PredictorConfig
from aux.config_template import TrainingConfig
from core.dot_dict import DotDict as dd
import warnings
warnings.filterwarnings("ignore")



pred_cfg = PredictorConfig()
# pre_train = False
pred_cfg.force_train = True
# pred_all = False
pred_cfg.n_codes = 20
# pred_cfg.is_solo = True
pred_all = True    
pred_cfg.set_seed()
pred_cfg.is_regression = False
pred_cfg.task_enum = TaskType.ClassificationTrading
num_classes = pred_cfg.num_classes = 2
pred_cfg.zigzag_labelling.up_pct = 0.01
# zigzag_labelling = pred_cfg.zigzag_labelling.in_use = True
zigzag_labelling = pred_cfg.zigzag_labelling.in_use = False
pred_cfg.use_triple_barrier = False
pred_cfg.feature_cfg.factor = True
pred_cfg.feature_cfg.rolling = False
# pred_cfg.use_rolling = False
# pred_cfg.scale_per_code = True

# pred_cfg.augment_data.rev = True
# pred_cfg.train_start_date = '2021.01.01'
# pred_cfg.train_end_date = '2022.01.01'
# pred_cfg.val_end_date = '2022.09.01'
# pred_cfg.test_end_date = '2023.01.01'
# pred_cfg.train_end_date = '2022.07.01'
# pred_cfg.val_end_date = '2023.01.01'
# pred_cfg.test_end_date = '2023.07.01'
# pred_cfg.train_end_date = '2022.03.01'
# pred_cfg.val_end_date = '2022.07.01'    
# pred_cfg.test_end_date = '2022.11.01'    

# pred_cfg.train_end_date = '2022.11.01'
# pred_cfg.val_end_date = '2023.03.01'    
# pred_cfg.test_end_date = '2023.07.01'

pred_cfg.train_end_date = '2023.07.01'
pred_cfg.val_end_date = '2023.08.01'    
pred_cfg.test_end_date = '2024.08.01'    
pred_cfg.price_change_threshold = 0.004
# pred_cfg.model_name = 'tsmx'
kline = pred_cfg.interval_cfg.base = 240
# pred_cfg.symbol = 'DOGEUSDT'
# pred_cfg.symbol = 'BNBUSDT'
# pred_cfg.symbol = 'ETHUSDT'
# pred_cfg.learning_rate *= 50 # when using bct model
pred_cfg.batch_size = 512
pred_cfg.use_presice_threshold = True


# pred_cfg.meta_thr = 0.98
# pred_cfg.pred_multi_step = False
pred_cfg.seq_len = 1
pred_cfg.pred_len = 1
# pred_cfg.fracdiff = .999
# pred_cfg.cum_feature_num = 7

if __name__ == '__main__':
    torch.set_float32_matmul_precision('medium')
    pred_cfg.execute_phase.train_val = True
    pred_cfg.execute_phase.train = False
    pred_cfg.execute_phase.val = False
    pred_cfg.augment_data.train_val = True
    pred_cfg.augment_data.rev = True
    pred_cfg.model_name = 'lgb'
    train_str = 'train_val'
    data_module = KlineDataModule(pred_cfg)
    model_name = 'auto'

    excluded_model_types = ['NN_TORCH', 'RF', 'XT', 'CAT']
    pred_dict = dd()
    model = LGBMClassifier()
    # model = XGBClassifier()
    # model = LinearDiscriminantAnalysis()
    # test_returns[0] = 0
    if zigzag_labelling and pred_all:
        test_returns = data_module.dataset_dict.test.label.flatten()
        test_returns = np.concatenate([test_returns[1:], np.array([0])])
        print(f'{test_returns.mean() = }')        
        label_str_list = [
            'is_top', 
            'is_hrz_top', 
            'is_long', 
            'is_trend',
            ]
        for label_str in label_str_list:
            label_train = data_module.dataset_dict[train_str].zigzag_label_dict[label_str]
            label_test = data_module.dataset_dict.test.zigzag_label_dict[label_str]
            train_set = data_module.dataset_dict[train_str].feature_df
            test_set = data_module.dataset_dict.test.feature_df
            # train_set[label_str] = label_train
            # test_set[label_str] = label_test

            model.fit(train_set, label_train)
            predictions = model.predict(test_set)

            report = classification_report(label_test, predictions, digits=4)
            cm = confusion_matrix(label_test, predictions)
            print(f'{label_str} classification report:\n{report}\n{cm}')
            pred_scores = model.predict_proba(test_set)[:, 1]
            pred_scores = 2 * pred_scores - 1
            pred_dict[label_str] = pred_scores
            for thr in np.arange(.1, 1, 0.1):                
                for sign in [-1, 1]:
                    signed_pred_scores = sign * pred_scores
                    signed_pred_scores[abs(signed_pred_scores) < thr] = 0
                    save_str = f'{label_str}_thr{thr:.1f}_s{sign}_{kline[0]}{kline[1]}'
                    backtest(signed_pred_scores, test_returns, pred_cfg.fee_ratio, save_str)
                
        sum_pred = (pred_dict['is_top'] + pred_dict['is_hrz_top']) / 2
        save_str = 'sum_top_hrz_top'
        backtest(sum_pred, test_returns, pred_cfg.fee_ratio, save_str)
    else:
        label_str = f'clf{num_classes}'
        if zigzag_labelling:
            label_str += '_zl'
        train_set = data_module.dataset_dict[train_str].make_entire_dataset(flatten1=True)
        test_set = data_module.dataset_dict.test.make_entire_dataset(flatten1=True)
        model.fit(*train_set)
        predictions = model.predict(test_set[0])
        score = model.predict_proba(test_set[0])[:, 1]
        label_test = test_set[1]
        report = classification_report(label_test, predictions, digits=4)
        cm = confusion_matrix(label_test, predictions)
        print(f'{label_str} classification report:\n{report}\n{cm}')
        pred_positions = predictions * 2 ** (num_classes == 2) - 1
        # pred_positions = score * 2 ** (num_classes == 2) - 1
        save_str = f'{label_str}_{kline[0]}{kline[1]}'
        save_folder = os.path.join(pred_cfg.get_ckpt_folder(), pred_cfg.datetime_str)
        test_returns = data_module.dataset_dict.test.make_label_set()[..., 0].flatten()
        backtest(pred_positions, test_returns, fee_ratio_dict=pred_cfg.fee_ratio, save_folder=save_folder, save_str=save_str, is_portfolio=False)
        

    # ckpt_folder = pred_cfg.get_ckpt_folder()
    # if not os.path.exists(ckpt_folder) or pre_train:
    #     lm = LightningClass(pred_cfg).to(pred_cfg.device)
    #     trainer.fit(lm, data_module)
    
    # ckpt_file_name = pred_cfg.get_last_ckpt_file_name()
    # # ckpt_file_name = 'last-v8.ckpt'
    # print(f'{ckpt_file_name = }')

    # ckpt_path = f'{ckpt_folder}/{ckpt_file_name}'
    # if not os.path.exists(ckpt_folder):
    #     os.makedirs(os.path.dirname(ckpt_folder), exist_ok=True)

    # lmc = LightningClass.load_from_checkpoint(ckpt_path, cfg=pred_cfg)
    
    # trainer.test(lmc, dataloaders=data_module.test_dataloader())

    # # meta classifier
    # pred_dict = dd()
    # for phase in ['train', 'val', 'test']:
    #     pred_list = trainer.predict(lmc, dataloaders=data_module.dataloader_dict[phase])
    #     pred = torch.cat(pred_list)
    #     pred_dict[phase] = pred

    # X_train, Y_train = data_module.dataset_dict.train.make_entire_dataset()
    # X_val, Y_val = data_module.dataset_dict.val.make_entire_dataset()
    # X_test, Y_test = data_module.dataset_dict.test.make_entire_dataset()
    
    
    
    # print(f'Training meta classifier on threshold {lmc.best_thr}')
    # lmc.train_meta(X_train, Y_train, pred_dict.train)

    # for phase, data_set, label_set in zip(['train', 'val', 'test'], [X_train, X_val, X_test], [Y_train, Y_val, Y_test]):
    #     pred_set = pred_dict[phase]
    #     meta_pred, meta_score = lmc.make_meta_clf(data_set, pred_set)
    #     meta_report = classification_report(label_set, meta_pred)
    #     cm = confusion_matrix(label_set, meta_pred)
    #     win_rate = calc_win_rate(label_set, meta_pred, pred_cfg.num_classes)
    #     meta_win_rate_dict = get_meta_win_rate_dict(label_set, meta_pred, meta_score, num_classes=pred_cfg.num_classes)
    #     print(f'Meta {phase} classifier report: \n{meta_report}')
    #     print(f'{cm}\n\n{win_rate = :4f}\n')
    #     pprint(meta_win_rate_dict)


    # lmc.get_highest_recall_threshold(X_val, Y_val, pred_dict.val)
    # print(f'Training meta classifier on threshold {lmc.best_thr}')
    # # reset meta classifier
    # lmc.meta = pred_cfg.get_meta_model()
    # lmc.train_meta(X_train, Y_train, pred_dict.train)
    
    # for phase, data_set, label_set in zip(['train', 'val', 'test'], [X_train, X_val, X_test], [Y_train, Y_val, Y_test]):
    #     pred_set = pred_dict[phase]
    #     meta_pred, meta_score = lmc.make_meta_clf(data_set, pred_set)
    #     meta_report = classification_report(label_set, meta_pred)
    #     cm = confusion_matrix(label_set, meta_pred)
    #     win_rate = calc_win_rate(label_set, meta_pred, pred_cfg.num_classes)
    #     meta_win_rate_dict = get_meta_win_rate_dict(label_set, meta_pred, meta_score, num_classes=pred_cfg.num_classes)
    #     print(f'Meta {phase} classifier report: \n{meta_report}')
    #     print(f'{cm}\n\n{win_rate = :4f}\n')
    #     pprint(meta_win_rate_dict)
