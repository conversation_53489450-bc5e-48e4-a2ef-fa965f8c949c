import os
from lightning import LightningModule
from pytorch_lightning.trainer import Trainer
import torch
from core import cst
from core.data_module import KlineDataModule
from models.dva.lightning_dva import LightningDVA
from core.predictor_config import PredictorConfig
from aux.config_template import TrainingConfig

if __name__ == '__main__':
    torch.set_float32_matmul_precision('medium')
    cfg = TrainingConfig()
    pred_cfg = cfg.predictor
    pred_cfg.set_seed()
    # pred_cfg.model_name = 'tsmx'
    pred_cfg.interval_cfg.base = 60
    pred_cfg.symbol = 'BTCUSDT'
    pred_cfg.learning_rate *= .100
    # pred_cfg.pred_multi_step = False
    # pred_cfg.pred_len = 1
    # pred_cfg.fracdiff = .2
    # pred_cfg.cum_feature_num = 7
    data_module = KlineDataModule(cfg)
    LightningClass = LightningModule
    model_name = pred_cfg.model_name
    class_name = f'Lightning{model_name.upper()}'
    exec(f'from models.{model_name}.lightning_{model_name} import {class_name} as LightningClass')
    lm = LightningClass(pred_cfg).to(pred_cfg.device)
    # print(lm.model)
    
    trainer = pred_cfg.get_trainer()
    trainer.fit(lm, data_module)
    ckpt_folder = pred_cfg.get_ckpt_folder()
    ckpt_file_name = pred_cfg.get_last_ckpt_file_name()
    # ckpt_file_name = 'last-v8.ckpt'
    print(f'{ckpt_file_name = }')

    ckpt_path = f'{ckpt_folder}/{ckpt_file_name}'
    if not os.path.exists(ckpt_folder):
        os.makedirs(os.path.dirname(ckpt_folder), exist_ok=True)
    
    # lm.task_phase = cst.TaskPhase.VALIDATION_MODEL
    # trainer.test(lm, dataloaders=data_module.val_dataloader(), ckpt_path="best")
    # lm.task_phase = cst.TaskPhase.TESTING
    # trainer.test(lm, dataloaders=data_module.test_dataloader(), ckpt_path="best")
    lmc = LightningClass.load_from_checkpoint(ckpt_path, cfg=pred_cfg)
    
    trainer.test(lmc, dataloaders=data_module.test_dataloader())    