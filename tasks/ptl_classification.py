import os
from pprint import pprint
from lightning import LightningModule
import numpy as np
from pytorch_lightning.trainer import Trainer
from sklearn.metrics import classification_report, confusion_matrix
import torch
from core import cst
from core import dataset
from core.data_module import KlineDataModule
from models.dva.lightning_dva import LightningDVA
from models.lightning_base import get_meta_win_rate_dict, calc_win_rate
from core.predictor_config import PredictorConfig
from aux.config_template import TrainingConfig
from core.dot_dict import DotDict as dd
import warnings
warnings.filterwarnings("ignore")


if __name__ == '__main__':
    torch.set_float32_matmul_precision('medium')
    cfg = TrainingConfig()
    # pre_train = False
    pre_train = True
    pred_cfg = cfg.predictor
    pred_cfg.set_seed()
    pred_cfg.is_regression = False
    pred_cfg.num_classes = 2
    # pred_cfg.use_scaler = False
    # pred_cfg.zigzag_labelling.in_use = True
    pred_cfg.use_triple_barrier = False
    # pred_cfg.train_start_date = '2021.01.01'
    # pred_cfg.train_end_date = '2022.01.01'
    # pred_cfg.val_end_date = '2022.07.01'
    # pred_cfg.test_end_date = '2023.01.01'
    # pred_cfg.train_end_date = '2022.07.01'
    # pred_cfg.val_end_date = '2023.01.01'
    # pred_cfg.test_end_date = '2023.07.01'
    pred_cfg.price_change_threshold = 0.004
    # pred_cfg.model_name = 'tsmx'
    pred_cfg.interval_cfg.base = 30
    pred_cfg.symbol = 'BTCUSDT'
    # pred_cfg.learning_rate *= 0.10 # when using bct model
    pred_cfg.batch_size = 256
    pred_cfg.use_presice_threshold = True
    # pred_cfg.augment_data = True
    # pred_cfg.meta_thr = 0.98
    # pred_cfg.pred_multi_step = False
    pred_cfg.seq_len = 60
    pred_cfg.pred_len = 1
    # pred_cfg.fracdiff = .4
    # pred_cfg.cum_feature_num = 7
    data_module = KlineDataModule(cfg)
    LightningClass = LightningModule
    model_name = pred_cfg.model_name
    trainer = pred_cfg.get_trainer()

    class_name = f'Lightning{model_name.upper()}'
    exec(f'from models.{model_name}.lightning_{model_name} import {class_name} as LightningClass')

    ckpt_folder = pred_cfg.get_ckpt_folder()
    if not os.path.exists(ckpt_folder) or pre_train:
        lm = LightningClass(pred_cfg).to(pred_cfg.device)
        trainer.fit(lm, data_module)
    
    ckpt_file_name = pred_cfg.get_last_ckpt_file_name()
    # ckpt_file_name = 'last-v8.ckpt'
    print(f'{ckpt_file_name = }')

    ckpt_path = f'{ckpt_folder}/{ckpt_file_name}'
    if not os.path.exists(ckpt_folder):
        os.makedirs(os.path.dirname(ckpt_folder), exist_ok=True)
    
    # lm.task_phase = cst.TaskPhase.VALIDATION_MODEL
    # trainer.test(lm, dataloaders=data_module.val_dataloader(), ckpt_path="best")
    # lm.task_phase = cst.TaskPhase.TESTING
    # trainer.test(lm, dataloaders=data_module.test_dataloader(), ckpt_path="best")
    lmc = LightningClass.load_from_checkpoint(ckpt_path, cfg=pred_cfg)
    
    trainer.test(lmc, dataloaders=data_module.test_dataloader())

    # meta classifier
    pred_dict = dd()
    for phase in ['train', 'val', 'test']:
        pred_list = trainer.predict(lmc, dataloaders=data_module.dataloader_dict[phase])
        pred = torch.cat(pred_list)
        pred_dict[phase] = pred

    X_train, Y_train = data_module.dataset_dict.train.make_entire_dataset()
    X_val, Y_val = data_module.dataset_dict.val.make_entire_dataset()
    X_test, Y_test = data_module.dataset_dict.test.make_entire_dataset()
    
    
    
    print(f'Training meta classifier on threshold {lmc.best_thr}')
    lmc.train_meta(X_train, Y_train, pred_dict.train)

    for phase, data_set, label_set in zip(['train', 'val', 'test'], [X_train, X_val, X_test], [Y_train, Y_val, Y_test]):
        pred_set = pred_dict[phase]
        meta_pred, meta_score = lmc.make_meta_clf(data_set, pred_set)
        meta_report = classification_report(label_set, meta_pred, digits=3)
        cm = confusion_matrix(label_set, meta_pred)
        win_rate = calc_win_rate(label_set, meta_pred, pred_cfg.num_classes)
        meta_win_rate_dict = get_meta_win_rate_dict(label_set, meta_pred, meta_score, num_classes=pred_cfg.num_classes)
        print(f'Meta {phase} classifier report: \n{meta_report}')
        print(f'{cm}\n\n{win_rate = :4f}\n')
        pprint(meta_win_rate_dict)


    lmc.get_highest_recall_threshold(X_val, Y_val, pred_dict.val)
    print(f'Training meta classifier on threshold {lmc.best_thr}')
    # reset meta classifier
    lmc.meta = pred_cfg.get_meta_model()
    lmc.train_meta(X_train, Y_train, pred_dict.train)
    
    for phase, data_set, label_set in zip(['train', 'val', 'test'], [X_train, X_val, X_test], [Y_train, Y_val, Y_test]):
        pred_set = pred_dict[phase]
        meta_pred, meta_score = lmc.make_meta_clf(data_set, pred_set)
        meta_report = classification_report(label_set, meta_pred, digits=3)
        cm = confusion_matrix(label_set, meta_pred)
        win_rate = calc_win_rate(label_set, meta_pred, pred_cfg.num_classes)
        meta_win_rate_dict = get_meta_win_rate_dict(label_set, meta_pred, meta_score, num_classes=pred_cfg.num_classes)
        print(f'Meta {phase} classifier report: \n{meta_report}')
        print(f'{cm}\n\n{win_rate = :4f}\n')
        pprint(meta_win_rate_dict)
