import os
import time
import numpy as np
import pandas as pd
from datetime import datetime
import arcticdb as adb
from tqdm import tqdm
from core.predictor_config import PredictorConfig


def write_arcticdb(cfg: PredictorConfig):
    start = time.time()
    arctic = adb.Arctic(cfg.arctic.lmdb_name)
    lib = arctic.get_library(cfg.arctic.lib_name, create_if_missing=True)
    file_list = os.listdir(cfg.arctic.feather_folder)
    interval_list = cfg.arctic.interval_list
    df_dict = {interval_str: [] for interval_str in interval_list}
    print('Loading')
    for file in tqdm(file_list):
        if 'futures' not in file:
            continue
        code = file.split('_')[0]
        interval_str = file.split('-')[1].split('-')[0]
        if interval_str in interval_list:
            df = pd.read_feather(os.path.join(cfg.arctic.feather_folder, file))
            df['code'] = code
            # feather 数据的时间戳是 UTC 时间，需要去除
            df['date'] = df['date'].dt.tz_convert(None)
            df.rename(columns={'date': 'open_time'}, inplace=True)
            df = df.reset_index(drop=True)
            df_dict[interval_str].append(df)
    print('Writing')
    for interval_str in tqdm(interval_list):
        df_to_update = pd.concat(df_dict[interval_str], ignore_index=True)
        if cfg.arctic.is_updating:            
            existing_data = lib.read(interval_str).data            
            # 确保现有数据也使用RangeIndex
            existing_data = existing_data.reset_index(drop=True)
            
            # 使用merge检测新数据
            merged = df_to_update[['open_time', 'code']].merge(
                existing_data[['open_time', 'code']],
                how='left',
                indicator=True
            )
            df_to_update = df_to_update[merged['_merge'] == 'left_only']
            if len(df_to_update) == 0:
                continue    
            lib.append(interval_str, df_to_update)
        else:
            if lib.has_symbol(interval_str):
                try:
                    lib.append(interval_str, df_to_update)
                except Exception as e:
                    print(e)
            else:
                lib.write(interval_str, df_to_update)

    data = lib.read(interval_str).data
    print(data.head())
    print(data.tail())
    print(f'{lib.list_symbols() = }')
    elapsed = time.time() - start
    print(f'writing {elapsed = :.3f} s')


if __name__ == '__main__':
    cfg = PredictorConfig()
    # cfg.arctic.is_updating = True
    # cfg.arctic.interval_list = ['4h']
    write_arcticdb(cfg)