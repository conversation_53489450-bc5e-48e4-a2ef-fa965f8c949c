from math import e
import os
from re import T
import numpy as np
import pandas as pd
import polars as pl
from core.dot_dict import DotDict as dd
import dolphindb as ddb

from core.cst import BarType, DatabaseType, TopkType

'Removed:' 'MDTUSDT', 'SLPUSDT', 'CVXUSDT', 'DGBUSDT', 'CTKUSDT', 'STRAXUSDT', 'RAYUSDT', 'OCEANUSDT', 'IDEXUSDT', 'STPTUSDT', 'SNTUSDT', 'AGIXUSDT', 'RADUSDT', 'GLMRUSDT', 'FRONTUSDT', 'HNTUSDT', 'SRMUSDT', 'TOMOUSDT', 'BTSUSDT', 'ANTUSDT', 'BLUEBIRDUSDT', 'COCOSUSDT', 'AUDIOUSDT', 'GALUSDT', 'MBLUSDT', 'RNDRUSDT', 'MATICUSDT'
'Blank field:' 'FTTUSDT', 'SCUSDT', 'CVCUSDT', 'BTCSTUSDT', 'WAVESUSDT',
# 'Part missing:' 
# 'maybe missing:' 'APTUSDT', 'QNTUSDT', 
class DataConfig:
    is_solo = False
    n_codes = 20
    code_list = []
    n_alternate = 10
    alternate_code_list = []
    trade_alternate = False
    load_all_codes = False
    topk_enum = TopkType.Abs
    top_egnore_num = 0
    top_num = 1
    # top_num_list = [1, 2, 4, 8, 12, 16, 20]    
    top_num_list = [1, 2, 3, 5, 10, 15, 20, 30]
    # top_num_list = [8, 12, 16, 20, 24]
    # top_num_list = [16, 20, 24, 28, 32]
    code_sort_by_quote = False
    quote_quantile = 0.9
    mask_zero_quote_code = True
    filter_mask_name = 'cycle_idx'
    earliest_date_before_train_start = True
    filter_excluded_codes = []
    backtest_excluded_codes = [
        # 'APTUSDT', 'QNTUSDT', 
        'FTTUSDT', 'SCUSDT', 'CVCUSDT', 'BTCSTUSDT', 
        'MDTUSDT', 'SLPUSDT', 'CVXUSDT', 'DGBUSDT', 'CTKUSDT', 'STRAXUSDT', 'RAYUSDT', 'OCEANUSDT', 'IDEXUSDT', 'STPTUSDT', 'SNTUSDT', 'AGIXUSDT', 'RADUSDT', 'GLMRUSDT', 'FRONTUSDT', 
        'GALUSDT', 'HNTUSDT', 'SRMUSDT', 'TOMOUSDT', 'BTSUSDT', 'ANTUSDT', 'BLUEBIRDUSDT', 'COCOSUSDT', 'AUDIOUSDT', 'FOOTBALLUSDT', 'MBLUSDT', 'RNDRUSDT', 'WAVESUSDT', 
        'MATICUSDT', #'FTMUSDT'
        ]
    online_excluded_codes = [
        # 'APTUSDT', 'QNTUSDT', 
        'FTTUSDT', 'SCUSDT', 'CVCUSDT', 'BTCSTUSDT', 
        'MDTUSDT', 'SLPUSDT', 'CVXUSDT', 'DGBUSDT', 'CTKUSDT', 'STRAXUSDT', 'RAYUSDT', 'OCEANUSDT', 'IDEXUSDT', 'STPTUSDT', 'SNTUSDT', 'AGIXUSDT', 'RADUSDT', 'GLMRUSDT', 'FRONTUSDT', 
        'GALUSDT', 'HNTUSDT', 'SRMUSDT', 'TOMOUSDT', 'BTSUSDT', 'ANTUSDT', 'BLUEBIRDUSDT', 'COCOSUSDT', 'AUDIOUSDT', 'FOOTBALLUSDT', 'MBLUSDT', 'RNDRUSDT', 'WAVESUSDT', 
        'MATICUSDT', #'FTMUSDT' # 2025.01.06h17UTC+8
        ]
    selected_codes = [
        'BNBUSDT', 'DOGEUSDT', 'AXSUSDT', 'XMRUSDT', 'XRPUSDT','WAVESUSDT', 'DEFIUSDT', 'NEARUSDT', 'ENJUSDT', 'IOSTUSDT', 'YFIUSDT', 'ZECUSDT', 'ETHUSDT'
        ]
    selected_indices = []
    symbol = 'BTCUSDT' # '#' for multi-symbol
    group_by_col = 'open_time'
    group_by_str = 'bar_idx'
    n_range_groups = 20
    valid_index_dict = dd()
    data_module = None
    # train_start_date = '2020.01.01'
    start_date = dd(
            single="2020.01.01",
            multi="2021.01.01",
        )
    quote_start_date = ''
    quote_end_date = ''
    train_end_date = '2023.01.01'
    val_start_date = ''
    test_start_date = ''    
    val_end_date = '2023.08.01'
    test_end_date = '2024.03.01'
    save_data = True
    raw_date_dict = dd()
    augment_data = dd(
        train=False,
        train_val=False,
        train_test=False,
        val=False,        
        test=False,
        rev=False,
        rev_first=True,
        upside_down=True,
        predict=False,
        online=False,
    )
    
    dolphin = dd(
        linux_conf_path="/etc/resolv.conf",
        db_name="dfs://monthly",
        tb_name="ohlc_",
        conn_info=dd(
            host="localhost",
            port=8848,
            userid="admin",
            password="123456"
        )
    )

    arctic = dd(
        lmdb_name="lmdb://arcticdb",
        lib_name='binance_futures',
        feather_folder='/home/<USER>/Projects/freqtrade/user_data/data/binance/futures',
        # interval_list = ['1m', '3m', '5m'],
        # interval_list = ['15m', '30m', '1h', '2h', '4h'],
        interval_list = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h'],
        is_updating=False,
    )

    database_enum = DatabaseType.Arctic
    # dataframe_type = 'pandas'
    dataframe_type = 'polars'
    sim_base_interval = None
    sim_label_step = 2
    stream_restart_interval = 24
    bar_enum = BarType.Time
    agg_cfg = dd(
        time=1,
        count=1000,
        volume=10000,
        quote=1000000000,
        move=.02,
        quote_move=1e7,
    )

    cycle_mask_idx = None

    interval_type = dd(
        options=["kline", "aggTrades", "order_flow"],
        index=0,
        kline=[240, 'min'],
        # kline=(1440, 'min'),
        aggTrades=[1000000, 'ms'],
        order_flow=[1, 's'],
        multi=240,
    )
    
    interval_cfg = dd(
        base=60,
        feature=[60],
        label=240,
        unit='min',
        back_shift_in_sec=200,
    )

    execute_phase = dd(
        train=True,
        train_val=False,
        train_test=False,
        val=True,
        test=True,
        predict=True,
        online=True,
    )    

    platform = 'binance_future'
    
    account_id = "<EMAIL>"
    access_key = 'VK3vmWBe3GNOiYQTEEBX6QPtuASsvMhNru8azFI31fbRpwEwHz1u7sbgrQbj2aRG'
    secret_key = '1MaS6JwFN4nSSkvw6jZdfnfLFGsKT2ZTFRHxAjUgJ9YYJhi2JEmH2Rgkbqx9Pxbj'
    

    @property
    def trading_pairs_path(self) -> str:
        return f"aux/{self.platform}_trading_pairs.csv"
    
    
    @property
    def train_start_date(self) -> str:
        if self.n_codes == 1:
            return self.start_date.single
        else:
            return self.start_date.multi

    @property
    def task_date_dict(self):
        if len(self.val_start_date) > 0:
            val_start_date = self.val_start_date
        else:
            val_start_date = self.train_end_date
        
        if len(self.test_start_date) > 0:
            test_start_date = self.test_start_date
        else:
            test_start_date = self.val_end_date

        return dd(
            train=[self.train_start_date, self.train_end_date],
            train_val=[self.train_start_date, self.val_end_date],
            train_test=[self.train_start_date, self.test_end_date],
            val=[val_start_date, self.val_end_date],
            test=[test_start_date, self.test_end_date],
        )
    
    @property
    def sort_by_list(self):
        if self.code_sort_by_quote:
            return ['code', 'code_idx']
        else:
            return ['code_idx', 'code']


    @property
    def filter_mask_fn_dict(self):
        return dd(
            cycle_idx=lambda group: group['cycle_idx'] == self.cycle_mask_idx,
            is_segment_end=lambda group: group['is_segment_end'],
        )
    

    def filter_mask_fn(self, group: pl.DataFrame, cfg) -> np.ndarray:
        return np.ones(len(group), dtype=np.float32)
    

    def get_filter_mask_fn(self, mask_name: str = None):
        if mask_name is None:
            mask_name = self.filter_mask_name
        filter_mask_fn_dict = self.filter_mask_fn_dict
        if mask_name in filter_mask_fn_dict:
            return filter_mask_fn_dict[mask_name]
        else:
            return lambda group: np.ones(len(group), dtype=np.float32)

    
    def get_valid_arr(self, group: pl.DataFrame, phase) -> np.ndarray:
        if self.load_all_codes and self.quote_quantile < 1:
            quote_gt_zero = (group['rolling_quote_min'] > 0).values
            if not quote_gt_zero.all() and self.mask_zero_quote_code and phase != 'online':
                is_valid_arr = np.zeros(len(group), dtype=np.float32)
            else:
                is_valid_arr = ((group['quote_quantile'] <= self.quote_quantile).values & quote_gt_zero).astype(np.float32)
        else:
            is_valid_arr = np.ones(len(group), dtype=np.float32)

        return is_valid_arr


    def get_all_code_list(self):
        return (
			pl.read_csv(self.trading_pairs_path).select('symbol').filter(pl.col('symbol').str.ends_with('USDT') & pl.col('symbol').is_in(self.online_excluded_codes).not_())
			# .sort(by='symbol')
			.to_series().to_list()
		)
    
    def get_feather_file_name(self, code, interval_str):
        return f'{code}_USDT_USDT-{interval_str}-futures.feather'
    
    
    def get_arctic_symbol(self, code, interval_str):
        return f'{code}_{interval_str}'
    

    def get_result_of_dolphin(self, df_str: str, code_str: str = None, alternate_str: str = None) -> pd.DataFrame:
        s = ddb.session()
        s.connect(**self.dolphin.conn_info)
        df = s.run(df_str) # type: ignore
        if code_str is None:
            code_list = df.code.unique().tolist()
            alternate_code_list = []
        else:
            code_list = list(s.run(code_str))
            alternate_code_list = list(s.run(alternate_str))
        s.undefAll()
        s.close()
        return df.drop(columns=['interval', 'count']), code_list, alternate_code_list # type: ignore
    

    def get_ddb_str(self, quote_start_date: str = None, quote_end_date: str = None, end_date: str = None, code: str = None) -> tuple[str, str]:        
        if quote_start_date is None:
            quote_start_date = self.quote_start_date or self.train_start_date
        if quote_end_date is None:
            quote_end_date = self.quote_end_date or self.train_end_date
        if end_date is None:
            if self.execute_phase.test:
                end_date = self.test_end_date
            else:
                end_date = self.val_end_date
        if code is None:
            code = self.symbol
        ddb_cfg = self.dolphin
        interval = self.interval_cfg.base
        tb_name = ddb_cfg.tb_name + f'{interval}min'
        n_codes = self.n_codes
        n_codes_with_alternate = self.n_codes + self.n_alternate
        if self.code_sort_by_quote:
            sort_by_str = 'code_idx'
            tail_str = ''
        else:
            sort_by_str = 'code'
            tail_str = '\nsortBy!(codeTB, "code")'
        if self.load_all_codes:
            exclude_codes = self.backtest_excluded_codes
            all_code_list = self.get_all_code_list()
            df_str = f"""
			tbFunc = loadTable{{"{ddb_cfg.db_name}", "{tb_name}"}}
            //codeTB = select code from tbFunc() group by code //having min(quote) > 0
            //print(codeTB.code)
			//quote_start_date = {quote_start_date}
            //code in codeTB.code, code not in {exclude_codes}
			select * from tbFunc() where code like "%USDT", code in {all_code_list}, {quote_start_date} <= open_time < {end_date} order by open_time
			"""
            alternate_str = None
        elif self.n_codes == 1 and self.symbol != '#':
            df_str = f"""
			tbFunc = loadTable{{"{ddb_cfg.db_name}", "{tb_name}"}}

			select * , 0 as code_idx from tbFunc() where code == "{self.symbol}", {self.train_start_date} <= open_time < {end_date} order by open_time, {sort_by_str}
			"""
            code_str = None
            alternate_str = None
        else:
            exclude_codes = self.backtest_excluded_codes
            min_date_limit_str = f'having min(open_time) <= {quote_start_date if self.earliest_date_before_train_start else quote_end_date}'
            get_head_str = lambda n: f"""
			tbFunc = loadTable{{"{ddb_cfg.db_name}", "{tb_name}"}}
			//quote_start_date = {quote_start_date}
			codeTB = select top {n} sum(quote)/size(code) as quote_ratio
			from tbFunc() where code like "%USDT", code not in {exclude_codes}, open_time >= {quote_start_date} group by code
			{min_date_limit_str} order by quote_ratio desc"""
            df_str = f"""
			{get_head_str(n_codes)}
			selectedTB = select * from tbFunc() where code in codeTB.code, {self.train_start_date} <= open_time < {end_date}
            update selectedTB set code_idx = codeTB.code.find(selectedTB['code'])
            select * from selectedTB order by open_time, {sort_by_str}
			"""
            code_str = f'{get_head_str(n_codes)}{tail_str}\ncodeTB.code'
            alternate_str = f'{get_head_str(n_codes_with_alternate)}\ncodeTB.code[{n_codes}:]'
        return df_str, code_str, alternate_str


        return f"""DB_NAME = "dfs://monthly"
    select * from DB_NAME.loadTable("ohlc_{self.interval_cfg.base}min") where date(open_time) in {quote_start_date}..{end_date}, code = '{code}' order by code, open_time
    """        


    def get_data_from_dolphin(self) -> pd.DataFrame:
        ddb_cfg = self.dolphin
        interval = self.interval_cfg.base
        tb_name = ddb_cfg.tb_name + f'{interval}min'
        if self.n_codes == 1:
            ddb_str = f"""
			tbFunc = loadTable{{"{ddb_cfg.db_name}", "{tb_name}"}}
			start_date = {ddb_cfg.start_date.single}

			select * from tbFunc() where code == "{self.symbol}", open_time >= start_date order by open_time, code
			"""
        else:
            exclude_codes = self.backtest_excluded_codes
            ddb_str = f"""
			tbFunc = loadTable{{"{ddb_cfg.db_name}", "{tb_name}"}}
			start_date = {ddb_cfg.start_date.multi}
			codeTB = select top {self.n_codes} sum(quote)/size(code) as quote_ratio
			from tbFunc() where code like "%USDT", code not in {exclude_codes}, open_time >= start_date group by code
			having min(open_time) <= start_date order by quote_ratio desc
			print(codeTB.code)
			select * from tbFunc() where code in codeTB.code, open_time >= start_date order by open_time, code
			"""
        s = ddb.session()
        s.connect(**ddb_cfg.conn_info)
        # test_str = f"""
        # excludeCodes = {exclude_codes}
        # print(excludeCodes[0])
        # print(type(excludeCodes))
        # 'BC' in {exclude_codes}
        # """
        # tmp = s.run(test_str)
        # print(tmp)
        df = s.run(ddb_str).drop(columns=['interval']) # type: ignore
        s.undefAll()
        s.close()
        group_by_col = self.group_by_col
        raw_length = len(df)    
        if self.dataframe_type == 'pandas':
            df = df.set_index([group_by_col, 'code']).sort_index()
        else:
            df = (
                pl.from_pandas(df).with_columns(pl.col(group_by_col).cast(
                    pl.Datetime('ns')).alias(group_by_col))
                .groupby(group_by_col).agg(pl.exclude(group_by_col))
                .filter(
                    (pl.col('volume').list.min() > 0) & (pl.col('code').list.lengths() == self.n_codes)
                    )
                .sort(by=group_by_col)
            )
        print(f'{raw_length // self.n_codes - len(df) = }')        
        return df
    
    def get_kline_interval_str(self):        
        interval = self.interval_cfg.base
        if 60 <= interval < 1440:
            interval_str = f'{interval // 60}h'
        elif 1440 <= interval < 7 * 1440:
            interval_str = f'{interval // 1440}d'
        elif 7 * 1440 <= interval:
            interval_str = f'{interval // (7 * 1440)}w'
        else:
            interval_str = f'{interval}m'
        return interval, interval_str

                    
if __name__ == '__main__':
    dc = DataConfig()
    dc.n_codes = 30
    dc.interval_cfg.base = 120
    ddb_str = dc.get_ddb_str()
    df = dc.get_result_of_dolphin(ddb_str)
    folder = 'predictor/dataset'
    if not os.path.exists(folder):
        os.makedirs(folder)
    df = pl.from_pandas(df).select(
        pl.col('open_time').alias('date'),        
        'open',
        'high',
        'low',
        'close',
        'volume',
        pl.col('quote').alias('amount'),
        'code',
    )
    code_list = df['code'].unique().to_list()
    for code in code_list:
        df_code = df.filter(pl.col('code') == code).sort('date')
        df_code.write_csv(f'{folder}/{code}.csv')