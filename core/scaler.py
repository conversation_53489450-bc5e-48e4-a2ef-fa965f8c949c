import numpy as np
import pandas as pd
from core.cst import NormType


class ZScoreScaler:
    is_fit = False
    def fit(self, X: np.ndarray | pd.DataFrame, norm_enum: NormType = NormType.MeanStd):
        self.norm_enum = norm_enum
        if isinstance(X, pd.DataFrame):
            X = X.values
            self.columns = X.columns
        if norm_enum == NormType.MeanStd:
            self.mean = np.mean(X, axis=0)
            self.std = np.std(X, axis=0)# + 1e-12
        elif norm_enum == NormType.StdOnly:
            self.mean = 0
            self.std = np.std(X, axis=0)# + 1e-12
        elif norm_enum == NormType.StdOnAbs:
            self.mean = 0
            self.std = np.std(np.abs(X), axis=0)# + 1e-12
        elif norm_enum == NormType.StdOnSides:
            self.mean = 0
            self.std = np.std(X[X > 0], axis=0)# + 1e-12
            self.std_neg = np.std(X[X < 0], axis=0)# + 1e-12
        self.is_fit = True


    def transform(self, X: np.ndarray | pd.DataFrame):
        if isinstance(X, pd.DataFrame):
            X = X.values
            X = self.numpy_transform(X)
            return pd.DataFrame(X, columns=self.columns)
        return self.numpy_transform(X)


    def numpy_transform(self, X: np.ndarray):
        if self.norm_enum == NormType.StdOnSides:
            X[X > 0] = (X[X > 0] - self.mean) / self.std
            X[X < 0] = (X[X < 0] - self.mean) / self.std_neg
        else:
            X = (X - self.mean) / self.std
        return X


    def fit_transform(self, X: np.ndarray | pd.DataFrame):
        self.fit(X)
        return self.transform(X)
    

class QuantileScaler:
    is_fit = False
    def fit(self, X: np.ndarray | pd.DataFrame, clip_outliers=True):
        if isinstance(X, pd.DataFrame):
            X = X.values
            self.columns = X.columns
        self.median = np.median(X, axis=0)
        self.mad = np.median(np.abs(X - self.median), axis=0)# + 1e-12
        self.mad *= 1.4826  # scale factor for MAD to obtain std
        self.clip_outliers = clip_outliers
        self.is_fit = True


    def transform(self, X: np.ndarray | pd.DataFrame):
        if isinstance(X, pd.DataFrame):
            X = X.values            
        result = (X - self.median) / self.mad
        if self.clip_outliers:
            result = np.clip(result, -3, 3)
        if isinstance(X, pd.DataFrame):
            return pd.DataFrame(result, columns=self.columns)
        return result


    def fit_transform(self, X: np.ndarray | pd.DataFrame):
        self.fit(X)
        return self.transform(X)
    

class CrossSectionalScaler:
    def __init__(self, scaler_type_str: str = 'bypass', label_col: str = None, group_col: str = 'open_time'):
        self.scaler_func_dict = dict(
            zscore=zscore_norm,
            robust=robust_norm,
            rank=None,
            bypass=None
        )
        self.scaler_type_str = scaler_type_str
        self.label_col = label_col
        self.group_col = group_col


    def transform(self, df: pd.DataFrame):
        if self.scaler_type_str == 'bypass':
            return df
        scaler_func = self.scaler_func_dict[self.scaler_type_str]
        label_col = self.label_col
        group_col = self.group_col
        if label_col is None:
            for col in df.columns:
                if col!= group_col:
                    if self.scaler_type_str == 'rank':
                        df[col] = (df[col].groupby(group_col, group_keys=False).rank(pct=True) - 0.5) * 3.46
                    df[col] = df[col].groupby(group_col, group_keys=False).apply(scaler_func)
        elif self.scaler_type_str == 'rank':
            df[label_col] = (df[label_col].groupby(group_col, group_keys=False).rank(pct=True) - 0.5) * 3.46
        else:
            df[label_col] = df[label_col].groupby(group_col, group_keys=False).apply(scaler_func)
        return df
    


def zscore_norm(srs: pd.Series):
    return (srs - srs.mean()) / srs.std()


def robust_norm(srs: pd.Series, clip_outliers=True, zscore=False):
    median = srs.median()
    mad = (srs - median).mean()
    if clip_outliers:
        result = srs.clip(lower=-3*mad, upper=3*mad)
    if zscore:
        result = (srs - median) / mad
    return result.astype(np.float32)
