from ast import alias
from calendar import c
from copy import deepcopy
from functools import partial
import arcticdb as adb
from pprint import pprint
from datetime import datetime
from matplotlib import category
from sklearn.linear_model import LinearRegression
import sys
from typing import List, Tuple, Union
import numpy as np
import pandas as pd
import polars as pl
# import polars_talib as plta
import pytorch_lightning as ptl
from pytorch_lightning.utilities.types import EVAL_DATALOADERS
from sklearn.discriminant_analysis import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix
from torch.utils.data import DataLoader
# from core.data_loarder import KlineDataLoader
from aed_quant.utils.parser import parse_history_kline
from core.dataset import KlineDataset
from core.predictor_config import PredictorConfig
# from sklearn.preprocessing import RobustScaler
from core.dot_dict import DotDict as dd
# from fracdiff import fdiff
from core.cst import BarType, DatabaseType, LabelType, TaskType
from core.batch_sampler import RandomPairBatchSampler, multi_collate_fn
from utils.augment import get_upside_down
from utils.generate_feature import aggregate_bar, process_bar
# __path__ = os.path.dirname(os.path.dirname(__file__))
# sys.path.append(__path__)
#// 2


class KlineDataModule(ptl.LightningDataModule):
    def __init__(self, cfg: PredictorConfig, history_dict: dd[str, Union[list, dict]] = dd(), load_dolphin: bool = True, **kwargs):
        super().__init__()
        self.cfg = cfg
        # self.scaler = RobustScaler()
        self.feature_scaler_got_fit = False
        self.scaler_saved = False

        self.num_classes = cfg.num_classes
        self.batch_size = cfg.batch_size
        self.shuffling_train = cfg.shuffling.train
        self.num_workers = cfg.num_workers
        self.drop_last = cfg.drop_last
        self.pin_memory = cfg.pin_memory and cfg.device == 'cuda'

        self.head_cutoff_len = cfg.head_cutoff_len
        self.cutoff_len = cfg.cutoff_len
        self.dataset_dict = dd()
        self.batch_sampler_dict = dd()
        self.dataloader_dict = dd()
        self.iter_date_dict = dd()
        self.cum_adapt_count = 0
        self.next_adapt_count = 0
        if len(cfg.raw_date_dict) == 0:
            self.raw_date_dict = cfg.task_date_dict
        else:
            self.raw_date_dict = cfg.raw_date_dict
        self.iter_count = 0
        self.multi_index = ['open_time'] + cfg.sort_by_list
        if not cfg.use_scaler:
            cfg.feature_scaler = None

        if len(history_dict) > 0:
            self.raw_dict = dd()
            exclude_timestamp_ms = None
            if set(cfg.code_list) != set(history_dict):
                code_set = set(cfg.code_list) | set(history_dict)
                code_list = list(code_set)
            else:
                code_list = cfg.code_list
            latest_start_datetime = None
            for symbol, history in history_dict.items():
                code_idx = code_list.index(symbol)
                symbol_data, exclude_timestamp_ms = parse_history_kline(
                cfg.platform, history, symbol, code_idx, exclude_timestamp_ms)
                self.raw_dict[symbol] = symbol_data#[start_index:]
                start_datetime = symbol_data['open_time'][0]
                symbol_data['code_idx'] = code_idx
                latest_start_datetime = start_datetime if latest_start_datetime is None else max(latest_start_datetime, start_datetime)
            sorted_values = [self.raw_dict[k] for k in code_list if k in self.raw_dict]
            self.raw_history = pd.concat(sorted_values, ignore_index=True).set_index(self.multi_index)
            self.history_cols = ["open", "high", "low", "close", "volume", "quote",
			"buy_volume", "buy_quote"]
            self.raw_history[self.history_cols] = self.raw_history[self.history_cols].astype(np.float64)
            if not load_dolphin:
                self.raw_history = self.raw_history[self.raw_history.index.get_level_values('open_time') >= latest_start_datetime].sort_index(level=self.multi_index)
                print(f'{latest_start_datetime = }\n{self.raw_history.shape = }')
            else:
                self.raw_history.sort_index(level=self.multi_index, inplace=True)


            # print(self.raw_history.head(30))
            # print(self.raw_history.tail(30))
        else:
            self.raw_history = None

        if not load_dolphin:
            # self.phase = 'online'
            self.update_online_dataset()

        else:
            self.raw_data = self.load_data_from_database().set_index(self.multi_index)
            if self.raw_history is not None:
                interval = cfg.interval_cfg.base
                merge_datetime = self.raw_data.index.get_level_values('open_time')[-1] + pd.Timedelta(minutes=interval)
                merge_date_str = merge_datetime.strftime('%Y.%m.%d')
                end_datetime = self.raw_history.index.get_level_values('open_time')[-1] + pd.Timedelta(minutes=interval)
                end_datetime_str = end_datetime.strftime('%Y.%m.%d.%H')
                # print(pd.to_datetime(end_datetime_str))
                for phase in list(self.raw_date_dict.keys()):
                    if pd.to_datetime(self.raw_date_dict[phase][1]) >= pd.to_datetime(merge_date_str) and phase in ['val', 'test']:
                        self.raw_date_dict[phase][1] = end_datetime_str
                cfg.raw_date_dict = self.raw_date_dict

                history_div_merge = self.raw_history[(self.raw_history.index.get_level_values('open_time') >= merge_datetime)]
                if not cfg.load_all_codes:
                    history_div_merge = history_div_merge[history_div_merge.index.get_level_values('code').isin(cfg.code_list)]

                zero_quote_codes = history_div_merge[history_div_merge.quote == 0].index.get_level_values('code').unique()
                history_div_merge.drop(index=history_div_merge[history_div_merge.index.get_level_values('code').isin(zero_quote_codes)].index, inplace=True)

                # print(self.raw_data.tail(30))
                self.raw_data = pd.concat([self.raw_data, history_div_merge]).sort_index()
                # print(self.raw_data.tail(30))


            if cfg.load_all_codes:
                self.raw_data = self.get_rolling_quote_quantile(self.raw_data, from_dolphin=True)
            # else:
            #     self.raw_data.set_index(self.multi_index, inplace=True)

            if not cfg.rolling_retraining.in_use:
                self.set_dataloaders(self.raw_data, self.raw_date_dict)

            if not cfg.is_regression:
                self.report_occurrance()

            if cfg.concat_prediction and not cfg.prediction_appended:
                self.append_prediction()
                cfg.prediction_appended = True


    def update_online_dataset(self, online_data: pd.DataFrame = None) -> pd.DataFrame:

        start_date = self.raw_date_dict.train[1]
        end_date = ''
        date_dict = dd(
            # predict=[start_date, end_date],
            online=[start_date, end_date]
            )
        if online_data is None:
            online_data = self.raw_history
        online_data = self.get_rolling_quote_quantile(online_data)

        self.set_dataloaders(online_data, date_dict)



    def update_data_from_stream(self, new_row_dict: dict, is_back_shift: bool = False) -> bool:
        cfg = self.cfg
        if cfg.sim_base_interval is not None:
            base_interval = cfg.sim_base_interval
            label_interval = base_interval * cfg.sim_label_step
        else:
            base_interval = cfg.interval_cfg.base
            label_interval = cfg.interval_cfg.label

        sorted_values = [new_row_dict[k] for k in cfg.code_list]
        new_row = pd.concat(sorted_values).set_index(self.multi_index)
        new_row[self.history_cols] = new_row[self.history_cols].astype(np.float64)

        end_datetime: pd.Timestamp = new_row.index.get_level_values('open_time')[-1]
        start_datetime = end_datetime - pd.Timedelta(minutes=base_interval * (self.cutoff_len + 2))

        online_data = pd.concat([self.raw_history, new_row])#[-length:]
        online_data = online_data[online_data.index.get_level_values('open_time') >= start_datetime].sort_index()

        if is_back_shift:
            self.update_online_dataset(online_data)
            self.latest_batch = self.get_latest_batch()
            # return False
        else:
            self.raw_history = online_data
            # cycle_idx = (end_datetime.hour * 60 + end_datetime.minute) % label_interval // base_interval
            # cycle_mask_idx = cfg.cycle_mask_idx
            # if isinstance(cycle_mask_idx, int) and cycle_mask_idx != cycle_idx:
            #     print(f'{cycle_idx = } != {cycle_mask_idx = }, skipping calc')
            #     return True
            # else:
            #     print(f'{cycle_idx = }, {cycle_mask_idx = }, calc next timestamp')
            #     return False


    def get_latest_batch(self) -> np.ndarray:
        quantile_mask = self.quantile_mask.reshape(-1, 1, 1)
        dataloader = self.dataloader_dict.online

        latest_batch_x, latest_batch_y, latest_batch_z, category, image = iter(dataloader).__next__()

        latest_batch_x = np.nan_to_num(latest_batch_x) * quantile_mask
        return latest_batch_x, category, image


    def load_data_from_database(self) -> pd.DataFrame:
        cfg = self.cfg
        if cfg.database_enum == DatabaseType.Dolphin:
            raw_data = self.load_data_from_dolphin()
        elif cfg.database_enum == DatabaseType.Arctic:
            raw_data = self.load_data_from_arctic()
        return raw_data


    def load_data_from_arctic(self) -> pd.DataFrame:
        cfg = self.cfg
        arctic = adb.Arctic(cfg.arctic.lmdb_name)
        lib = arctic.get_library(cfg.arctic.lib_name)
        interval, interval_str = cfg.get_kline_interval_str()
        quote_start_date = cfg.quote_start_date or cfg.train_start_date
        quote_end_date = cfg.quote_end_date or cfg.train_end_date
        code_earliest_date = quote_start_date if cfg.earliest_date_before_train_start else quote_end_date
        if cfg.execute_phase.test:
            end_date = cfg.test_end_date
        else:
            end_date = cfg.val_end_date
            
        if cfg.n_codes == 1:
            exclude_code_expr = pl.col('code') == cfg.symbol
            cfg.code_list = [cfg.symbol]
            cfg.alternate_code_list = []
            cfg.n_alternate = 0
        else:
            exclude_code_expr = pl.col('code').is_in(cfg.backtest_excluded_codes).not_()

        earliest_date_limit_expr = pl.col('open_time').min().over('code') <= pd.to_datetime(code_earliest_date)
        df = lib.read(interval_str).data
        df['code'] = df['code'] + 'USDT'
        df: pl.DataFrame = pl.from_pandas(df)

        # 先筛选出符合条件的代码
        filtered_df = df.filter(
            exclude_code_expr,
            earliest_date_limit_expr,
        )

        if cfg.n_codes > 1:
            # 再筛选出code_earliest_date之前的数据并计算quote_sum
            code_quote_sum = (
                filtered_df.filter(
                    pl.col('open_time') <= pd.to_datetime(code_earliest_date)
                ).with_columns(
                    pl.col('quote').sum().over('code').alias('quote_sum'),
                ).select(['code', 'quote_sum'])
                .unique()
            )
            code_list = code_quote_sum.top_k(cfg.n_codes, by='quote_sum')['code'].to_list()
            code_list_with_alternate = code_quote_sum.top_k(cfg.n_codes + cfg.n_alternate, by='quote_sum')['code'].to_list()
            alternate_code_list = code_list_with_alternate[cfg.n_codes:]
            code_list_by_quote = deepcopy(code_list)
            if not cfg.code_sort_by_quote:
                code_list.sort()
                alternate_code_list.sort()

            n_codes = len(cfg.code_list)
            n_alternate = len(cfg.alternate_code_list)
            if n_codes == 0:
                cfg.code_list = code_list
                cfg.n_codes = len(code_list)
            else:
                cfg.n_codes = n_codes
            if n_alternate == 0:
                cfg.alternate_code_list = alternate_code_list
                cfg.n_alternate = len(alternate_code_list)
            else:
                cfg.n_alternate = n_alternate
            print(f'{cfg.code_list = }\n{cfg.alternate_code_list = }')

        raw_data = df.filter(
            pl.col('code').is_in(cfg.code_list),
            pl.col('open_time') >= pd.to_datetime(cfg.train_start_date),
            pl.col('open_time') < pd.to_datetime(end_date),
        ).to_pandas()
        raw_data['code_idx'] = raw_data.code.apply(lambda x: code_list_by_quote.index(x))
        return raw_data

    def load_data_from_dolphin(self) -> pd.DataFrame:
        cfg = self.cfg
        df_str, code_str, alternate_str = cfg.get_ddb_str()
        raw_data: pd.DataFrame
        raw_data, code_list, alternate_code_list = cfg.get_result_of_dolphin(df_str, code_str, alternate_str)#.set_index(['open_time', 'code'])
        n_codes = len(cfg.code_list)
        n_alternate = len(cfg.alternate_code_list)
        if n_codes == 0:
            cfg.code_list = code_list
            cfg.n_codes = len(code_list)
        else:
            cfg.n_codes = n_codes
        if n_alternate == 0:
            cfg.alternate_code_list = alternate_code_list
            cfg.n_alternate = len(alternate_code_list)
        else:
            cfg.n_alternate = n_alternate
        print(f'{cfg.code_list = }\n{cfg.alternate_code_list = }')
        raw_data['code_idx'] = raw_data.code.apply(lambda x: code_list.index(x))
        return raw_data
        # if sum(cfg.augment_data.values()) == 0:
        #     task_data, task_feature_df = self.process(raw_data)
        # if not os.path.exists(self.code_list_path):
        #     # 创建一个.py空文件，用于记录code_list
        #     with open(self.code_list_path, 'w') as f:
        #         lst_str = f'{cfg.code_list}'
        #         f.write(f'code_list = {lst_str}')
        # max_window = 0

        # task_date_dict = cfg.task_date_dict

        # if cfg.is_planning or cfg.concat_train_val:
        #     self.date_dict.train_val = (task_date_dict.train[0], task_date_dict.val[1])
            # self.date_dict.train_test = (task_date_dict.val[0], task_date_dict.test[1])

    def get_next_open_timestamp(self, base_interval: int = None) -> int:
        if base_interval is None:
            base_interval = self.cfg.interval_cfg.base
        end_open_time: pd.Timestamp = self.raw_history.index.get_level_values('open_time').max()
        return int(end_open_time.value // 1_000_000 + base_interval * 60_000)


    def get_next_trade_timestamp(self, label_interval: int = None, back_shift_in_sec: int = None) -> int:
        if label_interval is None:
            label_interval = self.cfg.interval_cfg.label
        if back_shift_in_sec is None:
            back_shift_in_sec = self.cfg.interval_cfg.back_shift_in_sec
        end_open_time: pd.Timestamp = self.raw_history.index.get_level_values('open_time').max()
        end_open_timestamp_in_ms = end_open_time.value // 1_000_000
        label_interval_in_ms = label_interval * 60_000
        next_trade_close_timestamp_in_ms = end_open_timestamp_in_ms // label_interval_in_ms * label_interval_in_ms + label_interval_in_ms
        next_trade_timestamp_in_ms =  next_trade_close_timestamp_in_ms - back_shift_in_sec * 1000
        deadline_timestamp_in_ms = int(datetime.now().timestamp() * 1000) - 30_000
        if next_trade_timestamp_in_ms < deadline_timestamp_in_ms:
            next_trade_timestamp_in_ms += label_interval_in_ms
        return next_trade_timestamp_in_ms


    def get_rolling_quote_quantile(self, raw_data, from_dolphin=False):
        cfg = self.cfg
        multi_index = self.multi_index if from_dolphin else raw_data.index.names
        raw_data: pl.DataFrame = pl.from_pandas(raw_data, include_index=True).with_row_index()
        end_datetime = raw_data['open_time'].max()
        if cfg.load_all_codes:
            raw_data = raw_data.with_columns(
                    rolling_quote=pl.col("quote").rolling_mean(cfg.seq_len, min_periods=1).over('code').fill_null(0.0),
                    rolling_quote_min=pl.col("quote").rolling_min(cfg.seq_len, min_periods=1).over('code').fill_null(0.0),
                ).with_columns(
                    quote_rank=pl.col("rolling_quote").rank(descending=True).over('open_time'),
                ).with_columns(
                    quote_rank_pct=pl.col('quote_rank') / pl.col('quote_rank').max().over('open_time'),
                ).sort(['open_time', 'rolling_quote'], descending=[False, True]).with_columns(
                    quote_cum = pl.col('quote').cumsum().over('open_time'),
                ).with_columns(
                    quote_cum_max=pl.col('quote_cum').max().over('open_time'),
                    quote_quantile=pl.col('quote_cum') / pl.col('quote_cum').max().over('open_time'),
                )


        result = raw_data.sort(by='index').drop('index').to_pandas().set_index(multi_index)
        end_row = result[result.index.get_level_values('open_time') == end_datetime]
        self.latest_close_arr = end_row['close'].to_numpy().astype(np.float32)
        if cfg.load_all_codes:
            quantile_mask = end_row['quote_quantile'] <= cfg.quote_quantile
            self.quantile_mask = quantile_mask.to_numpy().astype(np.float32)
        else:
            self.quantile_mask = np.ones(cfg.n_codes, dtype=np.float32)
        end_row_code_list = end_row.reset_index()["code"].tolist()
        print(f'{result.shape = }\n{len(end_row_code_list) = }\n{list(zip(cfg.code_list, end_row_code_list)) = }')
        return result


    def __iter__(self):
        return self

    def __next__(self):
        cfg = self.cfg
        # cfg.scaler = StandardScaler()
        self.feature_scaler_got_fit = False
        assert cfg.rolling_retraining.in_use
        interval = cfg.rolling_retraining.interval_in_day
        delta = pd.Timedelta(days=interval * self.iter_count)
        train_end = pd.to_datetime(self.raw_date_dict.train[1]) + delta
        if train_end > pd.to_datetime(self.raw_date_dict.val[1]):
            raise StopIteration
        val_start = pd.to_datetime(self.raw_date_dict.val[0]) + delta
        self.iter_date_dict = deepcopy(self.raw_date_dict)
        self.iter_date_dict.train[1] = train_end.strftime('%Y.%m.%d')
        self.iter_date_dict.val[0] = val_start.strftime('%Y.%m.%d')
        self.set_dataloaders(self.raw_data, self.iter_date_dict)
        self.iter_count += 1
        return self.dataloader_dict


    @property
    def date_dict(self):
        if self.iter_date_dict is None:
            return self.raw_date_dict
        else:
            return self.iter_date_dict


    def set_dataloaders(self, raw_data_pd: pd.DataFrame = None, date_dict: dict = None, selected_phase = None):
        if raw_data_pd is None:
            raw_data_pd = self.raw_data
        if date_dict is None:
            date_dict = self.raw_date_dict
        cfg = self.cfg
        raw_data = aggregate_bar(cfg, raw_data_pd)
        batch_size = self.batch_size
        for phase, is_execute in cfg.execute_phase.items():
            if selected_phase is not None and phase != selected_phase:
                continue
            if not is_execute or phase not in date_dict:
                continue
            if phase == 'online':
                phase_raw_data = raw_data
            else:
                start_date_str, end_date_str = date_dict[phase]

                if len(start_date_str) == 0:
                    start_date = raw_data['open_time'][0]
                else:
                    start_date = pd.to_datetime(start_date_str)

                # if True:
                if len(end_date_str) == 0:
                    end_date = raw_data['open_time'][-1] + pd.Timedelta(minutes=cfg.interval_cfg.base)
                else:
                    end_date = pd.to_datetime(end_date_str)

                phase_raw_data = raw_data.filter((pl.col('open_time') >= start_date) & (pl.col('open_time') < end_date))

                l = len(phase_raw_data)
                if l == 0:
                    print(f'No data for {phase} phase, skipping')
                    continue

                if not cfg.augment_data[phase] and phase in ['val', 'test']:
                    start_idx = phase_raw_data['bar_idx'][0] - self.head_cutoff_len
                    end_idx = phase_raw_data['bar_idx'][-1]
                    phase_raw_data = raw_data.filter((pl.col('bar_idx') >= start_idx) & (pl.col('bar_idx') < end_idx))

                    # if cfg.bar_enum == BarType.Time:
                    #     start_idx_date = phase_raw_data['open_time'][0]
                    #     start_idx = raw_data.index.get_level_values('open_time').get_loc(start_idx_date)
                    #     if isinstance(start_idx, slice):
                    #         start_idx = start_idx.start
                    #     end_idx_date = phase_raw_data.index.get_level_values('open_time')[-1]
                    #     end_idx = raw_data.index.get_level_values('open_time').get_loc(end_idx_date)
                    #     is_portfolio = 'pfl' in cfg.task_enum.value
                    #     if isinstance(end_idx, slice):
                    #         end_idx = end_idx.stop
                    #     else:
                    #         end_idx += 1

                    #     # support_size = cfg.sample_pair * batch_size * n_codes ** is_portfolio

                    #     # head_count = self.cutoff_len

                    #     start_date = start_idx_date - pd.Timedelta(minutes=cfg.interval_cfg.base * (self.head_cutoff_len))
                    #     phase_raw_data = raw_data[(raw_data.index.get_level_values('open_time') >= start_date) & (raw_data.index.get_level_values('open_time') < end_date)]

                    #     # print(pd.concat([phase_raw_data.head(), phase_raw_data.tail()]))
                    #     # l_phase_raw = phase_raw_data.shape[0]
                    #     # assert l_phase_raw % n_codes == 0, f'{l_phase_raw} % {n_codes} != 0'
                    #     # assert  l_phase_raw == (l + head_count) * n_codes + support_size, f'{phase_raw_data.shape[0]} != {(l + head_count) * n_codes + support_size}'

                    # elif cfg.bar_enum in [BarType.Volume, BarType.Quote]:
                    #     start_idx = phase_raw_data['bar_idx'][0] - self.head_cutoff_len
                    #     end_idx = phase_raw_data['bar_idx'][-1]
                    #     phase_raw_data = raw_data.filter((pl.col('bar_idx') >= start_idx) & (pl.col('bar_idx') < end_idx))


            # 如果是EDA模式，直接使用原始数据，跳过特征计算
            if cfg.is_eda:
                # 转换为pandas DataFrame
                phase_data = phase_raw_data.to_pandas()
                # 创建空的特征和标签DataFrame，保持接口一致
                phase_feature_df = pd.DataFrame(index=phase_data.index)
                phase_label_df = pd.DataFrame(index=phase_data.index)
            else:
                phase_data, phase_feature_df, phase_label_df = process_bar(cfg, phase_raw_data, phase)

            # else:
            #     phase_data = task_data[task_data.open_time.between(dates[0], dates[1])]
            #     phase_feature_df = task_feature_df[task_feature_df.index.isin(phase_data.index)]
            #     if phase in ['val', 'test']:
            #         n_codes = self.cfg.n_codes
            #         start_idx = task_data.index.get_loc(dates[0]).start
            #         phase_data = pd.concat([raw_data[start_idx - n_codes * (max_window - 1): start_idx], phase_data], ignore_index=True)
            #         phase_feature_df = pd.concat([task_feature_df.iloc[start_idx - n_codes * (max_window - 1): start_idx], phase_feature_df], ignore_index=True)

            dataset = self.dataset_dict[phase] = self.make_dataset(phase, phase_data, phase_feature_df, phase_label_df)
            prev_batch_sampler = self.batch_sampler_dict.get('online')
            batch_sampler = None
            droplast = self.drop_last or cfg.model_name == 'dva' or phase == 'online'
            if prev_batch_sampler is not None and 'da' in cfg.task_enum.value:
                if self.cum_adapt_count != 0:
                    adapted_size = self.cum_adapt_count * batch_size
                    dataset = KlineDataset(cfg, *dataset[adapted_size:], phase=phase)
            batch_sampler = self.batch_sampler_dict[phase] = RandomPairBatchSampler(cfg, dataset, droplast)
            self.next_adapt_count = batch_sampler.batch_count

            self.dataloader_dict[phase] = DataLoader(
                dataset, batch_sampler=batch_sampler,
                collate_fn=multi_collate_fn,
                pin_memory=self.pin_memory,
                num_workers=cfg.num_cpus, persistent_workers=True)


    def set_adapt_count(self):
        self.cum_adapt_count += self.next_adapt_count
        self.next_adapt_count = 0


    def make_dataset(self, phase: str, data: pd.DataFrame, feature_df: pd.DataFrame, label_df: pd.DataFrame):
        is_train = phase.startswith('train')
        cfg = self.cfg

        # 如果是EDA模式，直接返回数据集，不处理特征和标签
        if cfg.is_eda:
            return KlineDataset(cfg, data, feature_df, label_df, phase=phase)

        n_label = cfg.n_labels
        features = feature_df.values
        label = label_df.values
        n_feature_columns = cfg.n_all_features
        column_list = feature_df.columns.tolist()
        # dim_to_scale = 0
        # if dim_to_scale > 0 and (features[:, :dim_to_scale] == 0.0).any():
        #         print(f'Got zero in {phase} first {dim_to_scale} dim, when scaling')
        column_idx_to_scale = [column_list.index(col) for group in cfg.scale_group_list for col in cfg.column_group_dict[group]]

        if cfg.use_scaler or cfg.is_live_trading:
            # and not cfg.use_original:
            if cfg.scale_per_code and not cfg.load_all_codes:
                features = features.reshape(-1, cfg.n_codes * n_feature_columns)
                column_idx_to_scale = [i * n_feature_columns + idx for i in range(cfg.n_codes) for idx in column_idx_to_scale]
                feature_to_scale = features[:, column_idx_to_scale]
            else:
                features = features.reshape(-1, n_feature_columns)
                # feature_to_scale = feature_df[column_idx_to_scale].values
                feature_to_scale = features[:, column_idx_to_scale]
            if not cfg.feature_scaler_got_fit:
                if is_train:
                    print(f'Scaler fitting {phase} {feature_to_scale.shape = }')
                    cfg.feature_scaler.fit(feature_to_scale, cfg.norm_enum)
                    self.feature_scaler_got_fit = cfg.feature_scaler_got_fit = True
                    cfg.save_scaler(cfg.train_save_folder)
                else:
                    ckpt_folder = cfg.get_ckpt_folder(make_new_task_folder=not cfg.resume_from_ckpt)
                    cfg.load_scaler(ckpt_folder)
            if cfg.use_backbone:
                features[:, column_idx_to_scale] = np.nan_to_num(cfg.feature_scaler.transform(feature_to_scale), nan=0.0)
            feature_df = pd.DataFrame(features.reshape(-1, n_feature_columns), index=feature_df.index, columns=feature_df.columns)
            # feature_df.iloc[:] = features.reshape(-1, n_feature_columns)


        # if cfg.dataframe_type == 'polars':
        #     data = pl.from_pandas(data, include_index=True)
        dataset = KlineDataset(cfg, data, feature_df, label_df, phase=phase)
        return dataset


    def append_prediction(self):
        cfg = self.cfg
        if cfg.pretrain_front_end == 'Lightning':
            if cfg.model_name == 'dva':
                return
            trainer = cfg.get_trainer()
            predictor = cfg.get_predictor(self)
            for phase, dataloader in self.dataloader_dict.items():
                self.dataset_dict[phase].with_label = False
                if phase not in ('train_val', 'test'):
                    continue
                prediction_list = trainer.predict(predictor, dataloader)
                prediction_arr = np.squeeze(np.concatenate(prediction_list))
                # print(f'{prediction_arr.shape = }')
                self.dataset_dict[phase].prediction = prediction_arr
            print('prediction appended to dataset')
        else:
            pred_cfg = deepcopy(cfg)
            pred_cfg.seq_len = 1
            pred_cfg.task_enum = TaskType.ClassificationTrading
            predictor = pred_cfg.get_predictor()

            # train_str = 'train_val' if pred_cfg.concat_train_val else 'train'
            train_str = 'train'

            train_set = self.dataset_dict[train_str].make_entire_dataset(flatten1=True, cfg=pred_cfg)
            predictor.fit(*train_set)
            scaler = StandardScaler()
            scaled = False
            for phase in self.dataset_dict:
                if phase == train_str:
                    dataset = train_set
                else:
                    dataset = self.dataset_dict[phase].make_entire_dataset(flatten1=True, cfg=pred_cfg)
                if cfg.proba_as_prediction:
                    result = predictor.predict_proba(dataset[0])[:, 1: 2]
                else:
                    result = predictor.predict(dataset[0])
                result = result * 2 - 1
                # proba = predictor.predict_proba(dataset[0])[:, 1: 2] * 2 - 1
                # if not scaled:
                #     scaler.fit(proba)
                #     scaled = True
                # proba = scaler.transform(proba).flatten()
                self.dataset_dict[phase].prediction = result.reshape(-1, pred_cfg.n_codes).astype(np.float32)
                print(f'{phase} {result.shape = }\n{dataset[0].shape = }\n{dataset[1].shape = }')
                if phase == train_str:
                    continue
                prediction = predictor.predict(dataset[0])
                label_str = f'{predictor.__class__.__name__} pretrain cls_{self.num_classes} {phase}'
                label = dataset[1]
                report = classification_report(label, prediction, digits=4)
                cm = confusion_matrix(label, prediction)
                print(f'{label_str} classification report:\n{report}\n{cm}')


    def setup(self, stage=None):
        pass

    def train_dataloader(self):
        return self.dataloader_dict.train

    def val_dataloader(self):
        return self.dataloader_dict.val

    def test_dataloader(self):
        return self.dataloader_dict.test

    def predict_dataloader(self):
        return self.dataloader_dict.online


    def report_occurrance(self):
        cfg = self.cfg
        interval = cfg.interval_cfg.base
        print(f'interval: {interval}')
        for name, dataset in self.dataset_dict.items():
            label = []
            for i in range(len(dataset)):
                label.append((dataset[i][1] > 0).astype(int))
                # if not isinstance(y, (int)):
                #     print(f'{i}th label: {y}')
            label = np.array(label).flatten()
            occurrance_dict = dd()
            for i in range(cfg.num_classes):
                occurrance_dict[i] = [np.sum(label == i), round(np.sum(label == i) / len(label), 4)]
            print(f'\n{name} dataset occurrance:')
            pprint(occurrance_dict)



if __name__ == "__main__":
    cfg = PredictorConfig()
    # cfg.is_regression = False
    # cfg.num_classes = 2
    cfg.n_codes = 20
    # cfg.zigzag_labelling.in_use = True
    # cfg.price_change_threshold = 0.01
    cfg.interval_cfg.base = 240
    # cfg.step_size.inner = 4
    cfg.pred_len = 1
    # cfg.augment_data.train = True
    # cfg.augment_data.val = True
    cfg.sample_pair = True
    cfg.rolling_retraining.in_use = True
    # cfg.train_end_date = None

    dm = KlineDataModule(cfg)


    if cfg.rolling_retraining.in_use:
        for dataloader_dict in dm:
            print(f'{dm.iter_count = }')
            for data in dataloader_dict.train:
                print(f'{data = }')
                continue
    label = []
    train_set = dm.dataset_dict.train
    for batch in dm.dataloader_dict['train']:
        # print(f'{batch = }')
        continue
    interval = cfg.interval_cfg.base
    print(f'{cfg.code_list = }\ninterval: {interval}')
