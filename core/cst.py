from enum import Enum, auto
import torch
from utils.adan import Adan


class TaskPhase(Enum):
    TRAINING = 'training'
    VALIDATION_EPOCH = "validation-epoch-last"  # final validation
    VALIDATION_MODEL = "validation-model"       # intermediate validation
    TESTING = "testing"


class Optimizers(Enum):
    SGD = torch.optim.SGD
    ADAM = torch.optim.Adam
    ADAMAX = torch.optim.Adamax
    ADAMW = torch.optim.AdamW
    RMSPROP = torch.optim.RMSprop
    ADAN = Adan


class TaskType(Enum):
    # PreTraining = 'pre'
    Classification = 'clf'
    ClassificationTrading = 'clft'
    Regression = 'rgs'
    RegressionTrading = 'rgst'
    DirectTrading = 'drt'
    DirectTradingDoubleAdapt = 'drtda'
    Portfolio = 'pfl'
    PortfolioDoubleAdapt = 'pflda'
    MarketMaking = 'mkm'
    MarketMakingPortfolio = 'mkmpfl'
    MarketMakingDoubleAdapt = 'mkmda'
    GridTrading = 'grid'
    GridTradingDoubleAdapt = 'gridda'
    CTAStratege = 'cta'
    Rank = 'rank'


class LabelType(Enum):
    PercentChange = 'pct'
    LogReturn = 'log'
    RawNormalized = 'raw'
    ShiftEmaPercentChange = 'sepct'


class Objective(Enum):
    MSE ='mse'
    BCE = 'bce'
    CE = 'ce'    
    IC = 'ic'
    PNL = 'pnl'
    Sharpe = 'sharpe'
    Sortino ='sortino'
    Oracle = 'oracle'


class AllocationType(Enum):
    Equal = 'equal'
    Sharpe = 'sharpe'
    Sortino ='sortino'
    MinVar ='min_var'
    MaxReturn = 'max_return'


class PickUpStrategy(Enum):
    NaiveNext = 'nvn'
    NaiveGap = 'nvg'
    RollingNext = 'rln'
    RollingGap = 'rlg'
    RankNext = 'rkn'
    RankGap = 'rkg'


class PositionType(Enum):
    Long = 'long'
    Short ='short'
    Both = 'both'
    Hedge = 'hedge'


class ActionType(Enum):
    LongShort = 'long_short'
    Momentum = 'momentum'


class TopkType(Enum):
    Abs = 'abs'
    PositionVote = 'position_vote'
    SignVote = 'sign_vote'
    Hedge = 'hedge'
    EWM_PNL = 'ewm_pnl'
    ABS_EWM_PNL = 'abs_ewm_pnl'


class FC_Type(Enum):
    MLP = 'mlp'
    KAN = 'kan'
    TabM = 'tabm'
    VBLL = 'vbll'
    VMLP = 'vmlp'


class EmbeddingOperation(Enum):    
    Add = 'add'
    Attention = 'attention'
    Concat = 'concat'


class BarType(Enum):
    Time = 'time'
    Count = 'count'
    Volume = 'volume'
    Quote = 'quote'
    Move = 'move'
    QuoteMove = 'quote_move'
    Range = 'range'
    QuoteRange = 'quote_range'


class SegmentType(Enum):
    BarCount = 'bar_count'
    Reversal = 'reversal'
    Barrier = 'barrier'
    Indicator = 'indicator'


class AssetType(Enum):
    Crypto = 'crypto'
    Stock = 'stock'
    Forex = 'forex'


class NormType(Enum):
    MeanStd = 'mean_std'
    StdOnly = 'std_only'
    StdOnAbs = 'std_on_abs'
    StdOnSides = 'std_on_sides'


class DatabaseType(Enum):
    Arctic = 'arctic'
    Dolphin = 'dolphin'
    Feather = 'feather'
    

class EntryExitLogitsEnum(Enum):
    GUMBEL_SOFTMAX = auto()  # 使用Gumbel-Softmax近似
    STE = auto()  # 使用Straight-Through Estimator
    
class VizType(Enum):
    HTML = 'html'
    PLT = 'plt'