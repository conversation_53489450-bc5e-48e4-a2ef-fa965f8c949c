# import gc
# import types
from copy import deepcopy
# from dataclasses import dataclass



class DotDict(dict):

	"""dot.notation access to dictionary attributes"""

	# __getattr__ = dict.get  may get None value from existing __like__ key
	__setattr__ = dict.__setitem__
	__delattr__ = dict.__delitem__
	

	def __init__(self, *args, **kwargs):

		super(DotDict, self).__init__(*args, **kwargs)

		for key, value in self.items():

			if isinstance(value, dict):

				self[key] = DotDict(value)

	# special for deepcopy

	def __deepcopy__(self, memo):

		# result = DotDict()

		# memo[id(self)] = result

		# for key, value in self.items():

		# 	result[deepcopy(key, memo)] = deepcopy(value, memo)
		# return result

		return DotDict(deepcopy(dict(self), memo=memo))
	

	# avoid to get None value from existing __like__ key

	def __getattr__(self, key):

		if key.startswith('__') and key.endswith('__'):
			return super().__getattr__(key)

		return self.get(key, None)
	

	def get_index_item(self, options_index=None, index_name=None):

		options = self.get('options', ())

		if index_name is None:
			if options_index is None:

				index = self.get('index')	
			else:
				index = options_index

		else:

			index = self.get(index_name)

		if len(options) > 0 and isinstance(index, int):

			return options[index]



# @dataclass

class MyDict(dict):
	__setattr__ = dict.__setitem__
	__delattr__ = dict.__delitem__

	name = "MyDict"

	hidden = "Hidden"

	# def __init__(self, **kwargs):

	# 	super().__init__(kwargs)

	# 	for key, value in MyDict.__dict__.items():

	# 		if not (key.startswith('__') and key.endswith('__')):

	# 			self[key] = value
	

	def __getattribute__(self, key):

		if key.startswith('__') and key.endswith('__'):

			return super(MyDict, self).__getattribute__(key)
		return super().get(key) or type(self).__dict__.get(key)
	

	# def __getattr__(self, key):

	# 	if key.startswith('__') and key.endswith('__'):

	# 		return super(MyDict, self).__getattr__(key)
	# 	return self.__getitem__(key) or self.__dict__.__getitem__(key)
	

	# def __getitem__(self, key):

	# 	print(f"{key = }")

	# 	super(MyDict, self).__getitem__(key)
		
		

	# @classmethod

	# def set_cls_item(cls, key, value):

	# 	gc.get_referents(cls.__dict__)[0][key] = value

		# cls.__dict__[key] = value
		

	# def __setattr__(self, key, value):

	# 	print(f"{self.__dict__ = }\n{self.__dir__() = }\n{MyDict.__dict__ = }\n{MyDict.__dict__.__dir__() = }")

	# 	if key in MyDict.__dict__:

	# 		MyDict.set_cls_item(key, value)

	# 	else:

	# 		self.__setitem__(key, value)

	

if __name__ == "__main__":

	dd = MyDict(

		a = 0,

		b = dict()
	)

	# dd.n = 1

	dd.name = 'my_dict'

	print(f"{dd.name = }\n{dd.hidden = }\n{dd.random = }\n{dd = }\n{dd.__dict__ = }\n{type(dd).__dict__ = }")
	# print(type(dd).__dict__)
	# print(dd.__dict__)