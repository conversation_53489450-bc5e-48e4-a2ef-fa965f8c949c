import os
from lightning import LightningModule
import torch
from core.data_module import KlineDataModule
from core.predictor_config import PredictorConfig



def fit_val_test(cfg: PredictorConfig):
    
    history_kline_dict = cfg.get_history_kline_dict()
    local_vars = {}
    task_str = cfg.task_enum.value    
    class_name = f'Lightning{task_str.upper()}'
    exec(f'from models.lightning_{task_str} import {class_name}', globals(), local_vars)
    LightningClass: LightningModule = local_vars[class_name]
    # LightningClass = locals()[class_name] # buggy
    # pred_cfg.resume_from_ckpt = True # buggy
    trainer = cfg.get_trainer()
    train_save_folder = cfg.train_save_folder = cfg.get_ckpt_folder(make_new_task_folder=True)
    val_load_folder = train_load_folder = cfg.get_ckpt_folder(make_new_task_folder=False)
    data_module = KlineDataModule(cfg, history_kline_dict)
    # next_timestamp = data_module.get_next_open_timestamp()
    # print(f'{next_timestamp = }')    
    if cfg.model_name == 'koopa' and cfg.execute_phase.train:
        from models.koopa.model import get_mask_spectrum
        cfg.koopa_cfg.mask_spectrum = get_mask_spectrum(cfg.koopa_cfg.alpha, data_module.dataloader_dict.train)
    if cfg.execute_phase.train:
        # ! init data_module first to set cfg.n_seq_features
        
        # print(cfg.code_list)
        
        val_dataloader = data_module.dataloader_dict.val if cfg.execute_phase.val else None
        resume_path = None
        if cfg.resume_from_ckpt:
            ckpt_file_name = cfg.ckpt_file_name or cfg.get_last_ckpt_file_name()
            print(f'{ckpt_file_name = }')
            
            resume_path = os.path.join(train_load_folder, ckpt_file_name)
            lm = LightningClass.load_from_checkpoint(resume_path, cfg=cfg)
            # cfg.datetime_str = cfg.datetime_str
            # cfg.train_save_folder = train_save_folder
            if cfg.fine_tune:
                lm.ckpt_folder = cfg.train_save_folder.replace('train', 'val_before_fine_tune')
                cfg.fine_tune_idx_filter = None
                if cfg.freeze_parameters:
                    lm.freeze_parameters()
                trainer.validate(lm, dataloaders=data_module.dataloader_dict.train)
                lm.ckpt_folder = cfg.train_save_folder = train_save_folder.replace('train', 'fine_tune')
                data_module.set_dataloaders(selected_phase='train')
            else:
                lm.ckpt_folder = train_save_folder
            # if cfg.optimize_allocation:
            #     lm.freeze_parameters()
        else:
            if not os.path.exists(train_save_folder):
                os.makedirs(train_save_folder, exist_ok=True)
            val_load_folder = train_save_folder
            lm = LightningClass(cfg).to(cfg.device)
            # torch.compile(lm)
        print(lm.model)
        
        trainer.fit(lm, train_dataloaders=data_module.dataloader_dict.train, val_dataloaders=val_dataloader)
        # trainer.fit(lm, train_dataloaders=data_module.dataloader_dict.train, val_dataloaders=val_dataloader, ckpt_path=resume_path)
        

    ckpt_file_name = cfg.ckpt_file_name or cfg.get_last_ckpt_file_name()
    print(f'{ckpt_file_name = }')
    ckpt_path = os.path.join(val_load_folder, ckpt_file_name)
    # lm.task_phase = cst.TaskPhase.VALIDATION_MODEL
    # trainer.test(lm, dataloaders=data_module.val_dataloader(), ckpt_path="best")
    # lm.task_phase = cst.TaskPhase.TESTING
    # trainer.test(lm, dataloaders=data_module.test_dataloader(), ckpt_path="best")
    if cfg.for_deployment and cfg.execute_phase.train:
        return

    
    lmc = LightningClass.load_from_checkpoint(ckpt_path, cfg=cfg)
    datetime_str = cfg.datetime_str
    topk_enum = cfg.topk_enum
    group_by_str = cfg.group_by_str
    range_to_mask = cfg.range_to_mask
    cfg: PredictorConfig = lmc.hparams.cfg
    if not cfg.execute_phase.train:
        
        cfg.topk_enum = topk_enum
        # pred_cfg.adapt_distr_shift.inverse_coef = -0.1
        # pred_cfg.adapt_distr_shift.val = True
        # pred_cfg.adapt_distr_shift.test = True  
        cfg.datetime_str = datetime_str    
        cfg.ckpt_file_name = ckpt_file_name
        cfg.group_by_str = group_by_str
        cfg.range_to_mask = range_to_mask
        # lmc.cfg = pred_cfg
        lmc.ckpt_folder = cfg.train_save_folder
    
    # lmc.date_dict = data_module.date_dict
    # lmc = LightningClass.load_from_checkpoint(ckpt_path, cfg=pred_cfg)
    if cfg.execute_phase.val:
        trainer.validate(lmc, dataloaders=data_module.val_dataloader())
    if cfg.execute_phase.test:
        trainer.test(lmc, dataloaders=data_module.test_dataloader())
