from math import floor
from typing import Callable, Iterable, <PERSON><PERSON>
import numpy as np
import pandas as pd
import polars as pl
from torch import tensor
import torch
from torch.utils.data import Dataset
from core.cst import LabelType, TaskType
from layers.vmd import VMD
from core.predictor_config import PredictorConfig
from utils.zigzag import get_label_dict
from core.dot_dict import DotDict as dd
from simdkalman import KalmanFilter

class KlineDataset(Dataset):
    def __init__(self, cfg: PredictorConfig, data: pd.DataFrame | pl.DataFrame, feature_df: pd.DataFrame, label_df: pd.DataFrame, prediction: np.ndarray = None, linear_law_features: np.ndarray = None, phase: str = 'train', cycle_mask_idx: int = None, start_date: str = None, end_date: str = None):
        self.cfg = cfg
        self.n_codes = cfg.n_codes
        self.seq_len = cfg.seq_len
        self.pred_len = cfg.pred_len
        self.inner_step = cfg.inner_step
        self.label_step = cfg.label_step[phase]
        self.feature_len = self.inner_step * self.seq_len
        self.label_len = self.label_step * self.pred_len
        self.column_group_dict = cfg.column_group_dict

        # 如果是EDA模式，简化处理
        if cfg.is_eda:
            self.data = data
            self.feature_df = feature_df
            self.label_df = label_df
            self.feature_df_dict = dd()
            # 设置其他必要的属性，避免后续代码出错
            self.cache = dd(
                label={},
                original={},
                vmd={},
                kalman={},
                image={},
            )
            self.use_polars = isinstance(data, pl.DataFrame)
            if self.use_polars:
                self.datetime_index = data['open_time'].to_pandas() if 'open_time' in data.columns else pd.DatetimeIndex([])
            else:
                if isinstance(data.index, pd.MultiIndex) and 'open_time' in data.index.names:
                    self.datetime_index = data.index.get_level_values('open_time')
                elif 'open_time' in data.columns:
                    self.datetime_index = pd.DatetimeIndex(data['open_time'])
                else:
                    self.datetime_index = pd.DatetimeIndex([])
            self.temporal_onehots = datetime_index_to_onehot(self.datetime_index) if len(self.datetime_index) > 0 else np.array([])
            self.is_training = 'train' in phase
            self.phase = phase
            return
        else:
            self.feature_df_dict = dd({
                group_name: feature_df[col_list] for group_name, col_list in self.column_group_dict.items() if cfg.feature_cfg[group_name]
            })
            self.data = data
            self.feature_df = feature_df
            self.label_df = label_df
        self.cache = dd(
            label={},
            original={},
            vmd={},
            kalman={},
            image={},
        )
        if cfg.feature_cfg.kalman_dim > 0 or cfg.feature_cfg.kalman:
            self.kf = KalmanFilter(
                state_transition = np.array([[1,1],[0,1]]),
                process_noise = np.diag([0.1, 0.01]),
                observation_model = np.array([[1,0]]),
                observation_noise = 1.0
            )
            cfg.feature_cfg.kalman_dim = cfg.feature_cfg.original_dim

        if phase == 'train':
            self.label_df = cfg.label_scaler.transform(self.label_df)
        if cfg.num_classes == 2 and cfg.zigzag_labelling.in_use:
            self.zigzag_label_dict = get_label_dict(data['close'].to_numpy().flatten(), cfg.zigzag_labelling.up_pct)
            self.zigzag_label_type = cfg.zigzag_labelling.label_type
            self.zigzag_label = self.zigzag_label_dict[self.zigzag_label_type]


        # self.onehot_matrix = np.eye(cfg.n_codes)

        self.prediction = prediction
        self.phase = phase
        self.with_label = (phase != 'predict') or cfg.is_backtesting_history
        self.head_cutoff_len = (self.seq_len - 1) * self.inner_step
        self.cutoff_len = self.head_cutoff_len + self.with_label * self.label_len
        self.linear_law_features = linear_law_features
        self.use_polars = isinstance(data, pl.DataFrame)
        if self.use_polars:
            # print(f'{data.head() = }\n{data.columns = }')
            self.datetime_index = data['open_time'].to_pandas()
        else:
            self.datetime_index = data.index.get_level_values('open_time')
        self.datetime_index = pd.DatetimeIndex(self.datetime_index)
        # self.data_stamp = time_features(self.datetime_index)
        self.temporal_onehots = datetime_index_to_onehot(self.datetime_index)

        self.is_training = 'train' in phase

        self.make_linear_law_transformations()


        if 'pfl' not in cfg.task_enum.value:
            multi_index_list = data.index.names
            self.features = feature_df.sort_index(level=['code', 'open_time']).values#.copy()
            original_dim = cfg.feature_cfg.original_dim
            self.feature_not_scaled = self.features[:, :original_dim]
            if cfg.label.csnorm and self.phase == 'train':
                label_cols = label_df.columns.tolist()
                label_df[label_cols[0]] = label_df[label_cols[0]].groupby('open_time', group_keys=False).apply(lambda x: (x - x.mean()) / x.std())
            self.label = label_df.sort_index(level=['code', 'open_time']).values
            self.total_data_count, self.n_all_feature = self.features.shape
            # self.code_list = sort_code_df.index.get_level_values('code').unique().tolist()

            # print(f'{sort_code_df.tail() = }')
            # if cfg.load_all_codes:
            #     quote_quantile = cfg.quote_quantile
            # else:
            #     quote_quantile = 1.0
            # if cycle_mask_idx is None:
            #     cycle_mask_idx = cfg.cycle_mask_idx
            rolling_len = cfg.rolling_window_list[-1] ** cfg.feature_cfg.rolling - 1
            if cfg.feature_cfg.image or cfg.feature_cfg.macd:
                macd = cfg.ta_cfg
                rolling_len = max(rolling_len, macd.slow + macd.dea - 2)
            cross_len = 2 * cfg.feature_cfg.cross * cfg.inner_step - 1
            inner_len = cfg.feature_cfg.inner * cfg.inner_step - 1
            head_cutoff_len = self.feature_len
            if not cfg.drop_null_rows:
                head_cutoff_len += max(rolling_len, cross_len, inner_len)
            elif cfg.rolling_zscore:
                head_cutoff_len += self.seq_len
            tail_cutoff_len = self.label_len if phase != 'online' else 0
            # mask_zero_quote_code = cfg.mask_zero_quote_code and phase != 'online'
            code_group_df = feature_df.groupby('code', group_keys=False).apply(lambda x: set_valid(x, head_cutoff_len, tail_cutoff_len, cfg, phase)).reset_index().set_index(multi_index_list).sort_index(level=['code', 'open_time'])

            valid_idx_arr = np.arange(len(code_group_df))
            code_group_df['valid_idx'] = valid_idx_arr

            valid_df = code_group_df[code_group_df.is_valid == 1].sort_index(level=['open_time', 'code'])

            if cfg.fine_tune_idx_filter is not None and cfg.fine_tune and phase == 'train':
                print(f'{len(valid_df) = }\n{len(cfg.fine_tune_idx_filter) = }')
                valid_df = valid_df[cfg.fine_tune_idx_filter]
            self.indices_mapping_arr = valid_df.valid_idx.values.astype(np.int32)

            group_size_arr = valid_df.groupby('bar_idx').size().values
            end_idx_arr = group_size_arr.cumsum()
            start_idx_arr = end_idx_arr - group_size_arr
            indices = np.arange(end_idx_arr[-1])
            self.indices_list = [indices[start_idx: end_idx].tolist() for start_idx, end_idx in zip(start_idx_arr, end_idx_arr)]
            # print(f"{sort_code_df['valid_idx'].tail(10) = }")
            print(f"{self.phase} {self.indices_mapping_arr.shape = }")
            print(f'{self.features.shape = }')
            self.seq_features = self.features[:, cfg.seq_column_idx_list]
            self.category_features = self.features[:, cfg.category_column_idx_list]
            self.image_features = self.features[:, cfg.image_column_idx_list]
            self.image_features_list = [self.image_features]
            if cfg.feature_cfg.original_with_btc and cfg.image_cfg.with_btc:
                image_feature_dim = self.image_features.shape[-1] // 2
                btc_image_features = self.image_features[:, image_feature_dim:]
                self.image_features = self.image_features[:, :image_feature_dim]
                self.image_features_list.append(btc_image_features)
            step_list = cfg.step_list
            self.image_feature_len = self.image_features.shape[-1] // len(step_list)
            self.ohlc_height, self.volume_height, self.macd_height, self.rsi_height, self.image_step, self.image_channel, self.height, self.width = cfg.get_image_shape()
            self.time_idx = np.arange(self.seq_len, dtype=np.int32)
            self.image_idx = np.arange(self.seq_len * self.image_step, dtype=np.int32)
            if self.phase in ['val', 'test'] or cfg.fine_tune:
                group_count = len(valid_df.index.get_level_values('bar_idx').unique())
                # print(f'{valid_df.index[:10] = }')
                # print(f'{valid_df.tail() = }')
                print(f'{group_count = }')
                cfg.valid_index_dict[self.phase] = valid_df.index

                # self.set_group_dict()
                ...
        else:
            outer_step = self.outer_step = cfg.outer_step
            self.features = feature_df.values.reshape(-1, self.n_codes, cfg.n_all_features)
            self.label = label_df.values.reshape(-1, self.n_codes, cfg.n_labels)
            self.total_data_count, _, self.n_all_feature = self.features.shape

            self.seq_data_count = len(self.features) - self.cutoff_len
            self.seq_step_count = self.seq_data_count // outer_step + int(self.seq_data_count % outer_step != 0)



    def __getitem__(self, index: int | Iterable[int] | slice, cfg: PredictorConfig = None) -> tuple:
        if cfg is None:
            cfg = self.cfg

        # 如果是EDA模式，返回简化的数据
        if cfg.is_eda:
            # 返回空数据，保持接口一致
            empty_x = np.zeros((self.seq_len, cfg.n_all_features), dtype=np.float32)
            empty_y = np.zeros((self.pred_len, cfg.n_labels), dtype=np.float32)
            empty_z = np.zeros(1, dtype=np.float32)
            empty_category = np.zeros(1, dtype=np.float32)
            empty_image = np.zeros((1, 1, 1), dtype=np.float32)
            return empty_x, empty_y, empty_z, empty_category, empty_image

        n_codes = cfg.n_codes

        if cfg.task_enum in [TaskType.DirectTrading, TaskType.MarketMaking, TaskType.GridTrading]:
            x_end = self.indices_mapping_arr[index]
            x_start = x_end - self.head_cutoff_len
            x = self.seq_features[x_start : x_end + 1: self.inner_step]
            if self.phase == 'online':
                y = np.zeros(self.pred_len, dtype=np.float32)
            else:
                y = self.label[x_end + self.label_step: x_end + self.label_len + 1: self.label_step]
                if cfg.label_enum == LabelType.RawNormalized:
                    if index not in self.cache.label:
                        y = y.copy()
                        prev_close = self.label[x_end, 0]
                        y = y / prev_close - 1
                        self.cache.label[index] = y
                    else:
                        y = self.cache.label[index]
            z = np.zeros(2, dtype=np.float32)
            category = self.category_features[x_end]

            if cfg.feature_cfg.image:
                image_cfg = cfg.image_cfg
                if index not in self.cache.image:
                    code_image = []
                    for image_features in self.image_features_list:
                        step_image = []
                        for i, n_step in enumerate(cfg.step_list):
                            image_start = x_end - n_step * (self.seq_len - 1)
                            feature_start = i * self.image_feature_len
                            feature_end = (i + 1) * self.image_feature_len
                            image_data = image_features[image_start : x_end + 1: n_step, feature_start : feature_end]
                            # if i > 0:
                            #     print(f'{image_data.shape = }')
                            image_i = self.make_image(image_data, image_cfg)
                            if image_cfg.plot:
                                plot_ndarray(image_i)
                            step_image.append(image_i)
                        # step_image = np.concatenate(step_image, axis=0)
                        code_image.extend(step_image)
                    image = np.concatenate(code_image, axis=0)
                    if cfg.inverse_image:
                        image = image[..., ::-1].copy()
                    self.cache.image[index] = image
                else:
                    image = self.cache.image[index]
            else:
                image = z

            if cfg.feature_cfg.original:
                x = x.copy() # ! Do not modify original data
                original_dim = cfg.feature_cfg.original_dim
                if index not in self.cache.original:
                    original_arr = x[:, :original_dim].copy()
                    if cfg.feature_cfg.normalize_original:
                        group_size = cfg.feature_cfg.original_group_size
                        n_group = original_dim // group_size
                        for i in range(n_group):
                            latest_denorminator = original_arr[-1, (i + 1) * group_size - 1]
                            original_arr[:, i * group_size: (i + 1) * group_size] = original_arr[:, i * group_size: (i + 1) * group_size] / latest_denorminator - 1
                            # if i % 2 != 0:
                            #     x[:, i * 4: (i + 1) * 4] = np.log(np.abs(x[:, i * 4: (i + 1) * 4]) + 1)
                            if latest_denorminator <= 0:
                                print(f'{self.feature_not_scaled.min() = }')
                                print(f'{latest_denorminator = }')
                    else:
                        mean = original_arr.mean()
                        std = original_arr.std()
                        original_arr = (original_arr - mean) / std
                        self.cache.original[index] = original_arr
                else:
                    original_arr = self.cache.original[index]

                x[:, :original_dim] = original_arr

                vmd_dim = cfg.feature_cfg.vmd_dim
                if vmd_dim > 0:
                    if index not in self.cache.vmd:
                        vmd = VMD(x[:, original_dim - 1], K=vmd_dim)[0].reshape(-1, vmd_dim).astype(np.float32)
                        self.cache.vmd[index] = vmd
                    else:
                        vmd = self.cache.vmd[index]
                    x = np.concatenate([x, vmd], axis=1)
                kalman_dim = cfg.feature_cfg.kalman_dim
                if kalman_dim > 0:
                    if index not in self.cache.kalman:
                        kalman = self.kf.smooth(original_arr.copy().T).observations.mean.T.astype(np.float32)
                        self.cache.kalman[index] = kalman
                    else:
                        kalman = self.cache.kalman[index]
                    x = np.concatenate([x, kalman], axis=1)

            if cfg.inverse_seq:
                x = x[..., ::-1].copy()

            try:
                assert x.shape[0] == self.seq_len, f'{x.shape[0] = } != {self.seq_len = }'
                assert y.shape[0] == self.pred_len, f'{y.shape[0] = } != {self.pred_len = }'

            except Exception as e:
                print(f'{e}\n{x_start = }, {x_end = }, {len(self.features) = }')
                ...
            return x, y, z, category, image

        outer_step = self.outer_step
        if isinstance(index, int):
            z = np.zeros(cfg.n_codes, dtype=np.float32) # Do not set to None
            feature_indices = []
            if cfg.feature_cfg.original:
                feature_indices += cfg.column_group_dict.original
            if cfg.feature_cfg.rolling:
                feature_indices += cfg.column_group_dict.rolling
            if cfg.feature_cfg.cross:
                feature_indices += cfg.column_group_dict.cross
            if cfg.task_enum in [TaskType.DirectTrading, TaskType.DirectTradingDoubleAdapt, TaskType.ClassificationTrading, TaskType.Rank] and not cfg.stack_multi_codes_on_slicing:
                # code_index = index // self.seq_step_count
                # seq_index = index % self.seq_step_count

                code_index = index % n_codes
                seq_index = index // n_codes
                x_start = outer_step * seq_index
                x_end = x_start + self.feature_len
                category = self.temporal_onehots[x_end]
                try:
                    x = self.features[x_start: x_end, code_index, feature_indices]
                    y = self.label[x_end: x_end + self.label_len, code_index]
                    if cfg.feature_cfg.original and cfg.feature_cfg.normalize_original:
                        original_dim = cfg.feature_cfg.original_dim
                        latest_close = self.features[x_end - 1, code_index, 3]
                        x[..., :4] = x[..., :4] / latest_close - 1
                        latest_quote = self.features[x_end - 1, code_index, original_dim - 1]
                        x[..., 4: original_dim] = x[..., 4: original_dim] / latest_quote - 1
                    if cfg.label_enum == LabelType.RawNormalized:
                        prev_close = self.label[x_end - 1, code_index, 0]
                        y = y / prev_close - 1
                    if self.inner_step > 1:
                        x = x[self.inner_step - 1::self.inner_step]
                        y = y[self.inner_step - 1::self.inner_step]
                    if self.prediction is not None:
                        z = self.prediction[x_start: x_start + 1, code_index]

                    # if np.isnan(x).any():
                    #     print(f'{np.isnan(x).astype(int).sum() = }\n{x = }')
                    if cfg.feature_cfg.original:
                        # ft_len = x.shape[-1]
                        # x[..., :4] = x[..., :4] / x[-1:, :4].repeat(self.seq_len, axis=0)
                        # x[..., 4:8] = x[..., 4:8] / np.broadcast_to(x[-1:, 7:8], [*x.shape[:-1], 4])
                        # x[..., 8:] = x[..., 8:] / np.broadcast_to(x[-1:, -1:], [*x.shape[:-1], 4])
                        x[x == np.nan] = 0
                        x[x == np.inf] = 0
                        x[x == -np.inf] = 0
                    try:
                        assert x.shape[0] == self.feature_len, f'{x.shape[0] = }!= {self.feature_len = }'
                        assert y.shape[0] == self.label_len, f'{y.shape[0] = }!= {self.label_len = }'

                    except Exception as e:
                        print(f'{e}\n{x_start = }, {x_end = }, {len(self.features) = }')
                        ...
                    return x, y, z, category
                except Exception as e:
                    print(f'{e}\n{code_index = }, {x_start = }, {x_end = }, {len(self.features) = }')
                    return None, None, z, category

            if not cfg.overlapping_on_slicing:
                index = index * cfg.seq_len

            x_start = index * outer_step
            x_end = x_start + cfg.seq_len
            x_slc = slice(x_start, x_end)
            y_end = x_end + self.label_len
            y_start = x_end if cfg.pred_multi_step else y_end - 1


            x = self.get_predicting_feature(x_slc, feature_indices)

            if self.prediction is not None:
                z = self.prediction[index: index + 1]

            category = self.temporal_onehots[x_end]

            if not self.with_label:
                return x, None, z
            label_dim = 1 + 2 * (cfg.task_enum  in [TaskType.Portfolio, TaskType.PortfolioDoubleAdapt, TaskType.MarketMakingPortfolio, TaskType.GridTrading])
            if y_end > len(self.label):
                # print(f'{y_end = } > {len(self.label)}')
                label = np.zeros((self.label_len, cfg.n_codes, label_dim))
            else:
                label = self.label[y_start: y_end, :, :label_dim] # label_len, n_codes, 1 or 3
            if cfg.label_enum == LabelType.RawNormalized:
                prev_close = self.label[x_end - 1, code_index, 0]
                y = y / prev_close - 1
            if self.inner_step > 1:
                label = label[self.inner_step - 1::self.inner_step]

            if cfg.task_enum in [TaskType.Regression, TaskType.Portfolio, TaskType.PortfolioDoubleAdapt, TaskType.MarketMakingPortfolio, TaskType.GridTrading]:
                y = label

            elif cfg.task_enum == TaskType.Classification:
                cum_label = label.cumsum(axis=0)
                if cfg.num_classes == 2:
                    if cfg.zigzag_labelling.in_use:
                        y = self.zigzag_label[x_end]
                    else:
                        y = np.where(cum_label[-1] > 0, 1, 0)

                elif cfg.num_classes == 3:
                    class_list = [0, 2, 1]
                    thr = cfg.price_change_threshold
                    if cfg.use_triple_barrier:
                        lt = (cum_label < -thr).any()
                        gt = (cum_label > thr).any()
                        gt_idx = ((cum_label > thr).astype(int)).argmax()
                        lt_idx = ((cum_label < -thr).astype(int)).argmax()
                        lt_first = (lt_idx < gt_idx)
                        gt_first = (gt_idx < lt_idx)
                        cond_list = [
                            lt and (not gt or lt_first),
                            gt and (not lt or gt_first),
                        ]
                        # y = np.select(cond_list, class_list[:-1], default=class_list[-1])

                    else:
                        lt = (cum_label < -thr)
                        gt = (cum_label > thr)
                        cond_list = [lt, gt]

                    y = np.select(cond_list, class_list[:-1], default=class_list[-1])

                else:
                    raise ValueError(f'Invalid num_classes: {cfg.num_classes}')

                x = x.reshape(-1, x.shape[-1])
                y = y.reshape(-1, y.shape[-1])
                if x.shape[0] != y.shape[0]:
                    print(f'{x.shape = }, {y.shape = }')

            if cfg.shuffling.train and cfg.shuffling.codewise and self.phase == 'train':
                shuffled_idx = np.random.permutation(cfg.n_codes)
                x = x[:, shuffled_idx, :]
                y = y[:, shuffled_idx, :]
                z = z[shuffled_idx]
                category = category[shuffled_idx]
            if cfg.model_name == 'dva':
                return x, y, *self.get_mark(index)
            if cfg.feature_cfg.original:
                # ft_len = x.shape[-1]
                # x[..., :4] = x[..., :4] / x[-1:, :, :4].repeat(self.seq_len, axis=0)
                # x[..., 4:8] = x[..., 4:8] / np.broadcast_to(x[-1:, :, 7:8], [*x.shape[:-1], 4])
                # x[..., 8:] = x[..., 8:] / np.broadcast_to(x[-1:, :, -1:], [*x.shape[:-1], 4])
                x[x == np.nan] = 0
                x[x == np.inf] = 0
                x[x == -np.inf] = 0
            return x, y, z, category

        elif isinstance(index, Iterable):
            return [self.__getitem__(i, cfg=cfg) for i in index]

        elif isinstance(index, slice):
            start, end = index.start, index.stop
            if start is None:
                start = 0
            if end is None:
                end = len(self)
            n_codes = self.n_codes
            df_slc = slice(start * n_codes, (end + self.head_cutoff_len) * n_codes)
            arr_slc = slice(start, end + self.head_cutoff_len)
            data = self.data[df_slc]
            feature_df = self.feature_df[df_slc]
            features = self.features[arr_slc]
            label = self.label[arr_slc]
            assert len(data) == len(feature_df)
            assert len(features) == len(label)
            return data, feature_df, features, label

    def make_image(self, image_data: np.ndarray, image_cfg) -> np.ndarray:
        image = np.zeros([self.ohlc_height, self.width], dtype=np.float32)
        if image_cfg.ema:
            ohlc = image_data[:, :6]
        else:
            ohlc = image_data[:, :4]

        high = ohlc.max()
        low = ohlc.min()
        # 防止除零错误
        if high == low:
            high += 1e-7

        # 计算价格对应的像素位置 (归一化到height范围内)
        ohlc_idx = (self.ohlc_height * (high - ohlc) / (high - low)).clip(0, self.ohlc_height - 1).astype(np.int32)
        open_idx = ohlc_idx[:, 0]
        high_idx = ohlc_idx[:, 1]
        low_idx = ohlc_idx[:, 2]
        close_idx = ohlc_idx[:, 3]

        sign = image_data[:, 3] >= image_data[:, 0]
        sign = (sign * 2 - 1) ** image_cfg.sign
        sign = sign.astype(np.float32)

        # 使用广播和布尔掩码标记最高价到最低价之间的线段
        ohlc_coords = np.arange(self.ohlc_height)[:, None]  # shape: (ohlc_height, 1)
        hl_min_idx = np.minimum(high_idx, low_idx)     # shape: (time,)
        hl_max_idx = np.maximum(high_idx, low_idx)     # shape: (time,)

        # 创建布尔掩码,标记所有在high和low之间的位置
        hl_mask = ((ohlc_coords >= hl_min_idx) & (ohlc_coords <= hl_max_idx)) * sign  # shape: (ohlc_height, time)
        if self.image_step == 3:
        # 使用高级索引分别标记开盘和收盘位置
            image[open_idx, self.time_idx * self.image_step] = sign  # 开盘价位置
            image[close_idx, 2 + self.time_idx * self.image_step] = sign  # 收盘价位置
            image[:, 1::self.image_step] = hl_mask
        elif self.image_step == 2:
            image[close_idx, 1 + self.time_idx * self.image_step] = sign  # 收盘价位置
            image[:, 0::self.image_step] = hl_mask
        elif self.image_step == 1:
            assert image_cfg.sign
            oc_min_idx = np.minimum(open_idx, close_idx)
            oc_max_idx = np.maximum(open_idx, close_idx)
            oc_mask = ((ohlc_coords >= oc_min_idx) & (ohlc_coords <= oc_max_idx)) * sign
            image = (hl_mask + oc_mask) / 2
        if image_cfg.ema:
            ema_fast_idx = ohlc_idx[:, 4].repeat(self.image_step, axis=0)
            ema_slow_idx = ohlc_idx[:, 5].repeat(self.image_step, axis=0)
            image[ema_fast_idx, self.image_idx] = -0.25
            image[ema_slow_idx, self.image_idx] = 0.25

        volume_image = np.zeros([self.volume_height, self.width], dtype=np.float32)
        if image_cfg.volume:
            # 提取成交量数据并归一化
            volume_data = image_data[:, 6]
            buy_data = image_data[:, 7]
            sell_data = image_data[:, 8]
            delta_data = image_data[:, 9]
            buy_sell = image_data[:, 7: 9]
            volume_max = volume_data.max()


            # 防止除零错误
            if volume_max > 0:
                volume_height = self.volume_height
                volume_coords = np.arange(volume_height)[:, None] # shape: (volume_height, 1)
                if not image_cfg.buy_sell:
                    # 计算成交量的高度索引 (归一化到volume_height范围内)
                    volume_idx = (volume_height * (1 - volume_data / volume_max))
                    volume_idx = volume_idx.clip(0, volume_height - 1).astype(np.int32)

                    # 使用广播生成掩码来填充成交量柱状图
                    volume_mask = (volume_coords >= volume_idx) * sign # shape: (volume_height, time)
                    if image_cfg.buy:
                        buy_idx = (volume_height * (1 - buy_data / volume_max)).clip(0, volume_height - 1).astype(np.int32)
                        volume_mask = (volume_mask + (volume_coords >= buy_idx) * sign) / 2
                    if self.image_step == 3:
                        volume_image[:, 1::self.image_step] = volume_mask
                    elif self.image_step < 3:
                        volume_image[:, 0::self.image_step] = volume_mask

                elif image_cfg.buy_sell:
                    buy_sell_min = buy_sell.min()
                    buy_sell_max = buy_sell.max()
                    zero_location = ((volume_height - 1) * (buy_sell_max - 0) / (buy_sell_max - buy_sell_min))
                    buy_start_idx = np.round(zero_location + 0.5)
                    sell_start_idx = buy_start_idx + 1
                    new_zero_location = buy_start_idx + 0.5
                    location_shift = new_zero_location - zero_location
                    buy_idx = (location_shift + (volume_height - 1) * (buy_sell_max - buy_data) / (buy_sell_max - buy_sell_min)).clip(0, volume_height - 1).astype(np.int32)
                    sell_idx = (location_shift + (volume_height - 1) * (buy_sell_max - sell_data) / (buy_sell_max - buy_sell_min)).clip(0, volume_height - 1).astype(np.int32)

                    buy_mask = (volume_coords >= buy_idx) & (volume_coords <= buy_start_idx)
                    sell_mask = (volume_coords >= sell_start_idx) & (volume_coords <= sell_idx)
                    buy_sell_mask = buy_mask.astype(np.float32) - sell_mask.astype(np.float32)

                    if image_cfg.delta:
                        delta_idx = (location_shift + (volume_height - 1) * (buy_sell_max - delta_data) / (buy_sell_max - buy_sell_min)).clip(0, volume_height - 1).astype(np.int32)
                        pos_delta_mask = (volume_coords >= delta_idx) & (volume_coords <= buy_start_idx)
                        neg_delta_mask = (volume_coords >= sell_start_idx) & (volume_coords <= delta_idx)
                        delta_mask = pos_delta_mask.astype(np.float32) - neg_delta_mask.astype(np.float32)
                        buy_sell_mask = (buy_sell_mask + delta_mask) / 2
                    if self.image_step == 3:
                        volume_image[:, 1::self.image_step] = buy_sell_mask
                    elif self.image_step < 3:
                        volume_image[:, 0::self.image_step] = buy_sell_mask

        macd_height = self.macd_height
        macd_image = np.zeros([macd_height, self.width], dtype=np.float32)
        if image_cfg.macd:
            diff = image_data[:, -2]
            diff_ema = image_data[:, -1]
            if image_cfg.diff_as_bar:
                macd_bar = diff
            else:
                macd_bar = diff - diff_ema
            if image_cfg.macd_ema_line:
                macd = image_data[:, -2:]
            else:
                macd = macd_bar
            macd_max = max(0, macd.max(), macd_bar.max())
            macd_min = min(0, macd.min(), macd_bar.min())

            if macd_max > macd_min:
                macd_coods = np.arange(macd_height)[:, None]

                zero_location = ((macd_height - 1) * (macd_max - 0) / (macd_max - macd_min))
                pos_start_idx = np.round(zero_location + 0.5)
                neg_start_idx = pos_start_idx + 1
                new_zero_location = pos_start_idx + 0.5
                location_shift = new_zero_location - zero_location


                macd_bar_idx = (location_shift + (macd_height - 1) * (macd_max - macd_bar) / (macd_max - macd_min)).clip(0, macd_height - 1).astype(np.int32)
                pos_mask = (macd_coods >= macd_bar_idx) & (macd_coods <= pos_start_idx)
                neg_mask = (macd_coods >= neg_start_idx) & (macd_coods <= macd_bar_idx)
                macd_bar_mask = pos_mask.astype(np.float32) - neg_mask.astype(np.float32)
                if self.image_step == 3:
                    macd_image[:, 1::self.image_step] = macd_bar_mask
                elif self.image_step < 3:
                    macd_image[:, 0::self.image_step] = macd_bar_mask
                if image_cfg.macd_ema_line:
                    diff_idx = (location_shift + (macd_height - 1) * (macd_max - diff) / (macd_max - macd_min)).clip(0, macd_height - 1).astype(np.int32).repeat(self.image_step, axis=0)
                    diff_ema_idx = (location_shift + (macd_height - 1) * (macd_max - diff_ema) / (macd_max - macd_min)).clip(0, macd_height - 1).astype(np.int32).repeat(self.image_step, axis=0)
                    macd_image[diff_idx, self.image_idx] = -0.5
                    macd_image[diff_ema_idx, self.image_idx] = 0.5
                # plot_ndarray(macd_image)
        vstack_list = [macd_image, image, volume_image]
        blank_line = np.zeros([1, self.width], dtype=np.float32)
        image_list = [macd_image, blank_line, image, blank_line, volume_image]

        if image_cfg.rsi:
            rsi_height = self.rsi_height
            rsi_image = np.zeros([rsi_height, self.width], dtype=np.float32)
            rsi_coods = np.arange(rsi_height)[:, None]
            zero_location = rsi_height / 2 - 0.5
            rsi_inverse = (1 - image_data[:, -3] / 100)
            rsi_idx = np.round((rsi_height - 1) * rsi_inverse).clip(0, rsi_height - 1).astype(np.int32)
            pos_mask = (rsi_coods >= rsi_idx) & (rsi_coods <= zero_location)
            neg_mask = (rsi_coods >= zero_location) & (rsi_coods <= rsi_idx)
            rsi_mask = pos_mask.astype(np.float32) - neg_mask.astype(np.float32)
            if self.image_step == 3:
                rsi_image[:, 1::self.image_step] = rsi_mask
            elif self.image_step < 3:
                rsi_image[:, 0::self.image_step] = rsi_mask
            vstack_list.append(rsi_image)
            image_list += [blank_line, rsi_image]

        if image_cfg.vstack:
            image = np.stack(vstack_list)
        else:
            image = np.concatenate(image_list, axis=0)
            image = image.reshape(1, *image.shape)
        return image


    def set_group_dict(self):
        code_group = self.feature_df.groupby('code')
        datetime_group = self.feature_df.groupby('open_time')
        self.group_dict = dd(
            code={code: (len(group) - self.cutoff_len, check_bar_idx(group)) for code, group in code_group},
            datetime={dt: (len(group), group) for dt, group in datetime_group}
        )



    def make_entire_dataset(self, output_df: bool = False, label_str: str = 'label', flatten1: bool = False, cfg: PredictorConfig = None) -> pd.DataFrame | Tuple[np.ndarray, np.ndarray]:
        if cfg is None:
            cfg = self.cfg

        # 如果是EDA模式，返回简化的数据
        if cfg.is_eda:
            # 返回空数据，保持接口一致
            empty_x = np.zeros((1, cfg.n_all_features), dtype=np.float32)
            empty_y = np.zeros((1, cfg.n_labels), dtype=np.float32)
            return empty_x, empty_y

        X, Y = [], []
        for i in range(len(self)):
            tpl = self.__getitem__(i, cfg=cfg)
            if len(tpl) == 5:  # 处理返回5个元素的情况
                x, y, z, onehot, _ = tpl
            else:  # 处理返回4个元素的情况
                x, y, z, onehot = tpl
            X.append(x)
            Y.append(y)
        X = np.array(X)
        Y = np.array(Y)
        if flatten1:
            X = X.reshape(-1, X.shape[-1]) # (b c) f
            Y = Y.reshape(-1, Y.shape[-1])
        if np.isnan(X).any():
            print(f'{np.isnan(X).astype(int).sum() = }\n{X = }')
        if output_df:
            arr = np.concatenate([
                    X.reshape(-1, X.shape[-1]),
                    Y.reshape(-1, 1)], axis=1)
            if cfg.seq_len == 1:
                columns = self.feature_df.columns.tolist()
            else:
                columns = [f'{label_str}_{i}' for i in range(arr.shape[-1] - 1)]
            columns.append(label_str)
            df = pd.DataFrame(
                arr,
                columns=columns
                )
            return df, Y
        return X, Y


    def make_label_set(self) -> np.ndarray:
        cfg = self.cfg
        label_set = []
        for i in range(len(self)):
            if cfg.task_enum == TaskType.DirectTrading and not cfg.stack_multi_codes_on_slicing:
                max_step_count = len(self.features) - self.head_cutoff_len - self.with_label * self.label_len
                code_index = i // max_step_count
                x_start = i % max_step_count
                start_index = x_start + self.seq_len
                y = self.label[start_index: start_index + self.label_len, code_index]
            else:
                if not cfg.overlapping_on_slicing:
                    start_index = i * self.seq_len + self.seq_len
                else:
                    start_index = i + self.seq_len
                end_index = start_index + self.label_len
                y = self.label[start_index: end_index]
            label_set.append(y)
        return np.array(label_set)


    def get_planning_obs(self, index: int, is_trading_online: bool = False) -> np.ndarray:
        cfg = self.cfg
        x_end = index + self.seq_len
        x_slc = slice(index, x_end)
        if is_trading_online:
                ...
        elif self.prediction is None:
            return self.get_predicting_feature(x_slc, use_seq_feature=cfg.use_seq_feature_on_planning)
        else:
            if index >= len(self.prediction):
                print(f'{index = } >= {len(self.prediction) = }')
            prediction = self.prediction[index]
            if cfg.use_cum_prediction:
                cum_prediction = prediction.cumsum(axis=0)
                if cfg.cum_prediction_only:
                    prediction = cum_prediction
                else:
                    prediction = np.concatenate([prediction, cum_prediction], axis=0)
            if cfg.pred_feature_only:
                return prediction

            x = self.get_predicting_feature(x_slc, use_seq_feature=cfg.use_seq_feature_on_planning)

            return np.concatenate([x, prediction], axis=None)



    def make_linear_law_transformations(self):
        cfg = self.cfg
        if cfg.linear_law.in_use:
            dataset_to_transform = []
            for i in range(len(self)):
                dataset_to_transform.append(self[i][0])
            dataset_to_transform = np.array(dataset_to_transform)
            if self.phase == 'train':
                cfg.linear_law.model = cfg.get_linear_law_model(dataset_to_transform)
                cfg.linear_law.model.fit()
            self.linear_law_features = cfg.linear_law.model.transform(dataset_to_transform)
            del dataset_to_transform


    def get_predicting_feature(self, index: slice | int, feature_indices: list = None, use_seq_feature: bool = True) -> np.ndarray:
        cfg = self.cfg
        cum_feature_num = cfg.cum_feature_num
        use_cum_feature = cum_feature_num > 0
        inverse_slc = -1 ** use_cum_feature
        if isinstance(index, slice):
            if use_seq_feature:
                x = self.features[index]
            else:
                x = self.features[index.stop - 1]
            end = index.stop - 1
        elif isinstance(index, int):
            end = index + self.head_cutoff_len
            x = self.features[end]
        if cfg.feature_cfg.original:
            original_dim = cfg.feature_cfg.original_dim
            prev_close = self.features[end, 3]
            x[..., :4] = x[..., :4] / prev_close - 1
            prev_quote = self.features[end, original_dim - 1]
            x[..., 4: original_dim] = x[..., 4: original_dim] / prev_quote - 1

        if feature_indices is not None:
            x = x[:, :, feature_indices]
        if use_cum_feature:
            cum_x = x[::inverse_slc, :, -cum_feature_num:].cumsum(axis=0)
            if cfg.cum_feature_only:
                return cum_x
            x = np.concatenate([x, cum_x], axis=-1)
        # if cfg.linear_law.in_use and self.linear_law_features is not None:
        #     linear_law_feature = self.linear_law_features[slc.start]
        #     if cfg.linear_law_only:
        #         return linear_law_feature
        #     x = np.concatenate([x, linear_law_feature], axis=None)
        if np.isnan(x).any():
            print(f'{np.isnan(x).astype(int).sum() = }\n{x = }')
        return x

    def get_mark(self, index: int) -> tuple:
        y_start = x_end = index + self.seq_len
        y_end = y_start + self.label_len
        x_mark = self.data_stamp[index: x_end]
        y_mark = self.data_stamp[y_start: y_end]
        return x_mark, y_mark


    def __len__(self):
        cfg = self.cfg

        # 如果是EDA模式，返回0，避免循环
        if cfg.is_eda:
            return 0

        if cfg.task_enum in [TaskType.DirectTrading, TaskType.MarketMaking, TaskType.GridTrading]:
            return len(self.indices_mapping_arr)

        n_codes = self.n_codes
        result = self.seq_data_count

        if cfg.task_enum in [TaskType.DirectTrading, TaskType.DirectTradingDoubleAdapt, TaskType.Regression, TaskType.ClassificationTrading, TaskType.Rank]:

            if self.outer_step > 1:
                result = self.seq_step_count
            if not cfg.stack_multi_codes_on_slicing:
                result = result * n_codes

        elif cfg.task_enum in [TaskType.Portfolio, TaskType.PortfolioDoubleAdapt, TaskType.MarketMakingPortfolio]:
            if self.outer_step > 1:
                result = self.seq_data_count // self.outer_step

        elif self.prediction is not None:
            result = min(result, len(self.prediction))

        elif not self.cfg.overlapping_on_slicing:
            result = result // self.seq_len

        return result


    def get_sub_dataset(self, index: int, n_split: int = 1) -> Dataset:
        overlap_split = self.is_training and self.cfg.overlap_split
        unit_length = floor((self.total_data_count - self.head_cutoff_len) / (n_split + overlap_split))
        training_factor = 2 ** overlap_split
        split_length = training_factor * unit_length
        split_slice = slice(index * unit_length, index *
                            unit_length + split_length)
        self_slc = self[split_slice]
        prediction = self.prediction[split_slice] if self.prediction is not None else None
        if prediction is not None:
            assert len(prediction) == len(self_slc[-1])
        linear_law_features = self.linear_law_features[split_slice] if self.linear_law_features is not None else None
        return __class__(self.cfg, *self_slc, prediction, linear_law_features, self.phase)


    def get_prices(self, price='close'):
        if price in self.data.columns:
            if isinstance(self.data, pd.DataFrame):
                return self.data[price].values
            return self.data[price]
        else:
            return None


    # def set_prices(self):
    #     if isinstance(self.data, pd.DataFrame):
    #         self.data.reset_index(drop=True, inplace=True)
    #     self.highs = self.get_prices('high')
    #     self.lows = self.get_prices('low')
    #     self.opens = self.get_prices('open')
    #     self.closes = self.get_prices()
    #     self.avg_prices = self.get_prices('avg_price')
    #     self.rolling_highs = self.get_prices('rolling_high')
    #     self.rolling_lows = self.get_prices('rolling_low')
    #     self.range_highs = self.get_prices('range_high')
    #     self.range_lows = self.get_prices('range_low')
    #     self.range_closes = self.get_prices('range_close')


def datetime_index_to_onehot(dt_index: pd.DatetimeIndex) -> np.ndarray:
    # 提取相关特征
    month_of_year = dt_index.month.array
    day_of_month = np.clip(dt_index.day.array, 0, 30)
    day_of_week = dt_index.dayofweek.array
    hour_of_day = dt_index.hour.array

    # 生成 one-hot 编码
    month_of_year_onehot = np.eye(12)[month_of_year - 1]
    third_of_month_onehot = np.eye(3)[(day_of_month - 1) // 10]
    day_of_week_onehot = np.eye(7)[day_of_week]
    sixth_of_day_onehot = np.eye(24)[hour_of_day // 4]

    # 合并所有特征
    onehot = np.concatenate([
        month_of_year_onehot,
        third_of_month_onehot,
        day_of_week_onehot,
        sixth_of_day_onehot
    ], axis=1)

    return onehot.astype(np.float32)


def check_bar_idx(bar: pd.DataFrame):
    reset_bar = bar.reset_index()
    code = reset_bar['code'][0]
    bar_idx_diff = reset_bar['bar_idx'].diff().fillna(0)
    if bar_idx_diff.max() > 1:
        print(f'{code} {bar_idx_diff.max() = }')
    return bar


def set_valid(group: pd.DataFrame, head_cutoff_len: int, tail_cutoff_len: int, cfg: PredictorConfig, phase: str) -> pd.DataFrame:
    filter_mask = cfg.filter_mask_fn(group, cfg)
    cycle_or_segment_mask = cfg.get_filter_mask_fn()(group)
    is_valid_arr = cfg.get_valid_arr(group, phase)

    is_valid_arr[:head_cutoff_len] = 0
    if tail_cutoff_len > 0:
        is_valid_arr[-tail_cutoff_len:] = 0
    group['is_valid'] = is_valid_arr * cycle_or_segment_mask * filter_mask
    return group


def filter_valid(group: pd.DataFrame, head_cutoff_len: int, tail_cutoff_len: int, cfg: PredictorConfig, phase: str) -> pd.DataFrame:
    filter_mask = cfg.filter_mask_fn(group, cfg)
    is_valid_arr = cfg.get_valid_arr(group, phase)

    is_valid_arr[:head_cutoff_len] = 0
    if tail_cutoff_len > 0:
        is_valid_arr[-tail_cutoff_len:] = 0
    group['is_valid'] = is_valid_arr * filter_mask
    return group


def plot_ndarray(image: np.ndarray) -> None:
    """显示图像数据

    Args:
        image: 形状为 [height, width] 的 numpy 数组
    """
    import matplotlib.pyplot as plt
    if (ndim := image.ndim) == 3:
        for i in range(ndim):
            plot_ndarray(image[i])
    else:
        plt.figure()
        plt.imshow(image, cmap='binary')
        # plt.imshow(image + image.min(), cmap='binary')
        # plt.imshow(image + image.min())
        # plt.axis('off')
        plt.show()