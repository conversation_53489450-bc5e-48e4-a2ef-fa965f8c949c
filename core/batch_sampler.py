from copy import deepcopy
import random
from typing import List
from torch.utils.data._utils.collate import collate, default_collate_fn_map
from torch.utils.data.sampler import <PERSON><PERSON>, RandomSampler
from core.dataset import KlineDataset
from core.predictor_config import PredictorConfig


class RandomPairBatchSampler(Sampler[List[int]]):
    def __init__(self, cfg: PredictorConfig, data_source: KlineDataset, drop_last: bool = False):
        self.cfg = cfg
        self.data_source = data_source

        # 如果是EDA模式，使用简化的初始化
        if cfg.is_eda:
            self.data_count = 0
            self.phase = getattr(data_source, 'phase', 'train')
            self.batch_size = cfg.batch_size
            self.last_count = 0
            self.sample_pair = False
            self.drop_last = drop_last
            self.batch_indices = []
            self.batch_count = 0
            return

        self.data_count = len(self.data_source)
        self.phase = self.data_source.phase
        # if self.phase == 'online':
        #     self.batch_size = cfg.n_codes
        # else:
        self.batch_size = cfg.batch_size
        self.last_count = self.data_count % self.batch_size

        # self.sampler = RandomSampler(data_source, replacement=cfg.shuffling.samplewise and self.phase == 'train', num_samples=None)
        self.sample_pair = self.cfg.sample_pair
        self.drop_last = drop_last
        self.batch_indices = self.get_batch_indices()
        self.batch_count = len(self)
        for _ in range(cfg.shuffling.extra_count):
            self.reset_batch_indices()


    def reset_batch_indices(self):
        self.batch_indices = self.get_batch_indices()


    def get_batch_indices(self):
        # 生成按顺序的索引对-
        cfg = self.cfg
        if self.phase == 'online':
            return self.data_source.indices_list[-1:]

        n = len(self)
        batch_size = self.batch_size
        data_count = self.data_count

        if self.drop_last:
            data_count = data_count - self.last_count
        raw_indices = list(range(data_count))

        if cfg.sample_cross_section_in_a_batch:
            indices_list = deepcopy(self.data_source.indices_list)
            if cfg.shuffling.codewise and self.phase == 'train':
                for lst in indices_list:
                    random.shuffle(lst)
            batch_indices = indices_list
        else:
            if self.phase == 'train':
                if cfg.shuffling.samplewise:
                    random.shuffle(raw_indices)
                indices_list = [
                    raw_indices[i:i + batch_size]
                    for i in range(0, data_count, batch_size)
                ]
                # 生成批量索引
                if self.sample_pair:
                    # batch_indices = [
                    #     [indices_list[i], indices_list[i + 1]]
                    #     for i in range(n)
                    # ]
                    batch_indices = list(zip(indices_list[:-1], indices_list[1:]))
                    # batch_indices = [
                    #     [raw_indices[i:i + batch_size], raw_indices[i + batch_size:i + 2 * batch_size]]
                    #     for i in range(0, data_count - batch_size, batch_size)
                    # ]
                else:
                    batch_indices = indices_list
            else:
                indices_list = [
                    raw_indices[i:i + batch_size] for i in range(0, data_count, batch_size)
                ]
                if self.sample_pair:
                    if cfg.sample_pair_by_step_on_eval:
                        indices_list = [
                            raw_indices[i:i + batch_size]
                            for i in range(0, data_count - batch_size)
                        ]
                        batch_indices = list(zip(indices_list, [[r] for r in raw_indices[batch_size:]]))
                    else:
                        batch_indices = list(zip(indices_list[:-1], indices_list[1:]))
                else:
                    batch_indices = indices_list


        if cfg.shuffling.batchwise and self.phase == 'train':
            random.shuffle(batch_indices)

        assert len(batch_indices) == n

        return batch_indices


    def __iter__(self):
        # if self.phase == 'train':
        #     return iter(self.get_batch_indices())
        return iter(self.batch_indices)


    def __len__(self):
        # 如果是EDA模式，返回0
        if self.cfg.is_eda:
            return 0

        if self.cfg.sample_cross_section_in_a_batch:
            return len(self.data_source.indices_list)
        if self.phase != 'train' and self.sample_pair and self.cfg.sample_pair_by_step_on_eval:
            return self.data_count - self.batch_size
        return self.data_count // self.batch_size + (self.last_count > 0) * (1 - self.drop_last) - self.sample_pair


def multi_collate_fn(batch_data):
    if isinstance(batch_data[0][0], tuple):
        batch_data = [collate(sub_data, collate_fn_map=default_collate_fn_map) for sub_data in batch_data]
    else:
        batch_data = collate(batch_data, collate_fn_map=default_collate_fn_map)

    return batch_data