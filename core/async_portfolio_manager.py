import asyncio
from collections import deque
import joblib
import numpy as np
import pandas as pd
from datetime import datetime
import polars as pl
import torch
from aed_quant.asset import Asset
from aed_quant.utils.decorator import async_method_locker
from aed_quant.utils.parser import parse_stream_kline
from aed_quant.const import MARKET_TYPE_ORDERBOOK
from aed_quant.market import MarketSubscriber, Orderbook, Kline
from aed_quant.order import Order, ORDER_ACTION_BUY, ORDER_ACTION_SELL, ORDER_TYPE_MARKET, ORDER_TYPE_LIMIT
from aed_quant.position import Position
from aed_quant.tasks import SingleTask
from aed_quant.trader import Trader
from aed_quant.utils import logger
from aed_quant.error import Error
from core.dot_dict import DotDict as dd
from core.symbols import sd
from core.dataset import KlineDataset
from core.predictor_config import PredictorConfig
import sys

from core.data_module import KlineDataModule
from models.lightning_base import LightningBase
from models.lightning_drt import get_top_k_mask

is_debugging = True if sys.gettrace() else False


def get_buy_sell(side):
	return {1: ORDER_ACTION_BUY, -1: ORDER_ACTION_SELL}.get(side, 'Unknown Side')


class AsyncPortfolioManager:
	def __init__(
			self,
			cfg: PredictorConfig,
			model: LightningBase,
	):		
		self.model = model
		self.cfg = cfg		

		if cfg.load_all_codes:
			code_list = cfg.get_all_code_list()
		else:
			code_list = cfg.code_list
		alternate_code_list = cfg.alternate_code_list
		trade_alternate = self.trade_alternate = cfg.trade_alternate
		estimated_code_list = code_list + alternate_code_list
		self.next_trade_timestamp_ms = 0
		self.next_open_timestamp_ms = 0
		self.update_trade_row = True
		cc = dict(
			strategy=cfg.strategy,
			platform=cfg.platform,
			symbols=estimated_code_list,
			account_id=cfg.account_id,
			access_key=cfg.access_key,
			secret_key=cfg.secret_key,
			init_success_callback=self.on_event_init_success_callback,
			error_callback=self.on_event_error_callback,
			account_update_callback=self.on_event_account_update_callback,
			order_update_callback=self.on_event_order_update_callback,
			position_update_callback=self.on_event_position_update_callback
		)
		self.trader = Trader(**cc)
		trading_pairs_path = cfg.trading_pairs_path
		trading_pairs_df = self.async_to_sync(self.trader._t.get_trading_pairs_info())
		if trading_pairs_df is None:
			trading_pairs_df = pd.read_csv(trading_pairs_path).set_index('symbol')
		elif cfg.force_update_trading_pairs:
			trading_pairs_df.to_csv(trading_pairs_path, index=False)
		trading_pairs_list = trading_pairs_df.index.get_level_values('symbol').to_list()
		missing_code_list = [code for code in code_list if code not in trading_pairs_list]
		n_missing = self.n_missing_codes = len(missing_code_list)
		if n_missing > 0:			
			alt_str = f', replaced by {alternate_code_list[: n_missing]}' if trade_alternate else ''
			logger.info(f"Warning: missing codes {missing_code_list}{alt_str}", caller=self)
		valid_code_list = [code for code in estimated_code_list if code in trading_pairs_list]
		cfg.code_list = self.code_list = code_list = valid_code_list[: cfg.n_codes]
		cfg.alternate_code_list = alternate_code_list = valid_code_list[cfg.n_codes: ]

		# missing_code_list = [code for code in code_list if code not in trading_pairs_list]

		# n_alternate = len(alternate_code_list)
		# for i, missing in enumerate(missing_code_list):
		# 	if i >= n_alternate:
		# 		logger.error(f"missing code: {missing} not replaced from alternate_code_list", caller=self)
		# 		break
		# 	code_list.remove(missing)
		# 	if missing in trading_pairs_list:				
		# 		code_list.append(alternate_code_list[i])
		self.online_mask = cfg.online_mask
		self.back_shift_in_sec = cfg.interval_cfg.back_shift_in_sec
		if (sim_base_interval := cfg.sim_base_interval) is not None:
			self.kline_interval = sim_base_interval			
			self.kline_interval_str = f'{sim_base_interval}m'
			# label_len = cfg.interval_cfg.label // cfg.interval_cfg.base
			# sim_scale = sim_base_interval / cfg.interval_cfg.base
			# self.back_shift_in_sec = int(sim_scale * self.back_shift_in_sec)
			# self.label_interval = int(sim_scale * cfg.interval_cfg.label)
			self.label_interval = 2 * sim_base_interval
			self.save_history_kline = False
			
		else:
			self.kline_interval, self.kline_interval_str = cfg.get_kline_interval_str()
			self.label_interval = cfg.interval_cfg.label
			self.save_history_kline = True
		self.kline_type = 'kline_' + self.kline_interval_str
		
		self.orders = dd(
			market=dd(), 
			limit=dd(),
			profit=set(),
			target=set(),
			)
		self.new_row_dict = {}
		self.trade_row_dict = {}
		self.status_df: pd.DataFrame = pl.from_pandas(trading_pairs_df, include_index=True).filter(pl.col('symbol').is_in(code_list)).select(
			'symbol',
			'tick_size', 
			'lot_size', 
			'min_notional',
			index=pl.col('symbol').map_elements(lambda x: code_list.index(x), return_dtype=pl.Int32),
			tick_size_round=-(np.log10(pl.col('tick_size'))).cast(pl.Int32),
			lot_size_round=-(np.log10(pl.col('lot_size'))).cast(pl.Int32),
			close=0.,
			prev_lot_count=0.,
			lot_count=0.,
			target_lot_count=0.,
			prev_position=0.,
			position=0.,
			target_position=0.,
			avg_price=0.,
			prev_exposure=0.,
			exposure=0.,
			target_exposure=0.,
			prev_ratio=0.,
			ratio=0.,
			target_ratio=0.,
			realized_pnl=0.,
			unrealized_pnl=0.,
			cum_pnl=0.,
			order_side=0.,
			order_qty=0.,
			order_price=0.,
			order_qty_filled=0.,
			order_status=None,
		).sort(by='index').drop('index').to_pandas().set_index('symbol')		

		self.info_cols = ['position', 'target_position', 'ratio', 'target_ratio', 'order_side', 'order_qty', 'order_price', 'avg_price', 'close']
		
		# open_notional = open_qty * open_price
		# order_notional = order_qty * order_price
		# max_notional = available_margin * leverage + order_notional + open_notional + unrealized_pnl
		# open_margin = open_notional / leverage
		# order_margin = order_notional / leverage
		# balance = available_margin + order_margin + open_margin + unrealized_pnl
		# 		
		self.notional_mask = None
		self.max_notional = cfg.base_notional * cfg.leverage
		self.budget_ratio = cfg.budget_ratio
		self.budget_notional = self.balance = self.max_notional * self.budget_ratio
		self.ready = False
		for symbol in code_list:
			MarketSubscriber(MARKET_TYPE_ORDERBOOK, cfg.platform, symbol, self.on_event_orderbook_update)			
			MarketSubscriber(self.kline_type, cfg.platform, symbol, self.on_event_kline_update)

	def async_to_sync(self, task):
		loop = asyncio.get_event_loop()
		task = loop.create_task(task)
		return loop.run_until_complete(task)

	@property
	def balance_ratio(self):
		return self.balance / self.max_notional

	

	async def init_data_module(self):
		cfg = self.cfg
		history_kline_dict = {}
		results = await asyncio.gather(*[self.get_history_kline(symbol) for symbol in self.code_list])
		for symbol, (history_kline, error) in zip(self.code_list, results):
			# history_kline, error = await self.get_history_kline(symbol)
			if error is None:
				history_kline_dict[symbol] = history_kline
			else:
				logger.info("initialize data module error:", error, caller=self)

		if self.save_history_kline:
			path = cfg.get_history_kline_path()			
			joblib.dump(history_kline_dict, path)
			logger.info("history kline saved to:", path, caller=self)

		self.data_module = KlineDataModule(cfg, history_kline_dict, load_dolphin=False)
		online_batch_sampler = self.data_module.batch_sampler_dict.get('online')
		if cfg.meta_adapt.in_use and cfg.meta_adapt.on_init and online_batch_sampler is not None and online_batch_sampler.batch_count > 0:			
			trainer = cfg.get_trainer()
			trainer.test(self.model, self.data_module.dataloader_dict.online)
			self.data_module.set_adapt_count()

		self.next_open_timestamp_ms = self.data_module.get_next_open_timestamp(self.kline_interval)
		self.next_trade_timestamp_ms = self.data_module.get_next_trade_timestamp(self.label_interval, self.back_shift_in_sec)
		self.log_next(self.next_open_timestamp_ms, 'open')
		self.log_next(self.next_trade_timestamp_ms, 'trade')

		close_arr = self.data_module.latest_close_arr
		self.status_df['close'] = close_arr
		if self.notional_mask is None:
			lot_notional = self.status_df['lot_size'].to_numpy() * close_arr
			self.notional_mask = ((self.status_df['min_notional'].to_numpy() <= cfg.max_lot_notional) & (lot_notional <= cfg.max_lot_notional)).astype(np.float32)
			zero_indices = np.where(self.notional_mask == 0.)[0]
			notional_masked_code_list = [self.code_list[i] for i in zero_indices]
			logger.info(f'{notional_masked_code_list = }', caller=self)

		self.ready = True
		logger.info("data module ready:", self.ready, caller=self)


	def log_next(self, timestamp_ms: int, name: str):
		next_time = datetime.fromtimestamp(timestamp_ms / 1000)
		logger.info(f"next {name} time is {next_time.strftime('%Y-%m-%d %H:%M:%S')}", caller=self)


	# def log_next_times(self):
	# 	next_open_time = datetime.fromtimestamp(self.next_open_timestamp_ms / 1000)
	# 	next_trade_time = datetime.fromtimestamp(self.next_trade_timestamp_ms / 1000)
	# 	logger.info(f"next_open_time is {next_open_time.strftime('%Y-%m-%d %H:%M:%S')}, next_trade_time is {next_trade_time.strftime('%Y-%m-%d %H:%M:%S')}", caller=self)
	

	def log_all_status(self):
		for symbol in self.code_list:
			self.log_status(symbol)			


	def log_status(self, symbol):
		symbol_status = self.status_df.loc[symbol, self.info_cols].to_dict()
		logger.info(f"{symbol} status: {symbol_status}", caller=self)


	async def get_history_kline(self, symbol):
		success, error = await self.trader.rest_api.get_kline(
			symbol=symbol,
			interval=self.kline_interval_str,
			limit=self.cfg.history_kline_limit,
		)
		return success, error
	

	async def on_event_orderbook_update(self, orderbook: Orderbook):
		self.ask_price = float(orderbook.asks[0][0])
		self.bid_price = float(orderbook.bids[0][0])
	
	# self.mid_price = (self.ask_price + self.bid_price) / 2
	
	
	async def on_event_kline_update(self, kline: Kline):
		cfg = self.cfg
		if not (kline.symbol in self.code_list and kline.kline_type == self.kline_type and self.ready) :
			return
		
		code_idx = self.code_list.index(kline.symbol)

		if kline.symbol not in self.trade_row_dict and kline.timestamp >= self.next_trade_timestamp_ms and self.update_trade_row:
			self.trade_row_dict[kline.symbol] = parse_stream_kline(kline.data, code_idx)

		await self.check_and_trade()

		if not (is_debugging and cfg.debug_ticker or kline.is_closed):
			return
			
		if self.next_open_timestamp_ms == kline.open_timestamp and kline.symbol not in self.new_row_dict:
			self.new_row_dict[kline.symbol] = parse_stream_kline(kline.data, code_idx)
			await self.update_data()
	

	@async_method_locker('update_data')
	async def update_data(self):
		if len(self.new_row_dict) == len(self.code_list):
			self.data_module.update_data_from_stream(self.new_row_dict)
			if self.cfg.is_live_trading:
				await self.opponent_all_target()
			self.update_trade_row = True
			self.new_row_dict.clear()
			self.next_open_timestamp_ms += self.kline_interval * 60_000
			self.log_next(self.next_open_timestamp_ms, 'open')


	@async_method_locker('check_and_trade')
	async def check_and_trade(self):
		if len(self.trade_row_dict) == len(self.code_list) and self.update_trade_row:
			self.update_trade_row = False
			await self.execute()
			self.trade_row_dict.clear()
			self.next_trade_timestamp_ms += self.label_interval * 60_000
			self.log_next(self.next_trade_timestamp_ms, 'trade')

	
	async def execute(self):
		cfg = self.cfg
		if cfg.is_live_trading and self.orders.limit:
			await self.cancel_all()
		
		self.max_notional = max(self.balance, self.max_notional)
		min_budget_notional = self.budget_notional * self.budget_ratio
		if self.balance < min_budget_notional:
			self.max_notional = self.budget_notional
			self.budget_notional = self.max_notional * self.budget_ratio
			logger.info("balance < min_budget_notional", min_budget_notional, caller=self)
		elif self.balance == self.max_notional:
			self.budget_notional = self.max_notional * self.budget_ratio		
					
		self.status_df['prev_lot_count'] = self.status_df['lot_count']
		self.status_df['prev_position'] = self.status_df['position']
		self.status_df['prev_exposure'] = self.status_df['exposure']
		self.status_df['prev_ratio'] = self.status_df['ratio']

		self.status_df['exposure'] = self.status_df['position'] * self.status_df['avg_price']
		self.status_df['ratio'] = (self.status_df['exposure'] / self.budget_notional) if self.budget_notional > 0 else 0

		self.data_module.update_data_from_stream(self.trade_row_dict, is_back_shift=True)
		
		
		close_arr = self.data_module.latest_close_arr
		self.status_df['close'] = close_arr

		if cfg.meta_adapt.in_use and self.data_module.next_adapt_count > 0:
			trainer = cfg.get_trainer()
			trainer.test(self.model, self.data_module.dataloader_dict.online)
			self.data_module.set_adapt_count()

		dataset: KlineDataset = self.data_module.dataset_dict.online
		if 'pfl' in cfg.task_enum.value:
			len_dataset = len(dataset)
			latest_batch = dataset[len_dataset][0]
			target_ratio_arr = self.model.predict(latest_batch[None, ...]).flatten()
			take_profit_ratio_arr = np.ones_like(target_ratio_arr)
		elif 'drt' in cfg.task_enum.value:				
			if cfg.load_all_codes:
				mask = self.data_module.quantile_mask
			else:
				mask = self.online_mask
			mask = mask * self.notional_mask
			if not self.trade_alternate and self.n_missing_codes > 0:
				mask[-self.n_missing_codes:] = 0
			latest_batch = self.data_module.latest_batch
			result = self.model.predict(latest_batch)
			target_ratio_arr = result[0].flatten() * mask
			take_profit_ratio_arr = result[-1].flatten() * mask
			top_num = cfg.top_num
			top_k_mask = get_top_k_mask(target_ratio_arr, top_num, dim=0).numpy()
			target_ratio_arr = target_ratio_arr * top_k_mask 
			if cfg.equal_position_weight:
				target_ratio_arr = np.sign(target_ratio_arr)
			denominator = abs(target_ratio_arr).sum()
			if denominator > 0:
				target_ratio_arr = target_ratio_arr / denominator
		else:
			raise ValueError(f"invalid task: {cfg.task_enum.value}")
		non_zero_indices = np.where(target_ratio_arr != 0)[0]
		num_non_zero_positions = len(non_zero_indices)
		target_ratio_dict = dict(zip([self.code_list[i] for i in non_zero_indices], target_ratio_arr[non_zero_indices]))
		
		logger.info(f'{self.balance = :.4f} | {self.max_notional = :.4f} | {self.budget_notional = :.4f} | {self.balance_ratio = :.4f} | {num_non_zero_positions = } | {target_ratio_dict = }', caller=self)

		position_arr = self.status_df['position'].to_numpy()
		lot_size_arr = self.status_df['lot_size'].to_numpy()
		price_arr = close_arr
		if cfg.calc_qty_by_position:
			extimate_position_arr = self.budget_notional * target_ratio_arr / close_arr
			position_to_change_arr = extimate_position_arr - position_arr
			side_arr = np.sign(position_to_change_arr)
			is_flatten_arr = (extimate_position_arr == 0) & (position_arr != 0)
			is_reduce_only_arr = (position_arr * extimate_position_arr > 0) & (abs(extimate_position_arr) < abs(position_arr)) | is_flatten_arr
			
			if cfg.price_queue is None:
				price_arr *= (1 - cfg.use_limit_order * cfg.limit_shift_ratio * side_arr)

			order_notional_arr = abs(position_to_change_arr) * price_arr
						
		else:			
			ratio_arr = self.status_df['ratio'].to_numpy()
			ratio_to_change_arr = target_ratio_arr - ratio_arr
			side_arr = np.sign(ratio_to_change_arr)
			is_flatten_arr = (target_ratio_arr == 0) & (ratio_arr != 0)
			is_reduce_only_arr = (ratio_arr * target_ratio_arr > 0) & (abs(target_ratio_arr) < abs(ratio_arr)) | is_flatten_arr

			if cfg.price_queue is None:
				price_arr *= (1 - cfg.use_limit_order * cfg.limit_shift_ratio * side_arr)

			order_notional_arr = abs(self.budget_notional * ratio_to_change_arr)

		qty_arr = np.floor(order_notional_arr / price_arr / lot_size_arr) * lot_size_arr # unsigned
		order_notional_arr = qty_arr * price_arr

		min_notional_arr = self.status_df['min_notional'].to_numpy()
		lt_min_notional = order_notional_arr < min_notional_arr
		# order_notional_arr[lt_min_notional] = 0
		qty_arr[lt_min_notional & ~is_reduce_only_arr] = 0
		qty_arr[is_flatten_arr] = abs(position_arr[is_flatten_arr])
		target_position_arr = np.zeros_like(position_arr)

		# lot_count_to_change_arr[lt_min_notional & ~(is_reduce_only | is_flatten)] = 0
		# lot_count_to_change_arr[is_flatten] = -lot_count_arr[is_flatten]
		# self.status_df['target_lot_count'] = target_lot_count_arr = lot_count_arr + lot_count_to_change_arr

		# qty_arr = lot_count_to_change_arr * lot_size_arr
		# target_position_arr = target_lot_count_arr * lot_size_arr + qty_arr

		tick_size_arr = self.status_df['tick_size'].to_numpy()
		tick_size_round_arr = self.status_df['tick_size_round'].to_numpy()
		lot_size_round_arr = self.status_df['lot_size_round'].to_numpy()
		

		order_type_str = ORDER_TYPE_LIMIT if cfg.use_limit_order else ORDER_TYPE_MARKET
		price_queue = cfg.price_queue
		will_take_profit = cfg.take_profit.in_use
		# order_tasks = []			
		for i, symbol in enumerate(self.code_list):		
			qty = round(qty_arr[i], lot_size_round_arr[i])
			qty_arr[i] = qty
			side = side_arr[i]
			target_position = target_position_arr[i] = position_arr[i] + qty * side

			if will_take_profit and (target_position != 0):
				take_profit_price = round(price_arr[i] * (1 + side * take_profit_ratio_arr[i]), tick_size_round_arr[i])
				take_profit_qty = round(abs(target_position), lot_size_round_arr[i])
				take_profit_buy_sell = get_buy_sell(-int(side))
				take_profit_msg = f"{symbol} {ORDER_TYPE_LIMIT} {take_profit_buy_sell} {take_profit_qty} @ {take_profit_price}"
				logger.info("creating take profit order:", take_profit_msg, caller=self)
				if cfg.is_live_trading:
					good_til_date = self.next_trade_timestamp_ms + self.label_interval * 60_000
					take_profit_order_id_with_client, error = await self.trader.create_order(
						take_profit_buy_sell, 
						take_profit_price, 
						take_profit_qty, 
						order_type_str, 
						symbol=symbol, 
						post_only=True,
						good_til_date=good_til_date,
						)

					if error:
						logger.info("live take profit order sending error: ", error, caller=self)
					else:
						logger.info("live take profit order sending result: ", take_profit_order_id_with_client, caller=self)
						self.orders.profit.add(take_profit_order_id_with_client)

			if qty == 0:
				if side != 0:
					target_ratio = target_ratio_arr[i]
					position = position_arr[i]
					order_notional = order_notional_arr[i]
					min_notional = min_notional_arr[i]
					logger.info(f"Warning: {symbol} {order_notional = } {min_notional = } | {target_ratio = } {target_position = } {position = } | {qty = }, but {side = } is not 0, abnormal!", caller=self)
				continue
									
			buy_sell = get_buy_sell(int(side))
			
			price = round(price_arr[i] - side * tick_size_arr[i], tick_size_round_arr[i])
			is_queue_price = False
			if price_queue is not None:
				queue_price = round(price_arr[i] - side * tick_size_arr[i] * price_queue, tick_size_round_arr[i])
				min_max = min if side < 0 else max
				price = min_max(price, queue_price)
				is_queue_price = (price == queue_price) or (price_queue == 1)
			is_reduce_only = is_reduce_only_arr[i]
			if is_reduce_only:
				reduce_only_str = ' reduce only'  
			else:
				reduce_only_str = ''
			target_msg = f"{symbol} {order_type_str}{reduce_only_str} {buy_sell} {qty} @ {price} {price_queue = }"
			logger.info("creating target position order:", target_msg, caller=self)
			if cfg.is_live_trading:
				if is_queue_price:
					target_position_order_id_with_client, error = await self.trader.create_order(
						buy_sell, price, qty, order_type_str, symbol=symbol, 
						price_queue=price_queue, reduce_only=is_reduce_only,
					)
				else:
					target_position_order_id_with_client, error = await self.trader.create_order(
						buy_sell, price, qty, order_type_str, symbol=symbol, 
						reduce_only=is_reduce_only,
						post_only=True
					)
				if error:
					logger.info("live target position order sending error: ", error, caller=self)
				else:
					logger.info("live target position order sending result: ", target_position_order_id_with_client, caller=self)
					self.orders.target.add(target_position_order_id_with_client)

		
		self.status_df['target_position'] = target_position_arr
		self.status_df['target_exposure'] = self.status_df['exposure'] + side_arr * qty * price_arr
		self.status_df['target_ratio'] = self.status_df['target_exposure'] / self.budget_notional
		self.status_df['order_side'] = side_arr
		self.status_df['order_qty'] = qty_arr
		self.status_df['order_price'] = price_arr


	
	async def on_event_init_success_callback(self, success: bool, error: Error, **kwargs):
		logger.info("initialize success:", success, caller=self)
		# if error:
		# 	logger.info("error:", error, caller=self)
		if not success:
			return
		# SingleTask.call_later(self.revoke_order, 1)
		SingleTask.run(self.init_data_module)
	

	async def on_event_error_callback(self, error: Error, **kwargs):
		logger.info("error:", error, caller=self)
	

	async def on_event_order_update_callback(self, order: Order):
		logger.info("order:", order, caller=self)
		order_id_with_client = order.order_id_with_client
		if 'android' in order_id_with_client:
			return
		order_type_str = order.order_type.lower()
		# buy_sell_str = order.action.lower()
		if order.status == 'SUBMITTED':
			self.orders[order_type_str][order_id_with_client] = order
		# self.orders[buy_sell_str][order.order_id_with_client] = order
		if order.status in ['CANCELED', 'FILLED', 'FAILED']:
			self.orders[order_type_str].pop(order_id_with_client)
			for name in ['profit', 'target']:
				if order_id_with_client in self.orders[name]:
					self.orders[name].remove(order_id_with_client)
					break
				
		# self.orders[buy_sell_str].pop(order.order_id_with_client)
		# if order.status == 'FAILED':
		# 	order.order_id_with_client = None
		# 	logger.info("resending order:", order, caller=self)
		# 	SingleTask.run(self.resend_order)
	

	async def on_event_position_update_callback(self, position: Position):
		logger.info("position:", position, caller=self)
		# update position on init strategy
		symbol = position.symbol
		if symbol not in self.status_df.index:
			return
		amount = position.amount
		avg_price = position.avg_price
		self.status_df.loc[symbol, 'position'] = amount
		self.status_df.loc[symbol, 'avg_price'] = avg_price
		self.status_df.loc[symbol, 'exposure'] = amount * avg_price
		self.status_df.loc[symbol, 'lot_count'] = amount / self.status_df.loc[symbol, 'lot_size']
		self.status_df.loc[symbol, 'ratio'] = (amount * avg_price / self.budget_notional) if self.budget_notional > 0 else 0.
		self.log_status(symbol)


	async def on_event_asset_update_callback(self, asset: Asset):
		logger.info("asset:", asset, caller=self)
		# update asset(USDT) balance on init strategy
	

	async def on_event_account_update_callback(self, account_info: dict):
		logger.info("account update:", account_info, caller=self)
		# update balance and position on stream update
		balance_info = account_info.get('balances', {})		
		position_info = account_info.get('positions', {})
		unrealized_pnl = 0
		for symbol, pi in position_info.items():
			if symbol not in self.status_df.index:
				continue
			# side = 1 if pi.position_side == 'LONG' else -1
			amount = float(pi.position_amount)
			avg_price = float(pi.entry_price)
			unrealized_pnl += float(pi.unrealized_pnl)
			self.status_df.loc[symbol, 'position'] = amount
			self.status_df.loc[symbol, 'avg_price'] = avg_price
			self.status_df.loc[symbol, 'exposure'] = amount * avg_price
			self.status_df.loc[symbol, 'lot_count'] = amount / self.status_df.loc[symbol, 'lot_size']
			self.status_df.loc[symbol, 'ratio'] = (amount * avg_price / self.budget_notional) if self.budget_notional > 0 else 0
		self.balance = float(balance_info.get('USDT').get('wallet_balance')) + unrealized_pnl
		self.log_all_status()
	

	async def resend_order(self, count: int = None, order_type: str = ORDER_TYPE_LIMIT):
		if count is None:
			count = self.counts.pop()
		elif self.cfg.use_limit_order:
			self.counts.append(count)
		
		try:
			if self.cfg.use_limit_order:
				await self.cancel_all()
			else:
				order_type = ORDER_TYPE_MARKET
		except RuntimeError as e:
			logger.info(e, caller=self)
		finally:
			await self.send_order(count, order_type)
	

	async def cancel_all(self):
		# use tuple(keys()) to avoid RuntimeError('dict changed during iteration')
		# for order_id_with_client in tuple(self.orders.limit.keys()):
		# 	await self.cancel_order(order_id_with_client)	
		# 使用 asyncio.gather 来并行地取消所有订单，并在所有取消操作完成后再退出	
		for name in ['profit', 'target']:
			self.orders[name].clear()
		tasks = [self.cancel_order(order_id_with_client) for order_id_with_client in self.orders.limit.keys()]
		if tasks:
			await asyncio.gather(*tasks)

	
	async def opponent_all_target(self, price_queue=5):
		tasks = [self.queue_to_opponent(self.orders.limit[order_id_with_client], price_queue) for order_id_with_client in self.orders.target if order_id_with_client in self.orders.limit]
		if tasks:
			await asyncio.gather(*tasks)


	async def send_order(self, count, order_type):
		buy_sell = ORDER_ACTION_BUY if count > 0 else ORDER_ACTION_SELL
		shift = self.tick_size * self.cfg.tick_size.shift_count
		price = self.bid_price - shift if count > 0 else self.ask_price + shift
		quantity = round(abs(count) * self.envs.real.account.lot_size, self.lot_size_round)
		# double check if last limit order is filled.
		msg = f"{order_type} {buy_sell} {quantity} @ {price}"
		logger.info("sending order:", msg, caller=self)
		result, error = await self.trader.create_order(buy_sell, price, quantity, order_type)
		logger.info("order sending result:", result, caller=self)
	

	async def cancel_order(self, order_id_with_client):
		order = self.orders.limit[order_id_with_client]
		logger.info("cancelling order:", order, caller=self)
		result, error = await self.trader.revoke_order(order_id_with_client, symbol=order.symbol)
		if error:
			logger.info("order cancelling error:", error, caller=self)
		else:
			logger.info("order cancelling result:", result, caller=self)


	async def queue_to_opponent(self, order: Order, price_queue: int):
		logger.info(f"modifying {order = } into opponent {price_queue}", caller=self)
		result, error = await self.trader.modify_order(order, price_queue=price_queue, is_opponent=True)
		if error:
			logger.info("order modifying error:", error, caller=self)
		else:
			logger.info("order modifying result:", result, caller=self)


if __name__ == '__main__':
	from direct_trading import pred_cfg
	pred_cfg.code_list = pred_cfg.selected_codes
	ckpt_folder = pred_cfg.get_ckpt_folder(make_new_task_folder=False)
	model, cfg = pred_cfg.load_model_cfg_from_ckpt(ckpt_folder, pred_cfg.ckpt_file_name)
	apm = AsyncPortfolioManager(cfg, model)