sd = {'BTCUSDT': {'tick_size': 0.1, 'lot_size': 0.001, 'min_notional': 5.0}, 'ETHUSDT': {'tick_size': 0.01, 'lot_size': 0.001, 'min_notional': 5.0}, 'BCHUSDT': {'tick_size': 0.01, 'lot_size': 0.001, 'min_notional': 5.0}, 'XRPUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'EOSUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'LTCUSDT': {'tick_size': 0.01, 'lot_size': 0.001, 'min_notional': 5.0}, 'TRXUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'ETCUSDT': {'tick_size': 0.001, 'lot_size': 0.01, 'min_notional': 5.0}, 'LINKUSDT': {'tick_size': 0.001, 'lot_size': 0.01, 'min_notional': 5.0}, 'XLMUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'ADAUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'XMRUSDT': {'tick_size': 0.01, 'lot_size': 0.001, 'min_notional': 5.0}, 'DASHUSDT': {'tick_size': 0.01, 'lot_size': 0.001, 'min_notional': 5.0}, 'ZECUSDT': {'tick_size': 0.01, 'lot_size': 0.001, 'min_notional': 5.0}, 'XTZUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'BNBUSDT': {'tick_size': 0.01, 'lot_size': 0.01, 'min_notional': 5.0}, 'ATOMUSDT': {'tick_size': 0.001, 'lot_size': 0.01, 'min_notional': 5.0}, 'ONTUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'IOTAUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'BATUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'VETUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'NEOUSDT': {'tick_size': 0.001, 'lot_size': 0.01, 'min_notional': 5.0}, 'QTUMUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'IOSTUSDT': {'tick_size': 1e-06, 'lot_size': 1.0, 'min_notional': 5.0}, 'THETAUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'ALGOUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'ZILUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'KNCUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'ZRXUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'COMPUSDT': {'tick_size': 0.01, 'lot_size': 0.001, 'min_notional': 5.0}, 'OMGUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'DOGEUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'SXPUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'KAVAUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'BANDUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'RLCUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'WAVESUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'MKRUSDT': {'tick_size': 0.1, 'lot_size': 0.001, 'min_notional': 5.0}, 'SNXUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'DOTUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'DEFIUSDT': {'tick_size': 0.1, 'lot_size': 0.001, 'min_notional': 5.0}, 'YFIUSDT': {'tick_size': 1.0, 'lot_size': 0.001, 'min_notional': 5.0}, 'BALUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'CRVUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'TRBUSDT': {'tick_size': 0.01, 'lot_size': 0.1, 'min_notional': 5.0}, 'RUNEUSDT': {'tick_size': 0.001, 'lot_size': 1.0, 'min_notional': 5.0}, 'SUSHIUSDT': {'tick_size': 0.001, 'lot_size': 1.0, 'min_notional': 5.0}, 'SRMUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'EGLDUSDT': {'tick_size': 0.01, 'lot_size': 0.1, 'min_notional': 5.0}, 'SOLUSDT': {'tick_size': 0.001, 'lot_size': 1.0, 'min_notional': 5.0}, 'ICXUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'STORJUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'BLZUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'UNIUSDT': {'tick_size': 0.001, 'lot_size': 1.0, 'min_notional': 5.0}, 'AVAXUSDT': {'tick_size': 0.001, 'lot_size': 1.0, 'min_notional': 5.0}, 'FTMUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'HNTUSDT': {'tick_size': 0.001, 'lot_size': 1.0, 'min_notional': 5.0}, 'ENJUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'FLMUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'TOMOUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'RENUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'KSMUSDT': {'tick_size': 0.01, 'lot_size': 0.1, 'min_notional': 5.0}, 'NEARUSDT': {'tick_size': 0.001, 'lot_size': 1.0, 'min_notional': 5.0}, 'AAVEUSDT': {'tick_size': 0.01, 'lot_size': 0.1, 'min_notional': 5.0}, 'FILUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'RSRUSDT': {'tick_size': 1e-06, 'lot_size': 1.0, 'min_notional': 5.0}, 'LRCUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'MATICUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'OCEANUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'CVCUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'BELUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'CTKUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'AXSUSDT': {'tick_size': 0.001, 'lot_size': 1.0, 'min_notional': 5.0}, 'ALPHAUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'ZENUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'SKLUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'GRTUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, '1INCHUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'BTCBUSD': {'tick_size': 0.1, 'lot_size': 0.001, 'min_notional': 5.0}, 'CHZUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'SANDUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'ANKRUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'BTSUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'LITUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'UNFIUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'REEFUSDT': {'tick_size': 1e-06, 'lot_size': 1.0, 'min_notional': 5.0}, 'RVNUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'SFPUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'XEMUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'BTCSTUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'COTIUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'CHRUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'MANAUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'ALICEUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'HBARUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'ONEUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'LINAUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'STMXUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'DENTUSDT': {'tick_size': 1e-06, 'lot_size': 1.0, 'min_notional': 5.0}, 'CELRUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'HOTUSDT': {'tick_size': 1e-06, 'lot_size': 1.0, 'min_notional': 5.0}, 'MTLUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'OGNUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'NKNUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'SCUSDT': {'tick_size': 1e-06, 'lot_size': 1.0, 'min_notional': 5.0}, 'DGBUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, '1000SHIBUSDT': {'tick_size': 1e-06, 'lot_size': 1.0, 'min_notional': 5.0}, 'BAKEUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'GTCUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'ETHBUSD': {'tick_size': 0.01, 'lot_size': 0.001, 'min_notional': 5.0}, 'BTCDOMUSDT': {'tick_size': 0.1, 'lot_size': 0.001, 'min_notional': 5.0}, 'TLMUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'BNBBUSD': {'tick_size': 0.01, 'lot_size': 0.01, 'min_notional': 5.0}, 'ADABUSD': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'XRPBUSD': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'IOTXUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'DOGEBUSD': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'AUDIOUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'RAYUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'C98USDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'MASKUSDT': {'tick_size': 0.001, 'lot_size': 1.0, 'min_notional': 5.0}, 'ATAUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'SOLBUSD': {'tick_size': 0.001, 'lot_size': 1.0, 'min_notional': 5.0}, 'FTTBUSD': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'DYDXUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, '1000XECUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'GALAUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'CELOUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'ARUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'KLAYUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'ARPAUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'CTSIUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'LPTUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'ENSUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'PEOPLEUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'ANTUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'ROSEUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'DUSKUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'FLOWUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'IMXUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'API3USDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'GMTUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'APEUSDT': {'tick_size': 0.001, 'lot_size': 1.0, 'min_notional': 5.0}, 'WOOUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'FTTUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'JASMYUSDT': {'tick_size': 1e-06, 'lot_size': 1.0, 'min_notional': 5.0}, 'DARUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'GALUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'AVAXBUSD': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'NEARBUSD': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'GMTBUSD': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'APEBUSD': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'GALBUSD': {'tick_size': 0.001, 'lot_size': 1.0, 'min_notional': 5.0}, 'FTMBUSD': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'DODOBUSD': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'ANCBUSD': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'GALABUSD': {'tick_size': 1e-06, 'lot_size': 1.0, 'min_notional': 5.0}, 'TRXBUSD': {'tick_size': 1e-06, 'lot_size': 1.0, 'min_notional': 5.0}, '1000LUNCBUSD': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'LUNA2BUSD': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'OPUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'DOTBUSD': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'TLMBUSD': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'ICPBUSD': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'WAVESBUSD': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'LINKBUSD': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'SANDBUSD': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'LTCBUSD': {'tick_size': 0.01, 'lot_size': 0.01, 'min_notional': 5.0}, 'MATICBUSD': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'CVXBUSD': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'FILBUSD': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, '1000SHIBBUSD': {'tick_size': 1e-06, 'lot_size': 1.0, 'min_notional': 5.0}, 'LEVERBUSD': {'tick_size': 1e-07, 'lot_size': 1.0, 'min_notional': 5.0}, 'ETCBUSD': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'LDOBUSD': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'UNIBUSD': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'AUCTIONBUSD': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'INJUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'STGUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'FOOTBALLUSDT': {'tick_size': 0.01, 'lot_size': 0.01, 'min_notional': 5.0}, 'SPELLUSDT': {'tick_size': 1e-07, 'lot_size': 1.0, 'min_notional': 5.0}, '1000LUNCUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'LUNA2USDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'AMBBUSD': {'tick_size': 1e-06, 'lot_size': 1.0, 'min_notional': 5.0}, 'PHBBUSD': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'LDOUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'CVXUSDT': {'tick_size': 0.001, 'lot_size': 1.0, 'min_notional': 5.0}, 'ICPUSDT': {'tick_size': 0.001, 'lot_size': 1.0, 'min_notional': 5.0}, 'APTUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'QNTUSDT': {'tick_size': 0.01, 'lot_size': 0.1, 'min_notional': 5.0}, 'APTBUSD': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'BLUEBIRDUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'ETHUSDT_230331': {'tick_size': 0.01, 'lot_size': 0.001, 'min_notional': 5.0}, 'BTCUSDT_230331': {'tick_size': 0.1, 'lot_size': 0.001, 'min_notional': 5.0}, 'FETUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'AGIXBUSD': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'FXSUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'HOOKUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'MAGICUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'TUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'RNDRUSDT': {'tick_size': 0.0001, 'lot_size': 0.1, 'min_notional': 5.0}, 'HIGHUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'MINAUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'ASTRUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}, 'AGIXUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'PHBUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'GMXUSDT': {'tick_size': 0.01, 'lot_size': 0.01, 'min_notional': 5.0}, 'CFXUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'STXUSDT': {'tick_size': 0.0001, 'lot_size': 1.0, 'min_notional': 5.0}, 'COCOSUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'BNXUSDT': {'tick_size': 0.001, 'lot_size': 0.1, 'min_notional': 5.0}, 'ACHUSDT': {'tick_size': 1e-05, 'lot_size': 1.0, 'min_notional': 5.0}}
# print(len(sd), sd)