import os
from matplotlib import pyplot as plt
from numpy import ndarray
import numpy as np
import pandas as pd
from sklearn.metrics import classification_report, confusion_matrix
from aed_quant import asset
from utils.analyze_results import get_mdd_arr
from utils.zigzag import max_drawdown
from core.predictor_config import PredictorConfig
from core.dot_dict import DotDict as dd


def backtest(
        filled_position: ndarray, 
        asset_return: ndarray, 
        position_after_stop: ndarray = None, filled_final_filled_return: ndarray = None, 
        high_return: ndarray = None, 
        low_return: ndarray = None, 
        is_stop_loss: ndarray = None, 
        stop_loss_ratio_per_code: ndarray = None, 
        is_all_stop_loss: ndarray = None, 
        all_stop_loss_ratio: ndarray = None, 
        fee_ratio_dict: dd = None, 
        save_folder: str = "", 
        save_str: str = "", 
        init_position: float = None, 
        n_codes: int = 1, 
        code_list: list = None, 
        is_portfolio: bool = True, 
        is_market_making: bool = False,
        spread_pnl: ndarray = None,         
        spread_fee: ndarray = None, 
        spread_position: ndarray = None, 
        label: ndarray = None,
    ) -> float:

    filled_position = filled_position.flatten()#[-length:]
    asset_return = asset_return.flatten()#[-length:]
    if high_return is None:
        high_return = asset_return
    if low_return is None:
        low_return = asset_return
    if position_after_stop is None:
        position_after_stop = filled_position
    if filled_final_filled_return is None:
        filled_final_filled_return = asset_return
    if init_position is None:
        init_position = np.zeros(n_codes)
    last_positions = np.concatenate([init_position.reshape(1, -1) , position_after_stop.reshape(n_codes, -1).T], axis=0)[:-1].T.flatten()
    position_diff = filled_position - last_positions
    # print(f'{len(position_diff) = }\n{len(pred_positions) = }')
    fee_arr = fee_ratio_dict.limit * abs(position_diff)    
    if not is_market_making:
        # fee_arr[is_market] = fee_ratio_dict.market * abs(position_diff[is_market])
        if fee_ratio_dict is None:
            fee_ratio_dict = PredictorConfig.fee_ratio
        if stop_loss_ratio_per_code is None:
            stop_loss_ratio_per_code = 0
        # elif len(stop_loss_ratio.shape) == 1:
        #     stop_loss_ratio = stop_loss_ratio.reshape(1, -1).repeat(n_codes, axis=0)
        # else:
        #     stop_loss_ratio = 0
        #     stop_per_code = False
        # if is_portfolio:
        #     stop_loss_ratio = stop_loss_ratio.repeat(n_codes)
        if is_stop_loss is None:
            is_stop_loss = 0
        stop_loss_per_step = np.mean(is_stop_loss)

        stop_ratio_mean = np.mean(is_stop_loss * stop_loss_ratio_per_code)
        max_stop_ratio = np.max(is_stop_loss * stop_loss_ratio_per_code)
        min_stop_ratio = np.min(is_stop_loss * stop_loss_ratio_per_code)
        row_str = f'\nstop_loss Per Step: {stop_loss_per_step:.5f}, Stop Ratio Mean: {stop_ratio_mean:.5f}\nMax Stop Ratio: {max_stop_ratio:.5f}, Min Stop Ratio: {min_stop_ratio:.5f}'

    # stop_loss_loss = (-stop_loss_ratio - advised_position * asset_return - fee_ratio * abs(advised_position)) * is_stop_loss
    # position_after_stop = advised_position * is_stop_loss
        spread_pnl = spread_fee = 0
    else:
        spread_pnl = spread_pnl.flatten()
        spread_fee = spread_fee.flatten()
        # spread_position = spread_position.flatten()
        row_str = ""        
        

    before_fee_arr = filled_position * filled_final_filled_return + spread_pnl
    after_fee_arr = before_fee_arr - fee_arr - spread_fee

    cum_before_fee_arr = before_fee_arr.cumsum()
    cum_after_fee_arr = after_fee_arr.cumsum()
    if is_portfolio:
        before_fee = before_fee_arr.reshape(n_codes, -1).sum(axis=0)
        after_fee = after_fee_arr.reshape(n_codes, -1).sum(axis=0)
        # cum_before_fee = before_fee.cumsum()
    else:
        before_fee = before_fee_arr.reshape(n_codes, -1).mean(axis=0)
        after_fee = after_fee_arr.reshape(n_codes, -1).mean(axis=0)
    cum_before_fee = before_fee.cumsum()
    cum_after_fee = after_fee.cumsum()
    (drawdown, drawdown_norm, mdd, mdd_norm) = get_mdd_arr(1 + cum_before_fee)[0]
    sharpe = np.mean(before_fee) / np.std(before_fee)

    end_pnl_before_fee = cum_before_fee[-1]
    end_pnl_after_fee = cum_after_fee[-1]
    fee_per_step = fee_arr.mean()
    max_position = np.max(filled_position)
    min_position = np.min(filled_position)
    abs_position_sum = abs(filled_position).mean()
    position_mean = np.mean(filled_position)
    end_position = filled_position[-1]    
    if label is None:        
        label = np.sign(asset_return) + 1
    pred = np.sign(filled_position) + 1
    report = classification_report(label, pred, label_names=['down', 'neutral', 'up'], digits=4, zero_division=0)
    cm = confusion_matrix(label, pred)
    print(f"Classification Report:\n{report}\nConfusion Matrix:\n{cm}\n")
    print(f"Backtest MDD: {mdd:.5f}, MDD Norm: {mdd_norm:.5f}\nDrawdown: {drawdown:.5f}, Drawdown Norm: {drawdown_norm:.5f}, \nSharpe Ratio: {sharpe:.5f}, Pnl Mean: {np.mean(after_fee_arr):.5f}, Fee Per Step: {fee_per_step:.5f}{row_str}\nEnd PnL Before Fee: {end_pnl_before_fee:.5f}, End PnL After Fee: {end_pnl_after_fee:.5f}\nAbs Position Mean: {abs_position_sum:.5f}, Position Mean: {position_mean:.5f}\nMax Position: {max_position:.5f}, Min Position: {min_position:.5f}, End Position: {end_position:.5f}\n")
    if save_str:

        df = pd.DataFrame(np.stack([filled_position, asset_return, high_return, low_return, before_fee_arr, after_fee_arr, fee_arr]).T, columns=['position', 'asset_return', 'high_return', 'low_return', 'before_fee', 'after_fee', 'fee'])
        l = len(df) // n_codes
        df['index'] = df.index % l
        df['code'] = np.array(code_list).reshape(1, n_codes).repeat(l, axis=0).T.flatten()
        # print(df.head(30))
        df.set_index(['index', 'code'], inplace=True)
        # print(df.head(30))
        df.sort_index(inplace=True)
        # print(df.head(30))
        csv_str = save_str.split('_')[0]
        

        asset_return_arr = asset_return.cumsum()
        asset_price_arr = np.concatenate([np.array([0]),  asset_return_arr])
        pnl_before_fee_arr = np.concatenate([np.array([0]), cum_before_fee_arr])
        pnl_after_fee_arr = np.concatenate([np.array([0]), cum_after_fee_arr])
        chart_positions = np.concatenate([filled_position, np.array([0])])
        x_arr = np.arange(len(asset_price_arr))
        up_arr = np.where(chart_positions > 0, asset_price_arr, np.nan)
        down_arr = np.where(chart_positions < 0, asset_price_arr, np.nan)
        plt.figure(figsize=(20, 11))
        ax = plt.gca()
        plt.title(f"{save_str} Backtest Result")
        plt.xlabel("Step")
        plt.ylabel("Price Change / PnL")
        plt.plot(asset_price_arr, color='grey', alpha=0.5)
        plt.plot(pnl_before_fee_arr, alpha=0.5)
        plt.plot(pnl_after_fee_arr)
        plt.legend(["Asset Price Change", "PnL Before Fee", "PnL After Fee"])
        # ax = plt.twinx()
        plt.scatter(x_arr, down_arr, color='r', marker='v', alpha=(alpha := np.clip(0.2 + abs(chart_positions) * .8, 0, 1)), s=20)
        plt.scatter(x_arr, up_arr, color='g', marker='^', alpha=alpha, s=20)
        interval = len(asset_return_arr) // n_codes
        xticks = np.arange(0, len(asset_price_arr), interval)
        plt.xticks(xticks, rotation=30)
        plt.grid()
        if code_list is not None:
            code_xticks = [code_list[i].replace('USDT','') if i < len(code_list) else '' for i in xticks // interval]
            # print(f"{code_list = }\n{code_xticks = }")
            # plt.xticks(xticks)
            ax2 = plt.twiny()
            ax2.set_xlim(ax.get_xlim())
            ax2.set_xticks(xticks)
            ax2.set_xticklabels(code_xticks, rotation=30)
            ax2.grid()
        # ax.set_ylabel("Position")
        # save_folder = f'{save_folder}/'
        if save_folder != "":
            if not os.path.exists(save_folder):
                os.makedirs(save_folder)
            df.reindex().to_parquet(f"{save_folder}/{csv_str}.pqt")
            save_path = os.path.join(save_folder, f"{save_str}.png")
            plt.savefig(save_path)
        plt.close()
    return before_fee_arr.mean(), fee_arr.mean(), end_pnl_before_fee, fee_arr.sum()


if __name__ == '__main__':
    pred_positions = 2 * np.random.rand(100) - 1
    asset_returns = 2 * np.random.rand(100) - 1
    fee_ratio = 0.001
    backtest(pred_positions, asset_returns, fee_ratio, save_str="Test")