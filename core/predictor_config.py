from datetime import datetime
from functools import partial
from typing import Dict, <PERSON>, Tuple, Union
import multiprocessing
import os
import joblib
from lightgbm import LGBMClassifier
from lightning import LightningModule
from pytorch_lightning import Trainer
import numpy as np
import polars as pl
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
# from sklearn.preprocessing import RobustScaler, StandardScaler
from sklearn.linear_model import LogisticRegression
import torch
import torch.nn as nn
import pytorch_lightning as ptl
import pytorch_lightning.callbacks as cbk
from xgboost import XGBClassifier
from utils.utils import get_datetime_str
from core.cst import ActionType, AllocationType, EmbeddingOperation, FC_Type, NormType, Objective, PositionType, LabelType, SegmentType, TaskPhase, TaskType, Optimizers, EntryExitLogitsEnum, VizType
from core.data_config import DataConfig
from layers.linear_law import LinearLaw
from core.dot_dict import DotDict as dd
import random
# from models.slstm.model import sLSTM
from core.scaler import CrossSectionalScaler, ZScoreScaler, QuantileScaler


class PredictorConfig(DataConfig):
    task_folder = None
    train_save_folder = None
    datetime_str = get_datetime_str()
    script_name = __file__
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    num_cpus = multiprocessing.cpu_count()
    concat_prediction = False
    concat_hidden = False
    prediction_appended = False
    proba_as_prediction = True
    use_cum_prediction = False
    cum_prediction_only = False
    pred_feature_only = False
    cum_feature_only = False
    cum_feature_num = 0
    use_seq_feature_on_planning = False
    concat_train_val = False
    is_planning = False
    is_pretraining = False
    is_eda = False  # 用于标记是否为探索性数据分析模式
    stack_multi_codes_on_slicing = False
    multi_code_onehot_encoding = False
    is_channel_independent = False
    overlapping_on_slicing = True
    optimizer_enum = Optimizers.ADAMW
    allocation_enum = AllocationType.Equal
    optimize_allocation = False
    optimize_sharpe = False
    optimize_profit_ratio = False
    optimize_to_oracle = False
    optimize_cross_entropy = False
    # eps = 1e-8
    # momentum = 0.9
    cosine_lr_scheduler = False
    reduce_lr_on_plateau = dd(
        in_use=False,
        # mode='min',
        factor=0.9,
        patience=2,
        # threshold=1e-4,
        # threshold_mode='rel',
        # cooldown=0,
        min_lr=1e-6,
        # eps=1e-08,
    )
    monitor = 'metric'
    metric_mode = 'max'
    seed = 100

    num_epochs = 10
    patience = 3
    accumulate_grad_batches = 1
    save_top_k = 1 # set to -1 to save all checkpoints
    save_every_n_epochs = None # set to 1 to save every epoch
    num_sanity_val_steps = 0 # set to 0 to disable sanity check
    val_check_interval = None # set to 0 to disable validation
    pretrain_front_end = ''
    pretrain_model_name = 'lgb'
    front_end = 'Lightning'
    for_deployment = False
    force_train = True
    fine_tune = False
    fine_tune_idx_filter = None
    freeze_parameters = False
    resume_from_ckpt = False
    ckpt_file_name = None
    is_meta = False

    task_enum = TaskType.Portfolio
    # task_enum = TaskType.DirectTrading
    position_enum = PositionType.Both
    objective_enum = Objective.PNL
    viz_enum = VizType.HTML
    use_backbone = True
    backbone_name = 'tcn'
    vision_name = 'mbc'
    model_name = 'bct'
    # model_name = 'dva'
    # model_name = 'tsmx'
    inverse_hour_position = False
    use_code_sign = False
    sign_as_position = False
    score_as_predicted_return = True
    directional_balance = False
    align_pos_neg_return = False

    use_softmax = False
    pnl_loss_with_fee_scale = 0
    fee_ratio = dd(
        market=0.0005,
        limit=0.0002,
        stop=0.0005,
    )
    batch_size = 32
    num_classes = 2

    num_orders = 1
    loss_with_oracle = False
    pnl_decay = dd(
        in_use=False,
        threshold = 0.00002, # 0.00001 -> 0.00002 good
    )
    position_punishment = dd(
        in_use=False,
        exponent=3,
    )
    limit_margin_per_code = False
    margin_scale = 1
    skip_step = False
    stop_loss = dd(
        in_use=False,
        std_scale=0,
        learnable=False,
        stop_per_code=False,
        scale=0.01,
        min_ratio=0.005,
        with_position = False,
        train=False,
        val=False,
        test=False,
        predict=False,
        online=False,
    )
    take_profit = dd(
        in_use=False,
        std_scale=0,
        std_window=12,
        learnable=False,
        stop_per_code=False,
        scale=0.01,
        min_ratio=0.005,
        with_position = False,
        train=False,
        val=False,
        test=False,
        predict=False,
        online=False,
    )

    shuffling = dd(
        train=True,
        samplewise=True,
        batchwise=False,
        codewise=False,
        extra_count=0,
    )
    episodic_backward = False
    num_workers = multiprocessing.cpu_count() // 2
    sample_pair = False
    sample_pair_by_step_on_eval = False
    sample_cross_section_in_a_batch = False
    drop_last = False
    pin_memory = True
    lr = dd(
        fine_tune=1e-6,
    )
    learning_rate = 0.0005
    lr_decay = 0.15
    weight_decay = 0.
    dropout_rate = dd(
        backbone=0.,
        fc=0.,
        attention=0.,
        conv=0.,
    )
    use_batch_norm = False
    noise_std = 0.
    zeta = .5
    eta = 1.
    loss_weights = None

    # val_test_with_extra_loss = True
    val_test_with_extra_loss = False

    # use_mad_loss = True
    use_mad_loss = False

    # use_mse_loss = True
    use_mse_loss = False
    # use_bce_loss = True
    use_bce_loss = False


    pred_multi_step = True
    # pred_multi_step = False

    # use_cum_mad_loss = True
    use_cum_mad_loss = False

    # use_cum_mse_loss = True
    use_cum_mse_loss = False
    # use_cum_bce_loss = True
    use_cum_bce_loss = False

    sample_for_each_loss = True

    inverse_image = False
    inverse_seq = False
    seq_len_list = [42]
    seq_len_dict = dd(
        backbone=84,
        conv=84,
    )
    branch_dict = dd(
        backbone=1,
        conv=1,
    )
    selected_seq_len_list = [30, 42]
    pred_len = 12

    outer_step = 1
    inner_step = 1
    step_list = [1]

    label_step = dd(
        train=1,
        train_val=1,
        train_test=1,
        val=1,
        test=1,
        predict=1,
        online=1,
    )

    label_dim = 1

    label_enum = LabelType.PercentChange
    segment_enum = SegmentType.BarCount
    action_enum = ActionType.LongShort
    norm_enum = NormType.MeanStd
    use_segment_open = False
    barrier_range = 0.01
    label = dd(
        clip_quantile_classification=False,
        softmax_advantage_as_position=False,
        diff_as_position=False,
        min_max_softmax=False,
        ema_window=5,
        ema_index=1,
        start_quantile=0.2,
        end_quantile=1.,
        pos_max=1.,
        pos_min=0.,
        neg_max=0.,
        neg_min=-1.,
        csnorm=False,
    )

    equal_position_weight = True
    equal_backtest_weight = False
    tree_depth = 5
    n_estimators = 512
    n_labels = None
    n_columns = None
    n_all_features = None
    n_seq_features = None
    n_selected_features = .8
    drop_null_rows = False
    rolling_zscore = False
    diff_steps = 3000

    base_output_size = 1
    use_entry_idx = False
    use_exit_idx = False

    use_projection = False
    embedding_size = 128
    hidden_size = 128
    channel_size = 128

    beta_schedule = 'linear'
    beta_start = 0.
    beta_end = 1.
    scale = 0.1
    arch_instance = 'res_mbconv'

    channel_mult = 2
    mult = 1
    flatten_before_fc = True
    tcn_fc_size_list = []
    tcn_kernel_size = 2
    num_tcn_stride = 1
    num_block_convs = 4
    num_tcn_blocks = 2
    num_conv_layers = 3
    num_conv_channels = 16
    num_tabm_blocks = 3
    tcn_with_rnn = False
    tcn_with_ffn = False
    tcn_layer_with_temporal_att = False
    att_after_tcn = False
    hyper_connection = dd(
        in_use=False,
        transpose_last2 = True,
        fixed_layer_idx=False,
        layer_idx=1,
        expand_rate=4,
        is_dynamic=True,
    )
    num_heads = 4
    rnn_name = 'gru'
    tcn_before_rnn = False
    mhsa_before_rnn = False
    mhsa_after_rnn = False
    channel_att_after_rnn = False
    num_rnn_layers = 2
    bidirectional = False
    rnn_output_last = True
    fc_enum = FC_Type.MLP
    # fc_enum = FC_Type.VMLP
    # fc_enum = FC_Type.TabM
    backbone_with_fc = True


    num_preprocess_blocks = 1
    num_preprocess_cells = 3
    num_channels_enc = 32
    num_channels_dec = 32
    num_latent_per_group = 8
    groups_per_scale = 2
    num_postprocess_blocks = 1
    num_postprocess_cells = 2

    linear_law = dd(
        in_use=False,
        num_lag=10
    )
    history_feature = None
    feature_scaler = ZScoreScaler()
    label_scaler = CrossSectionalScaler()
    # scaler = StandardScaler()
    # scaler = RobustScaler() # bad
    use_scaler = True
    feature_scaler_got_fit = False
    neutral_scale = False
    scale_per_code = False
    clip_return = dd(
        train=False,
        train_val=False,
        train_test=False,
        val=False,
        test=False,
        predict=False,
        online=False,
        quantile=0.01
    )
    is_regression = True
    zigzag_labelling = dd(
        in_use=False,
        label_type='is_top',
        up_pct=0.001,
    )

    use_triple_barrier = True
    price_change_threshold = 0.001

    meta_thr = 0.
    fracdiff = 0.4
    use_presice_threshold = False

    rolling_window_list = [6, 12, 18, 30, 42, 84]
    # rolling_window_list = [2 ** i * 4 for i in range(6)]

    column_group_dict: Dict[str, List[str]] = dd(
        original=[],
        fracdiff=[],
        cross=[],
        inner=[],
        rolling=[],
        category=[],
        image=[],
    )
    column_idx_dict: Dict[str, List[int]] = dd()
    seq_column_idx_list = []
    non_seq_column_idx_list = []
    category_column_idx_list = []
    image_column_idx_list = []

    emb_op_enum = EmbeddingOperation.Concat

    seq_group_list = ['original', 'fracdiff', 'cross', 'inner']
    non_seq_group_list = ['rolling', 'category']
    scale_group_list = ['fracdiff', 'cross', 'inner', 'rolling']


    qtl_bin_dict = dd()
    qtl_std_dict = dd()

    filter_cfg = dd(
        quantile=0.9,
        n_quantiles=100,
        col_name='range_div_std'
    )

    feature_cfg = dd(
        original=False,
        original_with_btc=True,
        normalize_original=False,
        fracdiff=False,
        cross=True,
        extra=True,
        rolling=False,
        inner=False,
        filter=False,
        category=False,
        non_seq=False,
        onehot_dim=46,
        original_dim=0,
        original_group_size=4,
        market_dim=4,
        vmd_dim=0,
        kalman=False,
        kalman_dim=0,
        adapt=False,
        image=False,
        macd=False,
    )


    image_cfg = dd(
        # in_use=False,
        dim=4,
        step=3,
        sign=False,
        vstack=False,
        multi_branch=False,
        n_mixed_channel_layers=4,
        plot=False,
        ema=False,
        volume=False,
        buy=False,
        buy_sell=False,
        # sell=False,
        delta=False,
        macd=False,
        diff_as_bar=False,
        macd_ema=False,
        rsi=False,
    )

    ta_cfg = dd(
        fast=12,
        slow=26,
        dea=9,
        rsi=14,
        stoch=14,
    )

    decompsition = dd(
        in_use=False,
        kernel_size=7, # even number
        concat=False,
        with_original=False,
        trend_only=False,
    )
    output_attention = False
    d_model = 128
    d_ff = 128
    embed = 'fixed'
    freq = 'h'
    factor = 3
    n_head = 8
    activation = None
    e_layers = 2
    d_layers = 1

    meta_adapt = dd(
        in_use=False,
        on_init=False,
        offline_lr_dict=dd(
            outer=1e-4,
            inner=1e-4,
            feature=2e-4,
            label=2e-4,
            data=2e-4,
        ),
        override_online_lr=False,
        online_lr_dict=dd(
            outer=2e-4,
            inner=2e-4,
            feature=5e-4,
            label=5e-4,
            data=5e-4,
        ),
        double_adapt=True,
        is_osaka=False,
        okasa_gamma=0,
        okasa_lamda=0.5,
        inner_copy_initial_weights=False,
        transform_x=True,
        transform_y=True,
        first_order=False,
        sigma=0.1,
        reg=0.1,
        num_heads=4,
        temperature=1,
        hidden_size=128,
    )

    adapter_cfg = dd(
        in_use=False,
        freeze=False,
        tune_mode='down_up',
        generator_act='sigmoid',
        concept_dim=200,
        bottleneck_dim=32,
        ema=0,
        individual_generator=False,
        wo_clip=False,
    )

    koopa_cfg = dd(
        alpha=0.2,
        mask_spectrum=0,
        # seg_len=10,
        num_blocks=1,
        dynamic_dim=32,
        hidden_dim=32,
        hidden_layers=2,
        multistep=False,
    )

    rolling_retraining = dd(
        in_use=False,
        interval_in_day=31,
        num_epochs=10,
    )

    log_console = dd(
        online=False,
        stream=True,
    )
    ratio_mask = dd(
        in_use=False,
        value=1,
    )
    range_to_mask = []
    history_kline_limit = 1000
    history_file_name = None
    merge_history_data = False
    debug_ticker = False
    use_limit_order = False
    calc_qty_by_position = True
    limit_shift_scale = 0.
    limit_shift_ratio = 0.
    fair_price_shift_spread_for_market_making = True
    with_directional_profit = True
    with_fair_price_shift_spread_loss = False
    use_long_short_grid = False
    spot_grid = dd(
        position=0.5,
        size=0.0001,
    )
    grid_cfg = dd(
        output_count=False,
        max_count=10,
        min_count=1,
        fix_count=8,
        scale=0.1,
    )
    price_queue = None
    base_notional = 100.
    budget_ratio = .9
    max_lot_notional = 20.
    leverage = 1.
    ema_alpha = 0.5
    adapt_distr_shift = dd(
        val=False,
        test=False,
        predict=False,
        ema_alpha=0.1,
        inverse_coef=-1,
    )
    is_backtesting_history = False
    is_live_trading = False
    force_update_trading_pairs = False

    entry_exit_logits_enum = EntryExitLogitsEnum.GUMBEL_SOFTMAX  # 默认使用Gumbel-Softmax
    gumbel_tau = 0.5  # Gumbel-Softmax温度参数

    @property
    def trn_kwargs(self):
        channel, height, width = self.get_image_shape()[-3:]
        return dd(
            img_size=width,
            window_size=[3, 3, 3, None],
            patch_size=4,
            in_chans=channel,
            num_classes=0,
            # embed_dims=[16, 32, 64, 128],
            embed_dims=[32, 64, 128, 256],
            # embed_dims=[48, 96, 192, 384],
            num_heads=[2, 4, 8, 16],
            mlp_ratios=[8, 8, 4, 4],
            qkv_bias=True,
            drop_rate=0.,
            attn_drop_rate=0.,
            drop_path_rate=0.,
            norm_layer=partial(nn.LayerNorm, eps=1e-6),
            depths=[1, 1, 1, 1],
            sr_ratios=[8, 4, 2, 1],
            num_stages=4,
            fixed_pool_size=None,
        )


    @property
    def feature_len(self):
        return self.inner_step * self.seq_len


    @property
    def label_len(self):
        return self.inner_step * self.pred_len


    @property
    def head_cutoff_len(self):
        max_ta_window = self.rolling_window_list[-1] ** self.feature_cfg.rolling
        ta_cut_off = max_ta_window - 1
        if self.feature_cfg.image or self.feature_cfg.macd:
            macd = self.ta_cfg
            ta_cut_off = max(macd.slow - 1 + macd.dea - 1, ta_cut_off)
        return ta_cut_off + self.feature_len - 1


    @property
    def cutoff_len(self):
        return self.head_cutoff_len + self.label_len


    @property
    def category_size_dict(self):
        return dd(
            code_index=self.n_codes,
            day_of_week=7,
            hour_of_day=6,
        )


    @property
    def strategy(self):
        task_str = self.get_task_prefix()
        sim = f'_sim{self.sim_base_interval}min' if self.sim_base_interval is not None else ''
        return f'{self.task_enum.value}_{task_str}_{self.interval_cfg.base}min{sim}'


    @property
    def online_mask(self):
        result = np.ones(len(self.code_list))
        for code in self.online_excluded_codes:
            if code in self.code_list:
                print(f'Excluding {code} from online trading')
                result[self.code_list.index(code)] = 0
        return result.astype(np.float32)


    @property
    def custom_mask(self):
        if len(self.selected_indices) > 0:
            result = np.zeros(len(self.code_list))
            result[self.selected_indices] = 1
        else:
            result = np.ones(len(self.code_list))
        return result.astype(np.float32)


    @property
    def online_trading(self):
        log_folder = f'logs/{self.strategy}'
        return dd(
            RABBITMQ=dd(
                host='127.0.0.1',
                port=5672,
                username='guest',
                password='guest',
            ),
            LOG=dd(
                console=self.log_console.online,
                level='INFO',
                path=log_folder,
                name=f'{get_datetime_str()}.log',
                clear=False,
                backup_count=0,
            ),
            platform=self.platform,
            strategy=self.strategy,
            symbol=self.symbol,
            base_notional=self.base_notional,
            is_live_trading=self.is_live_trading,
            account_id=self.account_id,
            access_key=self.access_key,
            secret_key=self.secret_key,

        )


    @property
    def market_stream(self):
        return dd(
            RABBITMQ=dd(
                host='127.0.0.1',
                port=5672,
                username='guest',
                password='guest',
            ),
            LOG=dd(
                console=self.log_console.stream,
                level='INFO',
                path=f'logs/market_service/',
                name=f'{self.strategy}_{get_datetime_str()}.log',
                clear=False,
                backup_count=0,
            ),
            MARKETS=dd(
                binance_future=dd(
                    symbols=self.code_list,
                    channels=[
                        # 'orderbook',
                        'kline',
                    ],
                    orderbook_length=20,
                    kline_intervals=[
                        self.get_kline_interval_str()[-1]
                        # "1m",
                        # "3m"
                    ],
                ),
            ),
        )

    def get_range_masked_position(self, position_arr: np.ndarray | torch.Tensor):

        mask = self.get_range_mask(position_arr)
        return position_arr * mask

    def get_range_mask(self, position_arr, range_to_mask: list = None):
        if range_to_mask is None:
            range_to_mask = self.range_to_mask
        mask = np.ones_like(position_arr, dtype=np.float32)
        if len(range_to_mask) > 0:
            for low, high in range_to_mask:
                mask[(position_arr >= low) & (position_arr <= high)] = 0.
        return mask


    def get_history_kline_path(self, history_file_name: str = None, ):
        folder = 'history_kline'
        if not os.path.exists(folder):
            os.makedirs(folder)
        start_date = self.train_start_date.replace('.', '')

        if history_file_name is None:
            task_str = '' if self.load_all_codes else f'_{self.get_task_prefix()}'
            history_file_name = f'{self.datetime_str}_s{start_date}{task_str}_{self.interval_cfg.base}min'
        return os.path.join(folder, f'{history_file_name}.pkl')


    def get_history_kline_dict(self, history_file_name: str = None):
        if history_file_name is None:
            history_file_name = self.history_file_name
        if self.merge_history_data or self.is_backtesting_history:
            path = self.get_history_kline_path(history_file_name)
            return joblib.load(path)
        else:
            return dd()


    def get_model(self, name: str) -> nn.Module:
        exec(f'from models.{name}.model import Model')
        return eval('Model')(self)

    def get_backbone(self, name: str) -> nn.Module:
        exec(f'from models.{name}.backbone import Backbone')
        return eval('Backbone')(self)

    def get_vision(self, name: str) -> nn.Module:
        exec(f'from models.{name}.vision import Vision')
        return eval('Vision')(self)

    def set_seed(self, seed: int = None):
        if seed is None:
            seed = self.seed
        random.seed(seed)
        np.random.seed(seed)
        torch.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True


    def get_image_shape(self) -> tuple[int, int, int, int, int, int, int]:
        seq_len = self.seq_len_dict.conv
        image_step = self.image_cfg.step
        width = seq_len * image_step
        ohlc = volume = macd = rsi = 0

        if self.image_cfg.vstack:
            channel_list = [ohlc, volume, macd]
            if self.image_cfg.rsi:
                channel_list.append(rsi)
                rsi = seq_len * channel
            channel = len(channel_list)
            volume = macd = ohlc = height = seq_len * channel
        elif self.vision_name == 'trn':
            channel = len(self.step_list)
            if image_step == 3:
                ohlc = seq_len
                volume = macd = seq_len - 1
            elif image_step == 2:
                ohlc = seq_len - 2
                volume = macd = seq_len // 2
            elif image_step == 1:
                ohlc = seq_len // 2 - 2
                volume = macd = seq_len // 4
            height = ohlc + volume + macd + 2
        else:
            channel = len(self.step_list)
            if self.image_cfg.rsi:
                if image_step == 3:
                    ohlc = seq_len
                    volume = macd = rsi = seq_len // 2
                elif image_step == 2:
                    ohlc = volume = seq_len
                    macd = rsi = seq_len // 2
                elif image_step == 1:
                    # ohlc = volume = seq_len // 2
                    # macd = rsi = seq_len // 3
                    ohlc = volume = macd = rsi = seq_len // 2
                blank = 3
            else:
                if image_step == 3:
                    ohlc = seq_len * 2
                    volume = macd = seq_len
                elif image_step == 2:
                    ohlc = volume = macd = seq_len
                elif image_step == 1:
                    ohlc = volume = macd = seq_len // 2
                blank = 2
            height = ohlc + volume + macd + rsi + blank
        return ohlc, volume, macd, rsi, image_step, channel, height, width


    def get_blank_image(self) -> np.ndarray:
        ohlc, volume, macd, image_step, channel, height, width = self.get_image_shape()
        return np.zeros((height, width), dtype=np.float32)


    def get_flatten_size(self) -> int:
        prediction_size = self.pred_len
        if self.use_cum_prediction and not self.cum_prediction_only:
            prediction_size += self.pred_len

        if self.pred_feature_only:
            return prediction_size

        return prediction_size + self.input_size * self.seq_len ** self.use_seq_feature_on_planning


    @property
    def seq_len(self) -> int:
        return max(self.seq_len_dict.values())
        return max(self.seq_len_list)


    @property
    def fc_input_size(self) -> int:
        return self.pred_len * self.input_size

    @property
    def input_size(self) -> int:
        n_seq_features = self.n_seq_features + (self.feature_cfg.vmd_dim + self.feature_cfg.kalman_dim) * self.feature_cfg.original

        if self.cum_feature_num > 0:
            cum_size = self.cum_feature_num
            if not self.cum_feature_only:
                cum_size += n_seq_features
        else:
            cum_size = n_seq_features

        if self.task_enum in [TaskType.Portfolio, TaskType.PortfolioDoubleAdapt, TaskType.MarketMakingPortfolio]:
            cum_size *= self.n_codes
        if self.is_channel_independent:
            cum_size //= self.n_seq_features
        elif self.decompsition.concat and self.decompsition.in_use:
            cum_size *= (2 + self.decompsition.with_original)

        return cum_size # + self.multi_code_onehot_encoding * self.n_codes

    @property
    def output_size(self) -> int:
        if self.task_enum in [TaskType.DirectTrading, TaskType.DirectTradingDoubleAdapt, TaskType.CTAStratege, TaskType.ClassificationTrading, TaskType.Rank]:
            result = self.base_output_size + self.stop_loss.in_use * self.stop_loss.learnable + self.take_profit.in_use * self.take_profit.learnable + self.use_exit_idx * self.label_len + self.use_entry_idx * self.label_len
        elif self.task_enum in [TaskType.Portfolio, TaskType.PortfolioDoubleAdapt]:
            result = self.n_codes * (1 + (self.limit_shift_scale != 0) + self.skip_step + self.stop_loss.in_use * self.stop_loss.stop_per_code * self.stop_loss.learnable + self.limit_margin_per_code) + self.stop_loss.in_use * (1 - self.stop_loss.stop_per_code) * self.stop_loss.learnable
        elif self.task_enum == TaskType.MarketMaking:
            result = 4 - self.fair_price_shift_spread_for_market_making
        elif self.task_enum == TaskType.MarketMakingPortfolio:
            result = self.n_codes * (self.limit_margin_per_code + 4)
        elif self.task_enum == TaskType.GridTrading:
            result = self.base_output_size + 1
            if self.grid_cfg.output_count:
                result += self.grid_cfg.max_count - self.grid_cfg.min_count

        # elif self.task_enum == TaskType.ClassificationTrading:
        #     result = 3
        else:
            result = self.pred_len if self.pred_multi_step else 1
        return result


    @property
    def pretrain_model_dict(self):
        return dd(
            lgb=LGBMClassifier,
            xgb=XGBClassifier,
            lda=LinearDiscriminantAnalysis,
        )

    @property
    def rnn_class_dict(self):
        return dd(
                rnn=nn.RNN,
                lstm=nn.LSTM,
                gru=nn.GRU,
                # slstm=sLSTM,
            )

    def get_save_folder(
            self,
            front_end: str = None,
            task_name: str = None,
            model_name: str = None,
            ):
        if model_name == 'rnn':
            model_name = f'{model_name}_{self.rnn_name}'
        return f'ckpt/{front_end}/{task_name}/{model_name}'


    def get_last_ckpt_file_name(self, ckpt_folder: str = None, is_keras: bool = False):
        if ckpt_folder is None:
            ckpt_folder = self.get_ckpt_folder()
        if is_keras:
            if (file_list := os.listdir(ckpt_folder)):
                return max(file_list, key=lambda f: int(f.split('.')[0].split('-')[-1]))
            return None
        # 获取ckpt_folder下含有last和.ckpt的文件名
        ckpt_files = [f for f in os.listdir(ckpt_folder) if f.endswith('.ckpt') and 'last' in f]
        if len(ckpt_files) == 0:
            ckpt_files = [f for f in os.listdir(ckpt_folder) if f.endswith('.ckpt')]
            if len(ckpt_files) == 1:
                return ckpt_files[0]
            else:
                return None
        elif len(ckpt_files) == 1:
            return ckpt_files[0]
        else:
            ckpt_files.remove('last.ckpt')
            result = max(ckpt_files, key=lambda f: int(f.split('-v')[1].split('.')[0]))
            return result


    def get_ckpt_folder(
            self,
            interval_type_str: str = None,
            interval_unit: int = None,
            front_end: str = None,
            task_name: str = None,
            model_name: str = None,
            save_folder: str = None,
            seed: int = None,
            seq_len: int = None,
            pred_len: int = None,
            make_new_task_folder: bool = True,
            ):

        if interval_type_str is None:
            interval_type_str = self.interval_type.get_index_item()
        if interval_unit is None:
            interval_unit = self.interval_cfg.base
        if front_end is None:
            front_end = self.front_end.lower()
        if task_name is None:
            task_name = self.task_enum.value
        if model_name is None:
            model_name = self.model_name
        if save_folder is None:
            save_folder = self.get_save_folder(front_end, task_name, model_name)
        prefix = self.get_task_prefix()
        if self.position_enum != PositionType.Both:
            position_str = f'_{self.position_enum.value}'
        else:
            position_str = ''
        if seed is None:
            seed = self.seed
        if seq_len is None:
            # seq_len = '-'.join([str(sl) for sl in self.seq_len_list])
            seq_len = self.seq_len
        if pred_len is None:
            pred_len = self.pred_len


        if make_new_task_folder:
            if self.merge_history_data:
                val_end_date = self.history_file_name.split('_s')[0]
            else:
                val_end_date = self.val_end_date.replace(".", "")
            task_folder = f'{self.datetime_str}_v{val_end_date}'
            if self.execute_phase.train:
                task_folder += '_train'
            elif self.execute_phase.val:
                task_folder += '_val'
            elif self.execute_phase.test:
                task_folder += '_test'
        else:
            # if self.resume_from_ckpt:
            task_folder = self.task_folder

        if self.use_projection:
            projection = f'_proj{self.embedding_size}'
        else:
            projection = ''
        if self.code_sort_by_quote:
            code_sort = f'_quote'
        else:
            code_sort = ''
        if self.episodic_backward:
            episodic_backward = f'_epi'
        else:
            episodic_backward = ''
        if self.optimize_sharpe:
            sharpe = f'_sharpe'
        else:
            sharpe = ''
        if self.limit_margin_per_code:
            limit_margin = f'_lmpc'
        else:
            limit_margin = ''
        if self.shuffling.codewise:
            code_shuffle = f'_sfc'
        else:
            code_shuffle = ''
        # if self.task_enum == TaskType.PortfolioDoubleAdapt:
        #     double_adapt = f'_da'
        # else:
        #     double_adapt = ''
        take_profit_learnable = f'-{self.take_profit.min_ratio + self.take_profit.scale:.3f}' if self.take_profit.learnable else ''
        if self.take_profit.in_use:
            if self.take_profit.std_scale > 0:
                take_profit = f'_profit-std{self.take_profit.std_scale:.1f}'
            else:
                take_profit = f'_profit{self.take_profit.min_ratio:.3f}{take_profit_learnable}'
        else:
            take_profit = ''
        stop_loss_learnable = f'-{self.stop_loss.min_ratio + self.stop_loss.scale:.3f}' if self.stop_loss.learnable else ''
        if self.stop_loss.in_use:
            if self.stop_loss.std_scale > 0:
                stop_loss = f'_sl-std{self.stop_loss.std_scale:.1f}'
            else:
                stop_loss = f'_sl{self.stop_loss.min_ratio:.3f}{stop_loss_learnable}'
        else:
            stop_loss = ''
        non_seq = '_nsq' if self.feature_cfg.non_seq else ''
        log_label = '_loglbl' if self.label_enum == LabelType.LogReturn else ''
        raw_norm = '_rawnorm' if self.label_enum == LabelType.RawNormalized else ''
        att_after_rnn = '_att' if self.channel_att_after_rnn else ''
        okasa = f'_osaka' if self.meta_adapt.is_osaka else ''
        original = '_orig' if self.feature_cfg.original else ''
        # if self.adapt_distr_shift:
        #     adapt_distr = f'_ads'
        # else:

        adapt_distr = ''
        if not self.augment_data.train:
            noaug = f'_noaug'
        elif not self.augment_data.rev:
            noaug = f'_norev'
        else:
            noaug = ''
        if not self.feature_cfg.extra:
            noextra = f'_noextra'
        else:
            noextra = ''
        interval_unit_str = f'{interval_unit}min'
        train_range = f'{self.train_start_date.replace(".", "")}t{self.train_end_date.replace(".", "")}'
        quote_start_date = self.quote_start_date or self.train_start_date
        quote_end_date = self.quote_end_date or self.train_end_date
        if (quote_start_date != self.train_start_date) or (quote_end_date != self.train_end_date):
            quote_range = f'_{quote_start_date.replace(".", "")}q{quote_end_date.replace(".", "")}'
        else:
            quote_range = ''
        return f'{save_folder}/{prefix}{position_str}_{interval_unit_str}_seed{seed}_s{seq_len}p{pred_len}_hsz{self.hidden_size}_lr{self.learning_rate}_bsz{self.batch_size}_{train_range}{quote_range}{projection}{code_sort}{sharpe}{limit_margin}{code_shuffle}{take_profit}{stop_loss}{original}{non_seq}{log_label}{raw_norm}{att_after_rnn}{okasa}{adapt_distr}{episodic_backward}{noaug}{noextra}/{task_folder}'#


    def get_task_prefix(self):
        if self.load_all_codes:
            prefix = f'qql{self.quote_quantile:.2f}'
        elif self.n_codes == 1:
            prefix = self.symbol
        else:
            prefix = f'crypto{self.n_codes}'
        return prefix


    def make_ckpt_file_name(self, is_keras: bool = False):
        metric_str = ''
        if is_keras:
            metric_str = '.keras'
        elif self.execute_phase.val:
            metric_str = f'-{{{self.monitor}:.3f}}'
            # metric_str = '-{metric:.3f}'
        result = '{epoch}'
        # if self.save_top_k > 0:
        #     result += metric_str
        return result


    def reset_datetime_str(self):
        self.datetime_str = get_datetime_str()


    def get_state_dict_path(
            self,
            interval_type_str: str = None,
            interval_unit: int = None,
            model_name: str = None,
            symbol: str = None,
            seed: int = None,
            seq_len: int = None,
            pred_len: int = None,
            ):
        state_dict_folder = self.get_ckpt_folder(interval_type_str, interval_unit, model_name, symbol, seed, seq_len, pred_len)
        return f'{state_dict_folder}/best.std'


    def get_trainer(self):
        return Trainer(
        accelerator=self.device,
        check_val_every_n_epoch=1,
        num_sanity_val_steps=self.num_sanity_val_steps,
        val_check_interval=self.val_check_interval,
        max_epochs=self.num_epochs,
        # n_max_steps=10000000,
        log_every_n_steps=0,
        inference_mode=False,
        callbacks=self.get_lightning_callbacks(),
        accumulate_grad_batches=self.accumulate_grad_batches,
    )


    def get_predictor(self, data_module: ptl.LightningDataModule = None, other = None) -> Union[ptl.LightningModule, LogisticRegression, XGBClassifier, LGBMClassifier, LinearDiscriminantAnalysis]:
        if self.pretrain_front_end == 'Lightning':
            model_name = self.model_name
            class_name = f'Lightning{model_name.upper()}'
            exec(f'from models.{model_name}.model import {class_name}')
            ckpt_folder = self.get_ckpt_folder()
            if not os.path.exists(ckpt_folder):
                assert data_module is not None
                trainer = self.get_trainer()
                lm = eval(class_name)(self).to(self.device)
                trainer.fit(lm, data_module)
                return lm
            ckpt_file_name = self.get_last_ckpt_file_name()
            print(f'{ckpt_file_name = }\n{class_name = }')
            ckpt_path = f'{ckpt_folder}/{ckpt_file_name}'
            return eval(class_name).load_from_checkpoint(ckpt_path, cfg=self)
        else:
            model_name = self.pretrain_model_name
            return self.pretrain_model_dict[model_name]()


    def get_lightning_callbacks(self):
        return [
            cbk.ModelCheckpoint(
                dirpath=self.get_ckpt_folder(),
                filename=self.make_ckpt_file_name(),
                monitor=self.monitor,
                verbose=False,
                save_last='link',
                save_top_k=self.save_top_k,
                save_weights_only=False,
                mode=self.metric_mode,
                auto_insert_metric_name=True,
                every_n_train_steps=None,
                train_time_interval=None,
                every_n_epochs=None,
                save_on_train_epoch_end=None,
                enable_version_counter=True,
            ),
            cbk.EarlyStopping(
                monitor=self.monitor,
                min_delta=0,
                patience=self.patience,
                verbose=True,
                mode=self.metric_mode,
                strict=True,
                check_finite=True,
                stopping_threshold=None,
                divergence_threshold=None,
                check_on_train_epoch_end=False,
                log_rank_zero_only=False
            )
        ]


    def get_keras_callbacks(self):
        from keras import callbacks as kcbk
        return [
            kcbk.ModelCheckpoint(
                filepath=self.get_ckpt_folder() + '/' + self.make_ckpt_file_name(is_keras=True),
                monitor=self.monitor,
                verbose=False,
                save_best_only=True,
                save_weights_only=False,
                mode='min',
                save_freq="epoch",
                initial_value_threshold=None,
            ),
            kcbk.EarlyStopping(
                monitor=self.monitor,
                min_delta=0.0001,
                patience=self.patience,
                verbose=True,
                mode=self.metric_mode,
                baseline=None,
                restore_best_weights=False,
                start_from_epoch=0,
            )
        ]


    def get_torch_model(self, class_name: str):
        return self.torch_model_dict[class_name]


    def get_linear_law_model(self, data_set: np.ndarray):
        return LinearLaw(
            data_set,
            self.linear_law.num_lag,
            # vertical_step_on_sampled=self.linear_law.num_lag,
        )


    def get_meta_model(self):
        return LogisticRegression(max_iter=200)


    def load_model_from_ckpt(self, ckpt_folder: str = None, ckpt_file_name: str = None) -> LightningModule:
        task_name = self.task_enum.value
        class_name = f'Lightning{task_name.upper()}'
        exec(f'from models.lightning_{task_name} import {class_name}')
        if ckpt_folder is None:
            ckpt_folder = self.get_ckpt_folder()
        if ckpt_file_name is None:
            ckpt_file_name = self.get_last_ckpt_file_name()
        if 'last' in ckpt_file_name:
            ckpt_file_name = os.readlink(os.path.join(ckpt_folder, ckpt_file_name)).split('/')[-1]

        print(f'{ckpt_file_name = }\n{class_name = }')
        ckpt_path = os.path.join(ckpt_folder, ckpt_file_name)
        model = eval(class_name).load_from_checkpoint(ckpt_path)
        return model


    def load_cfg_from_ckpt(self, ckpt_folder: str = None, ckpt_file_name: str = None):
        if ckpt_folder is None:
            ckpt_folder = self.get_ckpt_folder()
        model = self.load_model_from_ckpt(ckpt_folder, ckpt_file_name)
        cfg: PredictorConfig = model.hparams.cfg
        cfg.load_scaler(ckpt_folder)
        return cfg


    def load_model_cfg_from_ckpt(self, ckpt_folder: str = None, ckpt_file_name: str = None) -> Tuple[LightningModule]:
        if ckpt_folder is None:
            ckpt_folder = self.get_ckpt_folder()
        model = self.load_model_from_ckpt(ckpt_folder, ckpt_file_name)
        cfg: PredictorConfig = model.hparams.cfg
        model.cfg = cfg
        cfg.load_scaler(ckpt_folder)
        return model, cfg


    def save_scaler(self, save_folder: str = None):
        if save_folder is None:
            save_folder = self.get_ckpt_folder(make_new_task_folder=True)
        if not os.path.exists(save_folder):
            os.makedirs(save_folder)
        save_path = os.path.join(save_folder, 'scaler.pkl')
        joblib.dump(self.feature_scaler, save_path)
        print(f'Scaler saved to: {save_path}')


    def load_scaler(self, ckpt_folder: str = None):
        if ckpt_folder is None:
            ckpt_folder = self.get_ckpt_folder()
        save_path = os.path.join(ckpt_folder, 'scaler.pkl')
        if os.path.exists(save_path) and self.use_backbone :
            self.feature_scaler = joblib.load(save_path)
            self.feature_scaler_got_fit = True
            print(f'Scaler loaded from: {save_path}')
        else:
            print(f'Scaler not found in: {save_path}')


    def replace_symbol_in_code_list(self, source: str, label: str):
        if source in self.code_list:
            idx = self.code_list.index(source)
            self.code_list[idx] = label
        else:
            print(f'{source} not in code_list')
    # def load_cfg_from_ckpt(self, ckpt_folder: str = None):
    #     if ckpt_folder is None:
    #         ckpt_folder = self.get_ckpt_folder()
    #     cfg_path = os.path.join(ckpt_folder, 'cfg.pkl')
    #     if os.path.exists(cfg_path):
    #         self = joblib.load(cfg_path)
    #         print(f'Config loaded from {cfg_path}')
    #     else:
    #         print(f'Config not found in {cfg_path}')

    # def save_cfg_to_ckpt(self, ckpt_folder: str = None):
    #     if ckpt_folder is None:
    #         ckpt_folder = self.get_ckpt_folder()
    #     cfg_path = os.path.join(ckpt_folder, 'cfg.pkl')
    #     joblib.dump(self, cfg_path)
    #     print(f'Config saved to {cfg_path}')

if __name__ == '__main__':
    cfg = PredictorConfig()
    cfg.fc_enum = FC_Type.VBLL
    pass