#%%
from core.data_module import KlineDataModule
from scalping import pred_cfg
import matplotlib.pyplot as plt

# pred_cfg.filter_quantile = 0
dm = KlineDataModule(pred_cfg)

#%%
tr_df = dm.dataset_dict.train.feature_df.reset_index()
#%%
vl_df = dm.dataset_dict.val.feature_df.reset_index()
#%%
tr_df[tr_df.code == 'ETHUSDT']['range_ratio_zscore'].plot()
#%%
vl_df[vl_df.code == 'ETHUSDT']['range_ratio_zscore'].plot()
#%%
tr_df[tr_df.code == 'ETHUSDT']['atr_zscore'].plot()
#%%
vl_df[vl_df.code == 'ETHUSDT']['atr_zscore'].plot()
#%%
tr_df[tr_df.code == 'ETHUSDT']['range_div_std_zscore'].plot()
#%%
vl_df[vl_df.code == 'ETHUSDT']['range_div_std_zscore'].plot()
#%%
tr_df['range_div_std_zscore'].plot()
#%%
vl_df['range_div_std_zscore'].plot()
#%%
tr_df[tr_df.code == 'ETHUSDT']['range_div_std'].plot()
#%%
vl_df[vl_df.code == 'ETHUSDT']['range_div_std'].plot()
#%%
tr_df['range_div_std'].plot()
#%%
vl_df['range_div_std'].plot()
#%%
# rr = tr_df['range_ratio']
# plt.hist(rr, bins=100)
#%%
tr_df['range_div_std_qtl_value']



#%%
vl_df = dm.dataset_dict.val.feature_df.reset_index()
vl_df[vl_df.code == 'ETHUSDT']['range_ratio'].plot()
#%%
tr_df.columns
