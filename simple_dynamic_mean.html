
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>动态均值图表</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .info { 
                background: #f0f8ff; 
                padding: 15px; 
                border-radius: 5px; 
                margin-bottom: 20px;
                border-left: 4px solid #007acc;
            }
        </style>
    </head>
    <body>
        <div class="info">
            <h3>使用方法：</h3>
            <ul>
                <li><strong>单击图例</strong>：显示/隐藏对应的数据线</li>
                <li><strong>双击图例</strong>：只显示该线，隐藏其他</li>
                <li><strong>红色虚线</strong>：选中线条的实时均值</li>
            </ul>
        </div>
        <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-2.35.2.min.js"></script>                <div id="0b1c8030-7219-4feb-a372-97ecc14b67a2" class="plotly-graph-div" style="height:600px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById("0b1c8030-7219-4feb-a372-97ecc14b67a2")) {                    Plotly.newPlot(                        "0b1c8030-7219-4feb-a372-97ecc14b67a2",                        [{"line":{"color":"blue","width":2},"mode":"lines","name":"Series A","opacity":0.8,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00","2024-01-31T00:00:00","2024-02-01T00:00:00","2024-02-02T00:00:00","2024-02-03T00:00:00","2024-02-04T00:00:00","2024-02-05T00:00:00","2024-02-06T00:00:00","2024-02-07T00:00:00","2024-02-08T00:00:00","2024-02-09T00:00:00","2024-02-10T00:00:00","2024-02-11T00:00:00","2024-02-12T00:00:00","2024-02-13T00:00:00","2024-02-14T00:00:00","2024-02-15T00:00:00","2024-02-16T00:00:00","2024-02-17T00:00:00","2024-02-18T00:00:00","2024-02-19T00:00:00"],"y":[0.04967141530112327,0.0358449851840048,0.10061383899407406,0.25291682463487664,0.22950148716254304,0.206087791467625,0.36400907301836416,0.44075254593365504,0.39380510734015983,0.4480611116987563,0.40171934241751006,0.35514636706048436,0.37934259421708777,0.18801456975130798,0.015522786500004687,-0.040705966424092584,-0.14198907845753497,-0.11056434519800759,-0.2013667527501287,-0.34259712288365785,-0.19603224599150243,-0.218609876040156,-0.2118570555713636,-0.3543318741927093,-0.40877014664522754,-0.39767788767424095,-0.5127772454164712,-0.475207443581904,-0.5352713125737845,-0.5644406875531122,-0.6246113487760518,-0.4393835303251581,-0.4407332527989515,-0.5465043456945415,-0.4642498544842226,-0.5863342194813248,-0.5654478599808492,-0.7614148723688268,-0.8942334772586699,-0.8745473536717575,-0.8007006956722165,-0.7835638675532194,-0.7951286957920435,-0.8252390653509724,-0.9730912643877151,-1.0450756852271859,-1.0911395623231646,-0.9854273397012729,-0.9510655107444268,-1.1273695262807002],"type":"scatter"},{"line":{"color":"green","width":2},"mode":"lines","name":"Series B","opacity":0.8,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00","2024-01-31T00:00:00","2024-02-01T00:00:00","2024-02-02T00:00:00","2024-02-03T00:00:00","2024-02-04T00:00:00","2024-02-05T00:00:00","2024-02-06T00:00:00","2024-02-07T00:00:00","2024-02-08T00:00:00","2024-02-09T00:00:00","2024-02-10T00:00:00","2024-02-11T00:00:00","2024-02-12T00:00:00","2024-02-13T00:00:00","2024-02-14T00:00:00","2024-02-15T00:00:00","2024-02-16T00:00:00","2024-02-17T00:00:00","2024-02-18T00:00:00","2024-02-19T00:00:00"],"y":[0.04861259540921925,-0.009149746653228229,-0.11068804669912202,-0.018936603372991842,0.13571332500140082,0.27540534286883056,0.1495227143854348,0.1031408580077526,0.1528303727182872,0.2991621417866411,0.2272860061098476,0.19943715961027503,0.0334869135093708,-0.1459440801027298,-0.024065206743600104,0.17937079754202334,0.16856927930497326,0.3190992139887769,0.373344617745922,0.27657665455515335,0.33078599538141545,0.5614914803513108,0.556117574484818,0.790814122856919,0.39785235724345736,0.521137732899741,0.5341947931354667,0.4893436905655866,0.503107807045912,0.20497246985577805,0.17202168668015128,0.22558857240691324,0.4472726791181406,0.36953214637709353,0.2482581059431154,0.1729945494054349,0.310304867060746,0.35961753350969866,0.28015350294464286,0.3571436179116463,0.3717052503138523,0.5170019988937857,0.41169403481218286,0.36254471282251766,0.303728489852694,0.08420124763287623,0.12861928919256263,0.167777580019546,0.16854459851591513,0.1333565285096431],"type":"scatter"},{"line":{"color":"orange","width":2},"mode":"lines","name":"Series C","opacity":0.8,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00","2024-01-31T00:00:00","2024-02-01T00:00:00","2024-02-02T00:00:00","2024-02-03T00:00:00","2024-02-04T00:00:00","2024-02-05T00:00:00","2024-02-06T00:00:00","2024-02-07T00:00:00","2024-02-08T00:00:00","2024-02-09T00:00:00","2024-02-10T00:00:00","2024-02-11T00:00:00","2024-02-12T00:00:00","2024-02-13T00:00:00","2024-02-14T00:00:00","2024-02-15T00:00:00","2024-02-16T00:00:00","2024-02-17T00:00:00","2024-02-18T00:00:00","2024-02-19T00:00:00"],"y":[-0.1698444890460497,-0.22032192777789278,-0.2614476697611051,-0.3577209420676994,-0.37707522746762046,-0.32858912464987583,-0.1022468165046122,-0.08129747896479153,-0.05039143207805981,-0.05932494196999988,-0.2895774878058849,-0.2927591528597909,-0.28553152766686773,0.010057525831366598,-0.013025789942368103,0.023159891137665395,0.0189944787730362,-0.12124688574130765,0.01589185200049481,0.1061238159229077,0.20104764956807333,0.09192115499270466,0.26025647230503657,0.09203434476996286,0.1624571960259953,0.4253118711231927,0.3064475121075101,0.23849178455517744,0.2504499483656944,0.1900328698717505,0.003953258143814586,0.012180815120537865,-0.11529563052659472,-0.058464538850372935,-0.1687954469584293,0.017196681643675438,-0.076793713436673,-0.11544109538135408,-0.017819029296993708,-0.16552274726906835,-0.1382275551165728,0.01862957539731855,-0.17426841275002874,-0.15211234972615223,-0.1209264144163414,-0.027107669803064158,-0.175541755108434,-0.33399654867854717,-0.27136356080451945,-0.23572540001653713],"type":"scatter"},{"line":{"color":"purple","width":2},"mode":"lines","name":"Series D","opacity":0.8,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00","2024-01-31T00:00:00","2024-02-01T00:00:00","2024-02-02T00:00:00","2024-02-03T00:00:00","2024-02-04T00:00:00","2024-02-05T00:00:00","2024-02-06T00:00:00","2024-02-07T00:00:00","2024-02-08T00:00:00","2024-02-09T00:00:00","2024-02-10T00:00:00","2024-02-11T00:00:00","2024-02-12T00:00:00","2024-02-13T00:00:00","2024-02-14T00:00:00","2024-02-15T00:00:00","2024-02-16T00:00:00","2024-02-17T00:00:00","2024-02-18T00:00:00","2024-02-19T00:00:00"],"y":[0.020039428027670125,0.04775528478742818,-0.006646692938851083,0.011933602834029201,0.0353794006979237,-0.021768712744185723,0.12749324814739482,0.16539988182033782,0.07009560204412593,0.12261989073483232,0.04464535711664659,0.10761212541604276,0.2002997717366351,0.13464518626849825,0.211715276608044,0.2447377507629639,0.3105025635625231,0.4622460021748389,0.4426149528946093,0.38231605974601013,0.31115490537596824,0.24589008257873318,0.23972194582560485,0.2670141038109364,0.2891493677573379,0.3553240276802198,0.35636417903045237,0.4726469052030377,0.4514743585440012,0.6690878918711707,0.7191412796923712,0.6505686751790686,0.5648972753341795,0.6034950685536343,0.5856180457275663,0.6427380852549336,0.6805970952208171,0.6747707822082674,0.607027284762795,0.4858395067879258,0.45011831062256413,0.5186302141684419,0.5357577136988583,0.4360986114018992,0.44995308546999374,0.4807784758483007,0.41006988095221003,0.42236788942785225,0.42702458690353223,0.33558696307708236],"type":"scatter"},{"line":{"color":"brown","width":2},"mode":"lines","name":"Series E","opacity":0.8,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00","2024-01-31T00:00:00","2024-02-01T00:00:00","2024-02-02T00:00:00","2024-02-03T00:00:00","2024-02-04T00:00:00","2024-02-05T00:00:00","2024-02-06T00:00:00","2024-02-07T00:00:00","2024-02-08T00:00:00","2024-02-09T00:00:00","2024-02-10T00:00:00","2024-02-11T00:00:00","2024-02-12T00:00:00","2024-02-13T00:00:00","2024-02-14T00:00:00","2024-02-15T00:00:00","2024-02-16T00:00:00","2024-02-17T00:00:00","2024-02-18T00:00:00","2024-02-19T00:00:00"],"y":[0.064401724862691,0.16534293960897317,0.36029216338052306,0.5499765327468056,0.30199604651452916,0.13318753932980706,0.22589388742736582,0.3183753585915634,0.411083942126652,1.1045756104445017,1.2073359023692718,1.4117377176017796,1.583458035030556,1.7007084602655997,1.6439599962303375,1.7805744559191257,1.6414659173023627,1.598838568089161,1.5114731294799224,1.5262104745694605,1.942849016570692,1.6067412819041773,1.7302681161715898,1.4399792593574525,1.3550315235153545,1.5510426309694805,1.5626130344066638,1.3686189743793886,1.2398643067125943,1.362191901520836,1.2307259078117514,1.269688453936507,1.2778913851191935,1.1606033225501464,1.5465132586287051,1.6606186826459473,1.296093017047578,1.329654793706075,1.2105332300477651,1.3639712303110854,1.2213174973931993,1.2006649379291574,1.2915626481456397,1.4473985830962617,1.231345229826222,1.1711350073748512,1.0856448513658792,0.968045589502611,1.2858273527532085,1.3587240607261806],"type":"scatter"},{"line":{"color":"red","dash":"dash","width":3},"mode":"lines","name":"Selected Mean","visible":false,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00","2024-01-31T00:00:00","2024-02-01T00:00:00","2024-02-02T00:00:00","2024-02-03T00:00:00","2024-02-04T00:00:00","2024-02-05T00:00:00","2024-02-06T00:00:00","2024-02-07T00:00:00","2024-02-08T00:00:00","2024-02-09T00:00:00","2024-02-10T00:00:00","2024-02-11T00:00:00","2024-02-12T00:00:00","2024-02-13T00:00:00","2024-02-14T00:00:00","2024-02-15T00:00:00","2024-02-16T00:00:00","2024-02-17T00:00:00","2024-02-18T00:00:00","2024-02-19T00:00:00"],"y":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],"type":"scatter"}],                        {"template":{"data":{"barpolar":[{"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmapgl":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmapgl"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"white","showlakes":true,"showland":true,"subunitcolor":"#C8D4E3"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"white","polar":{"angularaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""},"bgcolor":"white","radialaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"yaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"zaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"baxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"bgcolor":"white","caxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2}}},"title":{"text":"动态均值图表演示"},"xaxis":{"title":{"text":"日期"}},"yaxis":{"title":{"text":"数值"}},"legend":{"title":{"text":"点击选择\u002f取消选择"}},"height":600,"hovermode":"x unified"},                        {"responsive": true}                    )                };                            </script>        </div>
        
    <script>
        // 原始数据
        const data = {'Series A': [0.04967141530112327, 0.0358449851840048, 0.10061383899407406, 0.25291682463487664, 0.22950148716254304, 0.206087791467625, 0.36400907301836416, 0.44075254593365504, 0.39380510734015983, 0.4480611116987563, 0.40171934241751006, 0.35514636706048436, 0.37934259421708777, 0.18801456975130798, 0.015522786500004687, -0.040705966424092584, -0.14198907845753497, -0.11056434519800759, -0.2013667527501287, -0.34259712288365785, -0.19603224599150243, -0.218609876040156, -0.2118570555713636, -0.3543318741927093, -0.40877014664522754, -0.39767788767424095, -0.5127772454164712, -0.475207443581904, -0.5352713125737845, -0.5644406875531122, -0.6246113487760518, -0.4393835303251581, -0.4407332527989515, -0.5465043456945415, -0.4642498544842226, -0.5863342194813248, -0.5654478599808492, -0.7614148723688268, -0.8942334772586699, -0.8745473536717575, -0.8007006956722165, -0.7835638675532194, -0.7951286957920435, -0.8252390653509724, -0.9730912643877151, -1.0450756852271859, -1.0911395623231646, -0.9854273397012729, -0.9510655107444268, -1.1273695262807002], 'Series B': [0.04861259540921925, -0.009149746653228229, -0.11068804669912202, -0.018936603372991842, 0.13571332500140082, 0.27540534286883056, 0.1495227143854348, 0.1031408580077526, 0.1528303727182872, 0.2991621417866411, 0.2272860061098476, 0.19943715961027503, 0.0334869135093708, -0.1459440801027298, -0.024065206743600104, 0.17937079754202334, 0.16856927930497326, 0.3190992139887769, 0.373344617745922, 0.27657665455515335, 0.33078599538141545, 0.5614914803513108, 0.556117574484818, 0.790814122856919, 0.39785235724345736, 0.521137732899741, 0.5341947931354667, 0.4893436905655866, 0.503107807045912, 0.20497246985577805, 0.17202168668015128, 0.22558857240691324, 0.4472726791181406, 0.36953214637709353, 0.2482581059431154, 0.1729945494054349, 0.310304867060746, 0.35961753350969866, 0.28015350294464286, 0.3571436179116463, 0.3717052503138523, 0.5170019988937857, 0.41169403481218286, 0.36254471282251766, 0.303728489852694, 0.08420124763287623, 0.12861928919256263, 0.167777580019546, 0.16854459851591513, 0.1333565285096431], 'Series C': [-0.1698444890460497, -0.22032192777789278, -0.2614476697611051, -0.3577209420676994, -0.37707522746762046, -0.32858912464987583, -0.1022468165046122, -0.08129747896479153, -0.05039143207805981, -0.05932494196999988, -0.2895774878058849, -0.2927591528597909, -0.28553152766686773, 0.010057525831366598, -0.013025789942368103, 0.023159891137665395, 0.0189944787730362, -0.12124688574130765, 0.01589185200049481, 0.1061238159229077, 0.20104764956807333, 0.09192115499270466, 0.26025647230503657, 0.09203434476996286, 0.1624571960259953, 0.4253118711231927, 0.3064475121075101, 0.23849178455517744, 0.2504499483656944, 0.1900328698717505, 0.003953258143814586, 0.012180815120537865, -0.11529563052659472, -0.058464538850372935, -0.1687954469584293, 0.017196681643675438, -0.076793713436673, -0.11544109538135408, -0.017819029296993708, -0.16552274726906835, -0.1382275551165728, 0.01862957539731855, -0.17426841275002874, -0.15211234972615223, -0.1209264144163414, -0.027107669803064158, -0.175541755108434, -0.33399654867854717, -0.27136356080451945, -0.23572540001653713], 'Series D': [0.020039428027670125, 0.04775528478742818, -0.006646692938851083, 0.011933602834029201, 0.0353794006979237, -0.021768712744185723, 0.12749324814739482, 0.16539988182033782, 0.07009560204412593, 0.12261989073483232, 0.04464535711664659, 0.10761212541604276, 0.2002997717366351, 0.13464518626849825, 0.211715276608044, 0.2447377507629639, 0.3105025635625231, 0.4622460021748389, 0.4426149528946093, 0.38231605974601013, 0.31115490537596824, 0.24589008257873318, 0.23972194582560485, 0.2670141038109364, 0.2891493677573379, 0.3553240276802198, 0.35636417903045237, 0.4726469052030377, 0.4514743585440012, 0.6690878918711707, 0.7191412796923712, 0.6505686751790686, 0.5648972753341795, 0.6034950685536343, 0.5856180457275663, 0.6427380852549336, 0.6805970952208171, 0.6747707822082674, 0.607027284762795, 0.4858395067879258, 0.45011831062256413, 0.5186302141684419, 0.5357577136988583, 0.4360986114018992, 0.44995308546999374, 0.4807784758483007, 0.41006988095221003, 0.42236788942785225, 0.42702458690353223, 0.33558696307708236], 'Series E': [0.064401724862691, 0.16534293960897317, 0.36029216338052306, 0.5499765327468056, 0.30199604651452916, 0.13318753932980706, 0.22589388742736582, 0.3183753585915634, 0.411083942126652, 1.1045756104445017, 1.2073359023692718, 1.4117377176017796, 1.583458035030556, 1.7007084602655997, 1.6439599962303375, 1.7805744559191257, 1.6414659173023627, 1.598838568089161, 1.5114731294799224, 1.5262104745694605, 1.942849016570692, 1.6067412819041773, 1.7302681161715898, 1.4399792593574525, 1.3550315235153545, 1.5510426309694805, 1.5626130344066638, 1.3686189743793886, 1.2398643067125943, 1.362191901520836, 1.2307259078117514, 1.269688453936507, 1.2778913851191935, 1.1606033225501464, 1.5465132586287051, 1.6606186826459473, 1.296093017047578, 1.329654793706075, 1.2105332300477651, 1.3639712303110854, 1.2213174973931993, 1.2006649379291574, 1.2915626481456397, 1.4473985830962617, 1.231345229826222, 1.1711350073748512, 1.0856448513658792, 0.968045589502611, 1.2858273527532085, 1.3587240607261806]};
        const dates = ['2024-01-01 00:00:00', '2024-01-02 00:00:00', '2024-01-03 00:00:00', '2024-01-04 00:00:00', '2024-01-05 00:00:00', '2024-01-06 00:00:00', '2024-01-07 00:00:00', '2024-01-08 00:00:00', '2024-01-09 00:00:00', '2024-01-10 00:00:00', '2024-01-11 00:00:00', '2024-01-12 00:00:00', '2024-01-13 00:00:00', '2024-01-14 00:00:00', '2024-01-15 00:00:00', '2024-01-16 00:00:00', '2024-01-17 00:00:00', '2024-01-18 00:00:00', '2024-01-19 00:00:00', '2024-01-20 00:00:00', '2024-01-21 00:00:00', '2024-01-22 00:00:00', '2024-01-23 00:00:00', '2024-01-24 00:00:00', '2024-01-25 00:00:00', '2024-01-26 00:00:00', '2024-01-27 00:00:00', '2024-01-28 00:00:00', '2024-01-29 00:00:00', '2024-01-30 00:00:00', '2024-01-31 00:00:00', '2024-02-01 00:00:00', '2024-02-02 00:00:00', '2024-02-03 00:00:00', '2024-02-04 00:00:00', '2024-02-05 00:00:00', '2024-02-06 00:00:00', '2024-02-07 00:00:00', '2024-02-08 00:00:00', '2024-02-09 00:00:00', '2024-02-10 00:00:00', '2024-02-11 00:00:00', '2024-02-12 00:00:00', '2024-02-13 00:00:00', '2024-02-14 00:00:00', '2024-02-15 00:00:00', '2024-02-16 00:00:00', '2024-02-17 00:00:00', '2024-02-18 00:00:00', '2024-02-19 00:00:00'];
        const seriesNames = ['Series A', 'Series B', 'Series C', 'Series D', 'Series E'];
        
        document.addEventListener('DOMContentLoaded', function() {
            const plotDiv = document.getElementsByClassName('plotly-graph-div')[0];
            if (!plotDiv) return;
            
            // 监听图例点击
            plotDiv.on('plotly_legendclick', function(eventData) {
                setTimeout(updateMean, 50);
                return true; // 允许默认行为
            });
            
            // 监听图例双击
            plotDiv.on('plotly_legenddoubleclick', function(eventData) {
                setTimeout(updateMean, 50);
                return true;
            });
            
            function updateMean() {
                const traces = plotDiv.data;
                const meanTraceIndex = traces.length - 1;
                
                // 找出可见的数据线
                const visibleIndices = [];
                for (let i = 0; i < traces.length - 1; i++) {
                    if (traces[i].visible !== 'legendonly') {
                        visibleIndices.push(i);
                    }
                }
                
                if (visibleIndices.length <= 1) {
                    // 没有可见线或只有一条线，隐藏均值线（单条线的均值没有意义）
                    console.log('隐藏均值线 - 可见线条数量:', visibleIndices.length);
                    Plotly.restyle(plotDiv, {'visible': false}, [meanTraceIndex]);
                    return;
                }
                
                // 计算均值
                const meanValues = [];
                for (let i = 0; i < dates.length; i++) {
                    let sum = 0;
                    let count = 0;
                    
                    for (const idx of visibleIndices) {
                        sum += traces[idx].y[i];
                        count++;
                    }
                    
                    meanValues.push(count > 0 ? sum / count : 0);
                }
                
                // 更新均值线
                const selectedNames = visibleIndices.map(i => seriesNames[i]);
                const meanName = '均值 (' + selectedNames.length + '条线: ' + selectedNames.join(', ') + ')';

                console.log('显示均值线 - 可见线条数量:', visibleIndices.length, '选中:', selectedNames);

                Plotly.restyle(plotDiv, {
                    'y': [meanValues],
                    'visible': true,
                    'name': meanName
                }, [meanTraceIndex]);
            }
            
            // 初始化
            updateMean();
        });
    </script>
    
    </body>
    </html>
    