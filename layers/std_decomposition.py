import numpy as np

def STD_decomposition(data, period, is_reminder):
    """
    STD_decomposition - seasonal-trend-dispersion decomposition of time series
    
    Parameters:
    data (np.array): Time series, 1 x N or N x 1
    period (int): Length of the seasonal cycle
    is_reminder (bool): False for STD (without reminder), True for STDR (with reminder)
    
    Returns:
    tuple: (S, T, D, R) where
        S (np.array): Seasonal component, 1 x N
        T (np.array): Trend component, 1 x N
        D (np.array): Dispersion component, 1 x N
        R (np.array): Reminder component, empty for STD, 1 x N for STDR
    """
    y = np.ravel(data)
    n = period
    N = len(y)
    K = N // n
    
    if N % n != 0:
        print(f'Length of the series ({N}) should be a multiple of the seasonal period ({n})')

    yy = y.reshape((n, K))

    # Trend
    ym = np.mean(yy, axis=0)
    q = np.tile(ym, (n, 1))
    T = q.flatten()

    # Dispersion
    yd = np.sqrt(np.sum((yy - q) ** 2, axis=0))
    q = np.tile(yd, (n, 1))
    D = q.flatten()

    # Seasonal for STD
    S = (y - T) / D

    # Reminder for STD
    R = np.array([])

    # Seasonal and reminder for STDR
    if is_reminder:
        q = S.reshape((n, K))
        sp = np.mean(q, axis=1)
        S = np.tile(sp, K)
        R = y - (S * D + T)
    
    return S, T, D, R
