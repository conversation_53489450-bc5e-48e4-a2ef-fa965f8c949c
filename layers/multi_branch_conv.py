import numpy as np
import torch
from torch import nn
from torchsummary import summary


class MultiBranchConv(nn.Module):
    def __init__(self, image_shape: tuple, in_channels: int = 1, out_channels: int = 64, n_layers: int = 3, n_branches: int = 1, step: int = 3, dropout_rate: float = 0.1, n_mixed_channel_layers: int = 0):
        super().__init__()
        self.image_shape = image_shape
        padding = (1, 1)
        if step == 3:
            kernel_size_list = [9, 7, 5]
            stride_list = [3, 1, 1]
        elif step == 2:
            kernel_size_list = [7, 5, 3]
            stride_list = [2, 1, 1]
        elif step == 1:
            # kernel_size_list = [21, 11, 7, 3]
            # kernel_size_list = [21, 15, 7, 3]
            # kernel_size_list = [19, 13, 7, 3] # 1.03 @dr0.55
            # kernel_size_list = [17, 13, 7, 3]
            # kernel_size_list = [15, 13, 7, 3]
            kernel_size_list = [15, 11, 9, 3] # 1.00 @dr0.55
            # kernel_size_list = [15, 11, 7, 3]
            # kernel_size_list = [15, 15, 9, 2]
            # kernel_size_list = [13, 11, 9, 3]
            # kernel_size_list = [11, 9, 7, 5]
            # kernel_size_list = [9, 7, 5, 3]
            # kernel_size_list = [7, 5, 3, 3]
            # kernel_size_list = [5, 3, 3, 3]
            # kernel_size_list = [3, 3, 3, 3]
            # kernel_size_list = [7, 5, 3]
            # kernel_size_list = [5, 5, 3]
            # kernel_size_list = [5, 3, 3]
            # kernel_size_list = [5, 5, 5]
            stride_list = [1, 1, 1, 1]
            # stride_list = [1, 1, 1]
            padding = (0, 0)
            n_layers = 4
        
        self.branch_list = nn.ModuleList()
        for i in range(n_branches):            
            self.branch_list.append(KlineConvBlock(image_shape, in_channels, out_channels, n_layers, kernel_size_list, stride_list, padding, n_mixed_channel_layers=n_mixed_channel_layers, dropout_rate=dropout_rate))
        self.output_size = sum([np.prod(b.output_shape) for b in self.branch_list]).astype(int)

    def forward(self, x):
        if x.shape[1] > 1 and len(self.branch_list) > 1:
            return torch.cat([b(x[:, i: i + 1]).flatten(1) for i, b in enumerate(self.branch_list)], dim=1)
        return torch.cat([b(x).flatten(1) for b in self.branch_list], dim=1)


class KlineConvBlock(nn.Module):
    def __init__(self, image_shape, in_channels, out_channels, n_layers=3, kernel_size_list=[9, 7, 5], stride_list=[3, 1, 1], padding=(1, 1), dilation=(1, 1), n_mixed_channel_layers=0, bias=True, padding_mode='zeros', activation_layer=nn.LeakyReLU, dropout_rate = 0.1, add_residual=False):
        super().__init__()
        norm_layer = nn.BatchNorm2d
        # activation_layer = nn.Mish
        # add_residual = True
        if add_residual:
            self.downsample_list = nn.ModuleList()
            self.block_list = nn.ModuleList()
        else:
            self.downsample_list = None
            self.block_list = None
        sample = torch.randn(1, in_channels, *image_shape)        
        self.sequential = nn.Sequential()            
        for i, (kernel, stride) in enumerate(zip(kernel_size_list, stride_list)):
            if i > 0:
                in_channels_var = out_channels                
                out_channels *= 2
                
                # norm = nn.Identity()
            else:
                in_channels_var = in_channels
                proj_in = sample
                # norm = norm_layer(out_channels)
            if i < n_layers - 1 - n_mixed_channel_layers:
                groups = in_channels
            else:
                groups = 1
            kernel_size = (kernel, kernel)
            stride_size = (stride, stride)
            
            conv = nn.Conv2d(in_channels_var, out_channels, kernel_size, stride_size, padding, dilation, groups, bias, padding_mode)
            nn.init.xavier_uniform_(conv.weight)
            self.sequential.append(conv)
            
            norm = norm_layer(out_channels) if i == 0 else nn.Identity()
            # norm = nn.Identity()
            self.sequential.append(norm)

            act = activation_layer(inplace=True)
            self.sequential.append(act)

            dropout = nn.Dropout(dropout_rate)
            self.sequential.append(dropout)

            output = self.sequential(sample)
            w_pad = output.shape[-1] % 2 != 0
            h_pad = output.shape[-2] % 2 != 0
            pooling = nn.MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=(int(h_pad), int(w_pad)))
            self.sequential.append(pooling)
            if add_residual:
                block = nn.Sequential(conv, norm, act, dropout, pooling)
                self.block_list.append(block)

                output = self.sequential(sample)
                if i % 2 == 1:
                    projector = nn.Linear(np.prod(proj_in.shape), np.prod(output.shape))
                    self.downsample_list.append(projector)
                    nn.init.xavier_uniform_(projector.weight)
                    proj_in = output

        output = self.sequential(sample)
        kernel_size = output.shape[-2:]
        
        if kernel_size[-1] > 1:
            w_pad = output.shape[-1] % 2 != 0
            h_pad = output.shape[-2] % 2 != 0
            self.pooling = nn.MaxPool2d(kernel_size=(2, 2), stride=(2, 2), padding=(int(h_pad), int(w_pad)))
        # elif kernel_size[-2] > 1:
        #     self.pooling = nn.AdaptiveAvgPool2d((1, 1))
        else:
            self.pooling = nn.Identity()
        self.sequential.append(self.pooling)

        if add_residual:
            del self.sequential
        # else:
        #     self.sequential.append(self.pooling)
        # if kernel_size[0] > 1:
        #     self.block.append(nn.Conv2d(out_channels, out_channels * 2, kernel_size=kernel_size))
        #     nn.init.xavier_uniform_(self.block[-1].weight)
        #     # self.block.append(norm_layer(out_channels * 2))
        #     self.block.append(activation_layer(inplace=True))
        
        
        summary(self, input_size=sample.shape[1:], batch_size=sample.shape[0], device=str(sample.device))
        output = self(sample)
        self.output_shape = output.shape

        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if self.downsample_list is not None:
            residual = x
            for i, block in enumerate(self.block_list):
                x = block(x)
                if i % 2 == 1:
                    residual = self.downsample_list[i // 2](residual.flatten(1)).reshape(x.shape)
                    x += residual
            return self.pooling(x)
        else:
            return self.sequential(x)


if __name__ == '__main__':
    image_step = 1
    # seq_len = 96
    seq_len = 84
    # seq_len = 72
    # image_step = 2
    # seq_len = 54
    # image_step = 3
    # seq_len = 42
    if image_step == 3:       
        ohlc = seq_len * 2
        volume = macd = seq_len
    elif image_step == 2:                
        ohlc = volume = macd = seq_len        
    elif image_step == 1:
        ohlc = volume = macd = seq_len // 2
    height = ohlc + volume + macd + 2
    width = seq_len * image_step
    n_layers = 4
    n_channels = 1
    image_shape = (height, width)
    model = MultiBranchConv(image_shape, n_channels, 64, n_layers, step=image_step)
    print(model)
    x = torch.randn(256, n_channels, *image_shape)
    summary(model, x.shape[1:], batch_size=x.shape[0], device='cpu')
    out = model(x)
    print(out.shape)
    print(out.mean())
        