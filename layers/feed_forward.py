import torch
from torch import nn
from torch import Tensor
from torch.nn import functional as F

class FeedForward(nn.Module):
    """
    A Feed Forward (FFN) block with two linear layers.
    """
    def __init__(self, input_size: int, hidden_size: int, dropout: float = 0.15) -> None:
        super().__init__()
        self.linear_1 = nn.Linear(input_size, hidden_size)
        self.dropout_1 = nn.Dropout(dropout)
        self.linear_2 = nn.Linear(hidden_size, input_size)
        self.activation = nn.Tanh()
        self.layer_norm = nn.LayerNorm(input_size)
        self.dropout_2 = nn.Dropout(dropout)
        self.input_size = input_size
        self.hidden_size = hidden_size
    
    def forward(self, x: Tensor) -> Tensor:
        """
        Args:
            x: Input tensor with shape (batch_size, seq_len, input_size).
        Returns:
            Tensor with shape (batch_size, seq_len, input_size).
        """
        # output = self.layer_norm(x)
        output = self.linear_1(x)
        output = self.activation(output)
        output = self.dropout_1(output)
        output = self.linear_2(output)
        output = self.dropout_2(output)

        return x + output