import torch
import torch.nn as nn
import torch.nn.functional as F


class HyperConnection(nn.Module):
    def __init__(self, dim, layer_idx, expand_rate, is_dynamic, transpose_last2=False, device=None, **kwargs):
        super(HyperConnection, self).__init__()
        self.expand_rate = expand_rate
        self.layer_idx = layer_idx
        self.is_dynamic = is_dynamic
        self.static_beta = nn.Parameter(torch.ones((expand_rate,), device=device))  # (expand_rate,)
        init_alpha0 = torch.zeros((expand_rate, 1), device=device)  # (expand_rate, 1)
        init_alpha0[layer_idx % expand_rate, 0] = 1.
        self.static_alpha = nn.Parameter(torch.cat([init_alpha0, torch.eye((expand_rate), device=
        device)], dim=1))  # (expand_rate, expand_rate + 1)
        if self.is_dynamic:
            self.dynamic_alpha_fn = nn.Parameter(torch.zeros((dim, expand_rate+1), device=device))  # (dim, expand_rate + 1)
            self.dynamic_alpha_scale = nn.Parameter(torch.ones(1, device=device) * 0.01)  # (1,)
            self.dynamic_beta_fn = nn.Parameter(torch.zeros((dim, ), device=device))  # (dim,)
            self.dynamic_beta_scale = nn.Parameter(torch.ones(1, device=device) * 0.01)  # (1,)
            self.layer_norm = nn.LayerNorm(dim)  # (dim,)
        self.transpose_last2 = transpose_last2

    def width_connection(self, h):
        # get alpha and beta
        if self.is_dynamic:
            norm_h = self.layer_norm(h) # (batch, seq_len, dim)
        if self.is_dynamic:
            wc_weight = norm_h @ self.dynamic_alpha_fn  # (batch, seq_len, expand_rate + 1)
            wc_weight = F.tanh(wc_weight) 
            dynamic_alpha = wc_weight * self.dynamic_alpha_scale  # (batch, seq_len, expand_rate + 1)
            alpha = dynamic_alpha.unsqueeze(2).repeat(1, 1, self.expand_rate, 1) + self.static_alpha.repeat(h.shape[0], h.shape[1], 1, 1) # (batch, seq_len, expand_rate, expand_rate + 1)
        else:
            alpha = self.static_alpha.repeat(h.shape[0], 1, 1)  # (batch, seq_len, expand_rate, expand_rate + 1)
        if self.is_dynamic:
            dc_weight = norm_h @ self.dynamic_beta_fn  # (batch, seq_len)
            dc_weight = F.tanh(dc_weight)
            dynamic_beta = dc_weight * self.dynamic_beta_scale
            beta = dynamic_beta.unsqueeze(2).repeat(1, 1, self.expand_rate) + self.static_beta.repeat(h.shape[0], h.shape[1], 1)  # (batch, seq_len, expand_rate)
        else:
            beta = self.static_beta.repeat(h.shape[0], h.shape[1], 1)  # (batch, seq_len, expand_rate)
        # width connection
        mix_h = alpha.transpose(-1, -2) @ h.unsqueeze(2).repeat(1, 1, self.expand_rate, 1) # (batch, seq_len, expand_rate + 1, dim)
        return mix_h, beta
    

    def depth_connection(self, h_o:torch.Tensor, mix_h:torch.Tensor, beta:torch.Tensor):
        # mix_h: (batch, seq_len, expand_rate + 1, dim)
        # beta: (batch, seq_len, expand_rate)
        # h_o: (batch, seq_len, dim)
        # h: (batch, seq_len, expand_rate, dim)
        h = torch.einsum("blh,bln->blnh", h_o, beta) + mix_h[..., 1:, :]
        return h.sum(dim=2)
    

    def forward(self, h: torch.Tensor):
        if self.transpose_last2:
            h = h.transpose(-1, -2)
        # shape = h.shape
        # ls = len(shape)
        # if ls == 3:
        #     h = h.view(shape[0], -1)
        mix_h, beta = self.width_connection(h)
        h = self.depth_connection(h, mix_h, beta)
        # if ls == 3:
        #     h = h.view(shape[0], shape[1], -1)
        if self.transpose_last2:
            h = h.transpose(-1, -2)        
        return h
    

if __name__ == '__main__':
    device = 'cpu'
    batch_size = 2
    seq_len = 3
    dim = 10
    expand_rate = 4
    model = HyperConnection(dim, 1, expand_rate, True, device=device)
    x = torch.randn(batch_size, seq_len, dim).to(device)
    y = model(x)
    print(y.shape)