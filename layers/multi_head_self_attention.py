import math
from torch import nn
import torch
from torch.nn.functional import scaled_dot_product_attention, softmax

def scaled_dot_product(q, k, v, mask=None):
    d_k = q.size()[-1]
    attn_logits = torch.matmul(q, k.transpose(-2, -1))
    attn_logits = attn_logits / math.sqrt(d_k)
    if mask is not None:
        attn_logits = attn_logits.masked_fill(mask == 0, -9e15)
    attention = softmax(attn_logits, dim=-1)
    values = torch.matmul(attention, v)
    return values, attention


class MultiheadSelfAttention(nn.Module):
    def __init__(self, input_dim, embed_dim, output_dim, num_heads):
        super().__init__()
        assert embed_dim % num_heads == 0, "Embedding dimension must be 0 modulo number of heads."

        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads

        # Stack all weight matrices 1...h together for efficiency
        # Note that in many implementations you see "bias=False" which is optional
        self.qkv_proj = nn.Linear(input_dim, 3 * embed_dim)
        self.o_proj = nn.Linear(embed_dim, output_dim)

        self._reset_parameters()

    def _reset_parameters(self):
        # Original Transformer initialization, see PyTorch documentation
        nn.init.xavier_uniform_(self.qkv_proj.weight)
        self.qkv_proj.bias.data.fill_(0)
        nn.init.xavier_uniform_(self.o_proj.weight)
        self.o_proj.bias.data.fill_(0)

    def forward(self, x, mask=None, return_attention=False):
        batch_size, seq_length, embed_dim = x.size()
        qkv = self.qkv_proj(x)

        # Separate Q, K, V from linear output
        qkv = qkv.reshape(batch_size, seq_length, self.num_heads, 3 * self.head_dim)
        qkv = qkv.permute(0, 2, 1, 3)  # [Batch, Head, SeqLen, Dims]
        q, k, v = qkv.chunk(3, dim=-1)

        if return_attention:
            # Determine value outputs
            values, attention = scaled_dot_product(q, k, v, mask)
            values = values.permute(0, 2, 1, 3)  # [Batch, SeqLen, Head, Dims]
            values = values.reshape(batch_size, seq_length, embed_dim)
            o = self.o_proj(values)
            return o, attention
        else:
            # Determine value outputs
            values = scaled_dot_product_attention(q, k, v, mask)
            values = values.permute(0, 2, 1, 3)  # [Batch, SeqLen, Head, Dims]
            values = values.reshape(batch_size, seq_length, embed_dim)
            o = self.o_proj(values)
            return o
        

class Model(MultiheadSelfAttention):
    def __init__(self, cfg):
        if __name__ == '__main__':
            from core.predictor_config import PredictorConfig
            cfg: PredictorConfig = cfg
        input_dim = cfg.input_size
        output_dim = cfg.output_size
        embed_dim = cfg.embedding_size
        num_heads = cfg.num_heads
        super().__init__(input_dim, output_dim, embed_dim, num_heads)