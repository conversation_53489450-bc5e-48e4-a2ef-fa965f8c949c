"""This sub package collects the functions and classes which calculate the linear laws and generate the LLT features. """
from typing import <PERSON>ple
from matplotlib import pyplot as plt
import numpy as np
from polars import col


def get_embedding(raw: np.ndarray, num_lag: int, sampling_step=1, vertical_step_on_sampled=1, horizontal_step_on_sampled=1, reverse=True) -> Tuple[np.ndarray, int]:
    """
    Takes time series and creates a time delay embedding matrix from it.

    Parameters
    ----------
    raw : numpy.array shape=(dataset_size, seq_len, feature_count)
        array containing a time series
    num_lag : int
        The number of lagged datapoints corresponding to every base point. num_lag +1 will
        be the size of samples in the dataset created from the time series (lagges points +
        base point) which is also called the depth of the embedding.
    sampling_step : int
        The time series will be resampled with this step size. If it is 2 or 3 every second or
        third point will be taken from the original.
    vertical_step_on_sampled : int
        The step size which determines the position of the base points. It starts from the last
        point of the time series and takes every 'vertical_step_on_sampled'th point as a base point.
    horizontal_step_on_sampled : int
        The step size which determines the position of the lag points. It starts from the base
        points and moves backward in time.
    Returns
    -------
    nump.ndarray
        Time embedding matrix, shape(number of base points, number of lags+1) The time series is converted
        into this matrix where Y[n,k] = y(t = n * delta t - k * delta t). Every row corresponds to
        a slice of the resampled time series which starts at a given base point and contains
        lagged datapoints moving backward in time.

    Notes
    -----
    All of the prodecures are done using the resampled time series. Every selection starts from
    the last point (most recent) of the time series and moves backward.

    STEP is moving one unit in the resampled series, so horizontal_step_on_sampled=2 means taking every second point
    as lag point. (these are sampling_step * horizontal_step_on_sampled  steps in the original series)

    接受时间序列并从中创建一个时间延迟嵌入矩阵。
    参数
    ----------
    raw : numpy.array 形状=(数据集大小, 样本长度, 样本特征数)
            包含时间序列的数组
    num_lag : int
            每个基点的延迟数据点数量。num_lag + 1 将是来自时间序列的数据集中样本的大小（延迟点 + 基点），这也称为嵌入的深度。
    sampling_step : int
            时间序列将使用这个步长重新采样。如果是2或3，将从原始序列中每隔一个或两个点取一个点。
    vertical_step_on_sampled : int
            确定基点位置的步长。它从时间序列的最后一个点开始，每隔'vertical_step_on_sampled'个点取一个点作为基点。
    horizontal_step_on_sampled : int
            确定延迟点位置的步长。它从基点开始，向时间后退移动。
    返回
    -------
    nump.ndarray
            时间嵌入矩阵，形状(基点数量, 延迟数量) 时间序列被转换成这个矩阵，其中 Y[n,k] = y(t = n * delta t - k * delta t)。
            每一行对应于重新采样的时间序列的一个切片，该切片从给定的基点开始，并包含向后移动的延迟数据点。
    注释
    -----
    所有操作都是使用重新采样的时间序列完成的。每次选择都从时间序列的最后一个点（最近的）开始，并向后移动。
    STEP 是在重新采样的系列中移动一个单位，所以 horizontal_step_on_sampled=2 表示将每第二个点作为延迟点。（这些是原始系列中的 sampling_step * horizontal_step_on_sampled 步）

    """
    assert vertical_step_on_sampled % horizontal_step_on_sampled == 0, "vertical_step_on_sampled must be a multiple of horizontal_step_on_sampled"

    shape = raw.shape    
    if len(shape) == 1:
        raw = raw.reshape(1, -1, 1)
    elif len(shape) == 2:
        raw = raw.reshape(1, *shape)
    elif len(shape) > 3:
        raise ValueError("sample must be a array with 1, 2 or 3 dimensions")
    raw = raw.transpose((1, 0, 2))
    resampled = raw[::sampling_step * (-1) ** reverse]
    seq_len, dataset_size, feature_count = resampled.shape
    row_count = (seq_len + 1 - num_lag) // vertical_step_on_sampled
    row_start_indexes = np.arange(0, row_count * vertical_step_on_sampled, vertical_step_on_sampled)

    # reverse and resample the time series
    # resampled = sample[-1::-sampling_step]
    # row_start_indexes = np.arange(0, len(resampled), 1)[0: -num_lag * horizontal_step_on_sampled:vertical_step_on_sampled]
    # row_count = len(row_start_indexes)

    # --- create embedding matrix ---
    result = np.zeros((row_count, num_lag, dataset_size, feature_count))

    for i, ridx in enumerate(row_start_indexes):
        row_arr = resampled[ridx:ridx + num_lag * horizontal_step_on_sampled:horizontal_step_on_sampled]
        result[i] = row_arr

    return (
        result
        .transpose((2, 0, 1, 3))
        .reshape(dataset_size * row_count, num_lag * feature_count)
    ), row_count


class LinearLaw:
    """
    Fits a model of linear law and calculates properties.

    Attributes
    ----------
    learning_set : numpy.ndarray
            shape(number of samples, embedding length)
            The embedding matrix of a dataset. Columns corresponding to time delays and rows
            contain the different time delay slices.
    PCA_eigenvalues : numpy.ndarray
        array of the eigenvalues coming from the PCA decomposition in increasing order.
    PCA_eigenvectors : numpy.ndarray
        array of the eigenvectors corresponding to the PCA decomposition. Vectors are ordered according
        to the value their corresponding eigenvalues.
    linear_law : numpy.ndarray
        Array of the linear law corresponding to the smallest eigenvalue.
    score : float
        Measures how well the linera law fits the data. Lower the better.
        In a case of a perfect fit the linera law maps every line of the
        learning set to zero. In practice mapping is just close to zero.
        Score is: applying the linear law to the learning set and
        taking the square root of the variance of the resulting vector.
    _num_lag : int
        The number of lagged points in embedding generated from the data_set
    _sampling_step : int
        The resampling step applied on the data set
    _vertical_step_on_sampled : int
        The step size which determines the position of the base points while
        generating embedding from the data set. It starts from the last
        point of the time series and takes every 'vertical_step_on_sampled'th point as a base point.
    _horizontal_step_on_sampled : int
        The step size which determines the position of the lag points while
        generating embedding from the datas set. It starts from the base points
        and moves backward in time.

    拟合线性定律模型并计算属性。
    属性
    ----------
    learning_set : numpy.ndarray
            形状(样本数量, 嵌入长度)
            数据集的嵌入矩阵。列对应于时间延迟，行包含不同的时间延迟切片。
    PCA_eigenvalues : numpy.ndarray
            来自PCA分解的特征值数组，按递增顺序排列。
    PCA_eigenvectors : numpy.ndarray
            对应于PCA分解的特征向量数组。向量根据其对应特征值的值进行排序。
    linear_law : numpy.ndarray
            对应于最小特征值的线性定律数组。
    score : float
            衡量线性定律对数据的拟合程度。越低越好。
            在完美拟合的情况下，线性定律将学习集的每一行映射为零。
            实际应用中，映射只是接近零。
            得分是：将线性定律应用于学习集，并计算结果向量的方差的平方根。
    _num_lag : int
            从数据集生成的嵌入中的延迟点数量
    _sampling_step : int
            应用于数据集的重新采样步长
    _vertical_step_on_sampled : int
            在从数据集生成嵌入时确定基点位置的步长。
            它从时间序列的最后一个点开始，每隔'vertical_step_on_sampled'个点取一个点作为基点。
    _horizontal_step_on_sampled : int
            在从数据集生成嵌入时确定延迟点位置的步长。
            它从基点开始，向时间后退移动。

    """

    def __init__(self, data_set, num_lag, sampling_step=1, vertical_step_on_sampled=1, horizontal_step_on_sampled=1, reverse=True):
        """
        Creates a class instance and initializes with the learning set.

        Parameters
        ----------
        data_set : numpy.ndarray
            shape(dataset_size, seq_len, feature_count)
            The time series data of the peaks which will be used to determine the linear law.
            This can be considered the learning set.
        num_lag : int
            The number of lagged datapoints corresponding to every base point. num_lag +1 will
            be the size of samples in the dataset created from the time series (lagges points +
            base point) which is also called the lenght of the embedding.
        sampling_step : int
            The time series will be resampled with this step size. If it is 2 or 3 every second or
            third point will be taken from the original.
        vertical_step_on_sampled : int
            The step size which determines the position of the base points. It starts from the last
            point of the time series and takes every 'vertical_step_on_sampled'th point as a base point.
        horizontal_step_on_sampled : int
            The step size which determines the position of the lag points. It starts from the base
            points and moves backward in time.

    创建一个类实例，并使用学习集进行初始化。
    参数
    ----------
    data_set : numpy.ndarray
        形状(数据集大小, 样本长度, 样本特征数)
        将用于确定线性定律的峰值的时间序列数据。
        这可以被视为学习集。
    num_lag : int
        每个基点的延迟数据点数量。num_lag + 1 将是来自时间序列的数据集中样本的大小（延迟点 + 基点），这也称为嵌入的长度。
    sampling_step : int
        时间序列将使用这个步长重新采样。如果是2或3，将从原始序列中每隔一个或两个点取一个点。
    vertical_step_on_sampled : int
        确定基点位置的步长。它从时间序列的最后一个点开始，每隔'vertical_step_on_sampled'个点取一个点作为基点。
    horizontal_step_on_sampled : int
        确定延迟点位置的步长。它从基点开始，向时间后退移动。
            
        """
        # create a time delay embedding from the dataset
        # aka. A of shape(k := len(), l)
        self.dataset_embedding, self.transformed_dim = get_embedding(data_set, num_lag=num_lag,
                                              sampling_step=sampling_step,
                                              vertical_step_on_sampled=vertical_step_on_sampled,
                                              horizontal_step_on_sampled=horizontal_step_on_sampled,reverse=reverse)

        # create the attributes
        self.PCA_eigenvalues = None
        self.PCA_eigenvectors = None
        self.linear_law = None
        self.score = None
        self._num_lag = num_lag
        self._sampling_step = sampling_step
        self._vertical_step_on_sampled = vertical_step_on_sampled
        self._horizontal_step_on_sampled = horizontal_step_on_sampled
        self._reverse = reverse

    def fit(self):
        """
        Calculates the linear model from learning set.

        Parameters
        ----------
        None

        Returns
        -------
        None

        """

        # --- Create PCA matrix ---
        # aka. S of shape(l := len(A), l)
        PCA_matrix = self.dataset_embedding.T @ self.dataset_embedding / len(self.dataset_embedding)#[:, 0])

        # --- linear law ---
        # calculating the eigensystem
        eigenvals, eigenvectors = np.linalg.eig(PCA_matrix)
        idx_list = np.argsort(eigenvals)
        self.PCA_eigenvalues = eigenvals[idx_list]
        self.PCA_eigenvectors = [eigenvectors[:, i] for i in idx_list]

        # --- search for imaginary eigenvalues ---
        imag_list = np.where(np.imag(eigenvals) > 0)[0]
        if len(imag_list) != 0:
            raise ValueError('Numerical precision error, there are complex eigenvalues.')

        # --- minimal eigenvalue-vector pair ---
        minimal_index = np.argmin(eigenvals)
        # minimal_index = np.argmax(eigenvals)
        w = eigenvectors[:, minimal_index]
        self.linear_law = w

        # --- calculate the average accuracy of the linear law ---
        self.score = np.sqrt((self.dataset_embedding @ self.linear_law).var())

    def transform(self, data_set: np.ndarray, normalize=0.95) -> np.ndarray:
        """
        Generates features from the data set of using the linear law.
        The original structure of the  dataset will be preserved
        (sample indexes remain valid), but instead of the individual
        samples, the output will contain thecorresponding features.

        Parameters
        ----------
        data_set : numpy.ndarray
            shape(number of samples, signal length)
            The time series data of the peaks which will be used to determine the linear law.
            This can be considered the learning set.
        normalize : float
            Determines the normalization constant for the features.
            if None: no normalization happens
            if float: percentile
            the normalization constant is determined as the given percentile in the abs(dataset).

    使用线性定律从数据集中生成特征。原始数据集的结构将被保留（样本索引保持有效），但是输出将包含相应的特征，而不是单个样本。
    参数
    ----------
    data_set : numpy.ndarray
        形状(数据集大小, 样本长度, 样本特征数)
        将用于确定线性定律的峰值的时间序列数据。
        这可以被视为学习集。
    normalize : float
        确定特征的归一化常数。
        如果为None：不进行归一化
        如果为浮点数：百分位数
        归一化常数是根据给定百分位数在abs(dataset)中确定的。

        """
        dataset_size = len(data_set)
        
        # --- data set embedding ---
        dataset_embedding, _ = get_embedding(data_set,
                                       num_lag=self._num_lag,
                                       sampling_step=self._sampling_step,
                                       vertical_step_on_sampled=self._vertical_step_on_sampled,
                                       horizontal_step_on_sampled=self._horizontal_step_on_sampled,
                                       reverse=self._reverse)

        result = dataset_embedding @ self.linear_law
        # --- normlization constant ---
        if normalize is not None:
            N = np.percentile(np.abs((self.dataset_embedding @ self.linear_law)), normalize*100)
            result = result / N

        return result.reshape(dataset_size, -1)
    
        # transformed_data = []
        # for embedding in dataset_embedding:
        #     features = embedding @ self.linear_law

        #     # --- normalize ---
        #     if normalize is not None:
        #         features = features / N

        #     transformed_data.append(features)        

        # return np.array(transformed_data).reshape(dataset_size, -1)


if __name__ == '__main__':
    # example usage of time series classification using linear laws
    from sklearn.datasets import make_classification
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score
    from sklearn.linear_model import LogisticRegression

    # create a dataset
    X, y = make_classification(n_samples=1000, n_features=20, n_informative=2, n_redundant=0, n_clusters_per_class=1)
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    X_train = X_train.reshape(X_train.shape[0], -1, 1)
    X_test = X_test.reshape(X_test.shape[0], -1, 1)

    # create a linear law model
    model = LinearLaw(X_train, num_lag=10, sampling_step=1, vertical_step_on_sampled=1, horizontal_step_on_sampled=1)
    model.fit()

    X_train_transformed = model.transform(X_train)

    # transform the test set using the linear law
    X_test_transformed = model.transform(X_test)

    # train a logistic regression model on the transformed data
    clf = LogisticRegression(random_state=0).fit(X_train_transformed, y_train)

    # evaluate the model on the test set
    y_pred = clf.predict(X_test_transformed)
    acc = accuracy_score(y_test, y_pred)
    print('Accuracy:', acc)