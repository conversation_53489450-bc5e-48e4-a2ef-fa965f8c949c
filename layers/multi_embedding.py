import torch
import torch.nn as nn


class MultiEmbedding(nn.Module):
    def __init__(self, category_size_dict: dict):
        super(MultiEmbedding, self).__init__()
        self.embedding_list = nn.ModuleList()
        output_size = 0
        for category_size in category_size_dict.values():
            embedding_dim = min(6, round(category_size ** 0.25))
            self.embedding_list.append(nn.Embedding(category_size, embedding_dim))
            output_size += embedding_dim
        self.output_size = output_size

    def forward(self, x):
        embedded_list = [embedding(x[:, i]) for i, embedding in enumerate(self.embedding_list)]
        embedded = torch.cat(embedded_list, dim=-1)
        return embedded