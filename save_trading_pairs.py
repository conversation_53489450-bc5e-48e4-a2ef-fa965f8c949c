import sys
from aed_quant.quant import quant
from aed_quant.tasks import SingleTask
from aed_quant.trader import Trader
from direct_trading import pred_cfg


cfg = pred_cfg
# cfg.device = 'cpu'

if __name__ == "__main__":	
	online_cfg = cfg.online_trading
	online_cfg.LOG.console = True
	quant.initialize(online_cfg)
	cfg = cfg.load_cfg_from_ckpt(ckpt_file_name=cfg.ckpt_file_name)
	# cfg.debug_ticker = True
	trader = Trader(
			strategy=cfg.strategy,
			platform=cfg.platform,
			symbols=cfg.code_list,
			account_id=cfg.account_id,
			access_key=cfg.access_key,
			secret_key=cfg.secret_key,
			# init_success_callback=self.on_event_init_success_callback,
			# error_callback=self.on_event_error_callback,
			# account_update_callback=self.on_event_account_update_callback,
			# order_update_callback=self.on_event_order_update_callback,
			# position_update_callback=self.on_event_position_update_callback
		)
	save_path = cfg.trading_pairs_path
	SingleTask().run(trader._t.get_trading_pairs_info, save_path)
	
	quant.start()
	

	