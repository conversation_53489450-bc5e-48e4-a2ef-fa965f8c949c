//@version=5
strategy("Two-Way Market Maker", shorttitle="TWMM", overlay=true, pyramiding=100, default_qty_type=strategy.percent_of_equity, default_qty_value=1, commission_type=strategy.commission.percent, commission_value=0.05, process_orders_on_close=true)

// Input parameters
use_atr = input.bool(false, title="Use ATR for Entry/Scale-in (otherwise fixed %)", group="Entry Parameters")
atr_period = input.int(1, title="ATR Period", minval=1, group="Entry Parameters")
atr_multiplier = input.float(1.0, title="ATR Multiplier", minval=0.1, step=0.1, group="Entry Parameters")
fixed_percent = input.float(1.0, title="Fixed Percentage (%)", minval=0.1, step=0.1, group="Entry Parameters") / 100

cooldown_period = input.int(1, title="Cooldown Period (bars)", minval=1, group="Position Sizing")
initial_position_pct = input.float(1.0, title="Initial Position Size (%)", minval=0.1, step=0.1, group="Position Sizing") / 100
scale_in_coef = input.float(1.0, title="Scale-in Order Coefficient", minval=1.0, step=0.1, group="Position Sizing")
max_scale_in = input.int(100, title="Maximum Scale-in Orders", minval=1, maxval=100, group="Position Sizing")

take_profit_pct = input.float(1.0, title="Take Profit (%)", minval=0.1, step=0.1, group="Exit Parameters") / 100
inventory_decay = input.float(0.99, title="Inventory Decay", minval=0.1, step=0.01, group="Exit Parameters")

// Variables
var float base_price = na
var float long_base_price = na
var float short_base_price = na
var int long_scale_in_count = 0
var int short_scale_in_count = 0
var float long_avg_price = na
var float short_avg_price = na
var float long_position_size = 0.0
var float short_position_size = 0.0
var float long_tp_level = na
var float short_tp_level = na
var float long_entry_level = na
var float short_entry_level = na
var string long_tp_id = na
var string short_tp_id = na
var int max_long_count = 0
var int max_short_count = 0
// 冷却期变量
var int last_long_entry_bar = -9999  // 最后一次多头开仓的bar索引
var int last_short_entry_bar = -9999 // 最后一次空头开仓的bar索引
long_entry_str = "L_"
short_entry_str = "S_"
atr_distance = ta.atr(atr_period) * atr_multiplier

// Calculate entry levels - recalculated on each bar
get_entry_level(price, side) =>
    distance = use_atr ? atr_distance: price * fixed_percent
    price - side * distance

// Initialize base price on first bar
if bar_index == 0
    base_price := open
    long_base_price := open
    short_base_price := open
    long_tp_level := open * (1 + take_profit_pct)
    short_tp_level := open * (1 - take_profit_pct)
    long_entry_level := get_entry_level(open, 1)
    short_entry_level := get_entry_level(open, -1)


// Calculate position sizes for scale-in orders
get_scale_in_size(count) =>
    math.pow(scale_in_coef, count) * strategy.default_entry_qty(close)

get_tp_level(avg_price, count, side) =>
    avg_price * (1 + side * math.pow(inventory_decay, count) * take_profit_pct)

// Function to calculate average position price
calculate_avg_price(current_avg, current_size, new_price, new_size) =>
    total_size = current_size + new_size
    (current_avg * current_size + new_price * new_size) / total_size

// 检查是否在冷却期内
long_in_cooldown = (bar_index - last_long_entry_bar) < cooldown_period
short_in_cooldown = (bar_index - last_short_entry_bar) < cooldown_period

// Long entry logic
if close <= long_entry_level and long_scale_in_count < max_scale_in and not long_in_cooldown
    order_size = get_scale_in_size(long_scale_in_count)
    long_scale_in_count += 1
    max_long_count := math.max(max_long_count, long_scale_in_count)
    long_entry_comment = long_entry_str + str.tostring(long_scale_in_count)
    strategy.entry(long_entry_str, strategy.long, qty=order_size, comment=long_entry_comment)

    // 立即更新平均价格和仓位大小
    if na(long_avg_price) or long_position_size == 0
        long_avg_price := long_entry_level  // Use limit price instead of close
        long_position_size := order_size
    else
        long_avg_price := calculate_avg_price(long_avg_price, long_position_size, long_entry_level, order_size)
        long_position_size += order_size

    // 更新平仓价格并立即更新平仓限价单
    long_tp_level := get_tp_level(long_avg_price, long_scale_in_count, 1)
    strategy.exit(long_entry_str, limit=long_tp_level, comment=long_entry_comment)
    short_base_price := math.max(long_tp_level, base_price)
    short_entry_level := get_entry_level(short_base_price, -1)
    long_entry_level := get_entry_level(close, 1)

    // 更新最后一次多头开仓时间
    last_long_entry_bar := bar_index


// Short entry logic
else if close >= short_entry_level and short_scale_in_count < max_scale_in and not short_in_cooldown
    order_size = get_scale_in_size(short_scale_in_count)
    short_scale_in_count += 1
    max_short_count := math.max(max_short_count, short_scale_in_count)
    short_entry_comment = short_entry_str + str.tostring(short_scale_in_count)
    strategy.entry(short_entry_str, strategy.short, qty=order_size, comment=short_entry_comment)

    // 立即更新平均价格和仓位大小
    if na(short_avg_price) or short_position_size == 0
        short_avg_price := short_entry_level  // Use limit price instead of close
        short_position_size := order_size
    else
        short_avg_price := calculate_avg_price(short_avg_price, short_position_size, short_entry_level, order_size)
        short_position_size += order_size

    // 更新平仓价格并立即更新平仓限价单
    short_tp_level := get_tp_level(short_avg_price, short_scale_in_count, -1)
    strategy.exit(short_entry_str, limit=short_tp_level, comment=short_entry_comment)
    long_base_price := math.min(short_tp_level, base_price)
    long_entry_level := get_entry_level(long_base_price, 1)
    short_entry_level := get_entry_level(close, -1)

    // 更新最后一次空头开仓时间
    last_short_entry_bar := bar_index


// Check for full take profit (position closed)
if strategy.position_size == 0 and strategy.position_size[1] != 0
    // Update base price to current close price
    base_price := close
    long_base_price := close
    short_base_price := close
    long_tp_level := close * (1 + take_profit_pct)
    short_tp_level := close * (1 - take_profit_pct)
    long_entry_level := get_entry_level(base_price, 1)
    short_entry_level := get_entry_level(base_price, -1)
    // Cancel any existing take profit orders
    if strategy.position_size[1] > 0
        // Long position was closed
        long_scale_in_count := 0
        long_position_size := 0.0
    else
        // Short position was closed
        short_scale_in_count := 0
        short_position_size := 0.0


// Visualization using plotchar with line-like characters
plot(base_price, title="Base Price", color=color.orange, style = plot.style_stepline) // Solid line for base price

// Long entry and take profit levels with different characters
plot(long_entry_level, title="Long Entry Level", color=color.lime, style = plot.style_stepline) // Dashed line for entry
plot(long_tp_level, title="Long Take Profit", color=color.aqua, style = plot.style_stepline) // Dotted line for take profit

// Short entry and take profit levels with different characters
plot(short_entry_level, title="Short Entry Level", color=color.red, style = plot.style_stepline) // Dashed line for entry
plot(short_tp_level, title="Short Take Profit", color=color.fuchsia, style = plot.style_stepline) // Dotted line for take profit

// Display information
var table info_table = table.new(position.top_right, 7, 7, color.black, color.white, 1, color.gray, 1)
table.cell(info_table, 0, 0, "Base Price", text_color=color.orange)
table.cell(info_table, 1, 0, str.tostring(base_price, "#.##"), text_color=color.orange)
table.cell(info_table, 0, 1, "Long Avg Price", text_color=color.green)
table.cell(info_table, 1, 1, str.tostring(long_avg_price, "#.##"), text_color=color.green)
table.cell(info_table, 0, 2, "Short Avg Price", text_color=color.red)
table.cell(info_table, 1, 2, str.tostring(short_avg_price, "#.##"), text_color=color.red)
table.cell(info_table, 0, 3, "Long Scale-in Count", text_color=color.green)
table.cell(info_table, 1, 3, str.tostring(long_scale_in_count), text_color=color.green)
table.cell(info_table, 0, 4, "Short Scale-in Count", text_color=color.red)
table.cell(info_table, 1, 4, str.tostring(short_scale_in_count), text_color=color.red)
table.cell(info_table, 0, 5, "Max Long Count", text_color=color.green)
table.cell(info_table, 1, 5, str.tostring(max_long_count), text_color=color.green)
table.cell(info_table, 0, 6, "Max Short Count", text_color=color.red)
table.cell(info_table, 1, 6, str.tostring(max_short_count), text_color=color.red)

// // 显示冷却状态
// var label long_cooldown_label = na
// var label short_cooldown_label = na

// if long_in_cooldown
//     long_cooldown_label := label.new(bar_index, high, "L冷却中", color=color.new(color.green, 70), style=label.style_label_down, textcolor=color.white, size=size.small)
//     label.delete(long_cooldown_label[1])

// if short_in_cooldown
//     short_cooldown_label := label.new(bar_index, low, "S冷却中", color=color.new(color.red, 70), style=label.style_label_up, textcolor=color.white, size=size.small)
//     label.delete(short_cooldown_label[1])