import unittest
import pandas as pd
import numpy as np
import polars as pl
from two_way_market_maker_limit import TwoWayMarketMakerLimit

class TestTwoWayMarketMakerLimit(unittest.TestCase):
    """
    Test cases for the TwoWayMarketMakerLimit strategy
    """
    
    def setUp(self):
        """Set up test data and strategy instance"""
        # Create synthetic test data
        np.random.seed(42)  # For reproducibility
        dates = pd.date_range(start='2023-01-01', periods=100, freq='1h')
        
        # Generate price data with a slight uptrend
        base_price = 100
        returns = np.random.normal(0.0001, 0.005, 100)
        prices = base_price * np.cumprod(1 + returns)
        
        # Create DataFrame
        self.test_data = pd.DataFrame({
            'datetime': dates,
            'open': prices * (1 + np.random.normal(0, 0.002, 100)),
            'high': prices * (1 + np.abs(np.random.normal(0, 0.004, 100))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.004, 100))),
            'close': prices,
            'volume': np.random.randint(1000, 100000, 100)
        })
        
        # Ensure high is the highest and low is the lowest
        self.test_data['high'] = self.test_data[['open', 'close', 'high']].max(axis=1)
        self.test_data['low'] = self.test_data[['open', 'close', 'low']].min(axis=1)
        
        # Create strategy instance
        self.strategy = TwoWayMarketMakerLimit(
            trade_direction="Both",
            use_atr=False,
            fixed_percent=0.01,
            cooldown_period=2,
            initial_position_pct=0.02,
            scale_in_coef=1.5,
            max_scale_in=3,
            take_profit_pct=0.02,
            inventory_decay=0.95,
            commission_pct=0.001
        )
    
    def test_initialization(self):
        """Test strategy initialization"""
        self.assertEqual(self.strategy.trade_direction, "Both")
        self.assertEqual(self.strategy.fixed_percent, 0.01)
        self.assertEqual(self.strategy.max_scale_in, 3)
        self.assertTrue(self.strategy.allow_long)
        self.assertTrue(self.strategy.allow_short)
    
    def test_helper_functions(self):
        """Test helper functions"""
        # Test get_entry_level
        entry_level = self.strategy.get_entry_level(100.0, 1)
        self.assertEqual(entry_level, 99.0)  # 100 - 1 * (100 * 0.01)
        
        entry_level = self.strategy.get_entry_level(100.0, -1)
        self.assertEqual(entry_level, 101.0)  # 100 - (-1) * (100 * 0.01)
        
        # Test get_scale_in_ratio
        ratio = self.strategy.get_scale_in_ratio(0)
        self.assertEqual(ratio, 1.0)
        
        ratio = self.strategy.get_scale_in_ratio(1)
        self.assertEqual(ratio, 1.5)
        
        # Test get_tp_level
        tp_level = self.strategy.get_tp_level(100.0, 0, 1)
        self.assertEqual(tp_level, 102.0)  # 100 * (1 + 1 * 0.95^0 * 0.02)
        
        tp_level = self.strategy.get_tp_level(100.0, 0, -1)
        self.assertEqual(tp_level, 98.0)  # 100 * (1 + (-1) * 0.95^0 * 0.02)
        
        # Test calculate_avg_price
        avg_price = self.strategy.calculate_avg_price(100.0, 1.0, 110.0, 1.0)
        self.assertEqual(avg_price, 105.0)  # (100*1 + 110*1) / (1+1)
    
    def test_backtest_execution(self):
        """Test backtest execution"""
        # Run backtest
        results = self.strategy.run_backtest(self.test_data)
        
        # Check that results are returned
        self.assertIsNotNone(results)
        self.assertGreater(len(results), 0)
        
        # Check that all required columns are present
        required_columns = [
            'datetime', 'open', 'high', 'low', 'close',
            'base_price', 'long_entry_level', 'short_entry_level',
            'long_tp_level', 'short_tp_level', 'equity'
        ]
        for col in required_columns:
            self.assertIn(col, results.columns)
        
        # Check that equity is calculated
        self.assertGreater(results.select('equity').max().item(), 0)
    
    def test_long_only_mode(self):
        """Test long only trading mode"""
        # Create long-only strategy
        long_only_strategy = TwoWayMarketMakerLimit(
            trade_direction="Long Only",
            use_atr=False,
            fixed_percent=0.01,
            cooldown_period=2,
            initial_position_pct=0.02,
            scale_in_coef=1.5,
            max_scale_in=3,
            take_profit_pct=0.02,
            inventory_decay=0.95,
            commission_pct=0.001
        )
        
        # Run backtest
        results = long_only_strategy.run_backtest(self.test_data)
        
        # Check that only long positions are taken
        self.assertEqual(results.filter(pl.col('curr_short_pos') > 0).height, 0)
    
    def test_short_only_mode(self):
        """Test short only trading mode"""
        # Create short-only strategy
        short_only_strategy = TwoWayMarketMakerLimit(
            trade_direction="Short Only",
            use_atr=False,
            fixed_percent=0.01,
            cooldown_period=2,
            initial_position_pct=0.02,
            scale_in_coef=1.5,
            max_scale_in=3,
            take_profit_pct=0.02,
            inventory_decay=0.95,
            commission_pct=0.001
        )
        
        # Run backtest
        results = short_only_strategy.run_backtest(self.test_data)
        
        # Check that only short positions are taken
        self.assertEqual(results.filter(pl.col('curr_long_pos') > 0).height, 0)
    
    def test_performance_analysis(self):
        """Test performance analysis"""
        # Run backtest
        results = self.strategy.run_backtest(self.test_data)
        
        # Analyze performance
        metrics = self.strategy.analyze_performance(results)
        
        # Check that all metrics are calculated
        required_metrics = [
            'total_return', 'annual_return', 'annual_volatility',
            'sharpe_ratio', 'max_drawdown', 'total_trades',
            'long_trades', 'short_trades', 'win_rate'
        ]
        for metric in required_metrics:
            self.assertIn(metric, metrics)
    
    def test_atr_based_entries(self):
        """Test ATR-based entries"""
        # Create ATR-based strategy
        atr_strategy = TwoWayMarketMakerLimit(
            trade_direction="Both",
            use_atr=True,
            atr_period=14,
            atr_multiplier=1.0,
            cooldown_period=2,
            initial_position_pct=0.02,
            scale_in_coef=1.5,
            max_scale_in=3,
            take_profit_pct=0.02,
            inventory_decay=0.95,
            commission_pct=0.001
        )
        
        # Run backtest
        results = atr_strategy.run_backtest(self.test_data)
        
        # Check that ATR column is calculated
        self.assertIn('atr', results.columns)
        
        # Check that results are returned
        self.assertIsNotNone(results)
        self.assertGreater(len(results), 0)

if __name__ == '__main__':
    unittest.main()
