
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>加密货币累计收益动态均值图表</title>
        <style>
            body {
                margin: 0;
                padding: 20px;
                font-family: Arial, sans-serif;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .plotly-graph-div {
                width: 100% !important;
                height: 600px !important;
            }
            .instructions {
                margin-bottom: 20px;
                padding: 15px;
                background-color: #e3f2fd;
                border-left: 4px solid #2196f3;
                border-radius: 4px;
            }
            .instructions h3 {
                margin-top: 0;
                color: #1976d2;
            }
            .instructions ul {
                margin-bottom: 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="instructions">
                <h3>使用说明</h3>
                <ul>
                    <li><strong>单击图例</strong>：显示/隐藏对应的数据线</li>
                    <li><strong>双击图例</strong>：只显示该数据线，隐藏其他线</li>
                    <li><strong>红色虚线</strong>：当前选中（可见）数据线的均值</li>
                    <li><strong>均值线名称</strong>：显示选中线条数量和平均夏普比率</li>
                    <li><strong>悬停</strong>：查看具体数值和时间</li>
                </ul>
            </div>
            <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-2.35.2.min.js"></script>                <div id="4dd10f6f-0f6b-403b-85f2-8aeb15abfa6c" class="plotly-graph-div" style="height:600px; width:100%;"></div>            <script type="text/javascript">                                    window.PLOTLYENV=window.PLOTLYENV || {};                                    if (document.getElementById("4dd10f6f-0f6b-403b-85f2-8aeb15abfa6c")) {                    Plotly.newPlot(                        "4dd10f6f-0f6b-403b-85f2-8aeb15abfa6c",                        [{"customdata":["BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT","BTCUSDT"],"mode":"lines","name":"BTCUSDT_shp-1.1169","opacity":0.7,"visible":true,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00","2024-01-31T00:00:00","2024-02-01T00:00:00","2024-02-02T00:00:00","2024-02-03T00:00:00","2024-02-04T00:00:00","2024-02-05T00:00:00","2024-02-06T00:00:00","2024-02-07T00:00:00","2024-02-08T00:00:00","2024-02-09T00:00:00","2024-02-10T00:00:00","2024-02-11T00:00:00","2024-02-12T00:00:00","2024-02-13T00:00:00","2024-02-14T00:00:00","2024-02-15T00:00:00","2024-02-16T00:00:00","2024-02-17T00:00:00","2024-02-18T00:00:00","2024-02-19T00:00:00","2024-02-20T00:00:00","2024-02-21T00:00:00","2024-02-22T00:00:00","2024-02-23T00:00:00","2024-02-24T00:00:00","2024-02-25T00:00:00","2024-02-26T00:00:00","2024-02-27T00:00:00","2024-02-28T00:00:00","2024-02-29T00:00:00","2024-03-01T00:00:00","2024-03-02T00:00:00","2024-03-03T00:00:00","2024-03-04T00:00:00","2024-03-05T00:00:00","2024-03-06T00:00:00","2024-03-07T00:00:00","2024-03-08T00:00:00","2024-03-09T00:00:00","2024-03-10T00:00:00","2024-03-11T00:00:00","2024-03-12T00:00:00","2024-03-13T00:00:00","2024-03-14T00:00:00","2024-03-15T00:00:00","2024-03-16T00:00:00","2024-03-17T00:00:00","2024-03-18T00:00:00","2024-03-19T00:00:00","2024-03-20T00:00:00","2024-03-21T00:00:00","2024-03-22T00:00:00","2024-03-23T00:00:00","2024-03-24T00:00:00","2024-03-25T00:00:00","2024-03-26T00:00:00","2024-03-27T00:00:00","2024-03-28T00:00:00","2024-03-29T00:00:00","2024-03-30T00:00:00","2024-03-31T00:00:00","2024-04-01T00:00:00","2024-04-02T00:00:00","2024-04-03T00:00:00","2024-04-04T00:00:00","2024-04-05T00:00:00","2024-04-06T00:00:00","2024-04-07T00:00:00","2024-04-08T00:00:00","2024-04-09T00:00:00"],"y":[0.010934283060224592,0.009149694899738492,0.0232311384069257,0.055422601021335005,0.05153540834658776,0.04766287774224254,0.08180019347722278,0.09948621444027528,0.09026208834734972,0.1031830033591743,0.09406149591920077,0.08496481759741692,0.09130019345171636,0.0506322316227199,0.015437778419562731,0.005033856207472764,-0.014319701267452523,-0.00713907287535176,-0.024177044457696972,-0.05076438894136148,-0.021990233235011036,-0.02542845200784405,-0.023137659120335052,-0.04999645374642614,-0.05938976057653467,-0.05636245186382183,-0.07714122946207114,-0.06928404646545261,-0.0795338107322685,-0.08398322922910939,-0.09409067941605576,-0.059624848662252594,-0.05893832260602627,-0.07790468534865591,-0.06181329384319145,-0.08378269279101003,-0.07903918667201615,-0.11421381368154548,-0.1368578045947294,-0.13259627761295878,-0.11891790068364283,-0.11501702808538461,-0.11617898032636531,-0.12061759481195622,-0.14574193688702264,-0.15718633320803688,-0.16410817257362809,-0.1455994841564584,-0.13887333076340347,-0.16837622202852454,-0.1621542795503913,-0.16776922464329747,-0.17820410029135347,-0.16732884306927642,-0.14932650060851116,-0.1326315207522737,-0.14632236881053928,-0.15074804495037242,-0.1442722706602042,-0.126720522600846,-0.13421630368362036,-0.13656533028871032,-0.1548068550764179,-0.17418217470319752,-0.15993639072495924,-0.1363097692467944,-0.13668996778652687,-0.11849945738618439,-0.11124230979740668,-0.12182065496734229,-0.11459507249944845,-0.086473964479448,-0.08621499883345773,-0.056706255735226696,-0.10518674535608441,-0.08958294700264091,-0.08708754724289447,-0.09163398546426138,-0.0890585720322804,-0.12435880790932852,-0.12733024179193486,-0.12022474520504245,-0.09334067775479915,-0.10183190892935112,-0.11545700395734859,-0.12344897453366133,-0.10652449020847876,-0.09975639339273301,-0.10839441451565057,-0.09835016672594454,-0.09569791776898418,-0.07727466204912248,-0.08930798027732034,-0.09436527429903352,-0.10056177476747163,-0.12598916229322366,-0.11993890482713343,-0.11446395195725478,-0.11348785290547192,-0.1167606276241604],"type":"scatter"},{"customdata":["ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT","ETHUSDT"],"mode":"lines","name":"ETHUSDT_shp0.8408","opacity":0.7,"visible":true,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00","2024-01-31T00:00:00","2024-02-01T00:00:00","2024-02-02T00:00:00","2024-02-03T00:00:00","2024-02-04T00:00:00","2024-02-05T00:00:00","2024-02-06T00:00:00","2024-02-07T00:00:00","2024-02-08T00:00:00","2024-02-09T00:00:00","2024-02-10T00:00:00","2024-02-11T00:00:00","2024-02-12T00:00:00","2024-02-13T00:00:00","2024-02-14T00:00:00","2024-02-15T00:00:00","2024-02-16T00:00:00","2024-02-17T00:00:00","2024-02-18T00:00:00","2024-02-19T00:00:00","2024-02-20T00:00:00","2024-02-21T00:00:00","2024-02-22T00:00:00","2024-02-23T00:00:00","2024-02-24T00:00:00","2024-02-25T00:00:00","2024-02-26T00:00:00","2024-02-27T00:00:00","2024-02-28T00:00:00","2024-02-29T00:00:00","2024-03-01T00:00:00","2024-03-02T00:00:00","2024-03-03T00:00:00","2024-03-04T00:00:00","2024-03-05T00:00:00","2024-03-06T00:00:00","2024-03-07T00:00:00","2024-03-08T00:00:00","2024-03-09T00:00:00","2024-03-10T00:00:00","2024-03-11T00:00:00","2024-03-12T00:00:00","2024-03-13T00:00:00","2024-03-14T00:00:00","2024-03-15T00:00:00","2024-03-16T00:00:00","2024-03-17T00:00:00","2024-03-18T00:00:00","2024-03-19T00:00:00","2024-03-20T00:00:00","2024-03-21T00:00:00","2024-03-22T00:00:00","2024-03-23T00:00:00","2024-03-24T00:00:00","2024-03-25T00:00:00","2024-03-26T00:00:00","2024-03-27T00:00:00","2024-03-28T00:00:00","2024-03-29T00:00:00","2024-03-30T00:00:00","2024-03-31T00:00:00","2024-04-01T00:00:00","2024-04-02T00:00:00","2024-04-03T00:00:00","2024-04-04T00:00:00","2024-04-05T00:00:00","2024-04-06T00:00:00","2024-04-07T00:00:00","2024-04-08T00:00:00","2024-04-09T00:00:00"],"y":[-0.027307414841008293,-0.03451789398456284,-0.04017010654211395,-0.054611270765469455,-0.05671543591614725,-0.048149452625303235,-0.011290260427574972,-0.006849415010800719,-0.000740538001601232,-0.0012290942543324856,-0.03855858064104556,-0.038106969982578565,-0.03598637656978587,0.01246961613850095,0.009586893111199979,0.016685244893761775,0.01699611125698608,-0.005757713023167077,0.01796138463806396,0.034288121844880504,0.05168550890325441,0.03360940224843678,0.06364183943476864,0.034884132413363966,0.04806559843313973,0.09502848775768835,0.07443020636190778,0.06333568883868201,0.06651828158630435,0.05684548007891976,0.0251260927940804,0.027556932776339194,0.006752938794049257,0.017295503159495018,-0.0003937241169836847,0.03159236532815024,0.0164639953636343,0.010933180648692087,0.02839234479070063,0.004104508325556866,0.00967648374987129,0.03708198623168113,0.004777230103255148,0.009492325272545843,0.015748811323101908,0.032647273187885295,0.008133244887951863,-0.01748254606880828,-0.0062436946518615866,0.0006526694858313142,0.006666448943031478,0.01464827116791434,0.0018632012770609663,0.007518793129261292,0.014431832414336432,0.0009530478872254999,0.03930505460712541,0.05019349865658995,0.02622170840146487,0.04072332142805357,0.021476565846001217,0.03857781197306931,0.06368222301318593,0.04728700138075981,0.06851291433410234,0.0784026622728855,0.09721130223688901,0.13993216751055648,0.13547758353893746,0.11949605075186542,0.10069938898156594,0.08384085072682068,0.08325337193106996,0.0917277058442223,0.09886085378168197,0.1181389004608564,0.11954779778308278,0.15321336308254874,0.14826246051120884,0.21188008577641848,0.22825664184373418,0.20842870925008095,0.18375519317577949,0.19636153291111902,0.1922110488360096,0.21042804544346483,0.22309487534776573,0.22253643682615798,0.2030542497667016,0.16780843618835584,0.1585473660663821,0.17954948478176602,0.18577971757822342,0.1574220617518549,0.16258835229870217,0.17271025060322098,0.15315278734372506,0.1578513108191848,0.16035710294908023,0.13499238598107755],"type":"scatter"},{"customdata":["ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT","ADAUSDT"],"mode":"lines","name":"ADAUSDT_shp3.0511","opacity":0.7,"visible":true,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00","2024-01-31T00:00:00","2024-02-01T00:00:00","2024-02-02T00:00:00","2024-02-03T00:00:00","2024-02-04T00:00:00","2024-02-05T00:00:00","2024-02-06T00:00:00","2024-02-07T00:00:00","2024-02-08T00:00:00","2024-02-09T00:00:00","2024-02-10T00:00:00","2024-02-11T00:00:00","2024-02-12T00:00:00","2024-02-13T00:00:00","2024-02-14T00:00:00","2024-02-15T00:00:00","2024-02-16T00:00:00","2024-02-17T00:00:00","2024-02-18T00:00:00","2024-02-19T00:00:00","2024-02-20T00:00:00","2024-02-21T00:00:00","2024-02-22T00:00:00","2024-02-23T00:00:00","2024-02-24T00:00:00","2024-02-25T00:00:00","2024-02-26T00:00:00","2024-02-27T00:00:00","2024-02-28T00:00:00","2024-02-29T00:00:00","2024-03-01T00:00:00","2024-03-02T00:00:00","2024-03-03T00:00:00","2024-03-04T00:00:00","2024-03-05T00:00:00","2024-03-06T00:00:00","2024-03-07T00:00:00","2024-03-08T00:00:00","2024-03-09T00:00:00","2024-03-10T00:00:00","2024-03-11T00:00:00","2024-03-12T00:00:00","2024-03-13T00:00:00","2024-03-14T00:00:00","2024-03-15T00:00:00","2024-03-16T00:00:00","2024-03-17T00:00:00","2024-03-18T00:00:00","2024-03-19T00:00:00","2024-03-20T00:00:00","2024-03-21T00:00:00","2024-03-22T00:00:00","2024-03-23T00:00:00","2024-03-24T00:00:00","2024-03-25T00:00:00","2024-03-26T00:00:00","2024-03-27T00:00:00","2024-03-28T00:00:00","2024-03-29T00:00:00","2024-03-30T00:00:00","2024-03-31T00:00:00","2024-04-01T00:00:00","2024-04-02T00:00:00","2024-04-03T00:00:00","2024-04-04T00:00:00","2024-04-05T00:00:00","2024-04-06T00:00:00","2024-04-07T00:00:00","2024-04-08T00:00:00","2024-04-09T00:00:00"],"y":[0.008155747206965769,0.020471065818230105,0.0435959860132249,0.06663445383036071,0.03831169600719653,0.019874913548157958,0.03140021943407967,0.04302999850377387,0.054817232251821224,0.13715060083570996,0.15127152118146836,0.1785696803420922,0.20223540109135385,0.2191001489380655,0.2126323534250707,0.23225199841897481,0.21443794211654388,0.2099003500301755,0.19936541985199918,0.20252872550305323,0.25940012255168865,0.2136268424265093,0.23149774502748066,0.19300812359770636,0.18294076072787568,0.20988698244003667,0.2126523005891685,0.1877263592016789,0.17192238415442462,0.18902302282252914,0.17284359104109126,0.1790938760224725,0.18134763944548826,0.16713365643481382,0.2183461761745702,0.2350111786876543,0.18622471520717032,0.19183447425110778,0.1772515102592842,0.19849933038522782,0.180701118229053,0.17917243045245024,0.1922609444249619,0.21409732747822852,0.18616589160594388,0.17941657836445835,0.16939282746682882,0.15528224992337347,0.197229491110271,0.2081238415558362,0.17886588606404463,0.20168547470009246,0.25389044571469,0.281036302675153,0.24338997730620004,0.23259153142606515,0.26505580202862533,0.24841603056266326,0.26074587236868396,0.28153895195019785,0.2590625408006104,0.25882268042050627,0.17847788628122285,0.15551200052165925,0.1508306019244936,0.12326169111898078,0.16105745444721942,0.12900898574577435,0.12020171113934452,0.12425102921830833,0.15778233981987544,0.12569180533866864,0.1530047752253636,0.1543937553650041,0.1328871999682395,0.14449030938928198,0.15019123755014774,0.1375341449329428,0.14025972417914878,0.13261283238972665,0.13631686926072395,0.1525009912322497,0.19021121127785245,0.16193618480518612,0.21266709421819585,0.16653510852229925,0.1641603907846334,0.17902246295554614,0.18682739989785047,0.1732334902572219,0.1695232038637422,0.15916120641540554,0.14665699236825747,0.1672876930662235,0.1767897764193107,0.16165838764201834,0.18372060084603525,0.19217945691671634,0.21275318676134503,0.22923762763850064],"type":"scatter"},{"customdata":["DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT","DOTUSDT"],"mode":"lines","name":"DOTUSDT_shp1.8980","opacity":0.7,"visible":true,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00","2024-01-31T00:00:00","2024-02-01T00:00:00","2024-02-02T00:00:00","2024-02-03T00:00:00","2024-02-04T00:00:00","2024-02-05T00:00:00","2024-02-06T00:00:00","2024-02-07T00:00:00","2024-02-08T00:00:00","2024-02-09T00:00:00","2024-02-10T00:00:00","2024-02-11T00:00:00","2024-02-12T00:00:00","2024-02-13T00:00:00","2024-02-14T00:00:00","2024-02-15T00:00:00","2024-02-16T00:00:00","2024-02-17T00:00:00","2024-02-18T00:00:00","2024-02-19T00:00:00","2024-02-20T00:00:00","2024-02-21T00:00:00","2024-02-22T00:00:00","2024-02-23T00:00:00","2024-02-24T00:00:00","2024-02-25T00:00:00","2024-02-26T00:00:00","2024-02-27T00:00:00","2024-02-28T00:00:00","2024-02-29T00:00:00","2024-03-01T00:00:00","2024-03-02T00:00:00","2024-03-03T00:00:00","2024-03-04T00:00:00","2024-03-05T00:00:00","2024-03-06T00:00:00","2024-03-07T00:00:00","2024-03-08T00:00:00","2024-03-09T00:00:00","2024-03-10T00:00:00","2024-03-11T00:00:00","2024-03-12T00:00:00","2024-03-13T00:00:00","2024-03-14T00:00:00","2024-03-15T00:00:00","2024-03-16T00:00:00","2024-03-17T00:00:00","2024-03-18T00:00:00","2024-03-19T00:00:00","2024-03-20T00:00:00","2024-03-21T00:00:00","2024-03-22T00:00:00","2024-03-23T00:00:00","2024-03-24T00:00:00","2024-03-25T00:00:00","2024-03-26T00:00:00","2024-03-27T00:00:00","2024-03-28T00:00:00","2024-03-29T00:00:00","2024-03-30T00:00:00","2024-03-31T00:00:00","2024-04-01T00:00:00","2024-04-02T00:00:00","2024-04-03T00:00:00","2024-04-04T00:00:00","2024-04-05T00:00:00","2024-04-06T00:00:00","2024-04-07T00:00:00","2024-04-08T00:00:00","2024-04-09T00:00:00"],"y":[-0.015579900218441467,-0.02562454962838867,-0.010087283316980944,0.002986895152457647,0.003570601550933361,0.006929098405396017,0.03366638673541078,0.022470303922266455,0.034680590738188855,0.031531175067662565,0.02807180729562786,0.051692309183664964,0.07010568201495215,0.0885866133741311,0.1180977350577419,0.11968551974794162,0.13607666260985085,0.13016300284547122,0.13862038221531292,0.13679533191139215,0.1401374184476627,0.15484877175716316,0.13710519750775063,0.18582759163287887,0.16315415585161963,0.13607153938843597,0.16352154698461852,0.18310740057899855,0.19905852346762587,0.21532604276241218,0.2162436923655615,0.19563433666258567,0.1986426616522876,0.18360780598882198,0.2078746003597507,0.20552993744187353,0.18683223569916096,0.18039044639543844,0.19131924371469644,0.17907904480021442,0.16086886707262016,0.16768751388218672,0.17457608952411263,0.16384179896286954,0.15404135938200736,0.1605513052444436,0.12810033309711288,0.09747322637628386,0.08280123365327396,0.07926161810305987,0.08705189176771855,0.12021471899281155,0.14055019238773903,0.13804238415772674,0.1387476015303002,0.11705379094293034,0.11775724135902244,0.11242199292170496,0.1207143993955091,0.10329332119361223,0.11585644532575534,0.15117863367259154,0.14982576512429224,0.16021356065385284,0.177388062574247,0.16911760675564547,0.1755265336829237,0.17699811424144185,0.18047440403165194,0.16340451315753524,0.16513822261771538,0.17790809771890093,0.21327228194811587,0.23776268831347713,0.292303029142569,0.27376242057320366,0.29725876790868266,0.30331286716599193,0.3616961468215665,0.34104470975537615,0.31986366378268016,0.30536119598526,0.25121753592458473,0.23931207540596167,0.22173534199539646,0.22663190542048173,0.23624271300102984,0.2838670062805444,0.30955522944341674,0.2957550406897056,0.273768288949811,0.28757387806658996,0.25486349613833936,0.3020829746335416,0.3340996356239936,0.3229151939292869,0.2789114751676318,0.3148200449474514,0.3131228793027323,0.3469441005745648],"type":"scatter"},{"customdata":["LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT","LINKUSDT"],"mode":"lines","name":"LINKUSDT_shp0.0503","opacity":0.7,"visible":true,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00","2024-01-31T00:00:00","2024-02-01T00:00:00","2024-02-02T00:00:00","2024-02-03T00:00:00","2024-02-04T00:00:00","2024-02-05T00:00:00","2024-02-06T00:00:00","2024-02-07T00:00:00","2024-02-08T00:00:00","2024-02-09T00:00:00","2024-02-10T00:00:00","2024-02-11T00:00:00","2024-02-12T00:00:00","2024-02-13T00:00:00","2024-02-14T00:00:00","2024-02-15T00:00:00","2024-02-16T00:00:00","2024-02-17T00:00:00","2024-02-18T00:00:00","2024-02-19T00:00:00","2024-02-20T00:00:00","2024-02-21T00:00:00","2024-02-22T00:00:00","2024-02-23T00:00:00","2024-02-24T00:00:00","2024-02-25T00:00:00","2024-02-26T00:00:00","2024-02-27T00:00:00","2024-02-28T00:00:00","2024-02-29T00:00:00","2024-03-01T00:00:00","2024-03-02T00:00:00","2024-03-03T00:00:00","2024-03-04T00:00:00","2024-03-05T00:00:00","2024-03-06T00:00:00","2024-03-07T00:00:00","2024-03-08T00:00:00","2024-03-09T00:00:00","2024-03-10T00:00:00","2024-03-11T00:00:00","2024-03-12T00:00:00","2024-03-13T00:00:00","2024-03-14T00:00:00","2024-03-15T00:00:00","2024-03-16T00:00:00","2024-03-17T00:00:00","2024-03-18T00:00:00","2024-03-19T00:00:00","2024-03-20T00:00:00","2024-03-21T00:00:00","2024-03-22T00:00:00","2024-03-23T00:00:00","2024-03-24T00:00:00","2024-03-25T00:00:00","2024-03-26T00:00:00","2024-03-27T00:00:00","2024-03-28T00:00:00","2024-03-29T00:00:00","2024-03-30T00:00:00","2024-03-31T00:00:00","2024-04-01T00:00:00","2024-04-02T00:00:00","2024-04-03T00:00:00","2024-04-04T00:00:00","2024-04-05T00:00:00","2024-04-06T00:00:00","2024-04-07T00:00:00","2024-04-08T00:00:00","2024-04-09T00:00:00"],"y":[-0.030888553175887346,-0.041536665642762594,-0.04047768443008015,-0.03861658355219033,-0.046308909747660665,-0.03347509003652671,-0.05314619991424674,-0.05489559724309423,-0.05167665421707046,-0.0409712437449824,-0.026363032359404293,-0.04728925771132364,-0.0755678879761772,-0.051020946205122986,-0.043764786418304724,-0.057123134867666625,-0.026929351762044362,-0.023705089286285674,0.00029804240483866096,0.002649112542856935,0.044975903216548874,0.08270665676546507,0.07839826060514565,0.10043146733702413,0.11573573886808153,0.14739209744858006,0.1263965784797818,0.14297829540232776,0.1683163980135587,0.12838943077513076,0.10281429221457072,0.05893921869123164,0.05429244865438698,0.07047672874056365,0.10371197071986571,0.10645126861292886,0.14359739461114374,0.11317538336748312,0.0763652907518384,0.07624586374112119,0.08558908662139841,0.08596481447360427,0.042147391755983366,0.04133201481311044,0.015205629763464179,0.02981794222663381,0.03839834919724727,0.019917355175120832,0.010455236785455346,-0.009940064979092389,-0.010191126303540354,0.009706849459659272,-0.009189330504396853,0.0017897734750420558,-0.007832569939337786,-0.02257365451629001,-0.02368851404253003,-0.042926581959118915,-0.05256716920236981,-0.07431791322607928,-0.037018013919801196,-0.035375868627441465,-0.04791068670300036,-0.04288403766429738,-0.044077141089504335,-0.04734581605695598,-0.03469139234431573,-0.019101509479175882,-0.028527966486869727,-0.038744320796817444,-0.04307096523684417,-0.086169540167507,-0.11294826460973417,-0.08781144905440463,-0.05688884620483858,-0.06064310838309528,-0.04887189635835543,-0.04199999286973055,0.01794936386646695,0.0417607246082472,0.04013729487703266,0.021299567191275015,-0.010492391873435203,-0.005476307951954418,-0.01952595897357501,-0.0464351417728297,-0.05781256052852768,-0.07725079197287488,-0.04519187061299168,-0.02740112634108216,-0.02658361110671581,0.0032018428710196556,0.005757365290128602,-0.010561735926459814,0.02056844719725981,0.032588907373702236,0.01220051881760531,0.009359501162277128,-0.007307411405461095,-0.03376861970564293],"type":"scatter"},{"line":{"color":"red","dash":"dash","width":3},"mode":"lines","name":"Selected Mean","showlegend":true,"visible":false,"x":["2024-01-01T00:00:00","2024-01-02T00:00:00","2024-01-03T00:00:00","2024-01-04T00:00:00","2024-01-05T00:00:00","2024-01-06T00:00:00","2024-01-07T00:00:00","2024-01-08T00:00:00","2024-01-09T00:00:00","2024-01-10T00:00:00","2024-01-11T00:00:00","2024-01-12T00:00:00","2024-01-13T00:00:00","2024-01-14T00:00:00","2024-01-15T00:00:00","2024-01-16T00:00:00","2024-01-17T00:00:00","2024-01-18T00:00:00","2024-01-19T00:00:00","2024-01-20T00:00:00","2024-01-21T00:00:00","2024-01-22T00:00:00","2024-01-23T00:00:00","2024-01-24T00:00:00","2024-01-25T00:00:00","2024-01-26T00:00:00","2024-01-27T00:00:00","2024-01-28T00:00:00","2024-01-29T00:00:00","2024-01-30T00:00:00","2024-01-31T00:00:00","2024-02-01T00:00:00","2024-02-02T00:00:00","2024-02-03T00:00:00","2024-02-04T00:00:00","2024-02-05T00:00:00","2024-02-06T00:00:00","2024-02-07T00:00:00","2024-02-08T00:00:00","2024-02-09T00:00:00","2024-02-10T00:00:00","2024-02-11T00:00:00","2024-02-12T00:00:00","2024-02-13T00:00:00","2024-02-14T00:00:00","2024-02-15T00:00:00","2024-02-16T00:00:00","2024-02-17T00:00:00","2024-02-18T00:00:00","2024-02-19T00:00:00","2024-02-20T00:00:00","2024-02-21T00:00:00","2024-02-22T00:00:00","2024-02-23T00:00:00","2024-02-24T00:00:00","2024-02-25T00:00:00","2024-02-26T00:00:00","2024-02-27T00:00:00","2024-02-28T00:00:00","2024-02-29T00:00:00","2024-03-01T00:00:00","2024-03-02T00:00:00","2024-03-03T00:00:00","2024-03-04T00:00:00","2024-03-05T00:00:00","2024-03-06T00:00:00","2024-03-07T00:00:00","2024-03-08T00:00:00","2024-03-09T00:00:00","2024-03-10T00:00:00","2024-03-11T00:00:00","2024-03-12T00:00:00","2024-03-13T00:00:00","2024-03-14T00:00:00","2024-03-15T00:00:00","2024-03-16T00:00:00","2024-03-17T00:00:00","2024-03-18T00:00:00","2024-03-19T00:00:00","2024-03-20T00:00:00","2024-03-21T00:00:00","2024-03-22T00:00:00","2024-03-23T00:00:00","2024-03-24T00:00:00","2024-03-25T00:00:00","2024-03-26T00:00:00","2024-03-27T00:00:00","2024-03-28T00:00:00","2024-03-29T00:00:00","2024-03-30T00:00:00","2024-03-31T00:00:00","2024-04-01T00:00:00","2024-04-02T00:00:00","2024-04-03T00:00:00","2024-04-04T00:00:00","2024-04-05T00:00:00","2024-04-06T00:00:00","2024-04-07T00:00:00","2024-04-08T00:00:00","2024-04-09T00:00:00"],"y":[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],"type":"scatter"}],                        {"template":{"data":{"barpolar":[{"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"white","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"#C8D4E3","linecolor":"#C8D4E3","minorgridcolor":"#C8D4E3","startlinecolor":"#2a3f5f"},"type":"carpet"}],"choropleth":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"choropleth"}],"contourcarpet":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"contourcarpet"}],"contour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"contour"}],"heatmapgl":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmapgl"}],"heatmap":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"heatmap"}],"histogram2dcontour":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2dcontour"}],"histogram2d":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"histogram2d"}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"mesh3d":[{"colorbar":{"outlinewidth":0,"ticks":""},"type":"mesh3d"}],"parcoords":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"parcoords"}],"pie":[{"automargin":true,"type":"pie"}],"scatter3d":[{"line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatter3d"}],"scattercarpet":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattercarpet"}],"scattergeo":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergeo"}],"scattergl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattergl"}],"scattermapbox":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scattermapbox"}],"scatterpolargl":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolargl"}],"scatterpolar":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterpolar"}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"scatterternary":[{"marker":{"colorbar":{"outlinewidth":0,"ticks":""}},"type":"scatterternary"}],"surface":[{"colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"type":"surface"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}]},"layout":{"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"autotypenumbers":"strict","coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]],"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]},"colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"geo":{"bgcolor":"white","lakecolor":"white","landcolor":"white","showlakes":true,"showland":true,"subunitcolor":"#C8D4E3"},"hoverlabel":{"align":"left"},"hovermode":"closest","mapbox":{"style":"light"},"paper_bgcolor":"white","plot_bgcolor":"white","polar":{"angularaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""},"bgcolor":"white","radialaxis":{"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":""}},"scene":{"xaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"yaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"},"zaxis":{"backgroundcolor":"white","gridcolor":"#DFE8F3","gridwidth":2,"linecolor":"#EBF0F8","showbackground":true,"ticks":"","zerolinecolor":"#EBF0F8"}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"ternary":{"aaxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"baxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""},"bgcolor":"white","caxis":{"gridcolor":"#DFE8F3","linecolor":"#A2B1C6","ticks":""}},"title":{"x":0.05},"xaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2},"yaxis":{"automargin":true,"gridcolor":"#EBF0F8","linecolor":"#EBF0F8","ticks":"","title":{"standoff":15},"zerolinecolor":"#EBF0F8","zerolinewidth":2}}},"title":{"text":"加密货币累计收益动态均值图表","x":0.5,"xanchor":"center"},"xaxis":{"title":{"text":"Time"},"tickformat":"%Y-%m-%d","hoverformat":"%Y-%m-%d","showspikes":true,"spikethickness":1,"spikecolor":"gray","spikesnap":"cursor","spikemode":"across","spikedash":"solid","showgrid":true,"gridwidth":1,"gridcolor":"LightGrey"},"yaxis":{"title":{"text":"Cumulative Returns"},"tickformat":".4f","hoverformat":".4f","showspikes":true,"spikethickness":1,"spikecolor":"gray","spikesnap":"cursor","spikemode":"across","spikedash":"solid","showgrid":true,"gridwidth":1,"gridcolor":"LightGrey"},"legend":{"title":{"text":"Assets (Click to select\u002fdeselect)"}},"hovermode":"x unified","showlegend":true,"height":600},                        {"responsive": true}                    )                };                            </script>        </div>
            
    <script>
        // 存储原始数据
        var originalData = {"1704067200000":{"BTCUSDT":0.0109342831,"ETHUSDT":-0.0273074148,"ADAUSDT":0.0081557472,"DOTUSDT":-0.0155799002,"LINKUSDT":-0.0308885532},"1704153600000":{"BTCUSDT":0.0091496949,"ETHUSDT":-0.034517894,"ADAUSDT":0.0204710658,"DOTUSDT":-0.0256245496,"LINKUSDT":-0.0415366656},"1704240000000":{"BTCUSDT":0.0232311384,"ETHUSDT":-0.0401701065,"ADAUSDT":0.043595986,"DOTUSDT":-0.0100872833,"LINKUSDT":-0.0404776844},"1704326400000":{"BTCUSDT":0.055422601,"ETHUSDT":-0.0546112708,"ADAUSDT":0.0666344538,"DOTUSDT":0.0029868952,"LINKUSDT":-0.0386165836},"1704412800000":{"BTCUSDT":0.0515354083,"ETHUSDT":-0.0567154359,"ADAUSDT":0.038311696,"DOTUSDT":0.0035706016,"LINKUSDT":-0.0463089097},"1704499200000":{"BTCUSDT":0.0476628777,"ETHUSDT":-0.0481494526,"ADAUSDT":0.0198749135,"DOTUSDT":0.0069290984,"LINKUSDT":-0.03347509},"1704585600000":{"BTCUSDT":0.0818001935,"ETHUSDT":-0.0112902604,"ADAUSDT":0.0314002194,"DOTUSDT":0.0336663867,"LINKUSDT":-0.0531461999},"1704672000000":{"BTCUSDT":0.0994862144,"ETHUSDT":-0.006849415,"ADAUSDT":0.0430299985,"DOTUSDT":0.0224703039,"LINKUSDT":-0.0548955972},"1704758400000":{"BTCUSDT":0.0902620883,"ETHUSDT":-0.000740538,"ADAUSDT":0.0548172323,"DOTUSDT":0.0346805907,"LINKUSDT":-0.0516766542},"1704844800000":{"BTCUSDT":0.1031830034,"ETHUSDT":-0.0012290943,"ADAUSDT":0.1371506008,"DOTUSDT":0.0315311751,"LINKUSDT":-0.0409712437},"1704931200000":{"BTCUSDT":0.0940614959,"ETHUSDT":-0.0385585806,"ADAUSDT":0.1512715212,"DOTUSDT":0.0280718073,"LINKUSDT":-0.0263630324},"1705017600000":{"BTCUSDT":0.0849648176,"ETHUSDT":-0.03810697,"ADAUSDT":0.1785696803,"DOTUSDT":0.0516923092,"LINKUSDT":-0.0472892577},"1705104000000":{"BTCUSDT":0.0913001935,"ETHUSDT":-0.0359863766,"ADAUSDT":0.2022354011,"DOTUSDT":0.070105682,"LINKUSDT":-0.075567888},"1705190400000":{"BTCUSDT":0.0506322316,"ETHUSDT":0.0124696161,"ADAUSDT":0.2191001489,"DOTUSDT":0.0885866134,"LINKUSDT":-0.0510209462},"1705276800000":{"BTCUSDT":0.0154377784,"ETHUSDT":0.0095868931,"ADAUSDT":0.2126323534,"DOTUSDT":0.1180977351,"LINKUSDT":-0.0437647864},"1705363200000":{"BTCUSDT":0.0050338562,"ETHUSDT":0.0166852449,"ADAUSDT":0.2322519984,"DOTUSDT":0.1196855197,"LINKUSDT":-0.0571231349},"1705449600000":{"BTCUSDT":-0.0143197013,"ETHUSDT":0.0169961113,"ADAUSDT":0.2144379421,"DOTUSDT":0.1360766626,"LINKUSDT":-0.0269293518},"1705536000000":{"BTCUSDT":-0.0071390729,"ETHUSDT":-0.005757713,"ADAUSDT":0.20990035,"DOTUSDT":0.1301630028,"LINKUSDT":-0.0237050893},"1705622400000":{"BTCUSDT":-0.0241770445,"ETHUSDT":0.0179613846,"ADAUSDT":0.1993654199,"DOTUSDT":0.1386203822,"LINKUSDT":0.0002980424},"1705708800000":{"BTCUSDT":-0.0507643889,"ETHUSDT":0.0342881218,"ADAUSDT":0.2025287255,"DOTUSDT":0.1367953319,"LINKUSDT":0.0026491125},"1705795200000":{"BTCUSDT":-0.0219902332,"ETHUSDT":0.0516855089,"ADAUSDT":0.2594001226,"DOTUSDT":0.1401374184,"LINKUSDT":0.0449759032},"1705881600000":{"BTCUSDT":-0.025428452,"ETHUSDT":0.0336094022,"ADAUSDT":0.2136268424,"DOTUSDT":0.1548487718,"LINKUSDT":0.0827066568},"1705968000000":{"BTCUSDT":-0.0231376591,"ETHUSDT":0.0636418394,"ADAUSDT":0.231497745,"DOTUSDT":0.1371051975,"LINKUSDT":0.0783982606},"1706054400000":{"BTCUSDT":-0.0499964537,"ETHUSDT":0.0348841324,"ADAUSDT":0.1930081236,"DOTUSDT":0.1858275916,"LINKUSDT":0.1004314673},"1706140800000":{"BTCUSDT":-0.0593897606,"ETHUSDT":0.0480655984,"ADAUSDT":0.1829407607,"DOTUSDT":0.1631541559,"LINKUSDT":0.1157357389},"1706227200000":{"BTCUSDT":-0.0563624519,"ETHUSDT":0.0950284878,"ADAUSDT":0.2098869824,"DOTUSDT":0.1360715394,"LINKUSDT":0.1473920974},"1706313600000":{"BTCUSDT":-0.0771412295,"ETHUSDT":0.0744302064,"ADAUSDT":0.2126523006,"DOTUSDT":0.163521547,"LINKUSDT":0.1263965785},"1706400000000":{"BTCUSDT":-0.0692840465,"ETHUSDT":0.0633356888,"ADAUSDT":0.1877263592,"DOTUSDT":0.1831074006,"LINKUSDT":0.1429782954},"1706486400000":{"BTCUSDT":-0.0795338107,"ETHUSDT":0.0665182816,"ADAUSDT":0.1719223842,"DOTUSDT":0.1990585235,"LINKUSDT":0.168316398},"1706572800000":{"BTCUSDT":-0.0839832292,"ETHUSDT":0.0568454801,"ADAUSDT":0.1890230228,"DOTUSDT":0.2153260428,"LINKUSDT":0.1283894308},"1706659200000":{"BTCUSDT":-0.0940906794,"ETHUSDT":0.0251260928,"ADAUSDT":0.172843591,"DOTUSDT":0.2162436924,"LINKUSDT":0.1028142922},"1706745600000":{"BTCUSDT":-0.0596248487,"ETHUSDT":0.0275569328,"ADAUSDT":0.179093876,"DOTUSDT":0.1956343367,"LINKUSDT":0.0589392187},"1706832000000":{"BTCUSDT":-0.0589383226,"ETHUSDT":0.0067529388,"ADAUSDT":0.1813476394,"DOTUSDT":0.1986426617,"LINKUSDT":0.0542924487},"1706918400000":{"BTCUSDT":-0.0779046853,"ETHUSDT":0.0172955032,"ADAUSDT":0.1671336564,"DOTUSDT":0.183607806,"LINKUSDT":0.0704767287},"1707004800000":{"BTCUSDT":-0.0618132938,"ETHUSDT":-0.0003937241,"ADAUSDT":0.2183461762,"DOTUSDT":0.2078746004,"LINKUSDT":0.1037119707},"1707091200000":{"BTCUSDT":-0.0837826928,"ETHUSDT":0.0315923653,"ADAUSDT":0.2350111787,"DOTUSDT":0.2055299374,"LINKUSDT":0.1064512686},"1707177600000":{"BTCUSDT":-0.0790391867,"ETHUSDT":0.0164639954,"ADAUSDT":0.1862247152,"DOTUSDT":0.1868322357,"LINKUSDT":0.1435973946},"1707264000000":{"BTCUSDT":-0.1142138137,"ETHUSDT":0.0109331806,"ADAUSDT":0.1918344743,"DOTUSDT":0.1803904464,"LINKUSDT":0.1131753834},"1707350400000":{"BTCUSDT":-0.1368578046,"ETHUSDT":0.0283923448,"ADAUSDT":0.1772515103,"DOTUSDT":0.1913192437,"LINKUSDT":0.0763652908},"1707436800000":{"BTCUSDT":-0.1325962776,"ETHUSDT":0.0041045083,"ADAUSDT":0.1984993304,"DOTUSDT":0.1790790448,"LINKUSDT":0.0762458637},"1707523200000":{"BTCUSDT":-0.1189179007,"ETHUSDT":0.0096764837,"ADAUSDT":0.1807011182,"DOTUSDT":0.1608688671,"LINKUSDT":0.0855890866},"1707609600000":{"BTCUSDT":-0.1150170281,"ETHUSDT":0.0370819862,"ADAUSDT":0.1791724305,"DOTUSDT":0.1676875139,"LINKUSDT":0.0859648145},"1707696000000":{"BTCUSDT":-0.1161789803,"ETHUSDT":0.0047772301,"ADAUSDT":0.1922609444,"DOTUSDT":0.1745760895,"LINKUSDT":0.0421473918},"1707782400000":{"BTCUSDT":-0.1206175948,"ETHUSDT":0.0094923253,"ADAUSDT":0.2140973275,"DOTUSDT":0.163841799,"LINKUSDT":0.0413320148},"1707868800000":{"BTCUSDT":-0.1457419369,"ETHUSDT":0.0157488113,"ADAUSDT":0.1861658916,"DOTUSDT":0.1540413594,"LINKUSDT":0.0152056298},"1707955200000":{"BTCUSDT":-0.1571863332,"ETHUSDT":0.0326472732,"ADAUSDT":0.1794165784,"DOTUSDT":0.1605513052,"LINKUSDT":0.0298179422},"1708041600000":{"BTCUSDT":-0.1641081726,"ETHUSDT":0.0081332449,"ADAUSDT":0.1693928275,"DOTUSDT":0.1281003331,"LINKUSDT":0.0383983492},"1708128000000":{"BTCUSDT":-0.1455994842,"ETHUSDT":-0.0174825461,"ADAUSDT":0.1552822499,"DOTUSDT":0.0974732264,"LINKUSDT":0.0199173552},"1708214400000":{"BTCUSDT":-0.1388733308,"ETHUSDT":-0.0062436947,"ADAUSDT":0.1972294911,"DOTUSDT":0.0828012337,"LINKUSDT":0.0104552368},"1708300800000":{"BTCUSDT":-0.168376222,"ETHUSDT":0.0006526695,"ADAUSDT":0.2081238416,"DOTUSDT":0.0792616181,"LINKUSDT":-0.009940065},"1708387200000":{"BTCUSDT":-0.1621542796,"ETHUSDT":0.0066664489,"ADAUSDT":0.1788658861,"DOTUSDT":0.0870518918,"LINKUSDT":-0.0101911263},"1708473600000":{"BTCUSDT":-0.1677692246,"ETHUSDT":0.0146482712,"ADAUSDT":0.2016854747,"DOTUSDT":0.120214719,"LINKUSDT":0.0097068495},"1708560000000":{"BTCUSDT":-0.1782041003,"ETHUSDT":0.0018632013,"ADAUSDT":0.2538904457,"DOTUSDT":0.1405501924,"LINKUSDT":-0.0091893305},"1708646400000":{"BTCUSDT":-0.1673288431,"ETHUSDT":0.0075187931,"ADAUSDT":0.2810363027,"DOTUSDT":0.1380423842,"LINKUSDT":0.0017897735},"1708732800000":{"BTCUSDT":-0.1493265006,"ETHUSDT":0.0144318324,"ADAUSDT":0.2433899773,"DOTUSDT":0.1387476015,"LINKUSDT":-0.0078325699},"1708819200000":{"BTCUSDT":-0.1326315208,"ETHUSDT":0.0009530479,"ADAUSDT":0.2325915314,"DOTUSDT":0.1170537909,"LINKUSDT":-0.0225736545},"1708905600000":{"BTCUSDT":-0.1463223688,"ETHUSDT":0.0393050546,"ADAUSDT":0.265055802,"DOTUSDT":0.1177572414,"LINKUSDT":-0.023688514},"1708992000000":{"BTCUSDT":-0.150748045,"ETHUSDT":0.0501934987,"ADAUSDT":0.2484160306,"DOTUSDT":0.1124219929,"LINKUSDT":-0.042926582},"1709078400000":{"BTCUSDT":-0.1442722707,"ETHUSDT":0.0262217084,"ADAUSDT":0.2607458724,"DOTUSDT":0.1207143994,"LINKUSDT":-0.0525671692},"1709164800000":{"BTCUSDT":-0.1267205226,"ETHUSDT":0.0407233214,"ADAUSDT":0.281538952,"DOTUSDT":0.1032933212,"LINKUSDT":-0.0743179132},"1709251200000":{"BTCUSDT":-0.1342163037,"ETHUSDT":0.0214765658,"ADAUSDT":0.2590625408,"DOTUSDT":0.1158564453,"LINKUSDT":-0.0370180139},"1709337600000":{"BTCUSDT":-0.1365653303,"ETHUSDT":0.038577812,"ADAUSDT":0.2588226804,"DOTUSDT":0.1511786337,"LINKUSDT":-0.0353758686},"1709424000000":{"BTCUSDT":-0.1548068551,"ETHUSDT":0.063682223,"ADAUSDT":0.1784778863,"DOTUSDT":0.1498257651,"LINKUSDT":-0.0479106867},"1709510400000":{"BTCUSDT":-0.1741821747,"ETHUSDT":0.0472870014,"ADAUSDT":0.1555120005,"DOTUSDT":0.1602135607,"LINKUSDT":-0.0428840377},"1709596800000":{"BTCUSDT":-0.1599363907,"ETHUSDT":0.0685129143,"ADAUSDT":0.1508306019,"DOTUSDT":0.1773880626,"LINKUSDT":-0.0440771411},"1709683200000":{"BTCUSDT":-0.1363097692,"ETHUSDT":0.0784026623,"ADAUSDT":0.1232616911,"DOTUSDT":0.1691176068,"LINKUSDT":-0.0473458161},"1709769600000":{"BTCUSDT":-0.1366899678,"ETHUSDT":0.0972113022,"ADAUSDT":0.1610574544,"DOTUSDT":0.1755265337,"LINKUSDT":-0.0346913923},"1709856000000":{"BTCUSDT":-0.1184994574,"ETHUSDT":0.1399321675,"ADAUSDT":0.1290089857,"DOTUSDT":0.1769981142,"LINKUSDT":-0.0191015095},"1709942400000":{"BTCUSDT":-0.1112423098,"ETHUSDT":0.1354775835,"ADAUSDT":0.1202017111,"DOTUSDT":0.180474404,"LINKUSDT":-0.0285279665},"1710028800000":{"BTCUSDT":-0.121820655,"ETHUSDT":0.1194960508,"ADAUSDT":0.1242510292,"DOTUSDT":0.1634045132,"LINKUSDT":-0.0387443208},"1710115200000":{"BTCUSDT":-0.1145950725,"ETHUSDT":0.100699389,"ADAUSDT":0.1577823398,"DOTUSDT":0.1651382226,"LINKUSDT":-0.0430709652},"1710201600000":{"BTCUSDT":-0.0864739645,"ETHUSDT":0.0838408507,"ADAUSDT":0.1256918053,"DOTUSDT":0.1779080977,"LINKUSDT":-0.0861695402},"1710288000000":{"BTCUSDT":-0.0862149988,"ETHUSDT":0.0832533719,"ADAUSDT":0.1530047752,"DOTUSDT":0.2132722819,"LINKUSDT":-0.1129482646},"1710374400000":{"BTCUSDT":-0.0567062557,"ETHUSDT":0.0917277058,"ADAUSDT":0.1543937554,"DOTUSDT":0.2377626883,"LINKUSDT":-0.0878114491},"1710460800000":{"BTCUSDT":-0.1051867454,"ETHUSDT":0.0988608538,"ADAUSDT":0.1328872,"DOTUSDT":0.2923030291,"LINKUSDT":-0.0568888462},"1710547200000":{"BTCUSDT":-0.089582947,"ETHUSDT":0.1181389005,"ADAUSDT":0.1444903094,"DOTUSDT":0.2737624206,"LINKUSDT":-0.0606431084},"1710633600000":{"BTCUSDT":-0.0870875472,"ETHUSDT":0.1195477978,"ADAUSDT":0.1501912376,"DOTUSDT":0.2972587679,"LINKUSDT":-0.0488718964},"1710720000000":{"BTCUSDT":-0.0916339855,"ETHUSDT":0.1532133631,"ADAUSDT":0.1375341449,"DOTUSDT":0.3033128672,"LINKUSDT":-0.0419999929},"1710806400000":{"BTCUSDT":-0.089058572,"ETHUSDT":0.1482624605,"ADAUSDT":0.1402597242,"DOTUSDT":0.3616961468,"LINKUSDT":0.0179493639},"1710892800000":{"BTCUSDT":-0.1243588079,"ETHUSDT":0.2118800858,"ADAUSDT":0.1326128324,"DOTUSDT":0.3410447098,"LINKUSDT":0.0417607246},"1710979200000":{"BTCUSDT":-0.1273302418,"ETHUSDT":0.2282566418,"ADAUSDT":0.1363168693,"DOTUSDT":0.3198636638,"LINKUSDT":0.0401372949},"1711065600000":{"BTCUSDT":-0.1202247452,"ETHUSDT":0.2084287093,"ADAUSDT":0.1525009912,"DOTUSDT":0.305361196,"LINKUSDT":0.0212995672},"1711152000000":{"BTCUSDT":-0.0933406778,"ETHUSDT":0.1837551932,"ADAUSDT":0.1902112113,"DOTUSDT":0.2512175359,"LINKUSDT":-0.0104923919},"1711238400000":{"BTCUSDT":-0.1018319089,"ETHUSDT":0.1963615329,"ADAUSDT":0.1619361848,"DOTUSDT":0.2393120754,"LINKUSDT":-0.005476308},"1711324800000":{"BTCUSDT":-0.115457004,"ETHUSDT":0.1922110488,"ADAUSDT":0.2126670942,"DOTUSDT":0.221735342,"LINKUSDT":-0.019525959},"1711411200000":{"BTCUSDT":-0.1234489745,"ETHUSDT":0.2104280454,"ADAUSDT":0.1665351085,"DOTUSDT":0.2266319054,"LINKUSDT":-0.0464351418},"1711497600000":{"BTCUSDT":-0.1065244902,"ETHUSDT":0.2230948753,"ADAUSDT":0.1641603908,"DOTUSDT":0.236242713,"LINKUSDT":-0.0578125605},"1711584000000":{"BTCUSDT":-0.0997563934,"ETHUSDT":0.2225364368,"ADAUSDT":0.179022463,"DOTUSDT":0.2838670063,"LINKUSDT":-0.077250792},"1711670400000":{"BTCUSDT":-0.1083944145,"ETHUSDT":0.2030542498,"ADAUSDT":0.1868273999,"DOTUSDT":0.3095552294,"LINKUSDT":-0.0451918706},"1711756800000":{"BTCUSDT":-0.0983501667,"ETHUSDT":0.1678084362,"ADAUSDT":0.1732334903,"DOTUSDT":0.2957550407,"LINKUSDT":-0.0274011263},"1711843200000":{"BTCUSDT":-0.0956979178,"ETHUSDT":0.1585473661,"ADAUSDT":0.1695232039,"DOTUSDT":0.2737682889,"LINKUSDT":-0.0265836111},"1711929600000":{"BTCUSDT":-0.077274662,"ETHUSDT":0.1795494848,"ADAUSDT":0.1591612064,"DOTUSDT":0.2875738781,"LINKUSDT":0.0032018429},"1712016000000":{"BTCUSDT":-0.0893079803,"ETHUSDT":0.1857797176,"ADAUSDT":0.1466569924,"DOTUSDT":0.2548634961,"LINKUSDT":0.0057573653},"1712102400000":{"BTCUSDT":-0.0943652743,"ETHUSDT":0.1574220618,"ADAUSDT":0.1672876931,"DOTUSDT":0.3020829746,"LINKUSDT":-0.0105617359},"1712188800000":{"BTCUSDT":-0.1005617748,"ETHUSDT":0.1625883523,"ADAUSDT":0.1767897764,"DOTUSDT":0.3340996356,"LINKUSDT":0.0205684472},"1712275200000":{"BTCUSDT":-0.1259891623,"ETHUSDT":0.1727102506,"ADAUSDT":0.1616583876,"DOTUSDT":0.3229151939,"LINKUSDT":0.0325889074},"1712361600000":{"BTCUSDT":-0.1199389048,"ETHUSDT":0.1531527873,"ADAUSDT":0.1837206008,"DOTUSDT":0.2789114752,"LINKUSDT":0.0122005188},"1712448000000":{"BTCUSDT":-0.114463952,"ETHUSDT":0.1578513108,"ADAUSDT":0.1921794569,"DOTUSDT":0.3148200449,"LINKUSDT":0.0093595012},"1712534400000":{"BTCUSDT":-0.1134878529,"ETHUSDT":0.1603571029,"ADAUSDT":0.2127531868,"DOTUSDT":0.3131228793,"LINKUSDT":-0.0073074114},"1712620800000":{"BTCUSDT":-0.1167606276,"ETHUSDT":0.134992386,"ADAUSDT":0.2292376276,"DOTUSDT":0.3469441006,"LINKUSDT":-0.0337686197}};
        var columnNames = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT'];
        var timeIndex = ['2024-01-01 00:00:00', '2024-01-02 00:00:00', '2024-01-03 00:00:00', '2024-01-04 00:00:00', '2024-01-05 00:00:00', '2024-01-06 00:00:00', '2024-01-07 00:00:00', '2024-01-08 00:00:00', '2024-01-09 00:00:00', '2024-01-10 00:00:00', '2024-01-11 00:00:00', '2024-01-12 00:00:00', '2024-01-13 00:00:00', '2024-01-14 00:00:00', '2024-01-15 00:00:00', '2024-01-16 00:00:00', '2024-01-17 00:00:00', '2024-01-18 00:00:00', '2024-01-19 00:00:00', '2024-01-20 00:00:00', '2024-01-21 00:00:00', '2024-01-22 00:00:00', '2024-01-23 00:00:00', '2024-01-24 00:00:00', '2024-01-25 00:00:00', '2024-01-26 00:00:00', '2024-01-27 00:00:00', '2024-01-28 00:00:00', '2024-01-29 00:00:00', '2024-01-30 00:00:00', '2024-01-31 00:00:00', '2024-02-01 00:00:00', '2024-02-02 00:00:00', '2024-02-03 00:00:00', '2024-02-04 00:00:00', '2024-02-05 00:00:00', '2024-02-06 00:00:00', '2024-02-07 00:00:00', '2024-02-08 00:00:00', '2024-02-09 00:00:00', '2024-02-10 00:00:00', '2024-02-11 00:00:00', '2024-02-12 00:00:00', '2024-02-13 00:00:00', '2024-02-14 00:00:00', '2024-02-15 00:00:00', '2024-02-16 00:00:00', '2024-02-17 00:00:00', '2024-02-18 00:00:00', '2024-02-19 00:00:00', '2024-02-20 00:00:00', '2024-02-21 00:00:00', '2024-02-22 00:00:00', '2024-02-23 00:00:00', '2024-02-24 00:00:00', '2024-02-25 00:00:00', '2024-02-26 00:00:00', '2024-02-27 00:00:00', '2024-02-28 00:00:00', '2024-02-29 00:00:00', '2024-03-01 00:00:00', '2024-03-02 00:00:00', '2024-03-03 00:00:00', '2024-03-04 00:00:00', '2024-03-05 00:00:00', '2024-03-06 00:00:00', '2024-03-07 00:00:00', '2024-03-08 00:00:00', '2024-03-09 00:00:00', '2024-03-10 00:00:00', '2024-03-11 00:00:00', '2024-03-12 00:00:00', '2024-03-13 00:00:00', '2024-03-14 00:00:00', '2024-03-15 00:00:00', '2024-03-16 00:00:00', '2024-03-17 00:00:00', '2024-03-18 00:00:00', '2024-03-19 00:00:00', '2024-03-20 00:00:00', '2024-03-21 00:00:00', '2024-03-22 00:00:00', '2024-03-23 00:00:00', '2024-03-24 00:00:00', '2024-03-25 00:00:00', '2024-03-26 00:00:00', '2024-03-27 00:00:00', '2024-03-28 00:00:00', '2024-03-29 00:00:00', '2024-03-30 00:00:00', '2024-03-31 00:00:00', '2024-04-01 00:00:00', '2024-04-02 00:00:00', '2024-04-03 00:00:00', '2024-04-04 00:00:00', '2024-04-05 00:00:00', '2024-04-06 00:00:00', '2024-04-07 00:00:00', '2024-04-08 00:00:00', '2024-04-09 00:00:00'];
        
        // 等待图表加载完成
        document.addEventListener('DOMContentLoaded', function() {
            var gd = document.getElementsByClassName('plotly-graph-div')[0];
            if (!gd) return;
            
            // 监听图例点击事件
            gd.on('plotly_legendclick', function(data) {
                setTimeout(function() {
                    updateMeanLine();
                }, 100); // 延迟执行，确保图例状态已更新
                return true; // 允许默认的图例点击行为
            });
            
            // 监听图例双击事件
            gd.on('plotly_legenddoubleclick', function(data) {
                setTimeout(function() {
                    updateMeanLine();
                }, 100);
                return true;
            });
            
            function updateMeanLine() {
                var visibleTraces = [];
                var meanTraceIndex = gd.data.length - 1; // 均值线是最后一个trace
                
                // 找出当前可见的数据线（排除均值线）
                for (var i = 0; i < gd.data.length - 1; i++) {
                    if (gd.data[i].visible === true || gd.data[i].visible === undefined) {
                        visibleTraces.push(i);
                    }
                }
                
                if (visibleTraces.length === 0) {
                    // 如果没有可见的线，隐藏均值线
                    Plotly.restyle(gd, {'visible': false}, [meanTraceIndex]);
                    return;
                }
                
                // 计算选中线条的均值
                var meanValues = [];
                var selectedCodes = [];
                
                for (var i = 0; i < timeIndex.length; i++) {
                    var sum = 0;
                    var count = 0;
                    
                    for (var j = 0; j < visibleTraces.length; j++) {
                        var traceIndex = visibleTraces[j];
                        var colName = columnNames[traceIndex];
                        selectedCodes.push(colName);
                        
                        if (gd.data[traceIndex].y && gd.data[traceIndex].y[i] !== undefined) {
                            sum += gd.data[traceIndex].y[i];
                            count++;
                        }
                    }
                    
                    meanValues.push(count > 0 ? sum / count : 0);
                }
                
                // 去重选中的代码名称
                selectedCodes = [...new Set(selectedCodes)];
                
                // 计算选中线条的平均夏普比率
                var totalSharp = 0;
                for (var k = 0; k < selectedCodes.length; k++) {
                    var codeName = selectedCodes[k];
                    var sharpValue = parseFloat(gd.data[visibleTraces[k]].name.split('_shp')[1]);
                    if (!isNaN(sharpValue)) {
                        totalSharp += sharpValue;
                    }
                }
                var avgSharp = selectedCodes.length > 0 ? (totalSharp / selectedCodes.length).toFixed(4) : '0.0000';
                
                // 更新均值线
                var meanName = 'Mean of ' + selectedCodes.length + ' selected (avg_shp' + avgSharp + ')';
                
                Plotly.restyle(gd, {
                    'y': [meanValues],
                    'visible': true,
                    'name': meanName
                }, [meanTraceIndex]);
                
                console.log('Updated mean line for:', selectedCodes);
            }
            
            // 初始化时计算所有线的均值
            updateMeanLine();
        });
    </script>
    
        </div>
    </body>
    </html>
    